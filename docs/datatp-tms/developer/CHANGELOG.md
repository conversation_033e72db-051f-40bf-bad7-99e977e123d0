---
sidebar_position: 2
---

# Changelog

All notable changes to this project will be documented in this file.

### [Unreleased]

- [Quan]: Fix bug search TMS Partner Address.

### [R20250805]

- [Quan]: - Remove join location, account tại các query TMS và thực hiện load các thông tin location, account tại hàm search.
- [Chien]: - Fix bugs input VehicleTripGoodsTrackingList - Fix bugs webhook clientContext không nhận company id

### [R20250804]

- [Chien]: - Thêm cột và cập nhật giao diện cho VehicleTripGoodsTrackingList - Thêm chức năng gửi mail và in biên bản cho VehicleTripGoodsTrackingList

### [R20250731]

- [Chien]: - C<PERSON><PERSON> nhật VehicleTripGoodsTracking chia cung đường - Cậ<PERSON> nhật TMSBill cho phép đánh dấu các chuyến chạy ghép và thêm báo cáo tổng file ghép chuyến

### [R20250727]

1. Tasks:

- [Quan]: - <PERSON><PERSON><PERSON> l<PERSON> người export excel fee template và vehicle info template - Report tuyến đường dạng bảng
- [Chien]: - Cập nhật, fix bugs tms house-bill

### [R20250724]

1. Tasks:

- [Chien] - Cập nhật biểu đồ báo cáo tuyến đường TMSBill - Fix bugs BotEvent không hoạt động(Ko gửi tin nhắn zalo/mail)

### [R20250721]

### [R20250717]

1. Tasks:

- [Quan]:
  - Implement TMSConfigRoute, Zone, ZoneMapping.
  - Fix bug Vehicle Info Template.

### [R20250718]

1. Tasks:

- [Quan]: - Cập nhật địa chỉ kho cho tmsbill
- [Chien]:
  - Cập nhật báo cáo biểu diễn trên biểu đồ số chuyến theo tuyến đường cho từng thầu phụ dựa trên tms-bill
  - Config target số chuyến cho thầu phụ theo tuần/tháng và biểu diễn trên đồ thị

2. Script:

- migrate:run --script tms/DropTable.groovy
- server:migrate:run --company beehph --script tms/InitConfigRoute.groovy
- server:migrate:run --script tms/MigrationBillInvAddress.groovy

### [R20250716]

1. Tasks:

- [Quan] - Implement TMSConfigRoute, Zone, ZoneMapping.
- [Chien] - Câp nhật Zone, Zone Mapping Entity - Fix bugs BFSOne template

2. Script

- migrate:run --script tms/DropZoneTable.groovy
- server:migrate:run --script tms/MigrationLocation.groovy

### [R20250714]

1. Tasks:

- [Quan] - Cập nhập địa chỉ mới cho location kho/bãi
- [Chien] - Câp nhật Zone, Zone Mapping Entity - Fix bugs BFSOne template

2. Script

- migrate:run --script tms/DropZoneTable.groovy
- server:migrate:run --script tms/MigrationLocation.groovy

### [R20250714]

1. Tasks:

- [Chien] - Fix bugs TMSBillList - Màn TMSBillList thêm chức năng import/export file JSON để xử lỗi khi người dùng gặp các vấn đề lưu không thành công

### [R20250710]

1. Tasks:

- [Chien] - Cập nhật màn hình TMSVendorBill, tối ưu nhập thông tin

- [Quan] - Thêm màn hình TMSPartnerAddressList tổng hợp địa chỉ config của khách hàng. - Init dữ liêu thầu phụ cho văn phòng HCM
- [Chien] - Cập nhật dữ liệu location, subdistrict của Việt Nam sau sấp nhập.
  - Dữ liệu location, subdistrict cũ để về chế độ INACTIVE
  - Init location, subdistrict theo dữ liệu nguồn mới nhất lấy từ trang:
    https://thuvienphapluat.vn/phap-luat/ho-tro-phap-luat/danh-sach-3321-xa-phuong-dac-khu-chinh-thuc-cua-34-tinh-thanh-viet-nam-tra-cuu-3321-xa-phuong-dac-k-726625-221776.html
  - Điều chỉnh màn TMSParnerAddress, cảnh báo các địa chỉ cũ liên kết đến các location đã INACTIVE
  - Màn TMSBillList cảnh báo các địa chỉ giao/nhận của khách hàng đã là địa chỉ cũ khi người dùng input theo dữ liệu nguồn TMSPartnerAddress

2. Migrate script:

- server:migrate:run --script tms/MigrationHCMVehicleInfo.groovy --company beehcm
- server:migrate:run --script location/InitSubdistric.groovy

- server:migrate:run --script tms/MigrationHCMVehicleInfo.groovy --company beehcm

### [R20250707]

1. Tasks:

- [Quan] - Thêm màn InvoiceReconcileList vào module Vendor Bill, phân quyền cho thầu phụ. Tại màn InvoiceReconcileList của cus thêm nút hiển thị danh sách các InvoiceReconcile được tạo từ thầu phụ mà chưa có cus nào nhận. Thêm nút confirm để cus nhận yêu cầu thanh toán từ thầu phụ.

- [Chien] Cập nhật TMSHouseBill : Cho phép mở file mới bằng cách kéo dữ liệu từ BFSOne về TMS hoặc copy các lô đã tạo
- [Chien] Đồng bộ các chức năng hiện có trên TMSBill sang TMSHouseBill

2. Migrate script:
