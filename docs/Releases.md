# Changelog

All notable changes to this project will be documented in this file.

### [Unreleased]











-----------------------------------------------------------------------------------

### [R20250805]


#### 1. <PERSON><PERSON><PERSON> nhật nhánh maintenance.

- [Dat]:
    - Thêm bổ sung xử lý các document dạng ảnh, thêm type "image" và plugin
    - Thêm chức năng in cho document dạng ảnh
- [Dat]:
  - C<PERSON><PERSON> nhậ<PERSON> bổ sung phần tạo bảng kê cho hóa đơn nâng hạ

#### 2. Cậ<PERSON> nhật nhánh hr.

- <PERSON><PERSON> sung auto save khi edit trong màn hình KPI/KPI Template
- Cập nhật dữ liệu Account Thêm account mới/Xoá account nhân sự đã nghỉ
- Thêm trường Leader cho <PERSON>/<PERSON>late, c<PERSON> quyền tương tự với Manager
  <PERSON><PERSON><PERSON> vớ<PERSON> có Leader, b<PERSON><PERSON><PERSON> duy<PERSON>t KPI sau cùng phải thêm 1 bước duyệt bởi leader (leader => manager => director)
- Init dữ liệu KPI Q2 cho các chi nhánh
  `Script:`

#### 3. Cập nhật nhánh tms.

- [Quan]: - Remove join location, account tại các query TMS và thực hiện load các thông tin location, account tại hàm search.
- [Chien]: - Fix bugs input VehicleTripGoodsTrackingList
           - Fix bugs webhook clientContext không nhận company id


Download db mới tại đường dẫn:
- https://beelogistics.cloud/download/datatpdb-latest.dump
- https://beelogistics.cloud/download/document_ie_db-latest.dump
- https://beelogistics.cloud/download/datatp_crm_db-latest.dump

Chạy run update và các migrate script:
  - migrate:run --script hr/UpdateData.groovy
  - server:migrate:run --script hr/CheckAccount.groovy --company bee
  - server:migrate:run --script hr/CheckAccount.groovy --company beehph
  - server:migrate:run --script hr/CheckAccount.groovy --company beehan
  - server:migrate:run --script hr/CheckAccount.groovy --company beedad
  - server:migrate:run --script hr/CheckAccount.groovy --company beehcm
  - server:migrate:run --script hr/InitKpiTemplate.groovy --company bee
  - server:migrate:run --script hr/InitKpiTemplate.groovy --company beehph
  - server:migrate:run --script hr/InitKpiTemplate.groovy --company beehan
  - server:migrate:run --script hr/InitKpiTemplate.groovy --company beedad
  - server:migrate:run --script hr/InitKpiTemplate.groovy --company beehcm
  - server:migrate:run --script hr/InitKpi.groovy --company bee
  - server:migrate:run --script hr/InitKpi.groovy --company beehph
  - server:migrate:run --script hr/InitKpi.groovy --company beehan
  - server:migrate:run --script hr/InitKpi.groovy --company beedad
  - server:migrate:run --script hr/InitKpi.groovy --company beehcm

### [R20250804]

#### 1. Cập nhật nhánh crm.

- [Dan] - Fix UI Lead/ Agent Potential, Review code CRM.
  - Fix bugs Tạo, Transfer Lead, Agent Potential.
  - Cập nhật chức năng Sync Partner từ hệ thống BFSOne theo tax code, mã cho salesman.
  (Áp dụng với partner cũ được approve/ xin cấp thêm quyền trên BFSOne)

- [Dan] - Cập nhật code bee legacy, sync data house bill (2022 -> 2024)

- [Dan] - Cập nhật thêm các log, zalo message để monitor các api liên quan đến BFSOne.

- [An] - check script để work với database mới.
      server:migrate:run --script crm/SyncCustomerLeads.groovy --company bee
      server:migrate:run --script crm/SyncCustomerLeads.groovy --company beedad
      server:migrate:run --script crm/SyncCustomerLeads.groovy --company beehcm
      server:migrate:run --script crm/SyncCustomerLeads.groovy --company beehph
      server:migrate:run --script crm/SyncCustomerLeads.groovy --company beehan

#### 2. Cập nhật nhánh tms.

- [Chien]: - Thêm cột và cập nhật giao diện cho VehicleTripGoodsTrackingList
           - Thêm chức năng gửi mail và in biên bản cho VehicleTripGoodsTrackingList

Download db mới tại đường dẫn:
- https://beelogistics.cloud/download/datatpdb-latest.dump
- https://beelogistics.cloud/download/document_ie_db-latest.dump
- https://beelogistics.cloud/download/datatp_crm_db-latest.dump

Chạy run update và các migrate script:
- migrate:run --script crm/AlterTables.groovy
- migrate:run --script hr/UpdateData.groovy

### [R20250801]

#### 1. Cập nhật nhánh crm.

- [Dan] - Agent Transactions: (Báo cáo giao dịch theo agent)
  - Fix query, enhance search, view raw data.

- [Dan] - Cập nhật input nhập thông tin Container.
- [Dan] - Bỏ chức năng xuất báo giá nhanh trên màn hình search giá.

- [Nhat] - Enhance màn hình Leads: Khi convert Lead => chuyển trạng thái Lead thành Converted => Popup giao diện Request to BFSOne Agent/Customer
         Script:
          migrate:run --script hr/UpdateData.groovy

Download db mới tại đường dẫn:
- https://beelogistics.cloud/download/datatpdb-latest.dump
- https://beelogistics.cloud/download/document_ie_db-latest.dump
- https://beelogistics.cloud/download/datatp_crm_db-latest.dump

Chạy run update và các migrate script:
- migrate:run --script crm/AlterTables.groovy
- migrate:run --script hr/UpdateData.groovy

### [R20250731]

#### 1. Cập nhật nhánh crm.
- [Nhat] - Enhance màn hình lịch xe: Bổ sung thông `Start place`, `End place` lên màn hình calendar
          + Script: server:migrate:run --script crm/UpdateDataAsset.groovy --company beehph
                    migrate:run --script crm/AlterTables.groovy

- [Dan] - Enhance màn hình báo giá cho team BD.

- [An] - review, enhance code.
    - Hiển thị detail, phân quyền theo bộ phận task calendar cho màn hình company task (Quản lý).
    - Enhance query Agent Transaction gửi raw data qua client, thay vì gửi dữ liệu đã tổng hợp.

#### 2. Cập nhật nhánh tms.

- [Chien]: - Cập nhật VehicleTripGoodsTracking chia cung đường
           - Cập nhật TMSBill cho phép đánh dấu các chuyến chạy ghép và thêm báo cáo tổng file ghép chuyến

Download db mới tại đường dẫn:
- https://beelogistics.cloud/download/datatpdb-latest.dump
- https://beelogistics.cloud/download/document_ie_db-latest.dump
- https://beelogistics.cloud/download/datatp_crm_db-latest.dump

Chạy run update và các migrate script:
- migrate:run --script crm/AlterTables.groovy
- server:migrate:run --script crm/UpdateDataAsset.groovy --company beehph

### [R20250730]

#### 1. Cập nhật nhánh crm.

  - [Nhat] - Thêm bảng Job Tracking, phân quyền cho Team BEEHCM Sea Xuất (US + Non US)
    + Script: server:migrate:run --script jobtracking/InitJobTrackingData.groovy --company beehcm

  - [Nhat] - Thêm tính năng Xuất Excel Raw Data cho KPI: xuất toàn bộ mục tiêu, kết quả + thông tin cơ bản của KPI.

  - [Dan] - Enhance lịch họp, lịch xe. Cho phép xem theo VP.
  - [Dan] + [An] - Enhance Sale Task Calendar, cập nhật lại một số trường thông tin khác hàng, validate dữ liệu.
  - [An] - Tạo Bee TW company, migrate employee, CRM data.

Download db mới tại đường dẫn:
- https://beelogistics.cloud/download/datatpdb-latest.dump
- https://beelogistics.cloud/download/document_ie_db-latest.dump
- https://beelogistics.cloud/download/datatp_crm_db-latest.dump

Chạy run update và các migrate script:
- migrate:run --script crm/RenameCRMColumn.groovy
- server:migrate:run --script crm/CreateCompany.groovy --company bee


### [R20250729]

#### 1. Cập nhật nhánh crm.

- [Nhat] - Clean Code, kiểm tra + cập nhật các query sau khi tách database
        + Script:
            migrate:run --script crm/AlterTables.groovy
            server:migrate:run --script crm/UpdateIntegratedPartner.groovy --company bee

- [An] - Check script sync customer leads để work với database mới.
      server:migrate:run --script crm/SyncCustomerLeads.groovy --company bee
      server:migrate:run --script crm/SyncCustomerLeads.groovy --company beedad
      server:migrate:run --script crm/SyncCustomerLeads.groovy --company beehcm
      server:migrate:run --script crm/SyncCustomerLeads.groovy --company beehph
      server:migrate:run --script crm/SyncCustomerLeads.groovy --company beehan

- [Dan] - Tổ chức lại code Backend, Frontend CRM, bỏ phụ thuộc dự án datatp-logistics.

#### 2. Cập nhật nhánh tms.

- [Quan]: Fix bug TMSBill

Download db mới tại đường dẫn:
- https://beelogistics.cloud/download/datatpdb-latest.dump
- https://beelogistics.cloud/download/document_ie_db-latest.dump
- https://beelogistics.cloud/download/datatp_crm_db-latest.dump

Chạy run update và các migrate script:


### [R20250727]

#### 1. Cập nhật nhánh crm.

- [Dan] - Inquiry Request:
    - Enhance lại form để cho phép gửi request theo nhiều tuyến.
    - Cập nhật lại docs hướng dẫn requests.

- [Dan] - Enhance check Lead/ check partner tax code.
  - Bổ sung thêm check theo tên, check thêm dữ liệu agent potential, highlight keywords.

- [Dan] - Tách code, dependency, review query, tách database CRM app.
    - Review, tổ chức lại code UI CRM.

- [An] -Request Mr. Duy (HPH) - Cập nhật bảng giá trucking container.
    - Thay đổi mức giá cho các loại cont 20', 40'.
    - Cập nhật UI/ logic, template excel để input dữ liệu.

#### 2. Cập nhật nhánh tms.

- [Quan]: - Lưu lại người export excel fee template và vehicle info template
          - Report tuyến đường dạng bảng
- [Chien]: - Cập nhật, fix bugs tms house-bill

Download db mới tại đường dẫn:
- https://beelogistics.cloud/download/datatpdb-latest.dump
- https://beelogistics.cloud/download/document_ie_db-latest.dump
- https://beelogistics.cloud/download/datatp_crm_db-latest.dump


Chạy run update và các migrate script:

#### SETUP database cho CRM.

1. Tạo database, user, restore database mẫu.
  >./db.sh datatp_crm_db create-user
  >./db.sh datatp_crm_db new
  >./db.sh datatp_crm_db restore --file=datatp_crm_db_latest.dump
  >./db.sh datatp_crm_db vacuum

- [Optional] - Chạy script để drop bảng crm ở datatpdb.
  - migrate:run --script crm/DropCRMTables.groovy

2. Cập nhật file application-[dev|prod].yaml, thêm config cho database CRM.
in server-env/config:
  `mv application-[dev|prod]-sample.yaml application-[dev|prod].yaml`

3. Chạy build, run code như bình thường.

### [R20250725]

#### 1. Cập nhật nhánh crm.
- [An]  Task Calendar:
  - Cron Job: Tự động tích hoàn thành công việc (sale task calendar) vào cuối mỗi tuần.

- [An] - Phân quyền theo app space, cập nhật thêm trường note.

- [Nhat] - Cập nhật query Customer Map, thay vì merger dữ từ 2 dataSource(db_bee_legacy.integrated_house_bill + datatp.bfsone_partner), dùng dataSource db_bee_legacy(integrated_house_bill + integrated_partner)

- [NHAT] [An] - Clean code, viết script DROP Quotation Air/Sea/Truck/CustomClearance Charge + DELETE data trong các entity liên quan:
        TransportFrequency, CustomerAirTransportAdditionalCharge, CustomerAirTransportCommissionDistribution
        CustomerSeaTransportCommissionDistribution, CustomerSeaTransportAdditionalCharge,
        CustomerTruckTransportAdditionalCharge, CustomerTruckTransportCommissionDistribution
        + Script: migrate:run --script crm/AlterTables.groovy

- [Nhat] - Cập nhật giao diện tạo Lịch xe/Phòng họp + giao diện Approval Lịch xe

- [Dan] - Inquiry Request.
  - Thêm cột clientPartnerType: Sale khi báo giá có thể báo cho partner với các loại khác nhau như Agent, Customer Lead, Customer.
  - Cần thêm flag (clientPartnerType) để phân biệt sales đang báo giá cho loại partner nào, với từng loại partner,
    thông tin lúc gửi request (chung 1 partner) -> Quotation/ Booking yêu cầu thông tin riêng biệt (Agent, Customer, ... ) bóc tách rõ ràng ra.
  - Cập nhật lại thông tin IB gửi qua BFSOne nếu đối tượng sales báo giá là Agent.
      - Sellling Payer => Agent.
      - Client (để sales tự nhập)
      - Agent (Lấy thông tin từ request / báo giá).

Có thể download db mới tại đường dẫn:
- https://beelogistics.cloud/download/datatpdb-latest.dump
- https://beelogistics.cloud/download/document_ie_db-latest.dump

Chạy run update và các migrate script:
- migrate:run --script crm/AlterTables.groovy

### [R20250724]

#### 1. Cập nhật nhánh crm.
- [Nhat] High - Xử lý dữ liệu partners lost cho HCM
          Dữ liệu Tâm gửi dạng excel, gồm các khách hàng đã lâu ko phát sinh booking.
          Y/c dev kiểm tra/ check lại với hệ thống, nếu không phát sinh booking trong 1 năm gần đây => Import vào CRM.
  - Migrate script: server:migrate:run --script crm/UpdateDataBFSOnePartner.groovy --company beehcm

#### 2. Cập nhật nhánh tms.
- [Chien] - Cập nhật biểu đồ báo cáo tuyến đường TMSBill
          - Fix bugs BotEvent không hoạt động(Ko gửi tin nhắn zalo/mail)

Có thể download db mới tại đường dẫn:
- https://beelogistics.cloud/download/datatpdb-latest.dump
- https://beelogistics.cloud/download/document_ie_db-latest.dump

Chạy run update và các migrate script:

### [R20250723]

#### 1. Cập nhật nhánh crm.

1. [Dan] - Partner : Review code, enhance UI, update api integrate with BFSOne.

2. [Nhat] Làm việc, đấu nối với API Customer Lead a Quý gửi.
  - Tạo, cập nhật Lead ở DataTP -> tạo, cập nhật Lead ở BFSOne. => `DONE`
  - Kiểm tra synced Customer Leads (BFSOne) to DataTP. => `DONE`
  - Xoá Lead ở DataTP -> Xoá Lead ở BFSOne.
  - Transfer Lead ở DataTP -> Update Username Lead ở BFSOne.
  - [Nhat] + [An] - Script Migrate: Get data theo lead code ở bfsone -> cập nhật thông tin (tax code, ...) Lead ở DataTP. `DONE`
          + Script: migrate:run --script crm/AlterTables.groovy
                    server:migrate:run --script crm/SyncCustomerLeads.groovy --company bee
                    server:migrate:run --script crm/SyncCustomerLeads.groovy --company beehph
                    server:migrate:run --script crm/SyncCustomerLeads.groovy --company beehan
                    server:migrate:run --script crm/SyncCustomerLeads.groovy --company beehcm
                    server:migrate:run --script crm/SyncCustomerLeads.groovy --company beedad

Có thể download db mới tại đường dẫn:
- https://beelogistics.cloud/download/datatpdb-latest.dump
- https://beelogistics.cloud/download/document_ie_db-latest.dump

Chạy run update và các migrate script:
- migrate:run --script crm/AlterTables.groovy
- server:migrate:run --script crm/SyncCustomerLeads.groovy --company bee
- server:migrate:run --script crm/SyncCustomerLeads.groovy --company beehph
- server:migrate:run --script crm/SyncCustomerLeads.groovy --company beehan
- server:migrate:run --script crm/SyncCustomerLeads.groovy --company beehcm
- server:migrate:run --script crm/SyncCustomerLeads.groovy --company beedad

### [R20250721]

#### 1. Cập nhật nhánh crm.

-  [Nhat]  - Salesman Activity Tracker, Task Calendar (Company).
  + Check lại phân quyền, filter theo data scope là group.
  + HCM: Issacc, a Thanh ở HCM chỉ nhìn thấy dữ liệu của team Sale 1.
  + HCM: Abby.vnsgn, a Phương nhìn sales toàn Team sales còn lại (trừ Team 1).
  + HPH: Chị Oanh: tasks của VP Nam Định.
  + HPH: Anh Tản, chị Hạnh: VP Hà Nam
  + HPH: Chị Thuý: VP Thanh Hoá.
  + HPH: THAI THI BINH DUONG: VP Nghệ An.

 - [Nhật] + Cập nhật Asset Task: thay thế field PIC(Host) BBRefAccount bằng BBStringFIeld
   + Fix bugs OKR: check key result đã tồn tại khi sync BFSOne Data
   + Script migrate: migrate:run --script asset/AlterTables.groovy

#### 2. Cập nhật nhánh tms.
- [Quan]:
  - Implement TMSConfigRoute, Zone, ZoneMapping.
  - Fix bug Vehicle Info Template.

#### 3. Cập nhật nhánh maintenance.
- [Dat]:
    - Thêm bổ sung xử lý các document dạng ảnh, thêm type "image" và plugin
    - Thêm chức năng in cho document dạng ảnh

Có thể download db mới tại đường dẫn:
- https://beelogistics.cloud/download/datatpdb-latest.dump
- https://beelogistics.cloud/download/document_ie_db-latest.dump

Chạy run update và các migrate script:
- migrate:run --script asset/AlterTables.groovy

### [R20250721]

#### 1. Cập nhật nhánh crm.
- [Dan] - fix bugs, review code.
  - Fix bugs api MSAIntegrationService -> msaApiUrl
  - Fix bugs, review code Update. Resend Internal Booking.
  - Fix bugs BBRefMultiEmail.

Có thể download db mới tại đường dẫn:
- https://beelogistics.cloud/download/datatpdb-latest.dump
- https://beelogistics.cloud/download/document_ie_db-latest.dump

Chạy run update và các migrate script:


### [R20250719]

#### 1. Cập nhật nhánh crm.

1. Quản lý Quy trình (Workflow Management) - theo dõi các requests phát sinh khi làm hàng...
  - Xem chi tiết ở Features.

  Tasks:
    1. [Dan] - Design code, chuẩn bị code, QA.
    2. [Nhat] - Function gửi mail:
        Khi tạo request -> Gửi mail cho người liên quan -> Approved/ Rejected qua mail ->
        hiển thị màn hình thông báo cho người dùng. -> Gửi kết quả Approved/Rejected qua mail cho người liên quan.
    3. [An] - Implement code, Backend, Frontend.
    4. [An] - Dashboard => Pending.

[Nhat] - Viết script migrate/ sync data bfsone_partner (datatp) -> integrated_partner (bee legacy)
            Continent tính toán từ hệ thống của mình, join theo data country_label của bfsone_partner, settings_country_group lấy đúng continent cấp đầu tiên (không có partner_id)
            + script: server:migrate:run --script crm/UpdateIntegratedPartner.groovy
        - Viết Cron Job cập nhật data integrated_partner theo dữ liệu bfsone_partner


Có thể download db mới tại đường dẫn:
- https://beelogistics.cloud/download/datatpdb-latest.dump
- https://beelogistics.cloud/download/document_ie_db-latest.dump

Chạy run update và các migrate script:
- migrate:run --script crm/AlterTables.groovy


### [R20250718]

#### 1. Cập nhật nhánh crm.

-  Dan - Fix bugs create partner (category - CO-LOADER).

 - Dan - Api update partner data to Bee Legacy.
    - Viết api để cho phép sync/ cập nhật các thay đổi partner từ hệ thống khác như DataTP vào Bee Legacy.
    - Cập nhật thêm trường Continent (châu lục cho bảng partner) => Support cho báo cáo agent transactions. => Update data join theo Country với data DataTP.

#### 2. Cập nhật nhánh maintenance.
- [Dat] - Fix bugs view document.

#### 3. Cập nhật nhánh tms.
- [Quan]:  - Cập nhật địa chỉ kho cho tmsbill
- [Chien]:
    - Cập nhật báo cáo biểu diễn trên biểu đồ số chuyến theo tuyến đường cho từng thầu phụ dựa trên tms-bill
    - Config target số chuyến cho thầu phụ theo tuần/tháng và biểu diễn trên đồ thị


Có thể download db mới tại đường dẫn:
- https://beelogistics.cloud/download/datatpdb-latest.dump
- https://beelogistics.cloud/download/document_ie_db-latest.dump

Chạy run update và các migrate script:
- migrate:run --script tms/DropTable.groovy
- server:migrate:run --company beehph --script tms/InitConfigRoute.groovy
- server:migrate:run --script tms/MigrationBillInvAddress.groovy

### [R20250716]

1. Cập nhật nhánh crm.
  - [Nhat] - Cập nhật tính năng Clone Internal Booking.
          - Cập nhật Document bản English.

  - [Dan] - Báo cáo Key Account Performace (Doanh thu/ Profit khách hàng của từng sale).
      Viết api để tự động sync dữ liệu house bill (bee legacy) cho saleman khi làm báo cáo.
      - Bee Legacy: Cập nhật api sync house bill theo date, saleman id.
      - DataTP: Cập nhật thêm nút để cho phép salesman refresh dữ liệu.

- [Dan] - Request Mr. Tam (HCM) - Tổng hợp Daily Tasks của Saleman theo từng team.

2. Cập nhật nhánh tms:
  - [Quan]  - Cập nhập địa chỉ mới cho location kho/bãi
  - [Chien] - Câp nhật Zone, Zone Mapping Entity
            - Fix bugs BFSOne template

Có thể download db mới tại đường dẫn:
- https://beelogistics.cloud/download/datatpdb-latest.dump
- https://beelogistics.cloud/download/document_ie_db-latest.dump

Chạy run update và các migrate script:
 - migrate:run --script tms/DropZoneTable.groovy
 - server:migrate:run --script tms/MigrationLocation.groovy


### [R20250715]

1. Cập nhật nhánh crm.
- [Dan] - Request Mr. Tam (HCM) - Tổng hợp Daily Tasks của Saleman theo từng team.
- [Dan] - Req Ms Tram - Pricing Tools: để mặc định là màn hình search giá (Quick Rate Finder).
    chuyển màn hình theo dõi Inquiry, rename thành Inquiry Pricing Queue => Phù hợp hơn cho việc triển khai các VP nước ngoài, không sử dụng đến Inquiry Request.
- [Dan] - Review lại màn hình quotation, chỉ owner mới nhìn thấy báo giá của mình, còn lại người khác không được nhìn thấy (bao gồm quản lý trực tiếp).
    Nhưng các chỉ số như tổng báo giá tạo trong hệ thống, tỷ lệ win vẫn hiện thị trên Dashboard.


Có thể download db mới tại đường dẫn:
- https://beelogistics.cloud/download/datatpdb-latest.dump
- https://beelogistics.cloud/download/document_ie_db-latest.dump

Chạy run update và các migrate script:

### [R20250714]

1. Cập nhật nhánh crm:

 - [Dan] - Brainstorm, Design UI cho Inquiry Request, Quotation, Internal Booking.
      - Giao diện theo kiểu tích hợp All-in-One tập trung tất cả thao tác (từ nhập yêu cầu, kiểm tra giá, tạo báo giá, đến gửi Internal Booking) trong một màn hình duy nhất.

      - Toolbar (Top): Lọc, filter, thanh chức năng.
      - Main: Render Quotation List.
      - Interactive Panel: (Bottom) Giao diện tương tác theo ngữ cảnh.
        - Ex: Render các action tương ứng khi chọn một inquiry request, quotation, internal booking.
        - Ex: Customize cho từng user (Cho phép ghim các báo giá của khách hàng quen thuộc, có hàng hàng ngày/ hàng tuần/ ...)

        ```
        "Hệ quy chiếu của người báo giá là Quotation list, không phải Inquiry Request. Người báo giá sẽ theo dõi tất cả những báo giá của mình trên Quotation list.
        - Mark win/fail/feedback... trên Quotation list & tự động link qua Inquiry Request list.
        - Mark win trên Quotation list & tự động link qua IB list.
        - Inquiry request list đánh giá hiệu quả công việc của pricing team. Có trường hợp salesman tạo inquiry request là để biết giá thị trường, không dùng giá đó để bán cho khách.
        - Quotation list đánh giá hiệu quả công việc của team salesman."

        ```

 - [Nhat] Cập nhật query Pricing + Sales, loại các JOIN đến DB Core, migrate dữ liệu.
          Cập nhật tính năng archive/remove Asset Task.

2. Cập nhật nhánh tms:
  - [Chien] - Fix bugs TMSBillList
          - Cập nhật màn hình TMSVendorBill, tối ưu nhập thông tin

Có thể download db mới tại đường dẫn:
- https://beelogistics.cloud/download/datatpdb-latest.dump
- https://beelogistics.cloud/download/document_ie_db-latest.dump

Chạy run update và các migrate script:
  - migrate:run --script crm/AlterTables.groovy

### [R20250712]

1. Cập nhật nhánh crm:
  [Nhat] Cập nhật query Pricing, loại các JOIN đến DB Core, migrate dữ liệu.

  [Dan] - Fix bugs, enhance tạo, chỉnh sửa báo giá, internal booking.

  [An] - Theo dõi tần suất sử dụng giá, đánh giá hiệu quả của giá, để Pricing cải thiện.
   Count số lần giá đó được saleman export.
   Sau khi tạo Internal Booking thành công -> Cập nhật feedback của bảng giá đó theo format:
     [Tên Saleman] - [Ngày Tạo Booking].
     [Incoterm]    - [Volume hàng].
     [Mô tả hàng]
   Viết cron để cập nhật ngày 2 lần.
   - 8AM: Tổng hợp Booking từ 14h ngày hôm qua đến trước 8h ngày hôm sau.
   - 14PM : Tổng hợp Booking từ 8h đến trước 14h ngày hiện tại.
   Từ Internal Booking -> Quotation -> QuotationCharge -> Reference Code.


Có thể download db mới tại đường dẫn:
- https://beelogistics.cloud/download/datatpdb-latest.dump
- https://beelogistics.cloud/download/document_ie_db-latest.dump

Chạy run update và các migrate script:
- server:migrate:run --script crm/SyncCustomerLeads.groovy --company bee
- server:migrate:run --script crm/SyncCustomerLeads.groovy --company beehph
- server:migrate:run --script crm/SyncCustomerLeads.groovy --company beehan
- server:migrate:run --script crm/SyncCustomerLeads.groovy --company beehcm
- server:migrate:run --script crm/SyncCustomerLeads.groovy --company beedad
- migrate:run --script crm/AlterTables.groovy

### [R20250710]

1. Cập nhật nhánh tms:

- [Quan]  - Thêm màn hình TMSPartnerAddressList tổng hợp địa chỉ config của khách hàng.
          - Init dữ liêu thầu phụ cho văn phòng HCM
- [Chien] - Cập nhật dữ liệu location, subdistrict của Việt Nam sau sấp nhập.
   + Dữ liệu location, subdistrict cũ để về chế độ INACTIVE
   + Init location, subdistrict theo dữ liệu nguồn mới nhất lấy từ trang:
    https://thuvienphapluat.vn/phap-luat/ho-tro-phap-luat/danh-sach-3321-xa-phuong-dac-khu-chinh-thuc-cua-34-tinh-thanh-viet-nam-tra-cuu-3321-xa-phuong-dac-k-726625-221776.html
   + Điều chỉnh màn TMSParnerAddress, cảnh báo các địa chỉ cũ liên kết đến các location đã INACTIVE
   + Màn TMSBillList cảnh báo các địa chỉ giao/nhận của khách hàng đã là địa chỉ cũ khi người dùng input theo dữ liệu nguồn TMSPartnerAddress

2. Cập nhật nhánh crm:

  - [Nhat] Cập nhật document Partners:
    + Key Account Performance Report.
    + Customer Leads: Check Exist Lead.
    + Customer Leads: New Agent Approach.
    + Customer Leads: Update Lead Star.
    + Tạo khách hàng, request to BFSOne.

  - [Nhat] Fix bugs tạo Request Xe/Phòng họp.

Có thể download db mới tại đường dẫn:
- https://beelogistics.cloud/download/datatpdb-latest.dump
- https://beelogistics.cloud/download/document_ie_db-latest.dump

Chạy run update và các migrate script:
- server:migrate:run --script tms/MigrationHCMVehicleInfo.groovy --company beehcm
- server:migrate:run --script location/InitSubdistric.groovy

### [R20250708]

1. Cập nhật nhánh crm:
  - [Nhat] Cập nhật document Customer Lead:
    + Chuyển đổi Lead sang khách hàng.
    + Tạo khách hàng, request to BFSOne.

  - [Nhat] Cập nhật document hướng dẫn book lịch xe/phòng họp.

  - [An] - Thêm field variants Employees, cập nhật theo format để enhance search.
      - [Employee Name] - [Employee Code]
      - [Employee Name]

  - [Dan] - Fix bugs tạo quotation, internal booking.
  - [Dan] - Enhance search Employee, BBRefEmployee: thêm param cho phép filter employee toàn hệ thống.

  - [Dan] - Theo dõi tần suất sử dụng giá, đánh giá hiệu quả của giá, để Pricing cải thiện.
    - Count số lần giá đó được saleman export gửi cho khách.
    - Sau khi tạo Internal Booking thành công -> Tự động cập nhật feedback cho bảng giá đó theo format:
        [Tên Saleman] - [Ngày Tạo Booking].
        [Incoterm]    - [Volume hàng].
        [Mô tả hàng]

Có thể download db mới tại đường dẫn:
- https://beelogistics.cloud/download/datatpdb-latest.dump
- https://beelogistics.cloud/download/document_ie_db-latest.dump

Chạy run update và các migrate script:
  - server:migrate:run --script crm/UpdateDataEmployee.groovy --company bee
  - server:migrate:run --script crm/UpdateDataEmployee.groovy --company beehph
  - server:migrate:run --script crm/UpdateDataEmployee.groovy --company beehan
  - server:migrate:run --script crm/UpdateDataEmployee.groovy --company beehcm
  - server:migrate:run --script crm/UpdateDataEmployee.groovy --company beedad

  - server:migrate:run --script crm/UpdateDataQuotations.groovy --company bee
  - server:migrate:run --script crm/UpdateDataQuotations.groovy --company beehph
  - server:migrate:run --script crm/UpdateDataQuotations.groovy --company beehan
  - server:migrate:run --script crm/UpdateDataQuotations.groovy --company beehcm
  - server:migrate:run --script crm/UpdateDataQuotations.groovy --company beedad

### [R20250702]

1. Cập nhật nhánh tms:
- [Quan] - Thêm màn InvoiceReconcileList vào module Vendor Bill, phân quyền cho thầu phụ. Tại màn InvoiceReconcileList của cus thêm nút hiển thị danh sách các InvoiceReconcile được tạo từ thầu phụ mà chưa có cus nào nhận. Thêm nút confirm để cus nhận yêu cầu thanh toán từ thầu phụ.


2. Cập nhật nhánh crm:
  - [Dan] - Review Quotation - Booking process, clean code.
  - [Dan] - Tổ chức lại cấu trúc code, documentation.
  - [Nhat] - Tối ưu lại màn hình Lịch theo Tuần Book xe/phòng họp, bổ sung chức năng add email nhận thông báo.

Có thể download db mới tại đường dẫn:
- https://beelogistics.cloud/download/datatpdb-latest.dump
- https://beelogistics.cloud/download/document_ie_db-latest.dump

Chạy run update và các migrate script:
- migrate:run --script crm/AlterTables.groovy

### [R20250702]

1. Cập nhật nhánh tms:
  - Fix bugs TMSBillList
  - Xử lý save màn TMSHouseBillEditor
  - Xử lý Clone TMSHouseBill
  - Kéo HouseBill từ BFSOne về hệ thống TMSHouseBill

2. Cập nhật nhánh crm:
  - [Dan] - clean code datatp-logistics: module price/ sales/ management (module cũ đã tách thành datatp-crm)
  - [Dan] - Chỉnh báo cáo Key Account Performance cho teame BD - Mrs Hải y/c
  - [Dan] - Sửa lỗi Copy Quotation.
  - [Dan] - Báo cáo theo dõi sales, highlight các sales trực quan các sales không có activity.
  - [Dan] - Sync khách hàng cho tất cả employee bee corp/ beehcm/ bee dad.
  - [Dan] - Cập nhật, kiểm tra các script db, backup-tools.
      Document tại link https://gitlab.datatp.net/tuan/datatp-docs/-/blob/master/docs/shared/developer/SETUP.md

  - [Nhat] - CRM: Tracking số lần Saleman Export Quotation, hiển thị lên CRM Dashboard: Salesman Activity Tracker.
  - [Nhat] - Spreadsheet: Cập nhật màn hình Report team IST (BD HCM). Bổ sung thêm các biểu đồ báo cáo volume cho từng cá nhân:
    - TEU (1x20DC = 1 TEU, 1x40HC/1x45HC = 2 TEUs)
    - CBM (hàng LCL)
    - KG (hàng Air)
    - Shipment (Breakbulk).

  - [An] - Export dữ liệu Sales Daily Tasks theo excel Mrs Minh y/c.
  - [An] - Hiển thị chi tiết dữ liệu cho Salesman Activity Tracker
  - [An] - Fix bugs, enhance UI Bulk Cargo Inquiry Request - Mrs Cường BD yêu cầu.
  - [An] - Chỉnh form tạo mới Agent Approach.
  - [An] - Trên màn hình Dashboard, thêm các chức năng để cho phép view dữ liệu, mở rộng detail các chỉ số trên dashboard.

Có thể download db mới tại đường dẫn:
- https://beelogistics.cloud/download/datatpdb-latest.dump
- https://beelogistics.cloud/download/document_ie_db-latest.dump

hoặc chạy run update và các migrate script:

### [R20250702]

1. Cập nhật nhánh tms:

    [Chiến]
    - Làm tiếp phần TMSHouseBill, đồng bộ dữ liệu 2 TMSHouseBill và TMSBill khi tạo mới và update.
    - Update template export data thầu phụ và đối soát cước thầu phụ.

    [Quân]
    - Phân quyền TMSVendorBill cho thầu phụ và test.
    - Clean code TMS.

2. Cập nhật nhánh crm:
  - [Nhat] Thêm 2 option cho button Feedback ở màn hình Sea FCL/LCL Price
      + Popup Form Request Price
      + Popup Form Feedback
  - [Nhat] Migrate dữ liệu forwarder_customer_leads: cập nhật company_id, saleman_label
        + script: migrate:run --script crm/AlterTables.groovy
  - [An] Export dữ liệu Sales Daily Tasks theo excel Mrs Minh y/c
  - [An] Cập nhật theo feedback chỉnh sửa cho inquiry hàng rời
  - [An] Sync lại toàn bộ khách hàng cho sales HPH/ HAN
  - [An] Hiển thị chi tiết dữ liệu cho Salesman Activity Tracker
  - [Dan] Enhance lọc, báo cáo volume theo tuyến cho team pricing.
  - [Dan] Enhance UI Sale Dashboard, UI Pricing Dashboard.

Có thể download db mới tại đường dẫn:
- https://beelogistics.cloud/download/datatpdb-latest.dump
- https://beelogistics.cloud/download/document_ie_db-latest.dump

hoặc chạy run update và các migrate script:
- migrate:run --script crm/AlterTables.groovy
- server:migrate:run --script crm/SyncBFSOneBySaleman.groovy --company beehph
- server:migrate:run --script crm/SyncBFSOneBySaleman.groovy --company beehan

### [R20250701]

1. Cập nhật nhánh tms:

    Task [Chiến] :
    - Cập nhật lại template BFSOne theo form mới
    - Check bản kê thầu phụ khi import cost vào TMSBill, không  khớp số cont, biển số xe note lại lỗi để cus kiểm

2. Cập nhật nhánh crm:
- Fix báo cáo Key Account Report.
- Enhance lại Pricing Dashboard, thêm báo cáo volume theo từng tuyến.
- Thêm báo cáo theo dõi khách hàng, chỉ số chuyển đổi khách hàng cho Sale Dashboard.
- Fix, sửa lỗi tạo Partner, api với BFSOne.

Có thể download db mới tại đường dẫn:
- https://beelogistics.cloud/download/datatpdb-latest.dump
- https://beelogistics.cloud/download/document_ie_db-latest.dump

hoặc chạy run update và các migrate script:
- migrate:run --script crm/AlterTables.groovy
- server:migrate:run --script crm/UpdateDataKeyAccountReport.groovy --company bee
- server:migrate:run --script crm/UpdateDataKeyAccountReport.groovy --company beehph
- server:migrate:run --script crm/UpdateDataKeyAccountReport.groovy --company beehcm

### [R20250626]

Cập nhật nhánh tms:

Task:
[Quân]: clean code TMSPartnerVendor và các code không sử dụng đến trên TMS

[Chiến] :
- Fix bugs và điều chỉnh tiếp template export BFSOne và template TMSBill  gửi cho thầu phụ
- Cập nhật giao diện TMSHouseBill

Cập nhật nhánh crm:
- [Nhat] - Fix bugs create partner, enhance màn hình CRM Sale Dashboard.
- [An] - Enhance báo cáo Saleman Activities Tracker - Mrs. Minh Sales HPH
         - Cố định row header và cột đầu tiên.
         - Kiểm tra lại chỉ số Khách hàng mới/ lead mới.

Cập nhật nhánh document-ie:
- Em bổ sung trích rút biên lai ạ

Có thể download db mới tại đường dẫn https://beelogistics.cloud/download/datatpdb-latest.dump
hoặc chạy run update và các migrate script
- server:migrate:run --script document/MigrationDocument.groovy
- server:migrate:run --script crm/SyncBFSOneAuthorizePartner.groovy


### [R20250621]

Cập nhật nhánh tms:
[Quân] :
 - Cập nhật dữ liệu kho cảng từ file excell (Dương điều chỉnh)
- Update kho cảng tms bill theo địa chỉ mới

[Chiến]:
Update template excell Cho BFSOne:

-Xác minh thông tin bill đủ điều kiện thanh toán:[TMSBillFee] Thêm trường verifyPaymentInfo(true/fasle), verifyPaymentNote(note lại các thông tin thiếu)
-Xác minh thông tin xe:[TMSBill] Thêm verifyVehicleInfo, verifyVehicleNote
-TMSBillList show các cột verify trên
-Fix hàm searchInvoiceSummaryReconciles sau khi tách DB Documnet-set

Cập nhật nhánh hr:
Task Cập nhật UI + Query Sync dữ liệu OKR tự động
- Bổ sung service type: Crossborder
- Bổ sung đếm số lượng tờ khai hải quan

Cập nhật nhánh crm:

1. [An] - Company Pricing Dashboard:
- chức năng lọc theo Type Of Service cho toàn bộ dữ liệu, cho Top Route Performance.
- export excel từng section/ bảng.

2. [An] - Cập nhật thông tin các trường industry_sector (từ excel), date_created, date_modified (từ BFSOne) cho partner.


Có thể download db mới tại đường dẫn https://beelogistics.cloud/download/datatpdb-latest.dump
hoặc chạy run update và các migrate script
- server:migrate:run --script tms/MigrateAddressPortWarehouse.groovy
- server:migrate:run --script crm/UpdateIndustryBFSOnePartner.groovy
- server:migrate:run --script crm/UpdateDataKeyAccountReport.groovy --company bee


### [R20250621]

Cập nhật nhánh develop

Cập nhật dự án datatp-crm:
- Checkout code (nhánh develop) và setup:  git@gitlab:datatp.net:tuan/datatp-crm.git
- Remove node_modules pnpm-lock.yaml dist và pnpm install && pnpm run build ở các dự án lib, erp, document-ie, logistics, crm, phoenix.
- Build java code bằng command: ./datatp.sh lgc:build -clean -build
- Run code như bình thường.

Có thể download db mới tại đường dẫn https://beelogistics.cloud/download/datatpdb-latest.dump

### [R20250621]

Cập nhật nhánh document-ie:
- Em clear bảng nằm trên database cũ và fix bug bên doument set ạ

Cập nhật nhánh tms:
[Quân] :
1)Cho phép cus tự lên thông tin xe thay điều vận:
- Giao diện TMSBillList hiển thị popup các tab VehicleTrip Form cho phép nhập thông tin lô hàng
2)Tự động ghép chuyến cho các lô hàng theo request của cus:
- Giao diện: Thêm 1 checkbox Combine Trip tại popup Processing Goods Request
- Backend: Tạo VehicleTrip chung cho các VehicleTripGoodsTracking được tạo từ các TMSBill request.
3)Export template xuất thông tin xe cho BFSOne theo feedback của người dùng.
Thêm note cảnh báo các lô chưa đủ điều kiện thanh toán(Sai hbl, chưa nhập giá, sai đơn vị)

Chiến:
+ Dựng Entity TMSHouseBill: Số Hbl, Loại Hình(Nhập/Xuất), Customer, Kho bãi lấy/trả hàng, Carrier, COT/ETA,...
+ Thêm các hàm logic sử lý CRUD. Tối ưu hàm search khi join bảng tms-bill
+ Migration TMS HouseBill data, tạo liên kết đến TMSBill tương ứng
+ Dựng màn hình cho TMSHouseBillList và TMSHouseBillEditor

Có thể download db mới tại đường dẫn https://beelogistics.cloud/download/datatpdb-latest.dump
hoặc chạy run update và các migrate script
- migrate:run --script document/CleanDocument.groovy
- server:migrate:run --script tms/MigrationTMSHouseBill.groovy

### [R20250620]

Cập nhật nhánh document-ie:
Em fix bug search document set ạ

Có thể download db mới tại đường dẫn https://beelogistics.cloud/download/datatpdb-latest.dump

### [*********]

Cập nhật nhánh crm:
[Dan] - Implement UI Dashboard Salesman Activity Tracker.

Cập nhật dự án document-ie:
- Checkout code (nhánh develop):  git@gitlab:datatp.cloud:tuan/datatp-document-ie.git
- Tạo db, user và restore theo link db tương ứng ở dưới. (Sửa thông tin ở file env.sh, chạy lại các lệnh ở file postgres-admin.sh)
db_name: document_ie_db
username: document_ie
password: document_ie

Xem lại cấu hình các file config application.properties, bổ sung thêm datasource cho db mới.
document-ie:
type: com.zaxxer.hikari.HikariDataSource
connectionTimeout: 30000
idleTimeout: 600000
maxLifetime: 600000
minimumIdle: 5
maximumPoolSize: 15
auto-commit: false
driverClassName: org.postgresql.DriverjdbcUrl: ***********************************************:{spring.datasource.server.port}/document_ie_db
username: document_ie
password: document_ie

Có thể download db mới tại đường dẫn https://beelogistics.cloud/download/datatpdb-latest.dump
Có thể download db document_ie_db mới tại đường dẫn https://beelogistics.cloud/download/document_ie_db-latest.dump

### [*********]

Cập nhật nhánh crm:
- [Dan] - Implement UI Dashboard cho Pricing Company.

[Nhat]
- Cập nhật phân quyền màn hình UI Customer Lead, Customer Lead Detail
- Check Customer theo Tax Code: Bổ sung Button Check, Check thêm Customer Lead
- Thêm button tạo Shipping Instruction từ Partner Obligation, search Partner Obligation theo Cus/ Docs

Có thể download db mới tại đường dẫn https://beelogistics.cloud/download/datatpdb-latest.dump

### [*********]

Cập nhật nhánh quan-tms:
- Thêm nút clear trạng thái thanh toán với quyền moderator tại màn hình TMSBillList
- Điều chỉnh hiển thị các điểm dừng tại màn TMSBillList, VehicleTripGoodTrackingList theo yêu cầu

Có thể download db mới tại đường dẫn https://beelogistics.cloud/download/datatpdb-latest.dump

### [R20250616]

Cập nhật nhánh crm:
- [An] - Tiếp tục nhận feedback chỉnh sửa, bugs cho inquiry hàng rời (Req from Team BD)
- [An] -Refactor lại thông tin BFSOne Partner, Viết cron sync tự động theo ngày
- [An] - "Cập nhật bảng giá cont (Trucking): Chia lại mức giá cho cont 20' theo y/c của HPH.

- [Nhat] - Clean code Uncompleted Sale Daily Task, thay thế logic sendMessage cũ bằng CRMMessageSystem
- [Nhat] - Cập nhật phân quyền màn hình UI Customer Lead, Customer Lead Detail

- [Dan] - Implement UI Dashboard cho CRM Company.
- [Dan] - Viết api gen token, api cập nhật BFSOne Partner Code cho a Quý.
   Quy trình lúc tạo mới partner -> KT approve -> BFSOne gen code partner -> Gọi api để sync lại với DataTP
   Đã gửi lại api cho a Quý lúc  13/06
   https://docs.google.com/document/d/1hI71aD9YjN2zbHxVUAVEc_Sp0lgYsOJwgHvZv1x1zsA/edit?usp=sharing"
- [Dan] - Tạo bảng dữ liệu, lưu thông tin khách hàng
   Tạo các api service cho phép cập nhật, chỉnh sửa và xoá record.
- [Dan] - "Cập nhật api liên quan đến authorization, cho phép các hệ thống bên ngoài gọi vào các service để cập nhật dữ liệu.
   Hard code token, gửi cho client sau đó kiểm tra request để xác thực."
- [Dan] - Tạo bảng dữ liệu, lưu thông tin unit
   Tạo các api service cho phép sync, cập nhật, chỉnh sửa và xoá record.

Cập nhật nhánh quan-tms:
- Giao diện TMSBillList, VehicleTripGoodTrackingList cho phép nhập thêm các điểm dừng
- Hàm search TMSBillList, VehicleTripGoodTrackingList load thêm các điểm dừng

Có thể download db mới tại đường dẫn https://beelogistics.cloud/download/datatpdb-latest.dump
hoặc chạy run update và các migrate script

### [R20250612]

Cập nhật nhánh crm:
- Refactor lại thông tin BFSOne Partner
- Tiếp tục nhận feedback chỉnh sửa, bugs cho inquiry hàng rời (Req from Team BD)

Cập nhật nhánh tms:
- [Quân] Drop bảng document_document_set_category, drop column document_category_id tại bảng document_document_set,  drop các index đặt tên sai tại bảng document_document
- [Chiến] Fix bugs TMSBillFee save lỗi, Clear thông tin xe và giá cost khi copy lô hàng

Có thể download db mới tại đường dẫn https://beelogistics.cloud/download/datatpdb-latest.dump
hoặc chạy run update và các migrate script

- migrate:run --script document/CleanDocument.groovy

### [R20250612]

Cập nhật nhánh crm:
- Refactor lại thông tin BFSOne Partner
- Tiếp tục nhận feedback chỉnh sửa, bugs cho inquiry hàng rời (Req from Team BD)

Cập nhật nhánh tms:
- Địa chỉ config cho khách hàng thêm StreetName để lưu số nhà, đường,...(Kế toán yêu cầu tách)
 - Thêm senderStreetName, receiverStreetName trên TMSBill và đồng bộ với địa chỉ khách hàng đã config
- Fix BFSOne template export, chuẩn hóa địa chỉ  xã/phường, quận/huyên theo yêu cầu
- Fix lỗi query liên quan đến migration TMSVendor sang Vehicle Fleet (Quân chưa thay hết)

Có thể download db mới tại đường dẫn https://beelogistics.cloud/download/datatpdb-latest.dump

### [R20250612]

Cập nhật nhánh crm:
- Clean code groovy sql không sử dụng từ datatp-build/app/cli
- Drop các colume thừa, tạo nhầm, không còn sử dụng ở các bảng bfsone_partner, lgc_price_bulk_cargo_inquiry_request, lgc_price_truck_container_charge

Cập nhật nhánh nhat:
- Chỉnh sửa giao diện Asset Calendar+ fix bug màn hình tạo Task
- Bổ sung màn hình Admin KPI

Cập nhật nhánh tms:
Task:
Chiến:
- Push cước từ Vehicle Goods Tracking về phần Chi phí TMSBill
- Cho phép cus đồng bộ cước khi Vehicle Goods Tracking đã được nhập cước
- Thêm trạng thái thông báo thanh toán chi phí TMSBill và các lỗi dẫn đến chưa đc thanh toán
- UITMSBillList lọc các bill chưa thanh toán
- Verify HouseBill tmsBill với BFSOne, cảnh báo các lô hàng HouseBill chưa Verify

Quân:
- [Vehicle Fleet] Thêm field emails và các field webhook config
- Viết groovy script merge tms vendor vào vehicle fleet và migration dữ liệu các entity dùng TMSVendor sang Vehicle Fleets
- Thay thế trên các giao diện màn hình dùng BBRefTMSVendor sang BBRefVehicleFleet.
- Chuyển và kiểm tra các chức năng call webhook được cấu hình từ TMS Partner sang Vehicle Fleet

Có thể download db mới tại đường dẫn https://beelogistics.cloud/download/datatpdb-latest.dump
hoặc chạy run update và các migrate script
- migrate:run --script crm/AlterTables.groovy
- migrate:run --script tms/MigrationTmsBillFee.groovy
- server:migrate:run --script tms/MigrationVehicleFleet.groovy

### [R20250611]

Cập nhật nhánh crm:
- Sửa logo báo cáo

Cập nhật nhánh asset:
Task:
- [Asset] Thêm giao diện Calendar riêng cho book xe + phòng họp (trong module Asset). Default xem ở dạng week
- [Spreadsheet] Tạo bảng config màn hình Report team IST (BD HCM)

Cập nhật nhánh ocr:
Em fix bug trích rút bên kế toán ạ

Có thể download db mới tại đường dẫn https://beelogistics.cloud/download/datatpdb-latest.dump
hoặc chạy run update và các migrate script

### [R20250610]

Cập nhật nhánh crm:

- Cập nhật form, feedback chỉnh sửa, bugs cho inquiry hàng rời (Team BD)
- Enhance các báo cáo ở màn hình CRM. (Lọc, filter, xuất excel, ..)
   Báo cáo hoạt động khách hàng/ lead theo dõi gần đây
- Fix bugs lỗi spam mail nhắc cập nhật request
- Cập nhật response MSA: String => MapObject

Cập nhật nhánh tms:

- Fix bugs liên quan đến GPS
- Fix bugs TMS liên quan đến TMSPartner và TMSBillFee

Có thể download db mới tại đường dẫn https://beelogistics.cloud/download/datatpdb-latest.dump
hoặc chạy run update và các migrate script

# Cập nhật server , code nhánh develop và download db

Cập nhật nhánh ocr:
- Bổ sung trích rút tên người bán người mua bên python và thêm vào bên giao diện
- cập nhật trích rút bên dat-ocr, code datatp-python

Cập nhật nhánh maintenance
- Remove react-beautiful-dnd thư viện
- Cập nhật kanban sử dụng dndkit lib

Có thể download db mới tại đường dẫn https://beelogistics.cloud/download/datatpdb-latest.dump
hoặc chạy run update và các migrate script

Nếu không cập nhật db, chạy script:
- migrate:run --script tms/MigrationTmsPartnerAddess.groovy
