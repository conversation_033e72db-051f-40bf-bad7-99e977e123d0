---
sidebar_position: 1
hide_table_of_contents: true
displayed_sidebar: developerSidebar
---

# Changelog

All notable changes to this project will be documented in this file.

### [Unreleased]
1. [Nhat]
- <PERSON><PERSON> sung auto save khi edit trong màn hình <PERSON>PI/KPI Template
- C<PERSON><PERSON> nhật dữ liệu Account Thêm account mới/Xoá account nhân sự đã nghỉ
- Thêm trường Leader cho K<PERSON>/Template, c<PERSON> quyền tương tự với Manager
  <PERSON><PERSON><PERSON><PERSON><PERSON> có <PERSON>, bư<PERSON><PERSON> duyệt KPI sau cùng phải thêm 1 bư<PERSON><PERSON> duyệt bởi leader (leader => manager => director)
- Init dữ liệu KPI Q2 cho các chi nhánh
  `Script:`
    + migrate:run --script hr/UpdateData.groovy

    + server:migrate:run --script hr/CheckAccount.groovy --company bee
    + server:migrate:run --script hr/CheckAccount.groovy --company beehph
    + server:migrate:run --script hr/CheckAccount.groovy --company beehan
    + server:migrate:run --script hr/CheckAccount.groovy --company beedad
    + server:migrate:run --script hr/CheckAccount.groovy --company beehcm

    + server:migrate:run --script hr/InitKpiTemplate.groovy --company bee
    + server:migrate:run --script hr/InitKpiTemplate.groovy --company beehph
    + server:migrate:run --script hr/InitKpiTemplate.groovy --company beehan
    + server:migrate:run --script hr/InitKpiTemplate.groovy --company beedad
    + server:migrate:run --script hr/InitKpiTemplate.groovy --company beehcm

    + server:migrate:run --script hr/InitKpi.groovy --company bee
    + server:migrate:run --script hr/InitKpi.groovy --company beehph
    + server:migrate:run --script hr/InitKpi.groovy --company beehan
    + server:migrate:run --script hr/InitKpi.groovy --company beedad
    + server:migrate:run --script hr/InitKpi.groovy --company beehcm

### []