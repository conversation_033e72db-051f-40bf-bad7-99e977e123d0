---
sidebar_position: 4
hide_table_of_contents: false
displayed_sidebar: userSidebar
---

# Quotation

#### Flow diagram of quotation screens & functions.

<details>
<summary> Flow Diagram & Functions </summary>

```text
┌────────────────────────────┐ ┌────────────────────────────┐
│  Click "Request a Quote"   │ │  Click "Request Pricing"   │
│                            │ │                            │
└───────────┬────────────────┘ └───────────┬────────────────┘
            │                              │
            │                              ▼
            │                  ┌────────────────────────────┐
            │                  │  Pricing Department        │
            │                  │  updates price information │
            │                  └───────────┬────────────────┘
            │                              │
            │                              ▼
            │                  ┌────────────────────────────┐
            │                  │  Go to Quotation Workboard │
            │                  │  Click Create Quotation    │
            │                  └───────────┬────────────────┘
            │                              │
            ▼                              ▼
┌────────────────────────────────────────────────────────────┐
│           Quotation Form Screen                            │
│           - Add other costs                                │
│           - Add margin                                     │
│           - Edit quotation                                 │
│           - Export quotation to Excel                      │
│           - Send email to customer                         │
└────────────────────────────────────────────────────────────┘
```
</details>

### Quotation Workboard Screen.
Manage the list of created quotations, including the following functions:

#### 1. Update customer feedback, quotation status (Win, Mismatch, etc.).
Track and update feedback and status for each quotation.

    ![../img/update_status_quote.gif](../img/update_status_quote.gif)

#### 2. Edit, update quotation - <mark>Quotation Form</mark>
- Click on the quotation you want to edit.
- The system will switch to the [<mark>Quotation Form screen</mark>](#quotation-form-screen) for you to perform actions such as adding costs, margin, editing content, exporting Excel file, sending email to customer, etc.
    ![./img/quotation/view_quotation.gif](./img/quotation/view_quotation.gif)

#### 3. Send new price check request to Pricing department.
- When you need a new quotation, you can send a request directly from the old quotation.
- The Pricing department will update the price, then you return to the Quotation Workboard, click "Create Quotation" to switch to the Quotation Form and complete the quotation.

    ![./img/quotation/resend_request.gif](./img/quotation/resend_request.gif)

#### 4. Copy an old quotation to create a new one:
- Select the quotation you want to copy, use the copy function to create a new quotation based on the old information, saving input time.
  <div style={{
    background: '#fff3cd',
    color: '#856404',
    padding: '12px 16px',
    margin: '16px 16px',
    borderRadius: '6px',
    border: '1px solid #ffeeba',
  }}>
    <strong>Note:</strong> You can only copy a quotation if it was previously created (copy icon is green).
  </div>

    ![./img/quotation/copy_quotation.gif](./img/quotation/copy_quotation.gif)


#### 5. Create, update Internal Booking information, send to BFSOne.

- After the quotation is confirmed, you can create an Internal Booking from this quotation and send the information to the BFSOne system.
      ![./img/quotation/create_ib.gif](./img/quotation/create_ib.gif)

- You can resend a new Internal Booking based on the information of the old Internal Booking.
      ![./img/quotation/save_as_ib.gif](./img/quotation/save_as_ib.gif)

  <div style={{
    background: '#fff3cd',
    color: '#856404',
    padding: '12px 16px',
    margin: '16px 16px',
    borderRadius: '6px',
    border: '1px solid #ffeeba',
  }}>
    <strong>Note:</strong> <br />
    - Green icon indicates Internal Booking was previously created. <br />
    - Black icon indicates Internal Booking is being created for the first time.
  </div>

- When using standard pricing, creating a `quotation` and successfully sending an `IB`, the pricing information will be promoted to the price list, allowing other salespeople to easily reference and use these rates. Additionally, the pricing team can evaluate price effectiveness and frequency of use.
 ![./img/quotation/promote_price.gif](./img/quotation/promote_price.gif)

<hr />

### Quotation Form Screen.

_Case Study: ` Quotation FCL 2x40DC, NANSHA, CHINA -> HOCHIMINH, VIETNAM, term FOB `_

_Demo video_: [https://youtu.be/6cwWaDSQWGg](https://youtu.be/6cwWaDSQWGg)

<div style={{ position: 'relative', paddingBottom: '56.25%', height: 0, overflow: 'hidden', maxWidth: '100%', height: 'auto', marginLeft: '20px' }}>
  <iframe
        style={{ position: 'absolute', top: 0, left: 0, width: '100%', height: '100%' }}
        src="https://www.youtube.com/embed/6cwWaDSQWGg"
        frameBorder="0"
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
        allowFullScreen />
</div>

<hr />

### Internal Booking Screen.

_Demo video_: [https://youtu.be/L0-s335pG_Q](https://youtu.be/L0-s335pG_Q)

<div style={{ position: 'relative', paddingBottom: '56.25%', height: 0, overflow: 'hidden', maxWidth: '100%', height: 'auto', marginLeft: '20px' }}>
  <iframe
        style={{ position: 'absolute', top: 0, left: 0, width: '100%', height: '100%' }}
        src="https://www.youtube.com/embed/L0-s335pG_Q"
        frameBorder="0"
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
        allowFullScreen />
</div>


<hr />

  **If the system does not have a price available**

  - Click `Request a Quote` to create a custom quotation. Other operations are similar to the demo video above.

  ![./img/quotation_fcl_1.png](./img/quotation_fcl_1.png)