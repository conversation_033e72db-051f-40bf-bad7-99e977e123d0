2025-08-05T10:56:13.564+07:00  INFO 5934 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 5934 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-08-05T10:56:13.565+07:00  INFO 5934 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-08-05T10:56:14.461+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T10:56:14.551+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 86 ms. Found 22 JPA repository interfaces.
2025-08-05T10:56:14.565+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T10:56:14.568+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 1 JPA repository interface.
2025-08-05T10:56:14.569+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T10:56:14.578+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 9 JPA repository interfaces.
2025-08-05T10:56:14.621+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T10:56:14.626+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 3 JPA repository interfaces.
2025-08-05T10:56:14.626+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T10:56:14.632+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 6 JPA repository interfaces.
2025-08-05T10:56:14.647+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T10:56:14.655+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 1 JPA repository interface.
2025-08-05T10:56:14.666+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T10:56:14.671+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 5 JPA repository interfaces.
2025-08-05T10:56:14.679+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T10:56:14.684+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 3 JPA repository interfaces.
2025-08-05T10:56:14.685+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T10:56:14.685+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-08-05T10:56:14.690+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T10:56:14.698+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 10 JPA repository interfaces.
2025-08-05T10:56:14.703+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T10:56:14.709+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 3 JPA repository interfaces.
2025-08-05T10:56:14.709+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T10:56:14.716+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 6 JPA repository interfaces.
2025-08-05T10:56:14.718+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T10:56:14.729+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 12 JPA repository interfaces.
2025-08-05T10:56:14.731+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T10:56:14.734+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 4 JPA repository interfaces.
2025-08-05T10:56:14.735+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T10:56:14.735+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-08-05T10:56:14.735+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T10:56:14.738+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 1 JPA repository interface.
2025-08-05T10:56:14.739+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T10:56:14.744+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 7 JPA repository interfaces.
2025-08-05T10:56:14.746+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T10:56:14.750+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 2 JPA repository interfaces.
2025-08-05T10:56:14.751+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T10:56:14.751+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-08-05T10:56:14.751+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T10:56:14.763+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 12 ms. Found 19 JPA repository interfaces.
2025-08-05T10:56:14.774+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T10:56:14.782+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 8 JPA repository interfaces.
2025-08-05T10:56:14.782+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T10:56:14.786+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 5 JPA repository interfaces.
2025-08-05T10:56:14.786+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T10:56:14.791+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 7 JPA repository interfaces.
2025-08-05T10:56:14.792+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T10:56:14.799+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 9 JPA repository interfaces.
2025-08-05T10:56:14.799+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T10:56:14.804+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 6 JPA repository interfaces.
2025-08-05T10:56:14.805+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T10:56:14.810+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 5 JPA repository interfaces.
2025-08-05T10:56:14.811+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T10:56:14.824+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 12 ms. Found 7 JPA repository interfaces.
2025-08-05T10:56:14.825+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T10:56:14.836+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 11 ms. Found 14 JPA repository interfaces.
2025-08-05T10:56:14.836+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T10:56:14.855+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 18 ms. Found 20 JPA repository interfaces.
2025-08-05T10:56:14.855+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T10:56:14.856+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-08-05T10:56:14.862+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T10:56:14.863+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-08-05T10:56:14.863+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T10:56:14.874+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 11 ms. Found 12 JPA repository interfaces.
2025-08-05T10:56:14.879+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T10:56:14.926+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 46 ms. Found 69 JPA repository interfaces.
2025-08-05T10:56:14.926+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T10:56:14.928+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-08-05T10:56:14.934+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T10:56:14.938+07:00  INFO 5934 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 4 JPA repository interfaces.
2025-08-05T10:56:15.201+07:00  INFO 5934 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-08-05T10:56:15.210+07:00  INFO 5934 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-08-05T10:56:15.597+07:00  WARN 5934 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-08-05T10:56:15.847+07:00  INFO 5934 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-08-05T10:56:15.852+07:00  INFO 5934 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-08-05T10:56:15.872+07:00  INFO 5934 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-08-05T10:56:15.873+07:00  INFO 5934 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 2139 ms
2025-08-05T10:56:15.934+07:00  WARN 5934 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-05T10:56:15.934+07:00  INFO 5934 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-08-05T10:56:16.158+07:00  INFO 5934 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@4daa53e0
2025-08-05T10:56:16.158+07:00  INFO 5934 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-08-05T10:56:16.163+07:00  WARN 5934 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-05T10:56:16.163+07:00  INFO 5934 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-08-05T10:56:16.189+07:00  INFO 5934 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@2806c788
2025-08-05T10:56:16.189+07:00  INFO 5934 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-08-05T10:56:16.190+07:00  WARN 5934 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-05T10:56:16.190+07:00  INFO 5934 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-05T10:56:16.756+07:00  INFO 5934 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@1a0c384d
2025-08-05T10:56:16.757+07:00  INFO 5934 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-05T10:56:16.757+07:00  WARN 5934 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-05T10:56:16.757+07:00  INFO 5934 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-08-05T10:56:16.773+07:00  INFO 5934 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@534f4a65
2025-08-05T10:56:16.773+07:00  INFO 5934 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-08-05T10:56:16.773+07:00  WARN 5934 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-05T10:56:16.773+07:00  INFO 5934 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-08-05T10:56:16.797+07:00  INFO 5934 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@13453610
2025-08-05T10:56:16.797+07:00  INFO 5934 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-08-05T10:56:16.797+07:00  INFO 5934 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '****************************************************', '***********************************************', '**********************************************'
2025-08-05T10:56:16.868+07:00  INFO 5934 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-08-05T10:56:16.873+07:00  INFO 5934 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@3619ecd{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.8181448859215518411/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@193e65d5{STARTED}}
2025-08-05T10:56:16.874+07:00  INFO 5934 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@3619ecd{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.8181448859215518411/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@193e65d5{STARTED}}
2025-08-05T10:56:16.876+07:00  INFO 5934 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@5eb054{STARTING}[12.0.15,sto=0] @4099ms
2025-08-05T10:56:16.965+07:00  INFO 5934 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-05T10:56:17.008+07:00  INFO 5934 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-08-05T10:56:17.041+07:00  INFO 5934 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-05T10:56:17.183+07:00  INFO 5934 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-05T10:56:17.225+07:00  WARN 5934 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-05T10:56:18.099+07:00  INFO 5934 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-05T10:56:18.109+07:00  INFO 5934 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@1aec9516] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-08-05T10:56:18.303+07:00  INFO 5934 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-05T10:56:18.527+07:00  INFO 5934 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.kpi", "net.datatp.module.graphapi", "net.datatp.module.wfms", "net.datatp.module.monitor.activity", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "cloud.datatp.module.fleet", "net.datatp.module.okr", "net.datatp.module.core.template", "cloud.datatp.module.pegasus.shipment", "cloud.datatp.gps", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "net.datatp.module.groovy", "cloud.datatp.tms", "net.datatp.module.company.tmpl", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "net.datatp.module.resource", "cloud.datatp.vendor", "net.datatp.module.company.hr", "net.datatp.module.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "net.datatp.module.core.print", "cloud.datatp.module.odoo", "net.datatp.module.chatbot", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.zalo", "net.datatp.module.accounting" ]
2025-08-05T10:56:18.529+07:00  INFO 5934 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-08-05T10:56:18.535+07:00  INFO 5934 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-05T10:56:18.536+07:00  INFO 5934 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-05T10:56:18.562+07:00  INFO 5934 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-05T10:56:18.565+07:00  WARN 5934 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-05T10:56:20.738+07:00  INFO 5934 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-05T10:56:20.740+07:00  INFO 5934 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@ad0b708] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-08-05T10:56:20.947+07:00  WARN 5934 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-08-05T10:56:20.947+07:00  WARN 5934 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-08-05T10:56:20.954+07:00  WARN 5934 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-08-05T10:56:20.954+07:00  WARN 5934 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-08-05T10:56:20.969+07:00  WARN 5934 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-08-05T10:56:20.969+07:00  WARN 5934 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-08-05T10:56:21.455+07:00  INFO 5934 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-05T10:56:21.466+07:00  INFO 5934 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-05T10:56:21.467+07:00  INFO 5934 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-05T10:56:21.485+07:00  INFO 5934 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-05T10:56:21.487+07:00  WARN 5934 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-05T10:56:21.970+07:00  INFO 5934 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-05T10:56:21.970+07:00  INFO 5934 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@6e7ba872] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-08-05T10:56:22.030+07:00  WARN 5934 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-08-05T10:56:22.031+07:00  WARN 5934 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-08-05T10:56:22.364+07:00  INFO 5934 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-05T10:56:22.399+07:00  INFO 5934 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-08-05T10:56:22.404+07:00  INFO 5934 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-08-05T10:56:22.404+07:00  INFO 5934 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-08-05T10:56:22.411+07:00  WARN 5934 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-08-05T10:56:22.541+07:00  INFO 5934 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-08-05T10:56:23.017+07:00  INFO 5934 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-08-05T10:56:23.020+07:00  INFO 5934 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-08-05T10:56:23.055+07:00  INFO 5934 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-08-05T10:56:23.098+07:00  INFO 5934 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-08-05T10:56:23.188+07:00  INFO 5934 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-08-05T10:56:23.217+07:00  INFO 5934 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-08-05T10:56:23.239+07:00  INFO 5934 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 8ms : this is harmless.
2025-08-05T10:56:23.247+07:00  INFO 5934 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-08-05T10:56:23.251+07:00  INFO 5934 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-08-05T10:56:23.264+07:00  INFO 5934 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 937354722ms : this is harmless.
2025-08-05T10:56:23.265+07:00  INFO 5934 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-08-05T10:56:23.279+07:00  INFO 5934 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-08-05T10:56:23.280+07:00  INFO 5934 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-08-05T10:56:25.520+07:00  INFO 5934 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-08-05T10:56:25.520+07:00  INFO 5934 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-08-05T10:56:25.521+07:00  WARN 5934 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-08-05T10:56:25.632+07:00  INFO 5934 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 05/08/2025@10:45:00+0700 to 05/08/2025@11:00:00+0700
2025-08-05T10:56:25.632+07:00  INFO 5934 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 05/08/2025@10:45:00+0700 to 05/08/2025@11:00:00+0700
2025-08-05T10:56:26.095+07:00  INFO 5934 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-08-05T10:56:26.095+07:00  INFO 5934 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-08-05T10:56:26.096+07:00  WARN 5934 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-08-05T10:56:26.313+07:00  INFO 5934 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-08-05T10:56:26.313+07:00  INFO 5934 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-08-05T10:56:26.313+07:00  INFO 5934 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-08-05T10:56:26.313+07:00  INFO 5934 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-08-05T10:56:26.313+07:00  INFO 5934 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-08-05T10:56:28.030+07:00  WARN 5934 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 0ff51262-a86f-4651-9f55-70aec7479171

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-05T10:56:28.033+07:00  INFO 5934 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-05T10:56:28.344+07:00  INFO 5934 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-08-05T10:56:28.345+07:00  INFO 5934 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-08-05T10:56:28.345+07:00  INFO 5934 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-08-05T10:56:28.345+07:00  INFO 5934 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-08-05T10:56:28.345+07:00  INFO 5934 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-08-05T10:56:28.345+07:00  INFO 5934 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-08-05T10:56:28.345+07:00  INFO 5934 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-08-05T10:56:28.345+07:00  INFO 5934 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-08-05T10:56:28.345+07:00  INFO 5934 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-08-05T10:56:28.345+07:00  INFO 5934 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-08-05T10:56:28.345+07:00  INFO 5934 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-08-05T10:56:28.345+07:00  INFO 5934 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-08-05T10:56:28.347+07:00  INFO 5934 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-08-05T10:56:28.347+07:00  INFO 5934 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-08-05T10:56:28.348+07:00  INFO 5934 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-08-05T10:56:28.397+07:00  INFO 5934 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-05T10:56:28.397+07:00  INFO 5934 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-05T10:56:28.398+07:00  INFO 5934 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-08-05T10:56:28.407+07:00  INFO 5934 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@5cf4b5bd{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-08-05T10:56:28.407+07:00  INFO 5934 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-08-05T10:56:28.408+07:00  INFO 5934 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-08-05T10:56:28.436+07:00  INFO 5934 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-08-05T10:56:28.436+07:00  INFO 5934 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-08-05T10:56:28.442+07:00  INFO 5934 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 15.338 seconds (process running for 15.666)
2025-08-05T10:57:03.438+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T10:57:13.506+07:00  INFO 5934 --- [qtp1595278145-37] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0vclbm5wwqkhl2sgb5c0tqy0
2025-08-05T10:57:13.787+07:00  INFO 5934 --- [qtp1595278145-37] n.d.module.session.ClientSessionManager  : Add a client session id = node0vclbm5wwqkhl2sgb5c0tqy0, token = adc18e33db59b1165def340334dada3c
2025-08-05T10:57:14.172+07:00  INFO 5934 --- [qtp1595278145-37] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-05T10:57:31.511+07:00  INFO 5934 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 7, expire count 0
2025-08-05T10:57:31.530+07:00  INFO 5934 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T10:58:06.592+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T10:59:02.683+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T10:59:30.741+07:00  INFO 5934 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-08-05T10:59:30.749+07:00  INFO 5934 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T10:59:41.835+07:00  INFO 5934 --- [qtp1595278145-60] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le logout successfully 
2025-08-05T10:59:44.330+07:00  INFO 5934 --- [qtp1595278145-61] n.d.module.session.ClientSessionManager  : Add a client session id = node0vclbm5wwqkhl2sgb5c0tqy0, token = f140ec2c0eef6e09bb630d7a6f50d058
2025-08-05T10:59:44.334+07:00  INFO 5934 --- [qtp1595278145-61] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-05T11:00:05.808+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-05T11:00:05.810+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-05T11:00:05.810+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-08-05T11:00:05.811+07:00  INFO 5934 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 05/08/2025@11:00:05+0700
2025-08-05T11:00:05.817+07:00  INFO 5934 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 05/08/2025@11:00:00+0700 to 05/08/2025@11:15:00+0700
2025-08-05T11:00:05.817+07:00  INFO 5934 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 05/08/2025@11:00:00+0700 to 05/08/2025@11:15:00+0700
2025-08-05T11:00:05.817+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T11:00:08.301+07:00  INFO 5934 --- [qtp1595278145-68] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le logout successfully 
2025-08-05T11:00:09.780+07:00 ERROR 5934 --- [qtp1595278145-41] net.datatp.module.account.AccountLogic   : User ELAINE.VNSGN try to login into system, but fail
2025-08-05T11:00:09.785+07:00 ERROR 5934 --- [qtp1595278145-41] n.d.m.monitor.call.EndpointCallContext   : Start call with component CompanyAuthenticationService, method authenticate, arguments
[ {
  "tenantId" : "",
  "companyId" : null,
  "companyParentId" : null,
  "companyCode" : null,
  "companyLabel" : null,
  "companyFullName" : null,
  "loginId" : "anon",
  "accountId" : 0,
  "token" : "anon",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node0vclbm5wwqkhl2sgb5c0tqy0",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : null,
  "allowAccessCompanies" : null,
  "featurePermissions" : null,
  "attributes" : { },
  "clientId" : ":anon"
}, null, {
  "loginId" : "ELAINE.VNSGN",
  "authorization" : null,
  "company" : "",
  "password" : "ELAINE.VNSGN",
  "timeToLiveInMin" : 10080,
  "accessType" : "Employee"
} ]
2025-08-05T11:00:09.785+07:00 ERROR 5934 --- [qtp1595278145-41] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:115)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.authenticateCall(RPCController.java:61)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NullPointerException: Cannot invoke "java.lang.Long.longValue()" because the return value of "net.datatp.module.core.security.entity.AccessToken.getAccountId()" is null
	at net.datatp.module.company.auth.CompanyAuthenticationService.authenticate(CompanyAuthenticationService.java:84)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at net.datatp.module.company.auth.CompanyAuthenticationService$$SpringCGLIB$$0.authenticate(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 98 common frames omitted

2025-08-05T11:00:09.790+07:00  INFO 5934 --- [qtp1595278145-41] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint CompanyAuthenticationService/authenticate
2025-08-05T11:00:13.163+07:00 ERROR 5934 --- [qtp1595278145-65] net.datatp.module.account.AccountLogic   : User ELAINE.VNSGN try to login into system, but fail
2025-08-05T11:00:13.164+07:00 ERROR 5934 --- [qtp1595278145-65] n.d.m.monitor.call.EndpointCallContext   : Start call with component CompanyAuthenticationService, method authenticate, arguments
[ {
  "tenantId" : "",
  "companyId" : null,
  "companyParentId" : null,
  "companyCode" : null,
  "companyLabel" : null,
  "companyFullName" : null,
  "loginId" : "anon",
  "accountId" : 0,
  "token" : "anon",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node0vclbm5wwqkhl2sgb5c0tqy0",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : null,
  "allowAccessCompanies" : null,
  "featurePermissions" : null,
  "attributes" : { },
  "clientId" : ":anon"
}, null, {
  "loginId" : "ELAINE.VNSGN",
  "authorization" : null,
  "company" : "",
  "password" : "ELAINE.VNSGN",
  "timeToLiveInMin" : 10080,
  "accessType" : "Employee"
} ]
2025-08-05T11:00:13.164+07:00 ERROR 5934 --- [qtp1595278145-65] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:115)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.authenticateCall(RPCController.java:61)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NullPointerException: Cannot invoke "java.lang.Long.longValue()" because the return value of "net.datatp.module.core.security.entity.AccessToken.getAccountId()" is null
	at net.datatp.module.company.auth.CompanyAuthenticationService.authenticate(CompanyAuthenticationService.java:84)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at net.datatp.module.company.auth.CompanyAuthenticationService$$SpringCGLIB$$0.authenticate(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 98 common frames omitted

2025-08-05T11:00:13.166+07:00  INFO 5934 --- [qtp1595278145-65] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint CompanyAuthenticationService/authenticate
2025-08-05T11:00:17.176+07:00  INFO 5934 --- [qtp1595278145-68] n.d.module.session.ClientSessionManager  : Add a client session id = node0vclbm5wwqkhl2sgb5c0tqy0, token = 52b1924a81704f51333dbb043af259c1
2025-08-05T11:00:17.182+07:00  INFO 5934 --- [qtp1595278145-68] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-05T11:00:28.024+07:00  INFO 5934 --- [qtp1595278145-65] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le logout successfully 
2025-08-05T11:00:29.404+07:00  INFO 5934 --- [qtp1595278145-41] n.d.module.session.ClientSessionManager  : Add a client session id = node0vclbm5wwqkhl2sgb5c0tqy0, token = 4315e76ae436768ba88532a6c96806e5
2025-08-05T11:00:29.412+07:00  INFO 5934 --- [qtp1595278145-41] n.d.m.c.a.CompanyAuthenticationService   : User elaine.vnsgn is logged in successfully system
2025-08-05T11:00:47.800+07:00  INFO 5934 --- [qtp1595278145-35] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01afh0tsdh8gti6n7vw5sxdfx11
2025-08-05T11:00:47.947+07:00  INFO 5934 --- [qtp1595278145-35] n.d.module.session.ClientSessionManager  : Add a client session id = node01afh0tsdh8gti6n7vw5sxdfx11, token = bb465228695a9c6cbb1669ea1d158083
2025-08-05T11:00:47.955+07:00  INFO 5934 --- [qtp1595278145-35] n.d.m.c.a.CompanyAuthenticationService   : User stacy.vnhan is logged in successfully system
2025-08-05T11:01:06.924+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T11:01:35.021+07:00  INFO 5934 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 17, expire count 0
2025-08-05T11:01:35.038+07:00  INFO 5934 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T11:02:05.082+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T11:03:06.168+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T11:03:35.299+07:00  INFO 5934 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 26, expire count 0
2025-08-05T11:03:35.305+07:00  INFO 5934 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T11:04:04.359+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T11:05:06.480+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T11:05:06.482+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-05T11:05:34.541+07:00  INFO 5934 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T11:05:34.548+07:00  INFO 5934 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T11:06:03.602+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T11:07:06.721+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T11:07:34.796+07:00  INFO 5934 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 2
2025-08-05T11:07:34.811+07:00  INFO 5934 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T11:08:02.855+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T11:09:05.976+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T11:09:34.030+07:00  INFO 5934 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 7
2025-08-05T11:09:34.037+07:00  INFO 5934 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T11:10:02.090+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T11:10:02.091+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-05T11:10:21.902+07:00  INFO 5934 --- [qtp1595278145-35] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node019yeb70y7ux0qbqiy4g9nickp2
2025-08-05T11:10:22.012+07:00  INFO 5934 --- [qtp1595278145-35] n.d.module.session.ClientSessionManager  : Add a client session id = node019yeb70y7ux0qbqiy4g9nickp2, token = 80e30675df777ae4fa69602372343c2e
2025-08-05T11:10:22.025+07:00  INFO 5934 --- [qtp1595278145-35] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-05T11:11:05.184+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T11:11:33.265+07:00  INFO 5934 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 20, expire count 12
2025-08-05T11:11:33.285+07:00  INFO 5934 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T11:11:41.806+07:00  INFO 5934 --- [qtp1595278145-37] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le logout successfully 
2025-08-05T11:11:45.049+07:00  INFO 5934 --- [qtp1595278145-102] n.d.module.session.ClientSessionManager  : Add a client session id = node019yeb70y7ux0qbqiy4g9nickp2, token = f37763d4c14b341d6a2257e366ab6a64
2025-08-05T11:11:45.059+07:00  INFO 5934 --- [qtp1595278145-102] n.d.m.c.a.CompanyAuthenticationService   : User hana.luong is logged in successfully system
2025-08-05T11:11:48.517+07:00 ERROR 5934 --- [qtp1595278145-35] n.d.m.monitor.call.EndpointCallService   : Unknown Error when handle the endpoint service/[GET] get-company-node
2025-08-05T11:11:48.517+07:00 ERROR 5934 --- [qtp1595278145-35] n.d.m.monitor.call.EndpointCallService   : Exception: 

net.datatp.util.error.RuntimeError: /companies/bee/ is not existed!
	at net.datatp.module.storage.fs.FSStorageService.ensureSystemFileExists(FSStorageService.java:427)
	at net.datatp.module.storage.fs.FSStorageService.getNode(FSStorageService.java:111)
	at net.datatp.module.storage.Storage.getNode(Storage.java:42)
	at net.datatp.module.storage.http.StorageController.lambda$companyGetSNode$2(StorageController.java:90)
	at net.datatp.module.http.rest.v1.AbstractController$1.doCall(AbstractController.java:65)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.http.rest.v1.AbstractController.execute(AbstractController.java:70)
	at net.datatp.module.storage.http.StorageController.companyGetSNode(StorageController.java:92)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:500)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)

2025-08-05T11:11:48.526+07:00 ERROR 5934 --- [qtp1595278145-69] n.d.m.monitor.call.EndpointCallService   : Unknown Error when handle the endpoint service/[GET] get-company-node
2025-08-05T11:11:48.526+07:00 ERROR 5934 --- [qtp1595278145-69] n.d.m.monitor.call.EndpointCallService   : Exception: 

net.datatp.util.error.RuntimeError: /companies/bee/ is not existed!
	at net.datatp.module.storage.fs.FSStorageService.ensureSystemFileExists(FSStorageService.java:427)
	at net.datatp.module.storage.fs.FSStorageService.getNode(FSStorageService.java:111)
	at net.datatp.module.storage.Storage.getNode(Storage.java:42)
	at net.datatp.module.storage.http.StorageController.lambda$companyGetSNode$2(StorageController.java:90)
	at net.datatp.module.http.rest.v1.AbstractController$1.doCall(AbstractController.java:65)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.http.rest.v1.AbstractController.execute(AbstractController.java:70)
	at net.datatp.module.storage.http.StorageController.companyGetSNode(StorageController.java:92)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:500)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)

2025-08-05T11:12:06.337+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T11:12:07.638+07:00  INFO 5934 --- [qtp1595278145-80] n.d.module.session.ClientSessionManager  : Add a client session id = node019yeb70y7ux0qbqiy4g9nickp2, token = f37763d4c14b341d6a2257e366ab6a64
2025-08-05T11:12:07.638+07:00  INFO 5934 --- [qtp1595278145-101] n.d.module.session.ClientSessionManager  : Add a client session id = node019yeb70y7ux0qbqiy4g9nickp2, token = f37763d4c14b341d6a2257e366ab6a64
2025-08-05T11:12:07.656+07:00  INFO 5934 --- [qtp1595278145-80] n.d.m.c.a.CompanyAuthenticationService   : User hana.luong is logged in successfully system
2025-08-05T11:12:07.656+07:00  INFO 5934 --- [qtp1595278145-101] n.d.m.c.a.CompanyAuthenticationService   : User hana.luong is logged in successfully system
2025-08-05T11:12:08.165+07:00  INFO 5934 --- [qtp1595278145-102] n.d.module.session.ClientSessionManager  : Add a client session id = node0vclbm5wwqkhl2sgb5c0tqy0, token = 4315e76ae436768ba88532a6c96806e5
2025-08-05T11:12:08.166+07:00  INFO 5934 --- [qtp1595278145-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0vclbm5wwqkhl2sgb5c0tqy0, token = 4315e76ae436768ba88532a6c96806e5
2025-08-05T11:12:08.171+07:00  INFO 5934 --- [qtp1595278145-35] n.d.m.c.a.CompanyAuthenticationService   : User elaine.vnsgn is logged in successfully system
2025-08-05T11:12:08.172+07:00  INFO 5934 --- [qtp1595278145-102] n.d.m.c.a.CompanyAuthenticationService   : User elaine.vnsgn is logged in successfully system
2025-08-05T11:13:04.434+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T11:13:32.520+07:00  INFO 5934 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 8, expire count 9
2025-08-05T11:13:32.526+07:00  INFO 5934 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T11:14:06.564+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T11:14:10.102+07:00  INFO 5934 --- [qtp1595278145-80] n.d.module.session.ClientSessionManager  : Add a client session id = node019yeb70y7ux0qbqiy4g9nickp2, token = f37763d4c14b341d6a2257e366ab6a64
2025-08-05T11:14:10.119+07:00  INFO 5934 --- [qtp1595278145-80] n.d.m.c.a.CompanyAuthenticationService   : User hana.luong is logged in successfully system
2025-08-05T11:14:10.144+07:00  INFO 5934 --- [qtp1595278145-69] n.d.module.session.ClientSessionManager  : Add a client session id = node019yeb70y7ux0qbqiy4g9nickp2, token = f37763d4c14b341d6a2257e366ab6a64
2025-08-05T11:14:10.180+07:00  INFO 5934 --- [qtp1595278145-69] n.d.m.c.a.CompanyAuthenticationService   : User hana.luong is logged in successfully system
2025-08-05T11:14:13.976+07:00  INFO 5934 --- [qtp1595278145-101] n.d.module.session.ClientSessionManager  : Add a client session id = node0vclbm5wwqkhl2sgb5c0tqy0, token = 4315e76ae436768ba88532a6c96806e5
2025-08-05T11:14:13.977+07:00  INFO 5934 --- [qtp1595278145-80] n.d.module.session.ClientSessionManager  : Add a client session id = node0vclbm5wwqkhl2sgb5c0tqy0, token = 4315e76ae436768ba88532a6c96806e5
2025-08-05T11:14:13.985+07:00  INFO 5934 --- [qtp1595278145-80] n.d.m.c.a.CompanyAuthenticationService   : User elaine.vnsgn is logged in successfully system
2025-08-05T11:14:13.985+07:00  INFO 5934 --- [qtp1595278145-101] n.d.m.c.a.CompanyAuthenticationService   : User elaine.vnsgn is logged in successfully system
2025-08-05T11:14:31.521+07:00 ERROR 5934 --- [qtp1595278145-37] n.d.m.monitor.call.EndpointCallService   : Unknown Error when handle the endpoint service/[GET] get-company-node
2025-08-05T11:14:31.522+07:00 ERROR 5934 --- [qtp1595278145-37] n.d.m.monitor.call.EndpointCallService   : Exception: 

net.datatp.util.error.RuntimeError: /companies/bee/ is not existed!
	at net.datatp.module.storage.fs.FSStorageService.ensureSystemFileExists(FSStorageService.java:427)
	at net.datatp.module.storage.fs.FSStorageService.getNode(FSStorageService.java:111)
	at net.datatp.module.storage.Storage.getNode(Storage.java:42)
	at net.datatp.module.storage.http.StorageController.lambda$companyGetSNode$2(StorageController.java:90)
	at net.datatp.module.http.rest.v1.AbstractController$1.doCall(AbstractController.java:65)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.http.rest.v1.AbstractController.execute(AbstractController.java:70)
	at net.datatp.module.storage.http.StorageController.companyGetSNode(StorageController.java:92)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:500)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)

2025-08-05T11:14:31.538+07:00 ERROR 5934 --- [qtp1595278145-101] n.d.m.monitor.call.EndpointCallService   : Unknown Error when handle the endpoint service/[GET] get-company-node
2025-08-05T11:14:31.538+07:00 ERROR 5934 --- [qtp1595278145-101] n.d.m.monitor.call.EndpointCallService   : Exception: 

net.datatp.util.error.RuntimeError: /companies/bee/ is not existed!
	at net.datatp.module.storage.fs.FSStorageService.ensureSystemFileExists(FSStorageService.java:427)
	at net.datatp.module.storage.fs.FSStorageService.getNode(FSStorageService.java:111)
	at net.datatp.module.storage.Storage.getNode(Storage.java:42)
	at net.datatp.module.storage.http.StorageController.lambda$companyGetSNode$2(StorageController.java:90)
	at net.datatp.module.http.rest.v1.AbstractController$1.doCall(AbstractController.java:65)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.http.rest.v1.AbstractController.execute(AbstractController.java:70)
	at net.datatp.module.storage.http.StorageController.companyGetSNode(StorageController.java:92)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:500)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)

2025-08-05T11:15:03.663+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T11:15:03.668+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-05T11:15:03.669+07:00  INFO 5934 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 05/08/2025@11:15:03+0700
2025-08-05T11:15:03.695+07:00  INFO 5934 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 05/08/2025@11:15:00+0700 to 05/08/2025@11:30:00+0700
2025-08-05T11:15:03.696+07:00  INFO 5934 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 05/08/2025@11:15:00+0700 to 05/08/2025@11:30:00+0700
2025-08-05T11:15:03.696+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-05T11:15:31.800+07:00  INFO 5934 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 19, expire count 0
2025-08-05T11:15:31.811+07:00  INFO 5934 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T11:16:06.872+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T11:16:52.457+07:00  INFO 5934 --- [qtp1595278145-37] n.d.module.session.ClientSessionManager  : Add a client session id = node019yeb70y7ux0qbqiy4g9nickp2, token = f37763d4c14b341d6a2257e366ab6a64
2025-08-05T11:16:52.475+07:00  INFO 5934 --- [qtp1595278145-37] n.d.m.c.a.CompanyAuthenticationService   : User hana.luong is logged in successfully system
2025-08-05T11:16:52.485+07:00  INFO 5934 --- [qtp1595278145-39] n.d.module.session.ClientSessionManager  : Add a client session id = node019yeb70y7ux0qbqiy4g9nickp2, token = f37763d4c14b341d6a2257e366ab6a64
2025-08-05T11:16:52.492+07:00  INFO 5934 --- [qtp1595278145-39] n.d.m.c.a.CompanyAuthenticationService   : User hana.luong is logged in successfully system
2025-08-05T11:16:53.176+07:00  INFO 5934 --- [qtp1595278145-101] n.d.module.session.ClientSessionManager  : Add a client session id = node0vclbm5wwqkhl2sgb5c0tqy0, token = 4315e76ae436768ba88532a6c96806e5
2025-08-05T11:16:53.180+07:00  INFO 5934 --- [qtp1595278145-101] n.d.m.c.a.CompanyAuthenticationService   : User elaine.vnsgn is logged in successfully system
2025-08-05T11:16:53.206+07:00  INFO 5934 --- [qtp1595278145-64] n.d.module.session.ClientSessionManager  : Add a client session id = node0vclbm5wwqkhl2sgb5c0tqy0, token = 4315e76ae436768ba88532a6c96806e5
2025-08-05T11:16:53.213+07:00  INFO 5934 --- [qtp1595278145-64] n.d.m.c.a.CompanyAuthenticationService   : User elaine.vnsgn is logged in successfully system
2025-08-05T11:17:02.958+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T11:17:31.046+07:00  INFO 5934 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 0
2025-08-05T11:17:31.050+07:00  INFO 5934 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T11:17:52.046+07:00  INFO 5934 --- [qtp1595278145-101] n.d.module.session.ClientSessionManager  : Add a client session id = node019yeb70y7ux0qbqiy4g9nickp2, token = f37763d4c14b341d6a2257e366ab6a64
2025-08-05T11:17:52.075+07:00  INFO 5934 --- [qtp1595278145-101] n.d.m.c.a.CompanyAuthenticationService   : User hana.luong is logged in successfully system
2025-08-05T11:17:52.085+07:00  INFO 5934 --- [qtp1595278145-62] n.d.module.session.ClientSessionManager  : Add a client session id = node019yeb70y7ux0qbqiy4g9nickp2, token = f37763d4c14b341d6a2257e366ab6a64
2025-08-05T11:17:52.093+07:00  INFO 5934 --- [qtp1595278145-62] n.d.m.c.a.CompanyAuthenticationService   : User hana.luong is logged in successfully system
2025-08-05T11:17:52.192+07:00  INFO 5934 --- [qtp1595278145-40] n.d.module.session.ClientSessionManager  : Add a client session id = node0vclbm5wwqkhl2sgb5c0tqy0, token = 4315e76ae436768ba88532a6c96806e5
2025-08-05T11:17:52.192+07:00  INFO 5934 --- [qtp1595278145-71] n.d.module.session.ClientSessionManager  : Add a client session id = node0vclbm5wwqkhl2sgb5c0tqy0, token = 4315e76ae436768ba88532a6c96806e5
2025-08-05T11:17:52.197+07:00  INFO 5934 --- [qtp1595278145-71] n.d.m.c.a.CompanyAuthenticationService   : User elaine.vnsgn is logged in successfully system
2025-08-05T11:17:52.202+07:00  INFO 5934 --- [qtp1595278145-40] n.d.m.c.a.CompanyAuthenticationService   : User elaine.vnsgn is logged in successfully system
2025-08-05T11:18:06.104+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T11:18:16.894+07:00  INFO 5934 --- [Scheduler-*********-1] n.d.m.session.AppHttpSessionListener     : The session node01afh0tsdh8gti6n7vw5sxdfx11 is destroyed.
2025-08-05T11:19:02.184+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T11:19:25.283+07:00  INFO 5934 --- [qtp1595278145-37] n.d.module.session.ClientSessionManager  : Add a client session id = node019yeb70y7ux0qbqiy4g9nickp2, token = f37763d4c14b341d6a2257e366ab6a64
2025-08-05T11:19:25.284+07:00  INFO 5934 --- [qtp1595278145-61] n.d.module.session.ClientSessionManager  : Add a client session id = node019yeb70y7ux0qbqiy4g9nickp2, token = f37763d4c14b341d6a2257e366ab6a64
2025-08-05T11:19:25.297+07:00  INFO 5934 --- [qtp1595278145-61] n.d.m.c.a.CompanyAuthenticationService   : User hana.luong is logged in successfully system
2025-08-05T11:19:25.297+07:00  INFO 5934 --- [qtp1595278145-37] n.d.m.c.a.CompanyAuthenticationService   : User hana.luong is logged in successfully system
2025-08-05T11:19:26.160+07:00  INFO 5934 --- [qtp1595278145-101] n.d.module.session.ClientSessionManager  : Add a client session id = node0vclbm5wwqkhl2sgb5c0tqy0, token = 4315e76ae436768ba88532a6c96806e5
2025-08-05T11:19:26.167+07:00  INFO 5934 --- [qtp1595278145-101] n.d.m.c.a.CompanyAuthenticationService   : User elaine.vnsgn is logged in successfully system
2025-08-05T11:19:26.198+07:00  INFO 5934 --- [qtp1595278145-111] n.d.module.session.ClientSessionManager  : Add a client session id = node0vclbm5wwqkhl2sgb5c0tqy0, token = 4315e76ae436768ba88532a6c96806e5
2025-08-05T11:19:26.202+07:00  INFO 5934 --- [qtp1595278145-111] n.d.m.c.a.CompanyAuthenticationService   : User elaine.vnsgn is logged in successfully system
2025-08-05T11:19:35.240+07:00  INFO 5934 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 10, expire count 1
2025-08-05T11:19:35.255+07:00  INFO 5934 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T11:19:38.361+07:00  INFO 5934 --- [qtp1595278145-107] n.d.m.c.a.CompanyAuthenticationService   : User elaine.vnsgn logout successfully 
2025-08-05T11:19:44.254+07:00  INFO 5934 --- [qtp1595278145-101] n.d.module.session.ClientSessionManager  : Add a client session id = node0vclbm5wwqkhl2sgb5c0tqy0, token = 890c833bbd0e2b4abeb6944072489b40
2025-08-05T11:19:44.260+07:00  INFO 5934 --- [qtp1595278145-101] n.d.m.c.a.CompanyAuthenticationService   : User stacy.vnhan is logged in successfully system
2025-08-05T11:19:57.366+07:00 ERROR 5934 --- [qtp1595278145-72] n.d.m.monitor.call.EndpointCallService   : Unknown Error when handle the endpoint service/[GET] get-company-node
2025-08-05T11:19:57.367+07:00 ERROR 5934 --- [qtp1595278145-72] n.d.m.monitor.call.EndpointCallService   : Exception: 

net.datatp.util.error.RuntimeError: /companies/bee/ is not existed!
	at net.datatp.module.storage.fs.FSStorageService.ensureSystemFileExists(FSStorageService.java:427)
	at net.datatp.module.storage.fs.FSStorageService.getNode(FSStorageService.java:111)
	at net.datatp.module.storage.Storage.getNode(Storage.java:42)
	at net.datatp.module.storage.http.StorageController.lambda$companyGetSNode$2(StorageController.java:90)
	at net.datatp.module.http.rest.v1.AbstractController$1.doCall(AbstractController.java:65)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.http.rest.v1.AbstractController.execute(AbstractController.java:70)
	at net.datatp.module.storage.http.StorageController.companyGetSNode(StorageController.java:92)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:500)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)

2025-08-05T11:19:57.378+07:00 ERROR 5934 --- [qtp1595278145-69] n.d.m.monitor.call.EndpointCallService   : Unknown Error when handle the endpoint service/[GET] get-company-node
2025-08-05T11:19:57.379+07:00 ERROR 5934 --- [qtp1595278145-69] n.d.m.monitor.call.EndpointCallService   : Exception: 

net.datatp.util.error.RuntimeError: /companies/bee/ is not existed!
	at net.datatp.module.storage.fs.FSStorageService.ensureSystemFileExists(FSStorageService.java:427)
	at net.datatp.module.storage.fs.FSStorageService.getNode(FSStorageService.java:111)
	at net.datatp.module.storage.Storage.getNode(Storage.java:42)
	at net.datatp.module.storage.http.StorageController.lambda$companyGetSNode$2(StorageController.java:90)
	at net.datatp.module.http.rest.v1.AbstractController$1.doCall(AbstractController.java:65)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.http.rest.v1.AbstractController.execute(AbstractController.java:70)
	at net.datatp.module.storage.http.StorageController.companyGetSNode(StorageController.java:92)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:500)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)

2025-08-05T11:20:05.315+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-05T11:20:05.316+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T11:21:06.399+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T11:21:34.466+07:00  INFO 5934 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 10, expire count 18
2025-08-05T11:21:34.487+07:00  INFO 5934 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T11:21:49.155+07:00  INFO 5934 --- [qtp1595278145-111] n.d.module.session.ClientSessionManager  : Add a client session id = node0vclbm5wwqkhl2sgb5c0tqy0, token = 890c833bbd0e2b4abeb6944072489b40
2025-08-05T11:21:49.156+07:00  INFO 5934 --- [qtp1595278145-69] n.d.module.session.ClientSessionManager  : Add a client session id = node0vclbm5wwqkhl2sgb5c0tqy0, token = 890c833bbd0e2b4abeb6944072489b40
2025-08-05T11:21:49.171+07:00  INFO 5934 --- [qtp1595278145-69] n.d.m.c.a.CompanyAuthenticationService   : User stacy.vnhan is logged in successfully system
2025-08-05T11:21:49.171+07:00  INFO 5934 --- [qtp1595278145-111] n.d.m.c.a.CompanyAuthenticationService   : User stacy.vnhan is logged in successfully system
2025-08-05T11:21:49.242+07:00  INFO 5934 --- [qtp1595278145-66] n.d.module.session.ClientSessionManager  : Add a client session id = node019yeb70y7ux0qbqiy4g9nickp2, token = f37763d4c14b341d6a2257e366ab6a64
2025-08-05T11:21:49.247+07:00  INFO 5934 --- [qtp1595278145-66] n.d.m.c.a.CompanyAuthenticationService   : User hana.luong is logged in successfully system
2025-08-05T11:21:49.250+07:00  INFO 5934 --- [qtp1595278145-112] n.d.module.session.ClientSessionManager  : Add a client session id = node019yeb70y7ux0qbqiy4g9nickp2, token = f37763d4c14b341d6a2257e366ab6a64
2025-08-05T11:21:49.259+07:00  INFO 5934 --- [qtp1595278145-112] n.d.m.c.a.CompanyAuthenticationService   : User hana.luong is logged in successfully system
2025-08-05T11:22:04.540+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T11:22:35.727+07:00  INFO 5934 --- [qtp1595278145-39] n.d.module.session.ClientSessionManager  : Add a client session id = node019yeb70y7ux0qbqiy4g9nickp2, token = f37763d4c14b341d6a2257e366ab6a64
2025-08-05T11:22:35.742+07:00  INFO 5934 --- [qtp1595278145-69] n.d.module.session.ClientSessionManager  : Add a client session id = node019yeb70y7ux0qbqiy4g9nickp2, token = f37763d4c14b341d6a2257e366ab6a64
2025-08-05T11:22:35.831+07:00  INFO 5934 --- [qtp1595278145-39] n.d.m.c.a.CompanyAuthenticationService   : User hana.luong is logged in successfully system
2025-08-05T11:22:35.833+07:00  INFO 5934 --- [qtp1595278145-69] n.d.m.c.a.CompanyAuthenticationService   : User hana.luong is logged in successfully system
2025-08-05T11:22:36.243+07:00  INFO 5934 --- [qtp1595278145-66] n.d.module.session.ClientSessionManager  : Add a client session id = node0vclbm5wwqkhl2sgb5c0tqy0, token = 890c833bbd0e2b4abeb6944072489b40
2025-08-05T11:22:36.275+07:00  INFO 5934 --- [qtp1595278145-66] n.d.m.c.a.CompanyAuthenticationService   : User stacy.vnhan is logged in successfully system
2025-08-05T11:22:36.300+07:00  INFO 5934 --- [qtp1595278145-107] n.d.module.session.ClientSessionManager  : Add a client session id = node0vclbm5wwqkhl2sgb5c0tqy0, token = 890c833bbd0e2b4abeb6944072489b40
2025-08-05T11:22:36.308+07:00  INFO 5934 --- [qtp1595278145-107] n.d.m.c.a.CompanyAuthenticationService   : User stacy.vnhan is logged in successfully system
2025-08-05T11:22:39.936+07:00  INFO 5934 --- [qtp1595278145-37] n.d.module.session.ClientSessionManager  : Add a client session id = node019yeb70y7ux0qbqiy4g9nickp2, token = f37763d4c14b341d6a2257e366ab6a64
2025-08-05T11:22:39.937+07:00  INFO 5934 --- [qtp1595278145-69] n.d.module.session.ClientSessionManager  : Add a client session id = node019yeb70y7ux0qbqiy4g9nickp2, token = f37763d4c14b341d6a2257e366ab6a64
2025-08-05T11:22:39.945+07:00  INFO 5934 --- [qtp1595278145-69] n.d.m.c.a.CompanyAuthenticationService   : User hana.luong is logged in successfully system
2025-08-05T11:22:39.945+07:00  INFO 5934 --- [qtp1595278145-37] n.d.m.c.a.CompanyAuthenticationService   : User hana.luong is logged in successfully system
2025-08-05T11:22:40.145+07:00  INFO 5934 --- [qtp1595278145-66] n.d.module.session.ClientSessionManager  : Add a client session id = node0vclbm5wwqkhl2sgb5c0tqy0, token = 890c833bbd0e2b4abeb6944072489b40
2025-08-05T11:22:40.146+07:00  INFO 5934 --- [qtp1595278145-71] n.d.module.session.ClientSessionManager  : Add a client session id = node0vclbm5wwqkhl2sgb5c0tqy0, token = 890c833bbd0e2b4abeb6944072489b40
2025-08-05T11:22:40.148+07:00  INFO 5934 --- [qtp1595278145-66] n.d.m.c.a.CompanyAuthenticationService   : User stacy.vnhan is logged in successfully system
2025-08-05T11:22:40.148+07:00  INFO 5934 --- [qtp1595278145-71] n.d.m.c.a.CompanyAuthenticationService   : User stacy.vnhan is logged in successfully system
2025-08-05T11:23:04.794+07:00  INFO 5934 --- [qtp1595278145-111] n.d.module.session.ClientSessionManager  : Add a client session id = node019yeb70y7ux0qbqiy4g9nickp2, token = f37763d4c14b341d6a2257e366ab6a64
2025-08-05T11:23:04.804+07:00  INFO 5934 --- [qtp1595278145-111] n.d.m.c.a.CompanyAuthenticationService   : User hana.luong is logged in successfully system
2025-08-05T11:23:04.814+07:00  INFO 5934 --- [qtp1595278145-133] n.d.module.session.ClientSessionManager  : Add a client session id = node019yeb70y7ux0qbqiy4g9nickp2, token = f37763d4c14b341d6a2257e366ab6a64
2025-08-05T11:23:04.823+07:00  INFO 5934 --- [qtp1595278145-133] n.d.m.c.a.CompanyAuthenticationService   : User hana.luong is logged in successfully system
2025-08-05T11:23:04.833+07:00 ERROR 5934 --- [qtp1595278145-69] n.d.m.monitor.call.EndpointCallService   : Unknown Error when handle the endpoint service/[GET] get-company-node
2025-08-05T11:23:04.833+07:00 ERROR 5934 --- [qtp1595278145-69] n.d.m.monitor.call.EndpointCallService   : Exception: 

net.datatp.util.error.RuntimeError: /companies/bee/ is not existed!
	at net.datatp.module.storage.fs.FSStorageService.ensureSystemFileExists(FSStorageService.java:427)
	at net.datatp.module.storage.fs.FSStorageService.getNode(FSStorageService.java:111)
	at net.datatp.module.storage.Storage.getNode(Storage.java:42)
	at net.datatp.module.storage.http.StorageController.lambda$companyGetSNode$2(StorageController.java:90)
	at net.datatp.module.http.rest.v1.AbstractController$1.doCall(AbstractController.java:65)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.http.rest.v1.AbstractController.execute(AbstractController.java:70)
	at net.datatp.module.storage.http.StorageController.companyGetSNode(StorageController.java:92)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:500)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)

2025-08-05T11:23:04.838+07:00 ERROR 5934 --- [qtp1595278145-111] n.d.m.monitor.call.EndpointCallService   : Unknown Error when handle the endpoint service/[GET] get-company-node
2025-08-05T11:23:04.838+07:00 ERROR 5934 --- [qtp1595278145-111] n.d.m.monitor.call.EndpointCallService   : Exception: 

net.datatp.util.error.RuntimeError: /companies/bee/ is not existed!
	at net.datatp.module.storage.fs.FSStorageService.ensureSystemFileExists(FSStorageService.java:427)
	at net.datatp.module.storage.fs.FSStorageService.getNode(FSStorageService.java:111)
	at net.datatp.module.storage.Storage.getNode(Storage.java:42)
	at net.datatp.module.storage.http.StorageController.lambda$companyGetSNode$2(StorageController.java:90)
	at net.datatp.module.http.rest.v1.AbstractController$1.doCall(AbstractController.java:65)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.http.rest.v1.AbstractController.execute(AbstractController.java:70)
	at net.datatp.module.storage.http.StorageController.companyGetSNode(StorageController.java:92)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:500)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)

2025-08-05T11:23:05.151+07:00  INFO 5934 --- [qtp1595278145-133] n.d.module.session.ClientSessionManager  : Add a client session id = node0vclbm5wwqkhl2sgb5c0tqy0, token = 890c833bbd0e2b4abeb6944072489b40
2025-08-05T11:23:05.158+07:00  INFO 5934 --- [qtp1595278145-133] n.d.m.c.a.CompanyAuthenticationService   : User stacy.vnhan is logged in successfully system
2025-08-05T11:23:05.219+07:00  INFO 5934 --- [qtp1595278145-73] n.d.module.session.ClientSessionManager  : Add a client session id = node0vclbm5wwqkhl2sgb5c0tqy0, token = 890c833bbd0e2b4abeb6944072489b40
2025-08-05T11:23:05.231+07:00  INFO 5934 --- [qtp1595278145-73] n.d.m.c.a.CompanyAuthenticationService   : User stacy.vnhan is logged in successfully system
2025-08-05T11:23:06.656+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T11:23:34.722+07:00  INFO 5934 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 12, expire count 8
2025-08-05T11:23:34.733+07:00  INFO 5934 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T11:23:59.967+07:00  INFO 5934 --- [qtp1595278145-101] n.d.module.session.ClientSessionManager  : Add a client session id = node019yeb70y7ux0qbqiy4g9nickp2, token = f37763d4c14b341d6a2257e366ab6a64
2025-08-05T11:23:59.982+07:00  INFO 5934 --- [qtp1595278145-101] n.d.m.c.a.CompanyAuthenticationService   : User hana.luong is logged in successfully system
2025-08-05T11:24:00.027+07:00  INFO 5934 --- [qtp1595278145-83] n.d.module.session.ClientSessionManager  : Add a client session id = node019yeb70y7ux0qbqiy4g9nickp2, token = f37763d4c14b341d6a2257e366ab6a64
2025-08-05T11:24:00.035+07:00  INFO 5934 --- [qtp1595278145-83] n.d.m.c.a.CompanyAuthenticationService   : User hana.luong is logged in successfully system
2025-08-05T11:24:00.194+07:00  INFO 5934 --- [qtp1595278145-83] n.d.module.session.ClientSessionManager  : Add a client session id = node0vclbm5wwqkhl2sgb5c0tqy0, token = 890c833bbd0e2b4abeb6944072489b40
2025-08-05T11:24:00.241+07:00  INFO 5934 --- [qtp1595278145-83] n.d.m.c.a.CompanyAuthenticationService   : User stacy.vnhan is logged in successfully system
2025-08-05T11:24:00.241+07:00  INFO 5934 --- [qtp1595278145-62] n.d.module.session.ClientSessionManager  : Add a client session id = node0vclbm5wwqkhl2sgb5c0tqy0, token = 890c833bbd0e2b4abeb6944072489b40
2025-08-05T11:24:00.331+07:00  INFO 5934 --- [qtp1595278145-62] n.d.m.c.a.CompanyAuthenticationService   : User stacy.vnhan is logged in successfully system
2025-08-05T11:24:03.787+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T11:24:21.966+07:00  INFO 5934 --- [qtp1595278145-133] n.d.module.session.ClientSessionManager  : Add a client session id = node019yeb70y7ux0qbqiy4g9nickp2, token = f37763d4c14b341d6a2257e366ab6a64
2025-08-05T11:24:21.968+07:00  INFO 5934 --- [qtp1595278145-101] n.d.module.session.ClientSessionManager  : Add a client session id = node019yeb70y7ux0qbqiy4g9nickp2, token = f37763d4c14b341d6a2257e366ab6a64
2025-08-05T11:24:21.976+07:00  INFO 5934 --- [qtp1595278145-133] n.d.m.c.a.CompanyAuthenticationService   : User hana.luong is logged in successfully system
2025-08-05T11:24:21.976+07:00  INFO 5934 --- [qtp1595278145-101] n.d.m.c.a.CompanyAuthenticationService   : User hana.luong is logged in successfully system
2025-08-05T11:24:22.147+07:00  INFO 5934 --- [qtp1595278145-80] n.d.module.session.ClientSessionManager  : Add a client session id = node0vclbm5wwqkhl2sgb5c0tqy0, token = 890c833bbd0e2b4abeb6944072489b40
2025-08-05T11:24:22.147+07:00  INFO 5934 --- [qtp1595278145-111] n.d.module.session.ClientSessionManager  : Add a client session id = node0vclbm5wwqkhl2sgb5c0tqy0, token = 890c833bbd0e2b4abeb6944072489b40
2025-08-05T11:24:22.149+07:00  INFO 5934 --- [qtp1595278145-111] n.d.m.c.a.CompanyAuthenticationService   : User stacy.vnhan is logged in successfully system
2025-08-05T11:24:22.149+07:00  INFO 5934 --- [qtp1595278145-80] n.d.m.c.a.CompanyAuthenticationService   : User stacy.vnhan is logged in successfully system
2025-08-05T11:25:06.886+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T11:25:06.888+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-05T11:25:25.531+07:00  INFO 5934 --- [qtp1595278145-69] n.d.module.session.ClientSessionManager  : Add a client session id = node019yeb70y7ux0qbqiy4g9nickp2, token = f37763d4c14b341d6a2257e366ab6a64
2025-08-05T11:25:25.532+07:00  INFO 5934 --- [qtp1595278145-61] n.d.module.session.ClientSessionManager  : Add a client session id = node019yeb70y7ux0qbqiy4g9nickp2, token = f37763d4c14b341d6a2257e366ab6a64
2025-08-05T11:25:25.538+07:00  INFO 5934 --- [qtp1595278145-69] n.d.m.c.a.CompanyAuthenticationService   : User hana.luong is logged in successfully system
2025-08-05T11:25:25.538+07:00  INFO 5934 --- [qtp1595278145-61] n.d.m.c.a.CompanyAuthenticationService   : User hana.luong is logged in successfully system
2025-08-05T11:25:26.163+07:00  INFO 5934 --- [qtp1595278145-111] n.d.module.session.ClientSessionManager  : Add a client session id = node0vclbm5wwqkhl2sgb5c0tqy0, token = 890c833bbd0e2b4abeb6944072489b40
2025-08-05T11:25:26.164+07:00  INFO 5934 --- [qtp1595278145-80] n.d.module.session.ClientSessionManager  : Add a client session id = node0vclbm5wwqkhl2sgb5c0tqy0, token = 890c833bbd0e2b4abeb6944072489b40
2025-08-05T11:25:26.184+07:00  INFO 5934 --- [qtp1595278145-111] n.d.m.c.a.CompanyAuthenticationService   : User stacy.vnhan is logged in successfully system
2025-08-05T11:25:26.186+07:00  INFO 5934 --- [qtp1595278145-80] n.d.m.c.a.CompanyAuthenticationService   : User stacy.vnhan is logged in successfully system
2025-08-05T11:25:35.022+07:00  INFO 5934 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 10, expire count 3
2025-08-05T11:25:35.030+07:00  INFO 5934 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T11:25:36.317+07:00  INFO 5934 --- [qtp1595278145-111] n.d.module.session.ClientSessionManager  : Add a client session id = node019yeb70y7ux0qbqiy4g9nickp2, token = f37763d4c14b341d6a2257e366ab6a64
2025-08-05T11:25:36.329+07:00  INFO 5934 --- [qtp1595278145-111] n.d.m.c.a.CompanyAuthenticationService   : User hana.luong is logged in successfully system
2025-08-05T11:25:36.339+07:00  INFO 5934 --- [qtp1595278145-146] n.d.module.session.ClientSessionManager  : Add a client session id = node019yeb70y7ux0qbqiy4g9nickp2, token = f37763d4c14b341d6a2257e366ab6a64
2025-08-05T11:25:36.343+07:00  INFO 5934 --- [qtp1595278145-146] n.d.m.c.a.CompanyAuthenticationService   : User hana.luong is logged in successfully system
2025-08-05T11:25:37.168+07:00  INFO 5934 --- [qtp1595278145-80] n.d.module.session.ClientSessionManager  : Add a client session id = node0vclbm5wwqkhl2sgb5c0tqy0, token = 890c833bbd0e2b4abeb6944072489b40
2025-08-05T11:25:37.171+07:00  INFO 5934 --- [qtp1595278145-37] n.d.module.session.ClientSessionManager  : Add a client session id = node0vclbm5wwqkhl2sgb5c0tqy0, token = 890c833bbd0e2b4abeb6944072489b40
2025-08-05T11:25:37.175+07:00  INFO 5934 --- [qtp1595278145-37] n.d.m.c.a.CompanyAuthenticationService   : User stacy.vnhan is logged in successfully system
2025-08-05T11:25:37.175+07:00  INFO 5934 --- [qtp1595278145-80] n.d.m.c.a.CompanyAuthenticationService   : User stacy.vnhan is logged in successfully system
2025-08-05T11:26:03.077+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T11:26:13.313+07:00  INFO 5934 --- [qtp1595278145-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0vclbm5wwqkhl2sgb5c0tqy0, token = 890c833bbd0e2b4abeb6944072489b40
2025-08-05T11:26:13.328+07:00  INFO 5934 --- [qtp1595278145-35] n.d.m.c.a.CompanyAuthenticationService   : User stacy.vnhan is logged in successfully system
2025-08-05T11:26:13.355+07:00  INFO 5934 --- [qtp1595278145-34] n.d.module.session.ClientSessionManager  : Add a client session id = node0vclbm5wwqkhl2sgb5c0tqy0, token = 890c833bbd0e2b4abeb6944072489b40
2025-08-05T11:26:13.367+07:00  INFO 5934 --- [qtp1595278145-34] n.d.m.c.a.CompanyAuthenticationService   : User stacy.vnhan is logged in successfully system
2025-08-05T11:27:06.200+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T11:27:34.288+07:00  INFO 5934 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 0
2025-08-05T11:27:34.316+07:00  INFO 5934 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T11:28:02.357+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T11:29:05.465+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T11:29:33.542+07:00  INFO 5934 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 12, expire count 4
2025-08-05T11:29:33.566+07:00  INFO 5934 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T11:30:06.609+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-05T11:30:06.613+07:00  INFO 5934 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 05/08/2025@11:30:06+0700
2025-08-05T11:30:06.641+07:00  INFO 5934 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 05/08/2025@11:30:00+0700 to 05/08/2025@11:45:00+0700
2025-08-05T11:30:06.642+07:00  INFO 5934 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 05/08/2025@11:30:00+0700 to 05/08/2025@11:45:00+0700
2025-08-05T11:30:06.642+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T11:30:06.643+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-05T11:30:51.347+07:00  INFO 5934 --- [qtp1595278145-37] n.d.m.c.a.CompanyAuthenticationService   : User stacy.vnhan logout successfully 
2025-08-05T11:30:55.725+07:00  INFO 5934 --- [qtp1595278145-101] n.d.module.session.ClientSessionManager  : Add a client session id = node0vclbm5wwqkhl2sgb5c0tqy0, token = fc7e7cd3cec1695810edb6568d81f552
2025-08-05T11:30:55.828+07:00  INFO 5934 --- [qtp1595278145-101] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-05T11:31:04.740+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T11:31:32.821+07:00  INFO 5934 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 8, expire count 6
2025-08-05T11:31:32.846+07:00  INFO 5934 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T11:32:00.258+07:00  INFO 5934 --- [qtp1595278145-101] n.d.module.session.ClientSessionManager  : Add a client session id = node0vclbm5wwqkhl2sgb5c0tqy0, token = fc7e7cd3cec1695810edb6568d81f552
2025-08-05T11:32:00.264+07:00  INFO 5934 --- [qtp1595278145-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0vclbm5wwqkhl2sgb5c0tqy0, token = fc7e7cd3cec1695810edb6568d81f552
2025-08-05T11:32:00.439+07:00  INFO 5934 --- [qtp1595278145-101] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-05T11:32:00.440+07:00  INFO 5934 --- [qtp1595278145-35] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-05T11:32:02.160+07:00  INFO 5934 --- [qtp1595278145-35] n.d.module.session.ClientSessionManager  : Add a client session id = node019yeb70y7ux0qbqiy4g9nickp2, token = f37763d4c14b341d6a2257e366ab6a64
2025-08-05T11:32:02.161+07:00  INFO 5934 --- [qtp1595278145-69] n.d.module.session.ClientSessionManager  : Add a client session id = node019yeb70y7ux0qbqiy4g9nickp2, token = f37763d4c14b341d6a2257e366ab6a64
2025-08-05T11:32:02.174+07:00  INFO 5934 --- [qtp1595278145-35] n.d.m.c.a.CompanyAuthenticationService   : User hana.luong is logged in successfully system
2025-08-05T11:32:02.182+07:00  INFO 5934 --- [qtp1595278145-69] n.d.m.c.a.CompanyAuthenticationService   : User hana.luong is logged in successfully system
2025-08-05T11:32:06.930+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T11:32:10.369+07:00  INFO 5934 --- [qtp1595278145-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0vclbm5wwqkhl2sgb5c0tqy0, token = fc7e7cd3cec1695810edb6568d81f552
2025-08-05T11:32:10.386+07:00  INFO 5934 --- [qtp1595278145-71] n.d.module.session.ClientSessionManager  : Add a client session id = node0vclbm5wwqkhl2sgb5c0tqy0, token = fc7e7cd3cec1695810edb6568d81f552
2025-08-05T11:32:10.420+07:00  INFO 5934 --- [qtp1595278145-35] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-05T11:32:10.431+07:00  INFO 5934 --- [qtp1595278145-71] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-05T11:32:11.155+07:00  INFO 5934 --- [qtp1595278145-71] n.d.module.session.ClientSessionManager  : Add a client session id = node019yeb70y7ux0qbqiy4g9nickp2, token = f37763d4c14b341d6a2257e366ab6a64
2025-08-05T11:32:11.156+07:00  INFO 5934 --- [qtp1595278145-146] n.d.module.session.ClientSessionManager  : Add a client session id = node019yeb70y7ux0qbqiy4g9nickp2, token = f37763d4c14b341d6a2257e366ab6a64
2025-08-05T11:32:11.161+07:00  INFO 5934 --- [qtp1595278145-71] n.d.m.c.a.CompanyAuthenticationService   : User hana.luong is logged in successfully system
2025-08-05T11:32:11.161+07:00  INFO 5934 --- [qtp1595278145-146] n.d.m.c.a.CompanyAuthenticationService   : User hana.luong is logged in successfully system
2025-08-05T11:32:51.502+07:00  INFO 5934 --- [qtp1595278145-146] n.d.module.session.ClientSessionManager  : Add a client session id = node0vclbm5wwqkhl2sgb5c0tqy0, token = fc7e7cd3cec1695810edb6568d81f552
2025-08-05T11:32:51.504+07:00  INFO 5934 --- [qtp1595278145-71] n.d.module.session.ClientSessionManager  : Add a client session id = node0vclbm5wwqkhl2sgb5c0tqy0, token = fc7e7cd3cec1695810edb6568d81f552
2025-08-05T11:32:51.512+07:00  INFO 5934 --- [qtp1595278145-146] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-05T11:32:51.516+07:00  INFO 5934 --- [qtp1595278145-71] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-05T11:32:53.175+07:00  INFO 5934 --- [qtp1595278145-71] n.d.module.session.ClientSessionManager  : Add a client session id = node019yeb70y7ux0qbqiy4g9nickp2, token = f37763d4c14b341d6a2257e366ab6a64
2025-08-05T11:32:53.176+07:00  INFO 5934 --- [qtp1595278145-40] n.d.module.session.ClientSessionManager  : Add a client session id = node019yeb70y7ux0qbqiy4g9nickp2, token = f37763d4c14b341d6a2257e366ab6a64
2025-08-05T11:32:53.197+07:00  INFO 5934 --- [qtp1595278145-40] n.d.m.c.a.CompanyAuthenticationService   : User hana.luong is logged in successfully system
2025-08-05T11:32:53.196+07:00  INFO 5934 --- [qtp1595278145-71] n.d.m.c.a.CompanyAuthenticationService   : User hana.luong is logged in successfully system
2025-08-05T11:32:56.582+07:00  INFO 5934 --- [qtp1595278145-112] n.d.module.session.ClientSessionManager  : Add a client session id = node0vclbm5wwqkhl2sgb5c0tqy0, token = fc7e7cd3cec1695810edb6568d81f552
2025-08-05T11:32:56.640+07:00  INFO 5934 --- [qtp1595278145-112] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-05T11:32:56.643+07:00  INFO 5934 --- [qtp1595278145-103] n.d.module.session.ClientSessionManager  : Add a client session id = node0vclbm5wwqkhl2sgb5c0tqy0, token = fc7e7cd3cec1695810edb6568d81f552
2025-08-05T11:32:56.669+07:00  INFO 5934 --- [qtp1595278145-103] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-05T11:32:57.475+07:00  INFO 5934 --- [qtp1595278145-146] n.d.module.session.ClientSessionManager  : Add a client session id = node019yeb70y7ux0qbqiy4g9nickp2, token = f37763d4c14b341d6a2257e366ab6a64
2025-08-05T11:32:57.476+07:00  INFO 5934 --- [qtp1595278145-34] n.d.module.session.ClientSessionManager  : Add a client session id = node019yeb70y7ux0qbqiy4g9nickp2, token = f37763d4c14b341d6a2257e366ab6a64
2025-08-05T11:32:57.487+07:00  INFO 5934 --- [qtp1595278145-146] n.d.m.c.a.CompanyAuthenticationService   : User hana.luong is logged in successfully system
2025-08-05T11:32:57.487+07:00  INFO 5934 --- [qtp1595278145-34] n.d.m.c.a.CompanyAuthenticationService   : User hana.luong is logged in successfully system
2025-08-05T11:33:04.056+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T11:33:29.585+07:00  INFO 5934 --- [qtp1595278145-40] n.d.module.session.ClientSessionManager  : Add a client session id = node0vclbm5wwqkhl2sgb5c0tqy0, token = fc7e7cd3cec1695810edb6568d81f552
2025-08-05T11:33:29.592+07:00  INFO 5934 --- [qtp1595278145-34] n.d.module.session.ClientSessionManager  : Add a client session id = node0vclbm5wwqkhl2sgb5c0tqy0, token = fc7e7cd3cec1695810edb6568d81f552
2025-08-05T11:33:29.625+07:00  INFO 5934 --- [qtp1595278145-34] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-05T11:33:29.630+07:00  INFO 5934 --- [qtp1595278145-40] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-05T11:33:30.167+07:00  INFO 5934 --- [qtp1595278145-69] n.d.module.session.ClientSessionManager  : Add a client session id = node019yeb70y7ux0qbqiy4g9nickp2, token = f37763d4c14b341d6a2257e366ab6a64
2025-08-05T11:33:30.167+07:00  INFO 5934 --- [qtp1595278145-107] n.d.module.session.ClientSessionManager  : Add a client session id = node019yeb70y7ux0qbqiy4g9nickp2, token = f37763d4c14b341d6a2257e366ab6a64
2025-08-05T11:33:30.172+07:00  INFO 5934 --- [qtp1595278145-69] n.d.m.c.a.CompanyAuthenticationService   : User hana.luong is logged in successfully system
2025-08-05T11:33:30.172+07:00  INFO 5934 --- [qtp1595278145-107] n.d.m.c.a.CompanyAuthenticationService   : User hana.luong is logged in successfully system
2025-08-05T11:33:32.175+07:00  INFO 5934 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 7, expire count 1
2025-08-05T11:33:32.188+07:00  INFO 5934 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T11:34:04.553+07:00  INFO 5934 --- [qtp1595278145-34] n.d.module.session.ClientSessionManager  : Add a client session id = node0vclbm5wwqkhl2sgb5c0tqy0, token = fc7e7cd3cec1695810edb6568d81f552
2025-08-05T11:34:04.557+07:00  INFO 5934 --- [qtp1595278145-61] n.d.module.session.ClientSessionManager  : Add a client session id = node0vclbm5wwqkhl2sgb5c0tqy0, token = fc7e7cd3cec1695810edb6568d81f552
2025-08-05T11:34:04.571+07:00  INFO 5934 --- [qtp1595278145-34] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-05T11:34:04.571+07:00  INFO 5934 --- [qtp1595278145-61] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-05T11:34:06.149+07:00  INFO 5934 --- [qtp1595278145-34] n.d.module.session.ClientSessionManager  : Add a client session id = node019yeb70y7ux0qbqiy4g9nickp2, token = f37763d4c14b341d6a2257e366ab6a64
2025-08-05T11:34:06.149+07:00  INFO 5934 --- [qtp1595278145-80] n.d.module.session.ClientSessionManager  : Add a client session id = node019yeb70y7ux0qbqiy4g9nickp2, token = f37763d4c14b341d6a2257e366ab6a64
2025-08-05T11:34:06.156+07:00  INFO 5934 --- [qtp1595278145-34] n.d.m.c.a.CompanyAuthenticationService   : User hana.luong is logged in successfully system
2025-08-05T11:34:06.156+07:00  INFO 5934 --- [qtp1595278145-80] n.d.m.c.a.CompanyAuthenticationService   : User hana.luong is logged in successfully system
2025-08-05T11:34:06.245+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T11:35:03.369+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T11:35:03.374+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-05T11:35:31.454+07:00  INFO 5934 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 0
2025-08-05T11:35:31.491+07:00  INFO 5934 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T11:35:37.834+07:00  INFO 5934 --- [qtp1595278145-107] n.d.module.session.ClientSessionManager  : Add a client session id = node0vclbm5wwqkhl2sgb5c0tqy0, token = fc7e7cd3cec1695810edb6568d81f552
2025-08-05T11:35:37.835+07:00  INFO 5934 --- [qtp1595278145-112] n.d.module.session.ClientSessionManager  : Add a client session id = node0vclbm5wwqkhl2sgb5c0tqy0, token = fc7e7cd3cec1695810edb6568d81f552
2025-08-05T11:35:37.870+07:00  INFO 5934 --- [qtp1595278145-107] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-05T11:35:37.873+07:00  INFO 5934 --- [qtp1595278145-112] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-05T11:35:38.337+07:00  INFO 5934 --- [qtp1595278145-103] n.d.module.session.ClientSessionManager  : Add a client session id = node019yeb70y7ux0qbqiy4g9nickp2, token = f37763d4c14b341d6a2257e366ab6a64
2025-08-05T11:35:38.396+07:00  INFO 5934 --- [qtp1595278145-103] n.d.m.c.a.CompanyAuthenticationService   : User hana.luong is logged in successfully system
2025-08-05T11:35:38.566+07:00  INFO 5934 --- [qtp1595278145-143] n.d.module.session.ClientSessionManager  : Add a client session id = node019yeb70y7ux0qbqiy4g9nickp2, token = f37763d4c14b341d6a2257e366ab6a64
2025-08-05T11:35:38.718+07:00  INFO 5934 --- [qtp1595278145-143] n.d.m.c.a.CompanyAuthenticationService   : User hana.luong is logged in successfully system
2025-08-05T11:35:46.549+07:00  INFO 5934 --- [qtp1595278145-103] n.d.module.session.ClientSessionManager  : Add a client session id = node0vclbm5wwqkhl2sgb5c0tqy0, token = fc7e7cd3cec1695810edb6568d81f552
2025-08-05T11:35:46.568+07:00  INFO 5934 --- [qtp1595278145-103] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-05T11:35:46.591+07:00  INFO 5934 --- [qtp1595278145-181] n.d.module.session.ClientSessionManager  : Add a client session id = node0vclbm5wwqkhl2sgb5c0tqy0, token = fc7e7cd3cec1695810edb6568d81f552
2025-08-05T11:35:46.613+07:00  INFO 5934 --- [qtp1595278145-181] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-05T11:35:48.202+07:00  INFO 5934 --- [qtp1595278145-146] n.d.module.session.ClientSessionManager  : Add a client session id = node019yeb70y7ux0qbqiy4g9nickp2, token = f37763d4c14b341d6a2257e366ab6a64
2025-08-05T11:35:48.203+07:00  INFO 5934 --- [qtp1595278145-103] n.d.module.session.ClientSessionManager  : Add a client session id = node019yeb70y7ux0qbqiy4g9nickp2, token = f37763d4c14b341d6a2257e366ab6a64
2025-08-05T11:35:48.218+07:00  INFO 5934 --- [qtp1595278145-146] n.d.m.c.a.CompanyAuthenticationService   : User hana.luong is logged in successfully system
2025-08-05T11:35:48.220+07:00  INFO 5934 --- [qtp1595278145-103] n.d.m.c.a.CompanyAuthenticationService   : User hana.luong is logged in successfully system
2025-08-05T11:36:06.552+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T11:36:15.928+07:00  INFO 5934 --- [qtp1595278145-103] n.d.module.session.ClientSessionManager  : Add a client session id = node0vclbm5wwqkhl2sgb5c0tqy0, token = fc7e7cd3cec1695810edb6568d81f552
2025-08-05T11:36:15.979+07:00  INFO 5934 --- [qtp1595278145-103] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-05T11:36:16.124+07:00  INFO 5934 --- [qtp1595278145-145] n.d.module.session.ClientSessionManager  : Add a client session id = node0vclbm5wwqkhl2sgb5c0tqy0, token = fc7e7cd3cec1695810edb6568d81f552
2025-08-05T11:36:16.172+07:00  INFO 5934 --- [qtp1595278145-145] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-05T11:36:17.192+07:00  INFO 5934 --- [qtp1595278145-103] n.d.module.session.ClientSessionManager  : Add a client session id = node019yeb70y7ux0qbqiy4g9nickp2, token = f37763d4c14b341d6a2257e366ab6a64
2025-08-05T11:36:17.195+07:00  INFO 5934 --- [qtp1595278145-66] n.d.module.session.ClientSessionManager  : Add a client session id = node019yeb70y7ux0qbqiy4g9nickp2, token = f37763d4c14b341d6a2257e366ab6a64
2025-08-05T11:36:17.198+07:00  INFO 5934 --- [qtp1595278145-66] n.d.m.c.a.CompanyAuthenticationService   : User hana.luong is logged in successfully system
2025-08-05T11:36:17.199+07:00  INFO 5934 --- [qtp1595278145-103] n.d.m.c.a.CompanyAuthenticationService   : User hana.luong is logged in successfully system
2025-08-05T11:36:23.612+07:00  INFO 5934 --- [qtp1595278145-112] n.d.module.session.ClientSessionManager  : Add a client session id = node0vclbm5wwqkhl2sgb5c0tqy0, token = fc7e7cd3cec1695810edb6568d81f552
2025-08-05T11:36:23.613+07:00  INFO 5934 --- [qtp1595278145-71] n.d.module.session.ClientSessionManager  : Add a client session id = node0vclbm5wwqkhl2sgb5c0tqy0, token = fc7e7cd3cec1695810edb6568d81f552
2025-08-05T11:36:23.621+07:00  INFO 5934 --- [qtp1595278145-112] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-05T11:36:23.621+07:00  INFO 5934 --- [qtp1595278145-71] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-05T11:36:24.177+07:00  INFO 5934 --- [qtp1595278145-73] n.d.module.session.ClientSessionManager  : Add a client session id = node019yeb70y7ux0qbqiy4g9nickp2, token = f37763d4c14b341d6a2257e366ab6a64
2025-08-05T11:36:24.177+07:00  INFO 5934 --- [qtp1595278145-37] n.d.module.session.ClientSessionManager  : Add a client session id = node019yeb70y7ux0qbqiy4g9nickp2, token = f37763d4c14b341d6a2257e366ab6a64
2025-08-05T11:36:24.182+07:00  INFO 5934 --- [qtp1595278145-73] n.d.m.c.a.CompanyAuthenticationService   : User hana.luong is logged in successfully system
2025-08-05T11:36:24.182+07:00  INFO 5934 --- [qtp1595278145-37] n.d.m.c.a.CompanyAuthenticationService   : User hana.luong is logged in successfully system
2025-08-05T11:36:33.121+07:00  INFO 5934 --- [qtp1595278145-37] n.d.module.session.ClientSessionManager  : Add a client session id = node0vclbm5wwqkhl2sgb5c0tqy0, token = fc7e7cd3cec1695810edb6568d81f552
2025-08-05T11:36:33.122+07:00  INFO 5934 --- [qtp1595278145-112] n.d.module.session.ClientSessionManager  : Add a client session id = node0vclbm5wwqkhl2sgb5c0tqy0, token = fc7e7cd3cec1695810edb6568d81f552
2025-08-05T11:36:33.148+07:00  INFO 5934 --- [qtp1595278145-37] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-05T11:36:33.149+07:00  INFO 5934 --- [qtp1595278145-112] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-05T11:36:34.180+07:00  INFO 5934 --- [qtp1595278145-71] n.d.module.session.ClientSessionManager  : Add a client session id = node019yeb70y7ux0qbqiy4g9nickp2, token = f37763d4c14b341d6a2257e366ab6a64
2025-08-05T11:36:34.182+07:00  INFO 5934 --- [qtp1595278145-146] n.d.module.session.ClientSessionManager  : Add a client session id = node019yeb70y7ux0qbqiy4g9nickp2, token = f37763d4c14b341d6a2257e366ab6a64
2025-08-05T11:36:34.185+07:00  INFO 5934 --- [qtp1595278145-71] n.d.m.c.a.CompanyAuthenticationService   : User hana.luong is logged in successfully system
2025-08-05T11:36:34.191+07:00  INFO 5934 --- [qtp1595278145-146] n.d.m.c.a.CompanyAuthenticationService   : User hana.luong is logged in successfully system
2025-08-05T11:37:02.647+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T11:37:30.754+07:00  INFO 5934 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 10
2025-08-05T11:37:30.775+07:00  INFO 5934 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T11:38:05.827+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T11:39:06.920+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T11:39:34.997+07:00  INFO 5934 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-08-05T11:39:35.013+07:00  INFO 5934 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T11:40:05.057+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T11:40:05.058+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-05T11:41:06.155+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T11:41:35.219+07:00  INFO 5934 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 7
2025-08-05T11:41:35.227+07:00  INFO 5934 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T11:42:04.270+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T11:43:06.357+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T11:43:35.431+07:00  INFO 5934 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-08-05T11:43:35.439+07:00  INFO 5934 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T11:44:03.487+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T11:45:06.591+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T11:45:06.593+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-05T11:45:06.595+07:00  INFO 5934 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 05/08/2025@11:45:06+0700
2025-08-05T11:45:06.641+07:00  INFO 5934 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 05/08/2025@11:45:00+0700 to 05/08/2025@12:00:00+0700
2025-08-05T11:45:06.641+07:00  INFO 5934 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 05/08/2025@11:45:00+0700 to 05/08/2025@12:00:00+0700
2025-08-05T11:45:06.642+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-05T11:45:34.700+07:00  INFO 5934 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-05T11:45:34.713+07:00  INFO 5934 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T11:46:02.759+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T11:47:05.844+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T11:47:33.910+07:00  INFO 5934 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 6
2025-08-05T11:47:33.915+07:00  INFO 5934 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T11:48:06.953+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T11:49:05.063+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T11:49:33.102+07:00  INFO 5934 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T11:49:33.104+07:00  INFO 5934 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T11:50:06.143+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T11:50:06.144+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-05T11:51:04.255+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T11:51:32.345+07:00  INFO 5934 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 0
2025-08-05T11:51:32.365+07:00  INFO 5934 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T11:52:06.426+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T11:53:03.531+07:00  INFO 5934 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T11:53:22.780+07:00  INFO 5934 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@5cf4b5bd{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-08-05T11:53:22.781+07:00  INFO 5934 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-08-05T11:53:22.781+07:00  INFO 5934 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-08-05T11:53:22.781+07:00  INFO 5934 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-08-05T11:53:22.782+07:00  INFO 5934 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-08-05T11:53:22.782+07:00  INFO 5934 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-08-05T11:53:22.782+07:00  INFO 5934 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-08-05T11:53:22.782+07:00  INFO 5934 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-08-05T11:53:22.782+07:00  INFO 5934 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-08-05T11:53:22.782+07:00  INFO 5934 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-08-05T11:53:22.782+07:00  INFO 5934 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-08-05T11:53:22.782+07:00  INFO 5934 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-08-05T11:53:22.782+07:00  INFO 5934 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-08-05T11:53:22.782+07:00  INFO 5934 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-08-05T11:53:22.782+07:00  INFO 5934 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-08-05T11:53:22.782+07:00  INFO 5934 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-08-05T11:53:22.798+07:00  INFO 5934 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T11:53:22.873+07:00  INFO 5934 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-08-05T11:53:22.878+07:00  INFO 5934 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-08-05T11:53:22.916+07:00  INFO 5934 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-05T11:53:22.921+07:00  INFO 5934 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-05T11:53:22.927+07:00  INFO 5934 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-05T11:53:22.927+07:00  INFO 5934 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-08-05T11:53:22.929+07:00  INFO 5934 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-08-05T11:53:22.929+07:00  INFO 5934 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-08-05T11:53:22.929+07:00  INFO 5934 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-08-05T11:53:22.929+07:00  INFO 5934 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-08-05T11:53:23.092+07:00  INFO 5934 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-08-05T11:53:23.093+07:00  INFO 5934 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-08-05T11:53:23.094+07:00  INFO 5934 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-08-05T11:53:23.094+07:00  INFO 5934 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-08-05T11:53:23.094+07:00  INFO 5934 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-08-05T11:53:23.097+07:00  INFO 5934 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@5eb054{STOPPING}[12.0.15,sto=0]
2025-08-05T11:53:23.107+07:00  INFO 5934 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-05T11:53:23.113+07:00  INFO 5934 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@3619ecd{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.8181448859215518411/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@193e65d5{STOPPED}}
