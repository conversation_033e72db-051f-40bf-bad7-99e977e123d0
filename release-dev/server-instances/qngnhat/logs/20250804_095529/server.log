2025-08-04T09:55:30.277+07:00  INFO 31294 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 31294 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-08-04T09:55:30.278+07:00  INFO 31294 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-08-04T09:55:30.975+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T09:55:31.036+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 58 ms. Found 22 JPA repository interfaces.
2025-08-04T09:55:31.045+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T09:55:31.047+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-08-04T09:55:31.047+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T09:55:31.053+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 9 JPA repository interfaces.
2025-08-04T09:55:31.054+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T09:55:31.057+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-08-04T09:55:31.057+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T09:55:31.061+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-08-04T09:55:31.072+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T09:55:31.077+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 1 JPA repository interface.
2025-08-04T09:55:31.086+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T09:55:31.091+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 5 JPA repository interfaces.
2025-08-04T09:55:31.094+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T09:55:31.096+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-08-04T09:55:31.096+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T09:55:31.097+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-08-04T09:55:31.101+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T09:55:31.107+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 10 JPA repository interfaces.
2025-08-04T09:55:31.111+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T09:55:31.113+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-08-04T09:55:31.113+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T09:55:31.117+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-08-04T09:55:31.118+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T09:55:31.163+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 44 ms. Found 12 JPA repository interfaces.
2025-08-04T09:55:31.163+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T09:55:31.166+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-08-04T09:55:31.166+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T09:55:31.166+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-08-04T09:55:31.166+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T09:55:31.167+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-08-04T09:55:31.167+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T09:55:31.171+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-08-04T09:55:31.171+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T09:55:31.172+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-08-04T09:55:31.173+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T09:55:31.173+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-08-04T09:55:31.173+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T09:55:31.183+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 19 JPA repository interfaces.
2025-08-04T09:55:31.192+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T09:55:31.198+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 8 JPA repository interfaces.
2025-08-04T09:55:31.198+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T09:55:31.200+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-08-04T09:55:31.201+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T09:55:31.205+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-08-04T09:55:31.205+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T09:55:31.210+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 9 JPA repository interfaces.
2025-08-04T09:55:31.210+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T09:55:31.213+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-08-04T09:55:31.213+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T09:55:31.216+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-08-04T09:55:31.217+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T09:55:31.222+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-08-04T09:55:31.222+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T09:55:31.230+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 14 JPA repository interfaces.
2025-08-04T09:55:31.230+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T09:55:31.242+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 11 ms. Found 20 JPA repository interfaces.
2025-08-04T09:55:31.242+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T09:55:31.243+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-08-04T09:55:31.247+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T09:55:31.248+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-08-04T09:55:31.248+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T09:55:31.255+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-08-04T09:55:31.257+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T09:55:31.293+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 35 ms. Found 69 JPA repository interfaces.
2025-08-04T09:55:31.293+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T09:55:31.294+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-08-04T09:55:31.298+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T09:55:31.301+07:00  INFO 31294 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-08-04T09:55:31.476+07:00  INFO 31294 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-08-04T09:55:31.480+07:00  INFO 31294 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-08-04T09:55:31.750+07:00  WARN 31294 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-08-04T09:55:31.942+07:00  INFO 31294 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-08-04T09:55:31.944+07:00  INFO 31294 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-08-04T09:55:31.961+07:00  INFO 31294 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-08-04T09:55:31.961+07:00  INFO 31294 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1571 ms
2025-08-04T09:55:32.021+07:00  WARN 31294 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-04T09:55:32.021+07:00  INFO 31294 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-08-04T09:55:32.149+07:00  INFO 31294 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@635f4be1
2025-08-04T09:55:32.150+07:00  INFO 31294 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-08-04T09:55:32.156+07:00  WARN 31294 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-04T09:55:32.156+07:00  INFO 31294 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-08-04T09:55:32.165+07:00  INFO 31294 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@2ea3f905
2025-08-04T09:55:32.165+07:00  INFO 31294 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-08-04T09:55:32.165+07:00  WARN 31294 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-04T09:55:32.166+07:00  INFO 31294 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-04T09:55:32.692+07:00  INFO 31294 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@64dd94ed
2025-08-04T09:55:32.692+07:00  INFO 31294 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-04T09:55:32.693+07:00  WARN 31294 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-04T09:55:32.693+07:00  INFO 31294 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-08-04T09:55:32.698+07:00  INFO 31294 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@67f25c
2025-08-04T09:55:32.698+07:00  INFO 31294 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-08-04T09:55:32.698+07:00  WARN 31294 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-04T09:55:32.698+07:00  INFO 31294 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-08-04T09:55:32.706+07:00  INFO 31294 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@43941806
2025-08-04T09:55:32.706+07:00  INFO 31294 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-08-04T09:55:32.706+07:00  INFO 31294 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '****************************************************', '***********************************************', '**********************************************'
2025-08-04T09:55:32.757+07:00  INFO 31294 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-08-04T09:55:32.759+07:00  INFO 31294 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@5068a2b1{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.5413374056200040543/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@3938e876{STARTED}}
2025-08-04T09:55:32.760+07:00  INFO 31294 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@5068a2b1{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.5413374056200040543/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@3938e876{STARTED}}
2025-08-04T09:55:32.761+07:00  INFO 31294 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@7da4c956{STARTING}[12.0.15,sto=0] @3089ms
2025-08-04T09:55:32.819+07:00  INFO 31294 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-04T09:55:32.850+07:00  INFO 31294 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-08-04T09:55:32.865+07:00  INFO 31294 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-04T09:55:32.988+07:00  INFO 31294 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-04T09:55:33.015+07:00  WARN 31294 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-04T09:55:33.663+07:00  INFO 31294 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-04T09:55:33.672+07:00  INFO 31294 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@c9be844] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-08-04T09:55:33.820+07:00  INFO 31294 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-04T09:55:33.983+07:00  INFO 31294 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.graphapi", "net.datatp.module.kpi", "net.datatp.module.monitor.activity", "net.datatp.module.wfms", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "cloud.datatp.module.fleet", "net.datatp.module.okr", "net.datatp.module.core.template", "cloud.datatp.module.pegasus.shipment", "cloud.datatp.gps", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "net.datatp.module.company.tmpl", "net.datatp.module.groovy", "cloud.datatp.tms", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "net.datatp.module.resource", "cloud.datatp.vendor", "net.datatp.module.hr", "net.datatp.module.company.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "net.datatp.module.core.print", "cloud.datatp.module.odoo", "net.datatp.module.chatbot", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.accounting", "net.datatp.module.zalo" ]
2025-08-04T09:55:33.985+07:00  INFO 31294 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-08-04T09:55:33.992+07:00  INFO 31294 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-04T09:55:33.994+07:00  INFO 31294 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-04T09:55:34.021+07:00  INFO 31294 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-04T09:55:34.026+07:00  WARN 31294 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-04T09:55:36.773+07:00  INFO 31294 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-04T09:55:36.774+07:00  INFO 31294 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@57de4078] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-08-04T09:55:37.058+07:00  WARN 31294 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-08-04T09:55:37.058+07:00  WARN 31294 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-08-04T09:55:37.065+07:00  WARN 31294 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-08-04T09:55:37.065+07:00  WARN 31294 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-08-04T09:55:37.080+07:00  WARN 31294 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-08-04T09:55:37.080+07:00  WARN 31294 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-08-04T09:55:37.822+07:00  INFO 31294 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-04T09:55:37.831+07:00  INFO 31294 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-04T09:55:37.834+07:00  INFO 31294 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-04T09:55:37.853+07:00  INFO 31294 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-04T09:55:37.859+07:00  WARN 31294 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-04T09:55:38.474+07:00  INFO 31294 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-04T09:55:38.475+07:00  INFO 31294 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@2c9d377f] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-08-04T09:55:38.598+07:00  WARN 31294 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-08-04T09:55:38.598+07:00  WARN 31294 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-08-04T09:55:39.347+07:00  INFO 31294 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-04T09:55:39.391+07:00  INFO 31294 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-08-04T09:55:39.397+07:00  INFO 31294 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-08-04T09:55:39.397+07:00  INFO 31294 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-08-04T09:55:39.407+07:00  WARN 31294 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-08-04T09:55:39.597+07:00  INFO 31294 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-08-04T09:55:40.212+07:00  INFO 31294 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-08-04T09:55:40.218+07:00  INFO 31294 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-08-04T09:55:40.273+07:00  INFO 31294 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-08-04T09:55:40.351+07:00  INFO 31294 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-08-04T09:55:40.417+07:00  INFO 31294 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-08-04T09:55:40.449+07:00  INFO 31294 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-08-04T09:55:40.478+07:00  INFO 31294 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 166071912ms : this is harmless.
2025-08-04T09:55:40.488+07:00  INFO 31294 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-08-04T09:55:40.491+07:00  INFO 31294 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-08-04T09:55:40.504+07:00  INFO 31294 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 848303715ms : this is harmless.
2025-08-04T09:55:40.506+07:00  INFO 31294 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-08-04T09:55:40.520+07:00  INFO 31294 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-08-04T09:55:40.520+07:00  INFO 31294 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-08-04T09:55:44.235+07:00  INFO 31294 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-08-04T09:55:44.235+07:00  INFO 31294 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-08-04T09:55:44.236+07:00  WARN 31294 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-08-04T09:55:44.421+07:00  INFO 31294 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 04/08/2025@09:45:00+0700 to 04/08/2025@10:00:00+0700
2025-08-04T09:55:44.422+07:00  INFO 31294 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 04/08/2025@09:45:00+0700 to 04/08/2025@10:00:00+0700
2025-08-04T09:55:45.079+07:00  INFO 31294 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-08-04T09:55:45.079+07:00  INFO 31294 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-08-04T09:55:45.080+07:00  WARN 31294 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-08-04T09:55:45.400+07:00  INFO 31294 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-08-04T09:55:45.400+07:00  INFO 31294 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-08-04T09:55:45.400+07:00  INFO 31294 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-08-04T09:55:45.400+07:00  INFO 31294 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-08-04T09:55:45.400+07:00  INFO 31294 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-08-04T09:55:47.802+07:00  WARN 31294 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 988c2297-f41e-42cf-82e3-df6a9a46ed20

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-04T09:55:47.807+07:00  INFO 31294 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-04T09:55:48.191+07:00  INFO 31294 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-08-04T09:55:48.192+07:00  INFO 31294 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-08-04T09:55:48.192+07:00  INFO 31294 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-08-04T09:55:48.192+07:00  INFO 31294 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-08-04T09:55:48.192+07:00  INFO 31294 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-08-04T09:55:48.192+07:00  INFO 31294 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-08-04T09:55:48.192+07:00  INFO 31294 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-08-04T09:55:48.192+07:00  INFO 31294 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-08-04T09:55:48.192+07:00  INFO 31294 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-08-04T09:55:48.192+07:00  INFO 31294 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-08-04T09:55:48.192+07:00  INFO 31294 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-08-04T09:55:48.192+07:00  INFO 31294 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-08-04T09:55:48.193+07:00  INFO 31294 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-08-04T09:55:48.193+07:00  INFO 31294 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-08-04T09:55:48.193+07:00  INFO 31294 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-08-04T09:55:48.253+07:00  INFO 31294 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-04T09:55:48.253+07:00  INFO 31294 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-04T09:55:48.255+07:00  INFO 31294 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-08-04T09:55:48.265+07:00  INFO 31294 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@1c89f3b{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-08-04T09:55:48.266+07:00  INFO 31294 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-08-04T09:55:48.267+07:00  INFO 31294 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-08-04T09:55:48.296+07:00  INFO 31294 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-08-04T09:55:48.296+07:00  INFO 31294 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-08-04T09:55:48.304+07:00  INFO 31294 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 18.347 seconds (process running for 18.631)
2025-08-04T09:55:52.163+07:00  INFO 31294 --- [qtp85102332-37] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0atst8taysnyk1jq3xp1fxbmxi0
2025-08-04T09:55:52.163+07:00  INFO 31294 --- [qtp85102332-39] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01vl55ljz5uxp9nw1jkab80tc61
2025-08-04T09:55:52.394+07:00  INFO 31294 --- [qtp85102332-37] n.d.module.session.ClientSessionManager  : Add a client session id = node0atst8taysnyk1jq3xp1fxbmxi0, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T09:55:52.394+07:00  INFO 31294 --- [qtp85102332-39] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T09:55:52.836+07:00  INFO 31294 --- [qtp85102332-39] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T09:55:52.841+07:00  INFO 31294 --- [qtp85102332-37] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T09:56:02.235+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T09:56:51.373+07:00  INFO 31294 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-08-04T09:56:51.405+07:00  INFO 31294 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T09:57:05.430+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T09:58:06.527+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T09:58:50.614+07:00  INFO 31294 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T09:58:50.621+07:00  INFO 31294 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T09:59:04.642+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T09:59:35.333+07:00  INFO 31294 --- [qtp85102332-41] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T09:59:35.334+07:00  INFO 31294 --- [qtp85102332-34] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T09:59:35.347+07:00  INFO 31294 --- [qtp85102332-34] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T09:59:35.349+07:00  INFO 31294 --- [qtp85102332-41] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:00:06.744+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T10:00:06.749+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-08-04T10:00:06.751+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T10:00:06.751+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-04T10:00:06.759+07:00  INFO 31294 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 04/08/2025@10:00:06+0700
2025-08-04T10:00:06.772+07:00  INFO 31294 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 04/08/2025@10:00:00+0700 to 04/08/2025@10:15:00+0700
2025-08-04T10:00:06.772+07:00  INFO 31294 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 04/08/2025@10:00:00+0700 to 04/08/2025@10:15:00+0700
2025-08-04T10:00:54.900+07:00  INFO 31294 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 0
2025-08-04T10:00:54.909+07:00  INFO 31294 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T10:01:03.931+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T10:02:06.029+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T10:02:55.158+07:00  INFO 31294 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 0
2025-08-04T10:02:55.171+07:00  INFO 31294 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T10:03:03.191+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T10:04:06.307+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T10:04:54.396+07:00  INFO 31294 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T10:04:54.400+07:00  INFO 31294 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T10:05:02.414+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T10:05:02.415+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T10:06:05.526+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T10:06:32.781+07:00  INFO 31294 --- [Scheduler-1390121014-1] n.d.m.session.AppHttpSessionListener     : The session node0atst8taysnyk1jq3xp1fxbmxi0 is destroyed.
2025-08-04T10:06:40.336+07:00  INFO 31294 --- [qtp85102332-35] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-08-04T10:06:54.630+07:00  INFO 31294 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-08-04T10:06:54.640+07:00  INFO 31294 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T10:07:06.655+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T10:07:43.346+07:00  INFO 31294 --- [qtp85102332-41] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:07:43.366+07:00  INFO 31294 --- [qtp85102332-41] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:07:43.379+07:00  INFO 31294 --- [qtp85102332-35] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:07:43.394+07:00  INFO 31294 --- [qtp85102332-35] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:08:04.766+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T10:08:19.342+07:00  INFO 31294 --- [qtp85102332-39] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:08:19.354+07:00  INFO 31294 --- [qtp85102332-39] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:08:19.376+07:00  INFO 31294 --- [qtp85102332-61] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:08:19.384+07:00  INFO 31294 --- [qtp85102332-61] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:08:53.885+07:00  INFO 31294 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 0
2025-08-04T10:08:53.901+07:00  INFO 31294 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T10:09:06.923+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T10:10:04.025+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T10:10:04.028+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T10:10:53.136+07:00  INFO 31294 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 3
2025-08-04T10:10:53.151+07:00  INFO 31294 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T10:11:06.167+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T10:12:03.273+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T10:12:52.359+07:00  INFO 31294 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T10:12:52.364+07:00  INFO 31294 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T10:13:06.389+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T10:13:42.382+07:00  INFO 31294 --- [qtp85102332-68] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:13:42.383+07:00  INFO 31294 --- [qtp85102332-63] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:13:42.392+07:00  INFO 31294 --- [qtp85102332-63] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:13:42.392+07:00  INFO 31294 --- [qtp85102332-68] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:13:47.146+07:00  INFO 31294 --- [qtp85102332-61] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:13:47.147+07:00  INFO 31294 --- [qtp85102332-41] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:13:47.161+07:00  INFO 31294 --- [qtp85102332-61] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:13:47.165+07:00  INFO 31294 --- [qtp85102332-41] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:14:02.500+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T10:14:12.282+07:00  INFO 31294 --- [qtp85102332-63] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:14:12.296+07:00  INFO 31294 --- [qtp85102332-63] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:14:12.313+07:00  INFO 31294 --- [qtp85102332-76] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:14:12.320+07:00  INFO 31294 --- [qtp85102332-76] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:14:51.622+07:00  INFO 31294 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-08-04T10:14:51.632+07:00  INFO 31294 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T10:15:05.657+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T10:15:05.660+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T10:15:05.660+07:00  INFO 31294 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 04/08/2025@10:15:05+0700
2025-08-04T10:15:05.685+07:00  INFO 31294 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 04/08/2025@10:15:00+0700 to 04/08/2025@10:30:00+0700
2025-08-04T10:15:05.685+07:00  INFO 31294 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 04/08/2025@10:15:00+0700 to 04/08/2025@10:30:00+0700
2025-08-04T10:15:05.686+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-04T10:16:06.786+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T10:16:18.258+07:00  INFO 31294 --- [qtp85102332-35] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:16:18.278+07:00  INFO 31294 --- [qtp85102332-35] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:16:18.290+07:00  INFO 31294 --- [qtp85102332-69] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:16:18.299+07:00  INFO 31294 --- [qtp85102332-69] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:16:50.219+07:00  INFO 31294 --- [qtp85102332-68] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:16:50.221+07:00  INFO 31294 --- [qtp85102332-61] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:16:50.233+07:00  INFO 31294 --- [qtp85102332-68] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:16:50.233+07:00  INFO 31294 --- [qtp85102332-61] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:16:50.870+07:00  INFO 31294 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-08-04T10:16:50.876+07:00  INFO 31294 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T10:17:04.898+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T10:17:19.753+07:00  INFO 31294 --- [qtp85102332-68] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:17:19.766+07:00  INFO 31294 --- [qtp85102332-68] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:17:20.445+07:00  INFO 31294 --- [qtp85102332-65] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:17:20.457+07:00  INFO 31294 --- [qtp85102332-65] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:18:07.003+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T10:18:55.176+07:00  INFO 31294 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-08-04T10:18:55.203+07:00  INFO 31294 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T10:19:04.220+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T10:20:06.345+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T10:20:06.346+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T10:20:34.997+07:00  INFO 31294 --- [qtp85102332-68] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:20:34.999+07:00  INFO 31294 --- [qtp85102332-41] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:20:35.009+07:00  INFO 31294 --- [qtp85102332-68] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:20:35.011+07:00  INFO 31294 --- [qtp85102332-41] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:20:46.209+07:00  INFO 31294 --- [qtp85102332-68] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:20:46.210+07:00  INFO 31294 --- [qtp85102332-63] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:20:46.218+07:00  INFO 31294 --- [qtp85102332-63] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:20:46.218+07:00  INFO 31294 --- [qtp85102332-68] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:20:54.434+07:00  INFO 31294 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T10:20:54.437+07:00  INFO 31294 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T10:21:03.460+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T10:22:06.571+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T10:22:08.814+07:00  INFO 31294 --- [qtp85102332-65] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:22:08.815+07:00  INFO 31294 --- [qtp85102332-63] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:22:08.827+07:00  INFO 31294 --- [qtp85102332-65] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:22:08.827+07:00  INFO 31294 --- [qtp85102332-63] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:22:19.864+07:00  INFO 31294 --- [qtp85102332-65] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:22:19.871+07:00  INFO 31294 --- [qtp85102332-65] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:22:19.886+07:00  INFO 31294 --- [qtp85102332-95] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:22:19.890+07:00  INFO 31294 --- [qtp85102332-95] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:22:54.661+07:00  INFO 31294 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-08-04T10:22:54.680+07:00  INFO 31294 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T10:23:02.695+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T10:24:05.791+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T10:24:54.901+07:00  INFO 31294 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-08-04T10:24:54.918+07:00  INFO 31294 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T10:25:06.935+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T10:25:06.939+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T10:25:51.470+07:00  INFO 31294 --- [qtp85102332-63] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:25:51.547+07:00  INFO 31294 --- [qtp85102332-63] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:25:51.547+07:00  INFO 31294 --- [qtp85102332-117] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:25:51.575+07:00  INFO 31294 --- [qtp85102332-117] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:25:59.525+07:00  INFO 31294 --- [qtp85102332-65] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:25:59.526+07:00  INFO 31294 --- [qtp85102332-35] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:25:59.681+07:00  INFO 31294 --- [qtp85102332-65] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:25:59.701+07:00  INFO 31294 --- [qtp85102332-35] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:26:05.098+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T10:26:49.931+07:00  INFO 31294 --- [qtp85102332-61] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:26:49.934+07:00  INFO 31294 --- [qtp85102332-65] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:26:49.949+07:00  INFO 31294 --- [qtp85102332-65] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:26:49.950+07:00  INFO 31294 --- [qtp85102332-61] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:26:54.200+07:00  INFO 31294 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 1
2025-08-04T10:26:54.207+07:00  INFO 31294 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T10:27:02.663+07:00  INFO 31294 --- [qtp85102332-94] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:27:02.664+07:00  INFO 31294 --- [qtp85102332-97] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:27:02.678+07:00  INFO 31294 --- [qtp85102332-97] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:27:02.685+07:00  INFO 31294 --- [qtp85102332-94] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:27:06.224+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T10:27:26.428+07:00  INFO 31294 --- [qtp85102332-95] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:27:26.429+07:00  INFO 31294 --- [qtp85102332-65] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:27:26.443+07:00  INFO 31294 --- [qtp85102332-95] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:27:26.443+07:00  INFO 31294 --- [qtp85102332-65] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:28:04.333+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T10:28:53.458+07:00  INFO 31294 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-08-04T10:28:53.465+07:00  INFO 31294 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T10:29:06.486+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T10:30:03.583+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-04T10:30:03.586+07:00  INFO 31294 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 04/08/2025@10:30:03+0700
2025-08-04T10:30:03.627+07:00  INFO 31294 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 04/08/2025@10:30:00+0700 to 04/08/2025@10:45:00+0700
2025-08-04T10:30:03.627+07:00  INFO 31294 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 04/08/2025@10:30:00+0700 to 04/08/2025@10:45:00+0700
2025-08-04T10:30:03.627+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T10:30:03.628+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T10:30:52.734+07:00  INFO 31294 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 0
2025-08-04T10:30:52.743+07:00  INFO 31294 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T10:31:06.769+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T10:32:02.863+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T10:32:51.972+07:00  INFO 31294 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T10:32:51.977+07:00  INFO 31294 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T10:33:06.003+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T10:34:02.105+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T10:34:39.598+07:00  INFO 31294 --- [qtp85102332-95] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:34:39.599+07:00  INFO 31294 --- [qtp85102332-61] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:34:39.618+07:00  INFO 31294 --- [qtp85102332-61] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:34:39.622+07:00  INFO 31294 --- [qtp85102332-95] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:34:51.210+07:00  INFO 31294 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 0
2025-08-04T10:34:51.243+07:00  INFO 31294 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T10:35:05.269+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T10:35:05.271+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T10:35:38.181+07:00  INFO 31294 --- [qtp85102332-61] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:35:38.184+07:00  INFO 31294 --- [qtp85102332-95] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:35:38.202+07:00  INFO 31294 --- [qtp85102332-95] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:35:38.202+07:00  INFO 31294 --- [qtp85102332-61] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:36:06.362+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T10:36:50.465+07:00  INFO 31294 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-08-04T10:36:50.471+07:00  INFO 31294 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T10:37:04.495+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T10:38:06.599+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T10:38:11.227+07:00  INFO 31294 --- [qtp85102332-140] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:38:11.238+07:00  INFO 31294 --- [qtp85102332-140] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:38:11.255+07:00  INFO 31294 --- [qtp85102332-168] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:38:11.265+07:00  INFO 31294 --- [qtp85102332-168] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:38:54.728+07:00  INFO 31294 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 0
2025-08-04T10:38:54.807+07:00  INFO 31294 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T10:39:03.829+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T10:39:13.098+07:00  INFO 31294 --- [qtp85102332-40] n.d.m.c.BotSendMessageBotHandler         : Process send message You have new message from CRM system !. delivered count = 0, delivered error count = 0, delivered abort count = 0
2025-08-04T10:39:13.099+07:00  INFO 31294 --- [qtp85102332-40] n.d.m.c.BotSendMessageBotHandler         : Process send message You have new message from CRM system !. delivered count = 0, delivered error count = 0, delivered abort count = 0
2025-08-04T10:39:13.796+07:00  WARN 31294 --- [ForkJoinPool.commonPool-worker-1] c.m.a.m.ConfidentialClientApplication    : [Correlation ID: 740f2fc2-52fa-4466-b42e-d467d40eba92] Execution of class com.microsoft.aad.msal4j.AcquireTokenSilentSupplier failed: Token not found in the cache
2025-08-04T10:39:14.100+07:00  INFO 31294 --- [qtp85102332-40] c.azure.identity.ClientSecretCredential  : Azure Identity => getToken() result for scopes [https://graph.microsoft.com/.default]: SUCCESS
2025-08-04T10:40:06.926+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T10:40:06.931+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T10:40:55.039+07:00  INFO 31294 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 1
2025-08-04T10:40:55.059+07:00  INFO 31294 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T10:41:03.082+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T10:42:06.186+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T10:42:55.295+07:00  INFO 31294 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 6, expire count 0
2025-08-04T10:42:55.299+07:00  INFO 31294 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T10:43:02.310+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T10:44:05.414+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T10:44:17.878+07:00  INFO 31294 --- [qtp85102332-140] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:44:17.887+07:00  INFO 31294 --- [qtp85102332-140] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:44:17.912+07:00  INFO 31294 --- [qtp85102332-148] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:44:17.924+07:00  INFO 31294 --- [qtp85102332-148] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:44:31.058+07:00  INFO 31294 --- [qtp85102332-168] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:44:31.099+07:00  INFO 31294 --- [qtp85102332-168] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:44:31.102+07:00  INFO 31294 --- [qtp85102332-116] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:44:31.107+07:00  INFO 31294 --- [qtp85102332-116] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:44:52.684+07:00  INFO 31294 --- [qtp85102332-148] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:44:52.685+07:00  INFO 31294 --- [qtp85102332-140] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:44:52.697+07:00  INFO 31294 --- [qtp85102332-148] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:44:52.698+07:00  INFO 31294 --- [qtp85102332-140] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:44:54.512+07:00  INFO 31294 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-08-04T10:44:54.520+07:00  INFO 31294 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T10:45:06.537+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T10:45:06.539+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T10:45:06.540+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-04T10:45:06.540+07:00  INFO 31294 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 04/08/2025@10:45:06+0700
2025-08-04T10:45:06.566+07:00  INFO 31294 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 04/08/2025@10:45:00+0700 to 04/08/2025@11:00:00+0700
2025-08-04T10:45:06.567+07:00  INFO 31294 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 04/08/2025@10:45:00+0700 to 04/08/2025@11:00:00+0700
2025-08-04T10:46:04.658+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T10:46:14.890+07:00  INFO 31294 --- [qtp85102332-140] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:46:14.892+07:00  INFO 31294 --- [qtp85102332-117] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:46:14.914+07:00  INFO 31294 --- [qtp85102332-140] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:46:14.914+07:00  INFO 31294 --- [qtp85102332-117] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:46:18.909+07:00  INFO 31294 --- [qtp85102332-116] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:46:18.916+07:00  INFO 31294 --- [qtp85102332-116] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:46:18.943+07:00  INFO 31294 --- [qtp85102332-40] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:46:18.963+07:00  INFO 31294 --- [qtp85102332-40] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:46:18.994+07:00 ERROR 31294 --- [qtp85102332-116] n.d.m.monitor.call.EndpointCallContext   : Start call with component AssetService, method searchAssetTasks, arguments
[ {
  "tenantId" : "default",
  "companyId" : 8,
  "companyParentId" : 4,
  "companyCode" : "beehph",
  "companyLabel" : "Bee HPH",
  "companyFullName" : "BEE LOGISTICS CORPORATION - HAI PHONG BRANCH OFFICE",
  "loginId" : "nhat.le",
  "accountId" : 2089,
  "token" : "2424ed7b945fe6b027853d7c785e04eb",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node01vl55ljz5uxp9nw1jkab80tc61",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : "Employee",
  "allowAccessCompanies" : {
    "bee" : {
      "companyId" : 4,
      "companyParentId" : 0,
      "companyCode" : "bee",
      "companyLabel" : "Bee Corp",
      "companyFullName" : null
    },
    "beescs" : {
      "companyId" : 12,
      "companyParentId" : 1,
      "companyCode" : "beescs",
      "companyLabel" : "BEESCS",
      "companyFullName" : null
    },
    "beehan" : {
      "companyId" : 16,
      "companyParentId" : 4,
      "companyCode" : "beehan",
      "companyLabel" : "Bee HAN",
      "companyFullName" : null
    },
    "beehph" : {
      "companyId" : 8,
      "companyParentId" : 4,
      "companyCode" : "beehph",
      "companyLabel" : "Bee HPH",
      "companyFullName" : null
    },
    "beehcm" : {
      "companyId" : 17,
      "companyParentId" : 4,
      "companyCode" : "beehcm",
      "companyLabel" : "Bee HCM",
      "companyFullName" : null
    },
    "beedad" : {
      "companyId" : 18,
      "companyParentId" : 4,
      "companyCode" : "beedad",
      "companyLabel" : "Bee DAD",
      "companyFullName" : null
    },
    "datatp" : {
      "companyId" : 10,
      "companyParentId" : 1,
      "companyCode" : "datatp",
      "companyLabel" : "DataTP Cloud",
      "companyFullName" : null
    }
  },
  "featurePermissions" : [ {
    "id" : 6989,
    "appId" : 2,
    "appModule" : "user",
    "appName" : "my-space",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 11332,
    "appId" : 68,
    "appModule" : "document",
    "appName" : "company-document",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 8423,
    "appId" : 15,
    "appModule" : "logistics",
    "appName" : "user-logistics-prices",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 983,
    "appId" : 5,
    "appModule" : "admin",
    "appName" : "admin-space",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 1302,
    "appId" : 26,
    "appModule" : "okr",
    "appName" : "okr",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 1356,
    "appId" : 10,
    "appModule" : "partner",
    "appName" : "partner",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 1576,
    "appId" : 13,
    "appModule" : "logistics",
    "appName" : "company-logistics-settings",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 5515,
    "appId" : 64,
    "appModule" : "job-tracking",
    "appName" : "job-tracking",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 1580,
    "appId" : 16,
    "appModule" : "logistics",
    "appName" : "user-logistics-sales",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 5031,
    "appId" : 27,
    "appModule" : "logistics",
    "appName" : "user-partner-logistics-crm",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 2139,
    "appId" : 18,
    "appModule" : "system",
    "appName" : "system",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 2758,
    "appId" : 19,
    "appModule" : "sample",
    "appName" : "reactjs-lib",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 2835,
    "appId" : 21,
    "appModule" : "tms",
    "appName" : "user-tms-app",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 3852,
    "appId" : 4,
    "appModule" : "communication",
    "appName" : "communication",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 4484,
    "appId" : 61,
    "appModule" : "tms",
    "appName" : "user-tms-ops",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 4487,
    "appId" : 62,
    "appModule" : "tms",
    "appName" : "user-tms-round-used",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 5726,
    "appId" : 9,
    "appModule" : "company",
    "appName" : "company-hr",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 11732,
    "appId" : 70,
    "appModule" : "hr",
    "appName" : "company-kpi",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 988,
    "appId" : 6,
    "appModule" : "company",
    "appName" : "company-space",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 12977,
    "appId" : 67,
    "appModule" : "tms",
    "appName" : "user-vehicle-trip",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 12978,
    "appId" : 25,
    "appModule" : "tms",
    "appName" : "user-vehicle-fleets",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 13064,
    "appId" : 7,
    "appModule" : "company",
    "appName" : "company-asset",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 14281,
    "appId" : 80,
    "appModule" : "logistics",
    "appName" : "company-logistics-sales",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 13102,
    "appId" : 79,
    "appModule" : "company",
    "appName" : "user-asset",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 14455,
    "appId" : 3,
    "appModule" : "project",
    "appName" : "project",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  } ],
  "attributes" : { },
  "clientId" : "default:nhat.le"
}, null, {
  "params" : {
    "entityRefType" : "asset_taskable_asset",
    "companyCode" : "beehph",
    "assetCompanyId" : 8,
    "companyId" : null
  },
  "filters" : [ {
    "name" : "search",
    "filterType" : "String",
    "filterValue" : "",
    "required" : true
  } ],
  "optionFilters" : [ {
    "name" : "storageState",
    "filterType" : "NotSet",
    "required" : true,
    "multiple" : true,
    "options" : [ "ACTIVE", "ARCHIVED" ],
    "selectOptions" : [ "ACTIVE" ]
  } ],
  "rangeFilters" : [ {
    "name" : "createdTime",
    "filterType" : "NotSet",
    "required" : true
  }, {
    "name" : "modifiedTime",
    "filterType" : "NotSet",
    "required" : true
  } ],
  "orderBy" : {
    "fields" : [ "modifiedTime" ],
    "selectFields" : [ ],
    "sort" : "DESC"
  },
  "maxReturn" : 5000
} ]
2025-08-04T10:46:18.997+07:00 ERROR 31294 --- [qtp85102332-116] n.d.m.monitor.call.EndpointCallContext   : ERROR: 

java.lang.reflect.InvocationTargetException: null
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:115)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:156)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:141)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.NullPointerException: Cannot invoke "net.datatp.module.data.db.entity.ICompany.getId()" because "company" is null
	at net.datatp.module.asset.TaskableAssetLogic.searchCompanyTaskableAssets(TaskableAssetLogic.java:364)
	at net.datatp.module.asset.TaskableAssetLogic.searchAssetTasks(TaskableAssetLogic.java:381)
	at net.datatp.module.asset.AssetService.searchAssetTasks(AssetService.java:152)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at net.datatp.module.asset.AssetService$$SpringCGLIB$$0.searchAssetTasks(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	... 98 common frames omitted

2025-08-04T10:46:19.006+07:00  INFO 31294 --- [qtp85102332-116] n.d.m.monitor.call.EndpointCallService   : Call fail, logic error. Endpoint AssetService/searchAssetTasks
2025-08-04T10:46:23.468+07:00  INFO 31294 --- [qtp85102332-116] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:46:23.471+07:00  INFO 31294 --- [qtp85102332-140] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:46:23.475+07:00  INFO 31294 --- [qtp85102332-116] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:46:23.482+07:00  INFO 31294 --- [qtp85102332-140] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:46:42.656+07:00  INFO 31294 --- [qtp85102332-94] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:46:42.657+07:00  INFO 31294 --- [qtp85102332-117] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:46:42.665+07:00  INFO 31294 --- [qtp85102332-94] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:46:42.667+07:00  INFO 31294 --- [qtp85102332-117] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:46:53.764+07:00  INFO 31294 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-08-04T10:46:53.775+07:00  INFO 31294 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T10:46:59.777+07:00  INFO 31294 --- [qtp85102332-117] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:46:59.779+07:00  INFO 31294 --- [qtp85102332-140] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:46:59.791+07:00  INFO 31294 --- [qtp85102332-140] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:46:59.791+07:00  INFO 31294 --- [qtp85102332-117] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:47:06.795+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T10:48:01.863+07:00  INFO 31294 --- [qtp85102332-168] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:48:01.867+07:00  INFO 31294 --- [qtp85102332-63] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:48:01.878+07:00  INFO 31294 --- [qtp85102332-63] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:48:01.878+07:00  INFO 31294 --- [qtp85102332-168] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:48:03.902+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T10:48:24.824+07:00  INFO 31294 --- [qtp85102332-116] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:48:24.825+07:00  INFO 31294 --- [qtp85102332-40] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:48:24.833+07:00  INFO 31294 --- [qtp85102332-40] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:48:24.833+07:00  INFO 31294 --- [qtp85102332-116] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:48:31.721+07:00  INFO 31294 --- [qtp85102332-95] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:48:31.723+07:00  INFO 31294 --- [qtp85102332-140] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:48:31.736+07:00  INFO 31294 --- [qtp85102332-95] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:48:31.736+07:00  INFO 31294 --- [qtp85102332-140] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:48:52.987+07:00  INFO 31294 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 6, expire count 0
2025-08-04T10:48:52.992+07:00  INFO 31294 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T10:49:06.007+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T10:49:17.505+07:00  INFO 31294 --- [qtp85102332-168] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:49:17.512+07:00  INFO 31294 --- [qtp85102332-168] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:49:17.551+07:00  INFO 31294 --- [qtp85102332-210] n.d.module.session.ClientSessionManager  : Add a client session id = node01vl55ljz5uxp9nw1jkab80tc61, token = 2424ed7b945fe6b027853d7c785e04eb
2025-08-04T10:49:17.556+07:00  INFO 31294 --- [qtp85102332-210] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T10:50:03.110+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T10:50:03.114+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T10:50:52.227+07:00  INFO 31294 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 6
2025-08-04T10:50:52.235+07:00  INFO 31294 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T10:51:06.254+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T10:52:02.354+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T10:52:51.455+07:00  INFO 31294 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-08-04T10:52:51.466+07:00  INFO 31294 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T10:53:05.488+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T10:53:15.372+07:00  INFO 31294 --- [qtp85102332-140] cloud.datatp.tms.TMSReportLogic          : tms-bill analysis route start...
2025-08-04T10:53:15.372+07:00  INFO 31294 --- [qtp85102332-168] cloud.datatp.tms.TMSReportLogic          : tms-bill analysis route start...
2025-08-04T10:53:15.607+07:00  INFO 31294 --- [qtp85102332-168] cloud.datatp.tms.TMSReportLogic          : tms-bill analysis route success!!!
2025-08-04T10:53:15.642+07:00  INFO 31294 --- [qtp85102332-140] cloud.datatp.tms.TMSReportLogic          : tms-bill analysis route success!!!
2025-08-04T10:54:06.573+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T10:54:50.675+07:00  INFO 31294 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 0
2025-08-04T10:54:50.686+07:00  INFO 31294 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T10:55:04.710+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T10:55:04.712+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T10:56:06.826+07:00  INFO 31294 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T10:56:54.977+07:00  INFO 31294 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 6, expire count 3
2025-08-04T10:56:54.991+07:00  INFO 31294 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T10:56:57.569+07:00  INFO 31294 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@1c89f3b{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-08-04T10:56:57.571+07:00  INFO 31294 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-08-04T10:56:57.571+07:00  INFO 31294 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-08-04T10:56:57.571+07:00  INFO 31294 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-08-04T10:56:57.571+07:00  INFO 31294 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-08-04T10:56:57.572+07:00  INFO 31294 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-08-04T10:56:57.572+07:00  INFO 31294 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-08-04T10:56:57.572+07:00  INFO 31294 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-08-04T10:56:57.572+07:00  INFO 31294 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-08-04T10:56:57.572+07:00  INFO 31294 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-08-04T10:56:57.572+07:00  INFO 31294 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-08-04T10:56:57.572+07:00  INFO 31294 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-08-04T10:56:57.572+07:00  INFO 31294 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-08-04T10:56:57.572+07:00  INFO 31294 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-08-04T10:56:57.572+07:00  INFO 31294 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-08-04T10:56:57.572+07:00  INFO 31294 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-08-04T10:56:57.607+07:00  INFO 31294 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T10:56:57.694+07:00  INFO 31294 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-08-04T10:56:57.703+07:00  INFO 31294 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-08-04T10:56:57.753+07:00  INFO 31294 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-04T10:56:57.762+07:00  INFO 31294 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-04T10:56:57.775+07:00  INFO 31294 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-04T10:56:57.778+07:00  INFO 31294 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-08-04T10:56:57.779+07:00  INFO 31294 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-08-04T10:56:57.779+07:00  INFO 31294 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-08-04T10:56:57.780+07:00  INFO 31294 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-08-04T10:56:57.780+07:00  INFO 31294 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-08-04T10:56:57.920+07:00  INFO 31294 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-08-04T10:56:57.921+07:00  INFO 31294 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-08-04T10:56:57.921+07:00  INFO 31294 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-08-04T10:56:57.921+07:00  INFO 31294 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-08-04T10:56:57.921+07:00  INFO 31294 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-08-04T10:56:57.928+07:00  INFO 31294 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@7da4c956{STOPPING}[12.0.15,sto=0]
2025-08-04T10:56:57.934+07:00  INFO 31294 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-04T10:56:57.937+07:00  INFO 31294 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@5068a2b1{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.5413374056200040543/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@3938e876{STOPPED}}
