2025-08-02T00:01:02.565+07:00  INFO 96310 --- [qtp1221993613-643] n.d.m.session.AppHttpSessionListener     : The session node01cozcpuaikesxv74r0mwq7bve12 is destroyed.
2025-08-02T00:01:02.593+07:00  INFO 96310 --- [qtp1221993613-643] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-08-02T00:07:00.380+07:00  INFO 96310 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-02T00:07:01.597+07:00  WARN 96310 --- [jdbc housekeeper] com.zaxxer.hikari.pool.HikariPool        : jdbc - Thread starvation or clock leap detected (housekeeper delta=40m26s728ms).
2025-08-02T00:07:01.615+07:00  WARN 96310 --- [rw housekeeper] com.zaxxer.hikari.pool.HikariPool        : rw - Thread starvation or clock leap detected (housekeeper delta=40m26s730ms).
2025-08-02T00:07:01.740+07:00  WARN 96310 --- [HikariPool-2 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Thread starvation or clock leap detected (housekeeper delta=40m26s730ms).
2025-08-02T00:07:01.760+07:00  WARN 96310 --- [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=40m26s730ms).
2025-08-02T00:07:01.760+07:00  WARN 96310 --- [rw housekeeper] com.zaxxer.hikari.pool.HikariPool        : rw - Thread starvation or clock leap detected (housekeeper delta=40m26s730ms).
2025-08-02T01:26:03.600+07:00  WARN 96310 --- [jdbc housekeeper] com.zaxxer.hikari.pool.HikariPool        : jdbc - Thread starvation or clock leap detected (housekeeper delta=1h19m2s2ms).
2025-08-02T01:26:03.619+07:00  WARN 96310 --- [rw housekeeper] com.zaxxer.hikari.pool.HikariPool        : rw - Thread starvation or clock leap detected (housekeeper delta=1h19m2s4ms).
2025-08-02T01:26:03.744+07:00  WARN 96310 --- [HikariPool-2 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Thread starvation or clock leap detected (housekeeper delta=1h19m2s4ms).
2025-08-02T01:26:03.764+07:00  WARN 96310 --- [rw housekeeper] com.zaxxer.hikari.pool.HikariPool        : rw - Thread starvation or clock leap detected (housekeeper delta=1h19m2s4ms).
2025-08-02T01:26:03.764+07:00  WARN 96310 --- [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1h19m2s4ms).
2025-08-02T02:24:30.739+07:00  INFO 96310 --- [qtp1221993613-749] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01sxrvqb0vysdgusrsh671pihy15
2025-08-02T02:24:30.739+07:00  INFO 96310 --- [qtp1221993613-699] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01dqpu3jjhn4z8trrygsfemddn14
2025-08-02T02:24:30.748+07:00  INFO 96310 --- [qtp1221993613-699] n.d.module.session.ClientSessionManager  : Add a client session id = node01dqpu3jjhn4z8trrygsfemddn14, token = 9e57ccec8e98a2e2c2d25e3766e4540f
2025-08-02T02:24:30.782+07:00  INFO 96310 --- [qtp1221993613-749] n.d.module.session.ClientSessionManager  : Add a client session id = node01sxrvqb0vysdgusrsh671pihy15, token = 9e57ccec8e98a2e2c2d25e3766e4540f
2025-08-02T02:24:30.838+07:00  INFO 96310 --- [qtp1221993613-699] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-02T02:24:30.862+07:00  INFO 96310 --- [qtp1221993613-749] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-02T02:42:12.752+07:00  INFO 96310 --- [qtp1221993613-748] n.d.m.session.AppHttpSessionListener     : The session node01sxrvqb0vysdgusrsh671pihy15 is destroyed.
2025-08-02T02:42:12.755+07:00  INFO 96310 --- [qtp1221993613-643] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-08-02T02:42:12.758+07:00  INFO 96310 --- [qtp1221993613-748] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-08-02T02:50:28.967+07:00  WARN 96310 --- [jdbc housekeeper] com.zaxxer.hikari.pool.HikariPool        : jdbc - Thread starvation or clock leap detected (housekeeper delta=1h24m25s368ms).
2025-08-02T02:50:28.984+07:00  WARN 96310 --- [rw housekeeper] com.zaxxer.hikari.pool.HikariPool        : rw - Thread starvation or clock leap detected (housekeeper delta=1h24m25s365ms).
2025-08-02T02:50:29.105+07:00  WARN 96310 --- [HikariPool-2 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Thread starvation or clock leap detected (housekeeper delta=1h24m25s361ms).
2025-08-02T02:50:29.129+07:00  WARN 96310 --- [rw housekeeper] com.zaxxer.hikari.pool.HikariPool        : rw - Thread starvation or clock leap detected (housekeeper delta=1h24m25s365ms).
2025-08-02T02:50:29.130+07:00  WARN 96310 --- [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1h24m25s366ms).
2025-08-02T02:50:30.930+07:00  INFO 96310 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-02T03:10:00.384+07:00  INFO 96310 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 10, expire count 10
2025-08-02T03:10:00.394+07:00  INFO 96310 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-02T03:41:09.834+07:00  WARN 96310 --- [jdbc housekeeper] com.zaxxer.hikari.pool.HikariPool        : jdbc - Thread starvation or clock leap detected (housekeeper delta=50m40s866ms).
2025-08-02T03:41:09.849+07:00  WARN 96310 --- [rw housekeeper] com.zaxxer.hikari.pool.HikariPool        : rw - Thread starvation or clock leap detected (housekeeper delta=50m40s865ms).
2025-08-02T03:41:09.971+07:00  WARN 96310 --- [HikariPool-2 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Thread starvation or clock leap detected (housekeeper delta=50m40s866ms).
2025-08-02T03:41:09.994+07:00  WARN 96310 --- [rw housekeeper] com.zaxxer.hikari.pool.HikariPool        : rw - Thread starvation or clock leap detected (housekeeper delta=50m40s865ms).
2025-08-02T03:41:09.994+07:00  WARN 96310 --- [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=50m40s864ms).
2025-08-02T03:57:26.794+07:00  INFO 96310 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-02T05:01:14.435+07:00  INFO 96310 --- [qtp1221993613-699] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01cfftgs8m7m80hmigy73kasvv17
2025-08-02T05:01:14.435+07:00  INFO 96310 --- [qtp1221993613-462] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0mjq21apg4dwfyij0cqgtoiop16
2025-08-02T05:01:14.458+07:00  INFO 96310 --- [qtp1221993613-699] n.d.module.session.ClientSessionManager  : Add a client session id = node01cfftgs8m7m80hmigy73kasvv17, token = 9e57ccec8e98a2e2c2d25e3766e4540f
2025-08-02T05:01:14.493+07:00  INFO 96310 --- [qtp1221993613-462] n.d.module.session.ClientSessionManager  : Add a client session id = node0mjq21apg4dwfyij0cqgtoiop16, token = 9e57ccec8e98a2e2c2d25e3766e4540f
2025-08-02T05:01:14.539+07:00  INFO 96310 --- [qtp1221993613-699] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-02T05:01:14.563+07:00  INFO 96310 --- [qtp1221993613-462] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-02T05:01:16.078+07:00  WARN 96310 --- [jdbc housekeeper] com.zaxxer.hikari.pool.HikariPool        : jdbc - Thread starvation or clock leap detected (housekeeper delta=1h20m6s245ms).
2025-08-02T05:01:16.091+07:00  WARN 96310 --- [rw housekeeper] com.zaxxer.hikari.pool.HikariPool        : rw - Thread starvation or clock leap detected (housekeeper delta=1h20m6s242ms).
2025-08-02T05:01:16.213+07:00  WARN 96310 --- [HikariPool-2 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Thread starvation or clock leap detected (housekeeper delta=1h20m6s242ms).
2025-08-02T05:01:16.236+07:00  WARN 96310 --- [rw housekeeper] com.zaxxer.hikari.pool.HikariPool        : rw - Thread starvation or clock leap detected (housekeeper delta=1h20m6s242ms).
2025-08-02T05:01:16.236+07:00  WARN 96310 --- [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1h20m6s242ms).
2025-08-02T05:42:51.255+07:00  INFO 96310 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-02T06:13:00.323+07:00  WARN 96310 --- [jdbc housekeeper] com.zaxxer.hikari.pool.HikariPool        : jdbc - Thread starvation or clock leap detected (housekeeper delta=1h11m44s245ms).
2025-08-02T06:13:00.336+07:00  WARN 96310 --- [rw housekeeper] com.zaxxer.hikari.pool.HikariPool        : rw - Thread starvation or clock leap detected (housekeeper delta=1h11m44s245ms).
2025-08-02T06:13:00.458+07:00  WARN 96310 --- [HikariPool-2 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Thread starvation or clock leap detected (housekeeper delta=1h11m44s245ms).
2025-08-02T06:13:00.482+07:00  WARN 96310 --- [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1h11m44s246ms).
2025-08-02T06:13:00.482+07:00  WARN 96310 --- [rw housekeeper] com.zaxxer.hikari.pool.HikariPool        : rw - Thread starvation or clock leap detected (housekeeper delta=1h11m44s246ms).
2025-08-02T06:13:02.731+07:00  INFO 96310 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-02T07:30:50.114+07:00  WARN 96310 --- [jdbc housekeeper] com.zaxxer.hikari.pool.HikariPool        : jdbc - Thread starvation or clock leap detected (housekeeper delta=1h17m49s791ms).
2025-08-02T07:30:50.125+07:00  WARN 96310 --- [rw housekeeper] com.zaxxer.hikari.pool.HikariPool        : rw - Thread starvation or clock leap detected (housekeeper delta=1h17m49s789ms).
2025-08-02T07:30:50.247+07:00  WARN 96310 --- [HikariPool-2 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Thread starvation or clock leap detected (housekeeper delta=1h17m49s789ms).
2025-08-02T07:30:50.270+07:00  WARN 96310 --- [rw housekeeper] com.zaxxer.hikari.pool.HikariPool        : rw - Thread starvation or clock leap detected (housekeeper delta=1h17m49s788ms).
2025-08-02T07:30:50.270+07:00  WARN 96310 --- [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1h17m49s788ms).
2025-08-02T07:30:51.392+07:00  INFO 96310 --- [qtp1221993613-643] n.d.m.session.AppHttpSessionListener     : The session node0mjq21apg4dwfyij0cqgtoiop16 is destroyed.
2025-08-02T07:30:51.403+07:00  INFO 96310 --- [qtp1221993613-643] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-08-02T08:05:45.074+07:00  INFO 96310 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-02T08:14:58.782+07:00  INFO 96310 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 6, expire count 6
2025-08-02T08:14:58.791+07:00  INFO 96310 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-02T08:47:20.343+07:00  WARN 96310 --- [jdbc housekeeper] com.zaxxer.hikari.pool.HikariPool        : jdbc - Thread starvation or clock leap detected (housekeeper delta=1h16m30s229ms).
2025-08-02T08:47:20.353+07:00  WARN 96310 --- [rw housekeeper] com.zaxxer.hikari.pool.HikariPool        : rw - Thread starvation or clock leap detected (housekeeper delta=1h16m30s228ms).
2025-08-02T08:47:20.475+07:00  WARN 96310 --- [HikariPool-2 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Thread starvation or clock leap detected (housekeeper delta=1h16m30s227ms).
2025-08-02T08:47:20.498+07:00  WARN 96310 --- [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1h16m30s228ms).
2025-08-02T08:47:20.498+07:00  WARN 96310 --- [rw housekeeper] com.zaxxer.hikari.pool.HikariPool        : rw - Thread starvation or clock leap detected (housekeeper delta=1h16m30s228ms).
2025-08-02T09:03:19.686+07:00  INFO 96310 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-02T10:03:47.759+07:00  WARN 96310 --- [jdbc housekeeper] com.zaxxer.hikari.pool.HikariPool        : jdbc - Thread starvation or clock leap detected (housekeeper delta=1h16m27s416ms).
2025-08-02T10:03:47.762+07:00  WARN 96310 --- [rw housekeeper] com.zaxxer.hikari.pool.HikariPool        : rw - Thread starvation or clock leap detected (housekeeper delta=1h16m27s409ms).
2025-08-02T10:03:47.889+07:00  WARN 96310 --- [HikariPool-2 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Thread starvation or clock leap detected (housekeeper delta=1h16m27s415ms).
2025-08-02T10:03:47.906+07:00  WARN 96310 --- [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1h16m27s408ms).
2025-08-02T10:03:47.911+07:00  WARN 96310 --- [rw housekeeper] com.zaxxer.hikari.pool.HikariPool        : rw - Thread starvation or clock leap detected (housekeeper delta=1h16m27s413ms).
2025-08-02T10:17:00.002+07:00  INFO 96310 --- [qtp1221993613-643] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node02o4o2q2y00g29s6a0ps5fdy018
2025-08-02T10:17:00.002+07:00  INFO 96310 --- [qtp1221993613-748] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0epb97871b92612muyxr5ft1yq19
2025-08-02T10:17:00.064+07:00  INFO 96310 --- [qtp1221993613-643] n.d.module.session.ClientSessionManager  : Add a client session id = node02o4o2q2y00g29s6a0ps5fdy018, token = 9e57ccec8e98a2e2c2d25e3766e4540f
2025-08-02T10:17:00.065+07:00  INFO 96310 --- [qtp1221993613-748] n.d.module.session.ClientSessionManager  : Add a client session id = node0epb97871b92612muyxr5ft1yq19, token = 9e57ccec8e98a2e2c2d25e3766e4540f
2025-08-02T10:17:00.317+07:00  INFO 96310 --- [qtp1221993613-643] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-02T10:17:00.362+07:00  INFO 96310 --- [qtp1221993613-748] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-02T11:03:33.418+07:00  INFO 96310 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-02T11:18:00.044+07:00  WARN 96310 --- [rw housekeeper] com.zaxxer.hikari.pool.HikariPool        : rw - Thread starvation or clock leap detected (housekeeper delta=1h14m12s282ms).
2025-08-02T11:18:00.044+07:00  WARN 96310 --- [jdbc housekeeper] com.zaxxer.hikari.pool.HikariPool        : jdbc - Thread starvation or clock leap detected (housekeeper delta=1h14m12s285ms).
2025-08-02T11:18:00.174+07:00  WARN 96310 --- [HikariPool-2 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Thread starvation or clock leap detected (housekeeper delta=1h14m12s285ms).
2025-08-02T11:18:00.191+07:00  WARN 96310 --- [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1h14m12s285ms).
2025-08-02T11:18:00.191+07:00  WARN 96310 --- [rw housekeeper] com.zaxxer.hikari.pool.HikariPool        : rw - Thread starvation or clock leap detected (housekeeper delta=1h14m12s280ms).
2025-08-02T11:35:33.992+07:00  INFO 96310 --- [qtp1221993613-750] n.d.m.session.AppHttpSessionListener     : The session node0epb97871b92612muyxr5ft1yq19 is destroyed.
2025-08-02T11:35:34.787+07:00  INFO 96310 --- [qtp1221993613-750] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint HRService/searchEmployees
2025-08-02T11:35:35.001+07:00  INFO 96310 --- [qtp1221993613-767] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-08-02T11:35:35.033+07:00  INFO 96310 --- [qtp1221993613-754] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-08-02T11:35:36.293+07:00  INFO 96310 --- [qtp1221993613-643] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node016edat023od7d14p58tgy4479n20
2025-08-02T11:35:36.293+07:00  INFO 96310 --- [qtp1221993613-557] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01ouo6a0nl21dm7c5il0j4rj2i21
2025-08-02T11:35:36.299+07:00  INFO 96310 --- [qtp1221993613-557] n.d.module.session.ClientSessionManager  : Add a client session id = node01ouo6a0nl21dm7c5il0j4rj2i21, token = 9e57ccec8e98a2e2c2d25e3766e4540f
2025-08-02T11:35:36.299+07:00  INFO 96310 --- [qtp1221993613-643] n.d.module.session.ClientSessionManager  : Add a client session id = node016edat023od7d14p58tgy4479n20, token = 9e57ccec8e98a2e2c2d25e3766e4540f
2025-08-02T11:35:36.364+07:00  INFO 96310 --- [qtp1221993613-643] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-02T11:35:36.392+07:00  INFO 96310 --- [qtp1221993613-557] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-02T11:35:41.564+07:00  INFO 96310 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-02T11:35:47.657+07:00  WARN 96310 --- [rw housekeeper] com.zaxxer.hikari.pool.HikariPool        : rw - Thread starvation or clock leap detected (housekeeper delta=17m47s613ms).
2025-08-02T11:35:47.664+07:00  WARN 96310 --- [jdbc housekeeper] com.zaxxer.hikari.pool.HikariPool        : jdbc - Thread starvation or clock leap detected (housekeeper delta=17m47s620ms).
2025-08-02T11:35:47.794+07:00  WARN 96310 --- [HikariPool-2 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Thread starvation or clock leap detected (housekeeper delta=17m47s620ms).
2025-08-02T11:35:47.811+07:00  WARN 96310 --- [rw housekeeper] com.zaxxer.hikari.pool.HikariPool        : rw - Thread starvation or clock leap detected (housekeeper delta=17m47s620ms).
2025-08-02T11:35:47.811+07:00  WARN 96310 --- [HikariPool-1 housekeeper] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=17m47s620ms).
2025-08-02T11:36:02.647+07:00  INFO 96310 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 6, expire count 0
2025-08-02T11:36:02.657+07:00  INFO 96310 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-02T11:36:02.658+07:00  INFO 96310 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-02T11:36:46.890+07:00  INFO 96310 --- [qtp1221993613-557] n.d.module.session.ClientSessionManager  : Add a client session id = node01ouo6a0nl21dm7c5il0j4rj2i21, token = 9e57ccec8e98a2e2c2d25e3766e4540f
2025-08-02T11:36:46.904+07:00  INFO 96310 --- [qtp1221993613-557] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-02T11:36:46.935+07:00  INFO 96310 --- [qtp1221993613-750] n.d.module.session.ClientSessionManager  : Add a client session id = node01ouo6a0nl21dm7c5il0j4rj2i21, token = 9e57ccec8e98a2e2c2d25e3766e4540f
2025-08-02T11:36:46.957+07:00  INFO 96310 --- [qtp1221993613-750] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-02T11:37:05.756+07:00  INFO 96310 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-02T11:38:01.883+07:00  INFO 96310 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-02T11:38:01.887+07:00  INFO 96310 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-02T11:38:06.894+07:00  INFO 96310 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-02T11:38:15.913+07:00  INFO 96310 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-02T11:39:05.003+07:00  INFO 96310 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-02T11:39:52.075+07:00  INFO 96310 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-02T11:39:52.076+07:00  INFO 96310 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 02/08/2025@11:39:52+0700
2025-08-02T11:39:52.101+07:00  INFO 96310 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 02/08/2025@11:30:00+0700 to 02/08/2025@11:45:00+0700
2025-08-02T11:39:52.102+07:00  INFO 96310 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 02/08/2025@11:30:00+0700 to 02/08/2025@11:45:00+0700
2025-08-02T11:40:06.140+07:00  INFO 96310 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 0
2025-08-02T11:40:06.147+07:00  INFO 96310 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-02T11:40:06.147+07:00  INFO 96310 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-02T11:40:06.148+07:00  INFO 96310 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-02T11:41:04.243+07:00  INFO 96310 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-02T11:42:06.371+07:00  INFO 96310 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-02T11:42:06.377+07:00  INFO 96310 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-02T11:42:06.378+07:00  INFO 96310 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-02T11:42:45.692+07:00  INFO 96310 --- [Scheduler-809533224-1] n.d.m.session.AppHttpSessionListener     : The session node016edat023od7d14p58tgy4479n20 is destroyed.
2025-08-02T11:42:45.694+07:00  INFO 96310 --- [Scheduler-809533224-1] n.d.m.session.AppHttpSessionListener     : The session node01cfftgs8m7m80hmigy73kasvv17 is destroyed.
2025-08-02T11:42:45.694+07:00  INFO 96310 --- [Scheduler-809533224-1] n.d.m.session.AppHttpSessionListener     : The session node01dqpu3jjhn4z8trrygsfemddn14 is destroyed.
2025-08-02T11:42:45.694+07:00  INFO 96310 --- [Scheduler-809533224-1] n.d.m.session.AppHttpSessionListener     : The session node0t9mjsz9wo0cl1gbmxmeuxzgs913 is destroyed.
2025-08-02T11:42:45.695+07:00  INFO 96310 --- [Scheduler-809533224-1] n.d.m.session.AppHttpSessionListener     : The session node02o4o2q2y00g29s6a0ps5fdy018 is destroyed.
2025-08-02T11:42:46.409+07:00  INFO 96310 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@58bb71b9{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-08-02T11:42:46.410+07:00  INFO 96310 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-08-02T11:42:46.410+07:00  INFO 96310 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-08-02T11:42:46.410+07:00  INFO 96310 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-08-02T11:42:46.410+07:00  INFO 96310 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-08-02T11:42:46.410+07:00  INFO 96310 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-08-02T11:42:46.410+07:00  INFO 96310 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-08-02T11:42:46.410+07:00  INFO 96310 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-08-02T11:42:46.410+07:00  INFO 96310 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-08-02T11:42:46.411+07:00  INFO 96310 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-08-02T11:42:46.411+07:00  INFO 96310 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-08-02T11:42:46.411+07:00  INFO 96310 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-08-02T11:42:46.411+07:00  INFO 96310 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-08-02T11:42:46.411+07:00  INFO 96310 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-08-02T11:42:46.411+07:00  INFO 96310 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-08-02T11:42:46.411+07:00  INFO 96310 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-08-02T11:42:46.427+07:00  INFO 96310 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-02T11:42:46.491+07:00  INFO 96310 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-08-02T11:42:46.496+07:00  INFO 96310 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-08-02T11:42:46.524+07:00  INFO 96310 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-02T11:42:46.526+07:00  INFO 96310 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-02T11:42:46.527+07:00  INFO 96310 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-02T11:42:46.527+07:00  INFO 96310 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-08-02T11:42:46.529+07:00  INFO 96310 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-08-02T11:42:46.529+07:00  INFO 96310 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-08-02T11:42:46.529+07:00  INFO 96310 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-08-02T11:42:46.529+07:00  INFO 96310 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-08-02T11:42:51.539+07:00  INFO 96310 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-08-02T11:42:51.540+07:00  INFO 96310 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-08-02T11:42:51.542+07:00  INFO 96310 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-08-02T11:42:51.542+07:00  INFO 96310 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-08-02T11:42:51.543+07:00  INFO 96310 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-08-02T11:42:51.547+07:00  INFO 96310 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@8fe054{STOPPING}[12.0.15,sto=0]
2025-08-02T11:42:51.559+07:00  INFO 96310 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-02T11:42:51.562+07:00  INFO 96310 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@7bd0e2e9{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.17364307854521027357/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@6ac49084{STOPPED}}
