2025-08-05T14:00:39.493+07:00  INFO 24921 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 24921 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-08-05T14:00:39.494+07:00  INFO 24921 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-08-05T14:00:40.210+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:00:40.274+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 61 ms. Found 22 JPA repository interfaces.
2025-08-05T14:00:40.295+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:00:40.296+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-08-05T14:00:40.296+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:00:40.306+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 9 JPA repository interfaces.
2025-08-05T14:00:40.307+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:00:40.346+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 38 ms. Found 3 JPA repository interfaces.
2025-08-05T14:00:40.346+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:00:40.350+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-08-05T14:00:40.361+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:00:40.366+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 1 JPA repository interface.
2025-08-05T14:00:40.377+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:00:40.381+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 5 JPA repository interfaces.
2025-08-05T14:00:40.385+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:00:40.387+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-08-05T14:00:40.387+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:00:40.388+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-08-05T14:00:40.392+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:00:40.398+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 10 JPA repository interfaces.
2025-08-05T14:00:40.403+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:00:40.405+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-08-05T14:00:40.405+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:00:40.409+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-08-05T14:00:40.410+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:00:40.418+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-08-05T14:00:40.418+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:00:40.421+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-08-05T14:00:40.421+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:00:40.421+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-08-05T14:00:40.421+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:00:40.423+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-08-05T14:00:40.423+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:00:40.432+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 7 JPA repository interfaces.
2025-08-05T14:00:40.432+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:00:40.434+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 2 JPA repository interfaces.
2025-08-05T14:00:40.434+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:00:40.435+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-08-05T14:00:40.435+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:00:40.448+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 12 ms. Found 19 JPA repository interfaces.
2025-08-05T14:00:40.457+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:00:40.463+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 8 JPA repository interfaces.
2025-08-05T14:00:40.464+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:00:40.466+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-08-05T14:00:40.466+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:00:40.471+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-08-05T14:00:40.471+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:00:40.476+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-08-05T14:00:40.477+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:00:40.481+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-08-05T14:00:40.481+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:00:40.484+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-08-05T14:00:40.484+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:00:40.489+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-08-05T14:00:40.489+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:00:40.497+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 14 JPA repository interfaces.
2025-08-05T14:00:40.497+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:00:40.509+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 11 ms. Found 20 JPA repository interfaces.
2025-08-05T14:00:40.509+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:00:40.510+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-08-05T14:00:40.514+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:00:40.515+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-08-05T14:00:40.515+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:00:40.522+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-08-05T14:00:40.524+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:00:40.560+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 36 ms. Found 69 JPA repository interfaces.
2025-08-05T14:00:40.560+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:00:40.562+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-08-05T14:00:40.565+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:00:40.568+07:00  INFO 24921 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-08-05T14:00:40.762+07:00  INFO 24921 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-08-05T14:00:40.766+07:00  INFO 24921 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-08-05T14:00:41.052+07:00  WARN 24921 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-08-05T14:00:41.258+07:00  INFO 24921 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-08-05T14:00:41.260+07:00  INFO 24921 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-08-05T14:00:41.271+07:00  INFO 24921 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-08-05T14:00:41.271+07:00  INFO 24921 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1669 ms
2025-08-05T14:00:41.329+07:00  WARN 24921 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-05T14:00:41.329+07:00  INFO 24921 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-08-05T14:00:41.415+07:00  INFO 24921 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@4daa53e0
2025-08-05T14:00:41.420+07:00  INFO 24921 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-08-05T14:00:41.425+07:00  WARN 24921 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-05T14:00:41.425+07:00  INFO 24921 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-08-05T14:00:41.429+07:00  INFO 24921 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@2806c788
2025-08-05T14:00:41.429+07:00  INFO 24921 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-08-05T14:00:41.430+07:00  WARN 24921 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-05T14:00:41.430+07:00  INFO 24921 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-05T14:00:41.994+07:00  INFO 24921 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@1a0c384d
2025-08-05T14:00:41.994+07:00  INFO 24921 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-05T14:00:41.994+07:00  WARN 24921 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-05T14:00:41.994+07:00  INFO 24921 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-08-05T14:00:42.007+07:00  INFO 24921 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@534f4a65
2025-08-05T14:00:42.018+07:00  INFO 24921 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-08-05T14:00:42.023+07:00  WARN 24921 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-05T14:00:42.023+07:00  INFO 24921 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-08-05T14:00:42.174+07:00  INFO 24921 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@13453610
2025-08-05T14:00:42.174+07:00  INFO 24921 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-08-05T14:00:42.175+07:00  INFO 24921 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '****************************************************', '***********************************************', '**********************************************'
2025-08-05T14:00:42.251+07:00  INFO 24921 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-08-05T14:00:42.254+07:00  INFO 24921 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@3619ecd{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.18009316869997297410/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@193e65d5{STARTED}}
2025-08-05T14:00:42.254+07:00  INFO 24921 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@3619ecd{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.18009316869997297410/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@193e65d5{STARTED}}
2025-08-05T14:00:42.255+07:00  INFO 24921 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@5eb054{STARTING}[12.0.15,sto=0] @3417ms
2025-08-05T14:00:42.308+07:00  INFO 24921 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-05T14:00:42.337+07:00  INFO 24921 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-08-05T14:00:42.354+07:00  INFO 24921 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-05T14:00:42.487+07:00  INFO 24921 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-05T14:00:42.520+07:00  WARN 24921 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-05T14:00:43.176+07:00  INFO 24921 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-05T14:00:43.185+07:00  INFO 24921 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@1aec9516] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-08-05T14:00:43.343+07:00  INFO 24921 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-05T14:00:43.594+07:00  INFO 24921 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.kpi", "net.datatp.module.graphapi", "net.datatp.module.wfms", "net.datatp.module.monitor.activity", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "cloud.datatp.module.fleet", "net.datatp.module.okr", "net.datatp.module.core.template", "cloud.datatp.module.pegasus.shipment", "cloud.datatp.gps", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "net.datatp.module.groovy", "cloud.datatp.tms", "net.datatp.module.company.tmpl", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "net.datatp.module.resource", "cloud.datatp.vendor", "net.datatp.module.company.hr", "net.datatp.module.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "net.datatp.module.core.print", "cloud.datatp.module.odoo", "net.datatp.module.chatbot", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.zalo", "net.datatp.module.accounting" ]
2025-08-05T14:00:43.596+07:00  INFO 24921 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-08-05T14:00:43.604+07:00  INFO 24921 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-05T14:00:43.605+07:00  INFO 24921 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-05T14:00:43.634+07:00  INFO 24921 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-05T14:00:43.640+07:00  WARN 24921 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-05T14:00:45.779+07:00  INFO 24921 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-05T14:00:45.780+07:00  INFO 24921 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@534b8fd3] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-08-05T14:00:45.962+07:00  WARN 24921 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-08-05T14:00:45.962+07:00  WARN 24921 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-08-05T14:00:45.970+07:00  WARN 24921 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-08-05T14:00:45.971+07:00  WARN 24921 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-08-05T14:00:45.985+07:00  WARN 24921 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-08-05T14:00:45.986+07:00  WARN 24921 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-08-05T14:00:46.568+07:00  INFO 24921 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-05T14:00:46.578+07:00  INFO 24921 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-05T14:00:46.581+07:00  INFO 24921 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-05T14:00:46.601+07:00  INFO 24921 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-05T14:00:46.606+07:00  WARN 24921 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-05T14:00:47.240+07:00  INFO 24921 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-05T14:00:47.240+07:00  INFO 24921 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@6915d84b] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-08-05T14:00:47.304+07:00  WARN 24921 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-08-05T14:00:47.304+07:00  WARN 24921 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-08-05T14:00:47.657+07:00  INFO 24921 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-05T14:00:47.688+07:00  INFO 24921 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-08-05T14:00:47.693+07:00  INFO 24921 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-08-05T14:00:47.693+07:00  INFO 24921 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-08-05T14:00:47.700+07:00  WARN 24921 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-08-05T14:00:47.851+07:00  INFO 24921 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-08-05T14:00:48.310+07:00  INFO 24921 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-08-05T14:00:48.313+07:00  INFO 24921 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-08-05T14:00:48.348+07:00  INFO 24921 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-08-05T14:00:48.384+07:00  INFO 24921 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-08-05T14:00:48.430+07:00  INFO 24921 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-08-05T14:00:48.459+07:00  INFO 24921 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-08-05T14:00:48.488+07:00  INFO 24921 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 12337892ms : this is harmless.
2025-08-05T14:00:48.499+07:00  INFO 24921 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-08-05T14:00:48.503+07:00  INFO 24921 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-08-05T14:00:48.514+07:00  INFO 24921 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 949692605ms : this is harmless.
2025-08-05T14:00:48.516+07:00  INFO 24921 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-08-05T14:00:48.529+07:00  INFO 24921 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-08-05T14:00:48.530+07:00  INFO 24921 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-08-05T14:00:50.782+07:00  INFO 24921 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-08-05T14:00:50.782+07:00  INFO 24921 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-08-05T14:00:50.782+07:00  WARN 24921 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-08-05T14:00:50.910+07:00  INFO 24921 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 05/08/2025@14:00:00+0700 to 05/08/2025@14:15:00+0700
2025-08-05T14:00:50.910+07:00  INFO 24921 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 05/08/2025@14:00:00+0700 to 05/08/2025@14:15:00+0700
2025-08-05T14:00:51.392+07:00  INFO 24921 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-08-05T14:00:51.392+07:00  INFO 24921 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-08-05T14:00:51.392+07:00  WARN 24921 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-08-05T14:00:51.627+07:00  INFO 24921 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-08-05T14:00:51.628+07:00  INFO 24921 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-08-05T14:00:51.628+07:00  INFO 24921 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-08-05T14:00:51.628+07:00  INFO 24921 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-08-05T14:00:51.628+07:00  INFO 24921 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-08-05T14:00:53.474+07:00  WARN 24921 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: c2e3f3e5-4486-40de-96b4-45e17406718f

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-05T14:00:53.477+07:00  INFO 24921 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-05T14:00:53.866+07:00  INFO 24921 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-08-05T14:00:53.866+07:00  INFO 24921 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-08-05T14:00:53.867+07:00  INFO 24921 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-08-05T14:00:53.867+07:00  INFO 24921 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-08-05T14:00:53.867+07:00  INFO 24921 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-08-05T14:00:53.867+07:00  INFO 24921 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-08-05T14:00:53.867+07:00  INFO 24921 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-08-05T14:00:53.867+07:00  INFO 24921 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-08-05T14:00:53.867+07:00  INFO 24921 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-08-05T14:00:53.867+07:00  INFO 24921 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-08-05T14:00:53.867+07:00  INFO 24921 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-08-05T14:00:53.867+07:00  INFO 24921 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-08-05T14:00:53.869+07:00  INFO 24921 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-08-05T14:00:53.870+07:00  INFO 24921 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-08-05T14:00:53.870+07:00  INFO 24921 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-08-05T14:00:53.936+07:00  INFO 24921 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-05T14:00:53.936+07:00  INFO 24921 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-05T14:00:53.937+07:00  INFO 24921 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-08-05T14:00:53.947+07:00  INFO 24921 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@3fdfdf1e{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-08-05T14:00:53.948+07:00  INFO 24921 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-08-05T14:00:53.950+07:00  INFO 24921 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-08-05T14:00:53.974+07:00  INFO 24921 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-08-05T14:00:53.974+07:00  INFO 24921 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-08-05T14:00:53.980+07:00  INFO 24921 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 14.832 seconds (process running for 15.142)
2025-08-05T14:01:06.921+07:00  INFO 24921 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T14:01:10.795+07:00  INFO 24921 --- [qtp1595278145-65] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01da684o0y0g6j1o4wh2r4tt96j1
2025-08-05T14:01:10.795+07:00  INFO 24921 --- [qtp1595278145-62] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01rf2lxhlqlsxu1dybjsjbvd8k00
2025-08-05T14:01:10.976+07:00  INFO 24921 --- [qtp1595278145-62] n.d.module.session.ClientSessionManager  : Add a client session id = node01rf2lxhlqlsxu1dybjsjbvd8k00, token = 72e7a9fa5137975187a0308d6ada08b9
2025-08-05T14:01:10.980+07:00  INFO 24921 --- [qtp1595278145-65] n.d.module.session.ClientSessionManager  : Add a client session id = node01da684o0y0g6j1o4wh2r4tt96j1, token = 72e7a9fa5137975187a0308d6ada08b9
2025-08-05T14:01:11.228+07:00  INFO 24921 --- [qtp1595278145-36] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0xk4un171qlns1t391qtog21ia2
2025-08-05T14:01:11.228+07:00  INFO 24921 --- [qtp1595278145-34] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0qbna5ps75z5v4kyes1xp1ds3
2025-08-05T14:01:11.280+07:00  INFO 24921 --- [qtp1595278145-36] n.d.module.session.ClientSessionManager  : Add a client session id = node0xk4un171qlns1t391qtog21ia2, token = c2199c7247b48ac51324a7c3dfccd5a9
2025-08-05T14:01:11.281+07:00  INFO 24921 --- [qtp1595278145-34] n.d.module.session.ClientSessionManager  : Add a client session id = node0qbna5ps75z5v4kyes1xp1ds3, token = c2199c7247b48ac51324a7c3dfccd5a9
2025-08-05T14:01:11.446+07:00  INFO 24921 --- [qtp1595278145-65] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-05T14:01:11.452+07:00  INFO 24921 --- [qtp1595278145-34] n.d.m.c.a.CompanyAuthenticationService   : User elaine.vnsgn is logged in successfully system
2025-08-05T14:01:11.488+07:00  INFO 24921 --- [qtp1595278145-62] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-05T14:01:11.647+07:00  INFO 24921 --- [qtp1595278145-36] n.d.m.c.a.CompanyAuthenticationService   : User elaine.vnsgn is logged in successfully system
2025-08-05T14:01:25.229+07:00  INFO 24921 --- [qtp1595278145-60] n.d.module.session.ClientSessionManager  : Add a client session id = node01rf2lxhlqlsxu1dybjsjbvd8k00, token = 72e7a9fa5137975187a0308d6ada08b9
2025-08-05T14:01:25.245+07:00  INFO 24921 --- [qtp1595278145-60] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-05T14:01:25.252+07:00  INFO 24921 --- [qtp1595278145-68] n.d.module.session.ClientSessionManager  : Add a client session id = node01rf2lxhlqlsxu1dybjsjbvd8k00, token = 72e7a9fa5137975187a0308d6ada08b9
2025-08-05T14:01:25.261+07:00  INFO 24921 --- [qtp1595278145-40] n.d.module.session.ClientSessionManager  : Add a client session id = node0xk4un171qlns1t391qtog21ia2, token = c2199c7247b48ac51324a7c3dfccd5a9
2025-08-05T14:01:25.263+07:00  INFO 24921 --- [qtp1595278145-68] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-05T14:01:25.274+07:00  INFO 24921 --- [qtp1595278145-40] n.d.m.c.a.CompanyAuthenticationService   : User elaine.vnsgn is logged in successfully system
2025-08-05T14:01:25.288+07:00  INFO 24921 --- [qtp1595278145-37] n.d.module.session.ClientSessionManager  : Add a client session id = node0xk4un171qlns1t391qtog21ia2, token = c2199c7247b48ac51324a7c3dfccd5a9
2025-08-05T14:01:25.312+07:00  INFO 24921 --- [qtp1595278145-37] n.d.m.c.a.CompanyAuthenticationService   : User elaine.vnsgn is logged in successfully system
2025-08-05T14:01:52.080+07:00  INFO 24921 --- [qtp1595278145-68] n.d.module.session.ClientSessionManager  : Add a client session id = node01rf2lxhlqlsxu1dybjsjbvd8k00, token = 72e7a9fa5137975187a0308d6ada08b9
2025-08-05T14:01:52.088+07:00  INFO 24921 --- [qtp1595278145-68] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-05T14:01:52.099+07:00  INFO 24921 --- [qtp1595278145-76] n.d.module.session.ClientSessionManager  : Add a client session id = node01rf2lxhlqlsxu1dybjsjbvd8k00, token = 72e7a9fa5137975187a0308d6ada08b9
2025-08-05T14:01:52.104+07:00  INFO 24921 --- [qtp1595278145-76] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-05T14:01:52.226+07:00  INFO 24921 --- [qtp1595278145-62] n.d.module.session.ClientSessionManager  : Add a client session id = node0xk4un171qlns1t391qtog21ia2, token = c2199c7247b48ac51324a7c3dfccd5a9
2025-08-05T14:01:52.233+07:00  INFO 24921 --- [qtp1595278145-62] n.d.m.c.a.CompanyAuthenticationService   : User elaine.vnsgn is logged in successfully system
2025-08-05T14:01:52.315+07:00  INFO 24921 --- [qtp1595278145-67] n.d.module.session.ClientSessionManager  : Add a client session id = node0xk4un171qlns1t391qtog21ia2, token = c2199c7247b48ac51324a7c3dfccd5a9
2025-08-05T14:01:52.319+07:00  INFO 24921 --- [qtp1595278145-67] n.d.m.c.a.CompanyAuthenticationService   : User elaine.vnsgn is logged in successfully system
2025-08-05T14:01:57.067+07:00  INFO 24921 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-05T14:01:57.087+07:00  INFO 24921 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T14:02:04.105+07:00  INFO 24921 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T14:03:06.195+07:00  INFO 24921 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T14:03:19.233+07:00  INFO 24921 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@3fdfdf1e{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-08-05T14:03:19.234+07:00  INFO 24921 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-08-05T14:03:19.234+07:00  INFO 24921 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-08-05T14:03:19.234+07:00  INFO 24921 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-08-05T14:03:19.234+07:00  INFO 24921 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-08-05T14:03:19.234+07:00  INFO 24921 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-08-05T14:03:19.234+07:00  INFO 24921 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-08-05T14:03:19.235+07:00  INFO 24921 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-08-05T14:03:19.235+07:00  INFO 24921 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-08-05T14:03:19.235+07:00  INFO 24921 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-08-05T14:03:19.235+07:00  INFO 24921 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-08-05T14:03:19.235+07:00  INFO 24921 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-08-05T14:03:19.235+07:00  INFO 24921 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-08-05T14:03:19.235+07:00  INFO 24921 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-08-05T14:03:19.235+07:00  INFO 24921 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-08-05T14:03:19.235+07:00  INFO 24921 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-08-05T14:03:19.249+07:00  INFO 24921 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T14:03:19.345+07:00  INFO 24921 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-08-05T14:03:19.349+07:00  INFO 24921 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-08-05T14:03:19.367+07:00  INFO 24921 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-05T14:03:19.368+07:00  INFO 24921 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-05T14:03:19.375+07:00  INFO 24921 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-05T14:03:19.375+07:00  INFO 24921 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-08-05T14:03:19.377+07:00  INFO 24921 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-08-05T14:03:19.378+07:00  INFO 24921 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-08-05T14:03:19.378+07:00  INFO 24921 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-08-05T14:03:19.378+07:00  INFO 24921 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-08-05T14:03:19.550+07:00  INFO 24921 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-08-05T14:03:19.551+07:00  INFO 24921 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-08-05T14:03:19.551+07:00  INFO 24921 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-08-05T14:03:19.551+07:00  INFO 24921 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-08-05T14:03:19.552+07:00  INFO 24921 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-08-05T14:03:19.554+07:00  INFO 24921 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@5eb054{STOPPING}[12.0.15,sto=0]
2025-08-05T14:03:19.559+07:00  INFO 24921 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-05T14:03:19.560+07:00  INFO 24921 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@3619ecd{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.18009316869997297410/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@193e65d5{STOPPED}}
