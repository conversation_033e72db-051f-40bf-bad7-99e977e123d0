2025-08-01T10:50:43.927+07:00  INFO 61422 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 61422 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-08-01T10:50:43.928+07:00  INFO 61422 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-08-01T10:50:44.678+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:50:44.743+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 61 ms. Found 22 JPA repository interfaces.
2025-08-01T10:50:44.752+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:50:44.754+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-08-01T10:50:44.754+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:50:44.761+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 9 JPA repository interfaces.
2025-08-01T10:50:44.762+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:50:44.799+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 36 ms. Found 3 JPA repository interfaces.
2025-08-01T10:50:44.799+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:50:44.803+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-08-01T10:50:44.814+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:50:44.819+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 1 JPA repository interface.
2025-08-01T10:50:44.828+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:50:44.833+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 5 JPA repository interfaces.
2025-08-01T10:50:44.836+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:50:44.838+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-08-01T10:50:44.839+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:50:44.839+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-08-01T10:50:44.843+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:50:44.850+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 10 JPA repository interfaces.
2025-08-01T10:50:44.854+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:50:44.856+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-08-01T10:50:44.856+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:50:44.860+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-08-01T10:50:44.861+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:50:44.868+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-08-01T10:50:44.868+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:50:44.871+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-08-01T10:50:44.871+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:50:44.871+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-08-01T10:50:44.872+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:50:44.872+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-08-01T10:50:44.873+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:50:44.876+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-08-01T10:50:44.877+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:50:44.878+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-08-01T10:50:44.878+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:50:44.878+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-08-01T10:50:44.878+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:50:44.889+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 19 JPA repository interfaces.
2025-08-01T10:50:44.899+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:50:44.905+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 8 JPA repository interfaces.
2025-08-01T10:50:44.906+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:50:44.908+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-08-01T10:50:44.908+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:50:44.913+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-08-01T10:50:44.913+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:50:44.918+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-08-01T10:50:44.918+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:50:44.922+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-08-01T10:50:44.922+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:50:44.925+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-08-01T10:50:44.925+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:50:44.929+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-08-01T10:50:44.930+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:50:44.939+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 14 JPA repository interfaces.
2025-08-01T10:50:44.939+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:50:44.950+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 11 ms. Found 20 JPA repository interfaces.
2025-08-01T10:50:44.951+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:50:44.952+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-08-01T10:50:44.957+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:50:44.958+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-08-01T10:50:44.958+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:50:44.966+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-08-01T10:50:44.968+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:50:45.009+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 40 ms. Found 69 JPA repository interfaces.
2025-08-01T10:50:45.009+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:50:45.010+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-08-01T10:50:45.015+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:50:45.018+07:00  INFO 61422 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-08-01T10:50:45.199+07:00  INFO 61422 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-08-01T10:50:45.202+07:00  INFO 61422 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-08-01T10:50:45.470+07:00  WARN 61422 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-08-01T10:50:45.673+07:00  INFO 61422 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-08-01T10:50:45.677+07:00  INFO 61422 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-08-01T10:50:45.694+07:00  INFO 61422 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-08-01T10:50:45.694+07:00  INFO 61422 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1641 ms
2025-08-01T10:50:45.749+07:00  WARN 61422 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-01T10:50:45.749+07:00  INFO 61422 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-08-01T10:50:45.845+07:00  INFO 61422 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@4ff7de80
2025-08-01T10:50:45.846+07:00  INFO 61422 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-08-01T10:50:45.851+07:00  WARN 61422 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-01T10:50:45.851+07:00  INFO 61422 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-08-01T10:50:45.856+07:00  INFO 61422 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@520b547c
2025-08-01T10:50:45.856+07:00  INFO 61422 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-08-01T10:50:45.857+07:00  WARN 61422 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-01T10:50:45.857+07:00  INFO 61422 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-01T10:50:46.264+07:00  INFO 61422 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@429ad94a
2025-08-01T10:50:46.264+07:00  INFO 61422 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-01T10:50:46.264+07:00  WARN 61422 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-01T10:50:46.264+07:00  INFO 61422 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-08-01T10:50:46.272+07:00  INFO 61422 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@12e25b4b
2025-08-01T10:50:46.272+07:00  INFO 61422 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-08-01T10:50:46.272+07:00  WARN 61422 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-01T10:50:46.272+07:00  INFO 61422 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-08-01T10:50:46.278+07:00  INFO 61422 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@760dc63
2025-08-01T10:50:46.278+07:00  INFO 61422 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-08-01T10:50:46.278+07:00  INFO 61422 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '****************************************************', '***********************************************', '**********************************************'
2025-08-01T10:50:46.329+07:00  INFO 61422 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-08-01T10:50:46.331+07:00  INFO 61422 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@283a5c2b{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.15421219078323286818/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@6701fe26{STARTED}}
2025-08-01T10:50:46.332+07:00  INFO 61422 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@283a5c2b{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.15421219078323286818/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@6701fe26{STARTED}}
2025-08-01T10:50:46.333+07:00  INFO 61422 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@8fe054{STARTING}[12.0.15,sto=0] @2986ms
2025-08-01T10:50:46.389+07:00  INFO 61422 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-01T10:50:46.418+07:00  INFO 61422 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-08-01T10:50:46.433+07:00  INFO 61422 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-01T10:50:46.559+07:00  INFO 61422 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-01T10:50:46.581+07:00  WARN 61422 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-01T10:50:47.192+07:00  INFO 61422 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-01T10:50:47.201+07:00  INFO 61422 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@7d76036a] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-08-01T10:50:47.313+07:00  INFO 61422 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-01T10:50:47.495+07:00  INFO 61422 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.graphapi", "net.datatp.module.kpi", "net.datatp.module.wfms", "net.datatp.module.monitor.activity", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "cloud.datatp.module.fleet", "net.datatp.module.okr", "net.datatp.module.core.template", "cloud.datatp.gps", "cloud.datatp.module.pegasus.shipment", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "cloud.datatp.tms", "net.datatp.module.company.tmpl", "net.datatp.module.groovy", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "cloud.datatp.vendor", "net.datatp.module.resource", "net.datatp.module.company.hr", "net.datatp.module.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "net.datatp.module.core.print", "net.datatp.module.chatbot", "cloud.datatp.module.odoo", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.accounting", "net.datatp.module.zalo" ]
2025-08-01T10:50:47.497+07:00  INFO 61422 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-08-01T10:50:47.503+07:00  INFO 61422 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-01T10:50:47.504+07:00  INFO 61422 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-01T10:50:47.530+07:00  INFO 61422 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-01T10:50:47.535+07:00  WARN 61422 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-01T10:50:49.495+07:00  INFO 61422 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-01T10:50:49.496+07:00  INFO 61422 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@6e045480] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-08-01T10:50:49.694+07:00  WARN 61422 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-08-01T10:50:49.697+07:00  WARN 61422 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-08-01T10:50:49.711+07:00  WARN 61422 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-08-01T10:50:49.711+07:00  WARN 61422 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-08-01T10:50:49.725+07:00  WARN 61422 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-08-01T10:50:49.725+07:00  WARN 61422 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-08-01T10:50:50.105+07:00  INFO 61422 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-01T10:50:50.110+07:00  INFO 61422 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-01T10:50:50.112+07:00  INFO 61422 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-01T10:50:50.126+07:00  INFO 61422 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-01T10:50:50.128+07:00  WARN 61422 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-01T10:50:50.678+07:00  INFO 61422 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-01T10:50:50.679+07:00  INFO 61422 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@9d1e03a] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-08-01T10:50:50.740+07:00  WARN 61422 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-08-01T10:50:50.740+07:00  WARN 61422 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-08-01T10:50:51.148+07:00  INFO 61422 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-01T10:50:51.181+07:00  INFO 61422 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-08-01T10:50:51.186+07:00  INFO 61422 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-08-01T10:50:51.186+07:00  INFO 61422 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-08-01T10:50:51.192+07:00  WARN 61422 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-08-01T10:50:51.320+07:00  INFO 61422 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-08-01T10:50:51.792+07:00  INFO 61422 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-08-01T10:50:51.795+07:00  INFO 61422 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-08-01T10:50:51.830+07:00  INFO 61422 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-08-01T10:50:51.868+07:00  INFO 61422 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-08-01T10:50:51.881+07:00  INFO 61422 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-08-01T10:50:51.915+07:00  INFO 61422 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-08-01T10:50:51.940+07:00  INFO 61422 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 37354ms : this is harmless.
2025-08-01T10:50:51.957+07:00  INFO 61422 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-08-01T10:50:51.961+07:00  INFO 61422 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-08-01T10:50:51.978+07:00  INFO 61422 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 592698250ms : this is harmless.
2025-08-01T10:50:51.980+07:00  INFO 61422 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-08-01T10:50:51.996+07:00  INFO 61422 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-08-01T10:50:51.997+07:00  INFO 61422 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-08-01T10:50:54.044+07:00  INFO 61422 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-08-01T10:50:54.044+07:00  INFO 61422 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-08-01T10:50:54.045+07:00  WARN 61422 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-08-01T10:50:54.253+07:00  INFO 61422 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-08-01T10:50:54.253+07:00  INFO 61422 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-08-01T10:50:54.254+07:00  WARN 61422 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-08-01T10:50:54.499+07:00  INFO 61422 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-08-01T10:50:54.499+07:00  INFO 61422 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-08-01T10:50:54.499+07:00  INFO 61422 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-08-01T10:50:54.499+07:00  INFO 61422 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-08-01T10:50:54.499+07:00  INFO 61422 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-08-01T10:50:54.962+07:00  INFO 61422 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 01/08/2025@10:45:00+0700 to 01/08/2025@11:00:00+0700
2025-08-01T10:50:54.962+07:00  INFO 61422 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 01/08/2025@10:45:00+0700 to 01/08/2025@11:00:00+0700
2025-08-01T10:50:56.313+07:00  WARN 61422 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: b82d900d-39ff-44d0-adf9-fa9353fcbe9f

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01T10:50:56.316+07:00  INFO 61422 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01T10:50:56.620+07:00  INFO 61422 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-08-01T10:50:56.620+07:00  INFO 61422 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-08-01T10:50:56.620+07:00  INFO 61422 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-08-01T10:50:56.620+07:00  INFO 61422 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-08-01T10:50:56.620+07:00  INFO 61422 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-08-01T10:50:56.620+07:00  INFO 61422 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-08-01T10:50:56.620+07:00  INFO 61422 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-08-01T10:50:56.620+07:00  INFO 61422 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-08-01T10:50:56.620+07:00  INFO 61422 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-08-01T10:50:56.620+07:00  INFO 61422 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-08-01T10:50:56.620+07:00  INFO 61422 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-08-01T10:50:56.620+07:00  INFO 61422 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-08-01T10:50:56.623+07:00  INFO 61422 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-08-01T10:50:56.623+07:00  INFO 61422 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-08-01T10:50:56.623+07:00  INFO 61422 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-08-01T10:50:56.636+07:00  INFO 61422 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01T10:50:56.637+07:00  INFO 61422 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-01T10:50:56.638+07:00  INFO 61422 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-08-01T10:50:56.646+07:00  INFO 61422 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@71142a7c{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-08-01T10:50:56.646+07:00  INFO 61422 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-08-01T10:50:56.647+07:00  INFO 61422 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-08-01T10:50:56.677+07:00  INFO 61422 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-08-01T10:50:56.677+07:00  INFO 61422 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-08-01T10:50:56.683+07:00  INFO 61422 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 13.044 seconds (process running for 13.336)
2025-08-01T10:51:01.787+07:00  INFO 61422 --- [qtp1221993613-34] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node015tvwjj683uhcppz63qjnp6ku0
2025-08-01T10:51:01.787+07:00  INFO 61422 --- [qtp1221993613-39] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0ffljxh2oknj83o3ovl8yd2g91
2025-08-01T10:51:01.982+07:00  INFO 61422 --- [qtp1221993613-39] n.d.module.session.ClientSessionManager  : Add a client session id = node0ffljxh2oknj83o3ovl8yd2g91, token = 9e57ccec8e98a2e2c2d25e3766e4540f
2025-08-01T10:51:01.983+07:00  INFO 61422 --- [qtp1221993613-34] n.d.module.session.ClientSessionManager  : Add a client session id = node015tvwjj683uhcppz63qjnp6ku0, token = 9e57ccec8e98a2e2c2d25e3766e4540f
2025-08-01T10:51:02.457+07:00  INFO 61422 --- [qtp1221993613-39] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-01T10:51:02.457+07:00  INFO 61422 --- [qtp1221993613-34] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-01T10:51:03.640+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T10:51:13.881+07:00 ERROR 61422 --- [qtp1221993613-41] n.datatp.lib.executable.ExecutableUnit   : Error: 

java.lang.RuntimeException: Invalid date
	at net.datatp.util.text.DateUtil.parseCompactDateTime(DateUtil.java:111)
	at net.datatp.module.data.db.ExecutableSqlBuilder.FILTER_BY_RANGE(ExecutableSqlBuilder.java:194)
	at net.datatp.module.data.db.ExecutableSqlBuilder.AND_FILTER_BY_RANGE(ExecutableSqlBuilder.java:205)
	at org.codehaus.groovy.vmplugin.v8.IndyInterface.fromCache(IndyInterface.java:321)
	at cloud.datatp.jobtracking.groovy.JobTrackingSql$JobTrackingReport.execute(JobTrackingSql.groovy:163)
	at net.datatp.lib.executable.ExecutableUnit.doExecute(ExecutableUnit.java:30)
	at net.datatp.lib.executable.Executor.execute(Executor.java:34)
	at net.datatp.module.service.ExecutableUnitManager.execute(ExecutableUnitManager.java:61)
	at net.datatp.module.data.db.SqlQueryUnitManager$QueryContext.createQuery(SqlQueryUnitManager.java:110)
	at net.datatp.module.data.db.SqlQueryUnitManager$QueryContext.createSqlSelectView(SqlQueryUnitManager.java:50)
	at net.datatp.module.data.db.DAOService.searchDbRecords(DAOService.java:126)
	at cloud.datatp.jobtracking.JobTrackingLogic.jobTrackingReport(JobTrackingLogic.java:560)
	at cloud.datatp.jobtracking.JobTrackingService.jobTrackingReport(JobTrackingService.java:241)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at cloud.datatp.jobtracking.JobTrackingService$$SpringCGLIB$$0.jobTrackingReport(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:158)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:143)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)

2025-08-01T10:51:13.894+07:00 ERROR 61422 --- [qtp1221993613-41] n.d.m.monitor.call.EndpointCallContext   : Start call with component JobTrackingService, method jobTrackingReport, arguments
[ {
  "tenantId" : "default",
  "companyId" : 8,
  "companyParentId" : 4,
  "companyCode" : "beehph",
  "companyLabel" : "Bee HPH",
  "companyFullName" : "BEE LOGISTICS CORPORATION - HAI PHONG BRANCH OFFICE",
  "loginId" : "nhat.le",
  "accountId" : 2089,
  "token" : "9e57ccec8e98a2e2c2d25e3766e4540f",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node015tvwjj683uhcppz63qjnp6ku0",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : "Employee",
  "allowAccessCompanies" : {
    "bee" : {
      "companyId" : 4,
      "companyParentId" : 0,
      "companyCode" : "bee",
      "companyLabel" : "Bee Corp",
      "companyFullName" : null
    },
    "beescs" : {
      "companyId" : 12,
      "companyParentId" : 1,
      "companyCode" : "beescs",
      "companyLabel" : "BEESCS",
      "companyFullName" : null
    },
    "beehan" : {
      "companyId" : 16,
      "companyParentId" : 4,
      "companyCode" : "beehan",
      "companyLabel" : "Bee HAN",
      "companyFullName" : null
    },
    "beehph" : {
      "companyId" : 8,
      "companyParentId" : 4,
      "companyCode" : "beehph",
      "companyLabel" : "Bee HPH",
      "companyFullName" : null
    },
    "beehcm" : {
      "companyId" : 17,
      "companyParentId" : 4,
      "companyCode" : "beehcm",
      "companyLabel" : "Bee HCM",
      "companyFullName" : null
    },
    "beedad" : {
      "companyId" : 18,
      "companyParentId" : 4,
      "companyCode" : "beedad",
      "companyLabel" : "Bee DAD",
      "companyFullName" : null
    },
    "datatp" : {
      "companyId" : 10,
      "companyParentId" : 1,
      "companyCode" : "datatp",
      "companyLabel" : "DataTP Cloud",
      "companyFullName" : null
    }
  },
  "featurePermissions" : [ {
    "id" : 6989,
    "appId" : 2,
    "appModule" : "user",
    "appName" : "my-space",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 11332,
    "appId" : 68,
    "appModule" : "document",
    "appName" : "company-document",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 8423,
    "appId" : 15,
    "appModule" : "logistics",
    "appName" : "user-logistics-prices",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 983,
    "appId" : 5,
    "appModule" : "admin",
    "appName" : "admin-space",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 1302,
    "appId" : 26,
    "appModule" : "okr",
    "appName" : "okr",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 1356,
    "appId" : 10,
    "appModule" : "partner",
    "appName" : "partner",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 1576,
    "appId" : 13,
    "appModule" : "logistics",
    "appName" : "company-logistics-settings",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 5515,
    "appId" : 64,
    "appModule" : "job-tracking",
    "appName" : "job-tracking",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 1580,
    "appId" : 16,
    "appModule" : "logistics",
    "appName" : "user-logistics-sales",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 5031,
    "appId" : 27,
    "appModule" : "logistics",
    "appName" : "user-partner-logistics-crm",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 2139,
    "appId" : 18,
    "appModule" : "system",
    "appName" : "system",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 2758,
    "appId" : 19,
    "appModule" : "sample",
    "appName" : "reactjs-lib",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 2835,
    "appId" : 21,
    "appModule" : "tms",
    "appName" : "user-tms-app",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 3852,
    "appId" : 4,
    "appModule" : "communication",
    "appName" : "communication",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 4484,
    "appId" : 61,
    "appModule" : "tms",
    "appName" : "user-tms-ops",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 4487,
    "appId" : 62,
    "appModule" : "tms",
    "appName" : "user-tms-round-used",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 5726,
    "appId" : 9,
    "appModule" : "company",
    "appName" : "company-hr",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 11732,
    "appId" : 70,
    "appModule" : "hr",
    "appName" : "company-kpi",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 988,
    "appId" : 6,
    "appModule" : "company",
    "appName" : "company-space",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 12977,
    "appId" : 67,
    "appModule" : "tms",
    "appName" : "user-vehicle-trip",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 12978,
    "appId" : 25,
    "appModule" : "tms",
    "appName" : "user-vehicle-fleets",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 13064,
    "appId" : 7,
    "appModule" : "company",
    "appName" : "company-asset",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 14281,
    "appId" : 80,
    "appModule" : "logistics",
    "appName" : "company-logistics-sales",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 13102,
    "appId" : 79,
    "appModule" : "company",
    "appName" : "user-asset",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 14455,
    "appId" : 3,
    "appModule" : "project",
    "appName" : "project",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  } ],
  "attributes" : { },
  "clientId" : "default:nhat.le"
}, {
  "params" : {
    "status" : "Done",
    "jobTrackingProjectId" : 45,
    "withPermission" : false
  },
  "filters" : [ {
    "name" : "search",
    "filterType" : "String",
    "filterValue" : "",
    "required" : true
  } ],
  "optionFilters" : [ {
    "name" : "storageState",
    "filterType" : "NotSet",
    "required" : true,
    "multiple" : true,
    "options" : [ "ACTIVE", "ARCHIVED" ],
    "selectOptions" : [ "ACTIVE" ]
  } ],
  "rangeFilters" : [ {
    "name" : "endTime",
    "filterType" : "NotSet",
    "required" : true,
    "fromValue" : "01/07/2025@00:00:00+0700",
    "toValue" : "Invalid date"
  } ],
  "maxReturn" : 7000
} ]
2025-08-01T10:51:13.897+07:00 ERROR 61422 --- [qtp1221993613-41] n.d.m.monitor.call.EndpointCallService   : Unknown Error when handle the endpoint JobTrackingService/jobTrackingReport
2025-08-01T10:51:13.898+07:00 ERROR 61422 --- [qtp1221993613-41] n.d.m.monitor.call.EndpointCallService   : Exception: 

net.datatp.util.error.RuntimeError: Cannot run script file JobTrackingReport
	at net.datatp.util.error.RuntimeError.UnknownError(RuntimeError.java:96)
	at net.datatp.lib.executable.ExecutableUnit.doExecute(ExecutableUnit.java:33)
	at net.datatp.lib.executable.Executor.execute(Executor.java:34)
	at net.datatp.module.service.ExecutableUnitManager.execute(ExecutableUnitManager.java:61)
	at net.datatp.module.data.db.SqlQueryUnitManager$QueryContext.createQuery(SqlQueryUnitManager.java:110)
	at net.datatp.module.data.db.SqlQueryUnitManager$QueryContext.createSqlSelectView(SqlQueryUnitManager.java:50)
	at net.datatp.module.data.db.DAOService.searchDbRecords(DAOService.java:126)
	at cloud.datatp.jobtracking.JobTrackingLogic.jobTrackingReport(JobTrackingLogic.java:560)
	at cloud.datatp.jobtracking.JobTrackingService.jobTrackingReport(JobTrackingService.java:241)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at cloud.datatp.jobtracking.JobTrackingService$$SpringCGLIB$$0.jobTrackingReport(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:158)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:143)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)

2025-08-01T10:51:59.806+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 8, expire count 0
2025-08-01T10:51:59.850+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T10:52:06.862+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T10:52:27.659+07:00  INFO 61422 --- [qtp1221993613-61] n.d.module.session.ClientSessionManager  : Add a client session id = node0ffljxh2oknj83o3ovl8yd2g91, token = 9e57ccec8e98a2e2c2d25e3766e4540f
2025-08-01T10:52:27.660+07:00  INFO 61422 --- [qtp1221993613-40] n.d.module.session.ClientSessionManager  : Add a client session id = node0ffljxh2oknj83o3ovl8yd2g91, token = 9e57ccec8e98a2e2c2d25e3766e4540f
2025-08-01T10:52:27.676+07:00  INFO 61422 --- [qtp1221993613-61] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-01T10:52:27.676+07:00  INFO 61422 --- [qtp1221993613-40] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-01T10:52:42.850+07:00  INFO 61422 --- [qtp1221993613-37] n.d.module.session.ClientSessionManager  : Add a client session id = node0ffljxh2oknj83o3ovl8yd2g91, token = 9e57ccec8e98a2e2c2d25e3766e4540f
2025-08-01T10:52:42.851+07:00  INFO 61422 --- [qtp1221993613-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0ffljxh2oknj83o3ovl8yd2g91, token = 9e57ccec8e98a2e2c2d25e3766e4540f
2025-08-01T10:52:42.864+07:00  INFO 61422 --- [qtp1221993613-35] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-01T10:52:42.864+07:00  INFO 61422 --- [qtp1221993613-37] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-01T10:53:02.960+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T10:53:23.709+07:00  INFO 61422 --- [qtp1221993613-40] n.d.module.session.ClientSessionManager  : Add a client session id = node0ffljxh2oknj83o3ovl8yd2g91, token = 9e57ccec8e98a2e2c2d25e3766e4540f
2025-08-01T10:53:23.717+07:00  INFO 61422 --- [qtp1221993613-40] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-01T10:53:23.730+07:00  INFO 61422 --- [qtp1221993613-64] n.d.module.session.ClientSessionManager  : Add a client session id = node0ffljxh2oknj83o3ovl8yd2g91, token = 9e57ccec8e98a2e2c2d25e3766e4540f
2025-08-01T10:53:23.735+07:00  INFO 61422 --- [qtp1221993613-64] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-01T10:53:59.054+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T10:53:59.061+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T10:54:06.075+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T10:54:15.822+07:00  INFO 61422 --- [qtp1221993613-69] n.d.module.session.ClientSessionManager  : Add a client session id = node0ffljxh2oknj83o3ovl8yd2g91, token = 9e57ccec8e98a2e2c2d25e3766e4540f
2025-08-01T10:54:15.828+07:00  INFO 61422 --- [qtp1221993613-62] n.d.module.session.ClientSessionManager  : Add a client session id = node0ffljxh2oknj83o3ovl8yd2g91, token = 9e57ccec8e98a2e2c2d25e3766e4540f
2025-08-01T10:54:15.830+07:00  INFO 61422 --- [qtp1221993613-69] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-01T10:54:15.850+07:00  INFO 61422 --- [qtp1221993613-62] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-01T10:55:02.237+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T10:55:02.244+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T10:56:03.293+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 8, expire count 0
2025-08-01T10:56:03.302+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T10:56:05.313+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T10:57:06.408+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T10:57:15.535+07:00  INFO 61422 --- [qtp1221993613-62] n.d.module.session.ClientSessionManager  : Add a client session id = node0ffljxh2oknj83o3ovl8yd2g91, token = 9e57ccec8e98a2e2c2d25e3766e4540f
2025-08-01T10:57:15.543+07:00  INFO 61422 --- [qtp1221993613-37] n.d.module.session.ClientSessionManager  : Add a client session id = node0ffljxh2oknj83o3ovl8yd2g91, token = 9e57ccec8e98a2e2c2d25e3766e4540f
2025-08-01T10:57:15.553+07:00  INFO 61422 --- [qtp1221993613-62] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-01T10:57:15.561+07:00  INFO 61422 --- [qtp1221993613-37] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-01T10:57:24.547+07:00  INFO 61422 --- [qtp1221993613-37] n.d.module.session.ClientSessionManager  : Add a client session id = node0ffljxh2oknj83o3ovl8yd2g91, token = 9e57ccec8e98a2e2c2d25e3766e4540f
2025-08-01T10:57:24.548+07:00  INFO 61422 --- [qtp1221993613-62] n.d.module.session.ClientSessionManager  : Add a client session id = node0ffljxh2oknj83o3ovl8yd2g91, token = 9e57ccec8e98a2e2c2d25e3766e4540f
2025-08-01T10:57:24.557+07:00  INFO 61422 --- [qtp1221993613-37] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-01T10:57:24.557+07:00  INFO 61422 --- [qtp1221993613-62] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-01T10:58:03.544+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-08-01T10:58:03.559+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T10:58:04.564+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T10:59:06.666+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:00:02.774+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T11:00:02.781+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T11:00:03.787+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T11:00:03.791+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-01T11:00:03.796+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-08-01T11:00:03.798+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 01/08/2025@11:00:03+0700
2025-08-01T11:00:03.829+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 01/08/2025@11:00:00+0700 to 01/08/2025@11:15:00+0700
2025-08-01T11:00:03.829+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 01/08/2025@11:00:00+0700 to 01/08/2025@11:15:00+0700
2025-08-01T11:00:03.830+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:00:46.297+07:00  INFO 61422 --- [Scheduler-809533224-1] n.d.m.session.AppHttpSessionListener     : The session node015tvwjj683uhcppz63qjnp6ku0 is destroyed.
2025-08-01T11:01:06.179+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:01:26.590+07:00  INFO 61422 --- [qtp1221993613-67] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-08-01T11:01:32.353+07:00  INFO 61422 --- [qtp1221993613-67] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint JobTrackingService/searchJobTrackingProjects
2025-08-01T11:01:32.353+07:00  INFO 61422 --- [qtp1221993613-62] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint ChatService/searchChatMessageHistory
2025-08-01T11:01:32.381+07:00  INFO 61422 --- [qtp1221993613-74] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint JobTrackingService/searchJobTrackingProjects
2025-08-01T11:01:32.382+07:00  INFO 61422 --- [qtp1221993613-36] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint ChatService/searchChatMessageHistory
2025-08-01T11:01:33.765+07:00  INFO 61422 --- [qtp1221993613-70] n.d.module.session.ClientSessionManager  : Add a client session id = node0ffljxh2oknj83o3ovl8yd2g91, token = 9e57ccec8e98a2e2c2d25e3766e4540f
2025-08-01T11:01:33.767+07:00  INFO 61422 --- [qtp1221993613-67] n.d.module.session.ClientSessionManager  : Add a client session id = node0ffljxh2oknj83o3ovl8yd2g91, token = 9e57ccec8e98a2e2c2d25e3766e4540f
2025-08-01T11:01:33.838+07:00  INFO 61422 --- [qtp1221993613-70] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-01T11:01:33.838+07:00  INFO 61422 --- [qtp1221993613-67] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-01T11:02:03.375+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 6, expire count 0
2025-08-01T11:02:03.399+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T11:02:03.400+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:03:06.518+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:04:02.658+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-08-01T11:04:02.663+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T11:04:02.664+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:05:05.759+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T11:05:05.763+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:06:01.872+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 4
2025-08-01T11:06:01.890+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T11:06:06.898+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:07:04.972+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:08:01.059+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T11:08:01.065+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T11:08:06.071+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:09:04.158+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:10:00.263+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T11:10:00.277+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T11:10:06.285+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T11:10:06.286+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:11:03.384+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:11:59.508+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 7
2025-08-01T11:11:59.533+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T11:12:06.546+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:13:02.646+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:13:58.764+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T11:13:58.782+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T11:14:05.799+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:15:06.889+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:15:06.894+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 01/08/2025@11:15:06+0700
2025-08-01T11:15:06.967+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 01/08/2025@11:15:00+0700 to 01/08/2025@11:30:00+0700
2025-08-01T11:15:06.968+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 01/08/2025@11:15:00+0700 to 01/08/2025@11:30:00+0700
2025-08-01T11:15:06.968+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-01T11:15:06.969+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T11:16:03.094+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-08-01T11:16:03.118+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T11:16:05.129+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:17:06.226+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:18:03.347+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T11:18:03.358+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T11:18:04.361+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:19:06.469+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:20:03.573+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T11:20:03.580+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T11:20:03.581+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:20:03.582+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T11:21:06.682+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:22:02.775+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T11:22:02.784+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T11:22:02.786+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:23:05.889+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:24:02.030+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T11:24:02.054+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T11:24:07.060+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:25:05.166+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:25:05.170+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T11:26:01.251+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-08-01T11:26:01.261+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T11:26:06.274+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:27:04.382+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:28:00.468+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T11:28:00.476+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T11:28:06.481+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:29:03.576+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:29:59.667+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T11:29:59.678+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T11:30:06.692+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:30:06.694+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T11:30:06.695+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-01T11:30:06.695+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 01/08/2025@11:30:06+0700
2025-08-01T11:30:06.718+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 01/08/2025@11:30:00+0700 to 01/08/2025@11:45:00+0700
2025-08-01T11:30:06.718+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 01/08/2025@11:30:00+0700 to 01/08/2025@11:45:00+0700
2025-08-01T11:31:02.812+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:31:58.934+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-08-01T11:31:58.949+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T11:32:05.959+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:33:02.044+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:34:03.151+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T11:34:03.158+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T11:34:05.167+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:35:06.267+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:35:06.269+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T11:36:03.378+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T11:36:03.387+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T11:36:04.392+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:37:06.488+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:38:03.589+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T11:38:03.611+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T11:38:03.612+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:39:06.780+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:40:02.882+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 0
2025-08-01T11:40:02.906+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T11:40:02.906+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:40:02.907+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T11:41:06.010+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:42:02.158+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-08-01T11:42:02.173+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T11:42:02.173+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:43:05.286+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:44:01.438+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-08-01T11:44:01.451+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T11:44:06.458+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:45:04.557+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:45:04.560+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T11:45:04.561+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-01T11:45:04.563+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 01/08/2025@11:45:04+0700
2025-08-01T11:45:04.609+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 01/08/2025@11:45:00+0700 to 01/08/2025@12:00:00+0700
2025-08-01T11:45:04.609+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 01/08/2025@11:45:00+0700 to 01/08/2025@12:00:00+0700
2025-08-01T11:46:00.724+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-08-01T11:46:00.737+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T11:46:06.747+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:47:03.841+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:47:59.949+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T11:47:59.956+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T11:48:06.968+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:49:03.062+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:49:59.184+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T11:49:59.196+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T11:50:06.209+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:50:06.210+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T11:51:02.306+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:52:03.430+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T11:52:03.436+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T11:52:05.446+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:53:06.538+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:54:03.649+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 4
2025-08-01T11:54:03.661+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T11:54:04.668+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:55:06.773+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:55:06.775+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T11:56:02.877+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-08-01T11:56:02.885+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T11:56:03.892+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:57:06.963+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:58:03.250+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T11:58:03.268+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T11:58:03.268+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T11:59:06.389+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:00:02.514+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T12:00:02.525+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T12:00:02.541+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 3 hour
2025-08-01T12:00:02.541+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at 12 PM every day
2025-08-01T12:00:02.541+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T12:00:02.542+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-01T12:00:02.542+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 01/08/2025@12:00:02+0700
2025-08-01T12:00:02.559+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 01/08/2025@12:00:00+0700 to 01/08/2025@12:15:00+0700
2025-08-01T12:00:02.559+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 01/08/2025@12:00:00+0700 to 01/08/2025@12:15:00+0700
2025-08-01T12:00:02.559+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:00:02.559+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-08-01T12:01:05.655+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:02:01.809+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-08-01T12:02:01.820+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T12:02:06.821+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:03:04.932+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:04:01.030+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T12:04:01.044+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T12:04:06.046+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:05:04.151+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T12:05:04.153+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:06:00.275+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T12:06:00.280+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T12:06:06.291+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:07:03.387+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:07:59.479+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T12:07:59.482+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T12:08:06.496+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:09:02.583+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:09:58.699+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T12:09:58.731+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T12:10:05.743+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T12:10:05.744+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:11:06.837+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:12:02.954+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-08-01T12:12:02.963+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T12:12:04.967+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:13:06.071+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:14:03.184+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T12:14:03.193+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T12:14:04.198+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:15:06.285+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:15:06.289+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-01T12:15:06.295+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 01/08/2025@12:15:06+0700
2025-08-01T12:15:06.324+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 01/08/2025@12:15:00+0700 to 01/08/2025@12:30:00+0700
2025-08-01T12:15:06.324+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 01/08/2025@12:15:00+0700 to 01/08/2025@12:30:00+0700
2025-08-01T12:15:06.325+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T12:16:03.455+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-08-01T12:16:03.464+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T12:16:03.464+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:17:06.563+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:18:02.659+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T12:18:02.666+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T12:18:02.666+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:19:05.770+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:20:01.884+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T12:20:01.900+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T12:20:06.906+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T12:20:06.907+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:21:04.998+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:22:01.086+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T12:22:01.091+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T12:22:06.097+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:23:04.198+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:24:00.302+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T12:24:00.309+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T12:24:06.318+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:25:03.462+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T12:25:03.464+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:25:59.632+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-08-01T12:25:59.638+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T12:26:06.649+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:27:02.820+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:27:58.955+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T12:27:58.973+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T12:28:05.991+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:29:02.096+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:30:03.211+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T12:30:03.220+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T12:30:05.230+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-01T12:30:05.232+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 01/08/2025@12:30:05+0700
2025-08-01T12:30:05.248+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 01/08/2025@12:30:00+0700 to 01/08/2025@12:45:00+0700
2025-08-01T12:30:05.248+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 01/08/2025@12:30:00+0700 to 01/08/2025@12:45:00+0700
2025-08-01T12:30:05.249+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T12:30:05.249+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:31:06.357+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:32:03.484+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-08-01T12:32:03.502+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T12:32:04.508+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:33:06.594+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:34:02.682+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T12:34:02.689+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T12:34:03.695+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:35:06.804+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T12:35:06.806+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:36:02.919+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T12:36:02.925+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T12:36:02.925+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:37:06.039+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:38:02.122+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T12:38:02.125+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T12:38:02.126+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:39:05.222+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:40:01.369+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T12:40:01.376+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T12:40:06.387+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T12:40:06.387+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:41:04.565+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:42:00.734+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-08-01T12:42:00.736+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T12:42:06.740+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:43:03.930+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:44:00.121+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T12:44:00.130+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T12:44:06.141+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:45:03.286+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 01/08/2025@12:45:03+0700
2025-08-01T12:45:03.303+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 01/08/2025@12:45:00+0700 to 01/08/2025@13:00:00+0700
2025-08-01T12:45:03.303+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 01/08/2025@12:45:00+0700 to 01/08/2025@13:00:00+0700
2025-08-01T12:45:03.303+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-01T12:45:03.304+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T12:45:03.304+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:45:59.413+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T12:45:59.420+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T12:46:06.424+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:47:02.507+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:48:03.604+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T12:48:03.608+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T12:48:05.618+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:49:06.715+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:50:02.820+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T12:50:02.825+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T12:50:04.834+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T12:50:04.835+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:51:06.927+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:52:03.068+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T12:52:03.077+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T12:52:04.082+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:53:06.187+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:54:03.287+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T12:54:03.292+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T12:54:03.293+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:55:06.393+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T12:55:06.393+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:56:02.489+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-08-01T12:56:02.492+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T12:56:02.493+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:57:05.605+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:58:01.701+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T12:58:01.709+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T12:58:06.710+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T12:59:04.812+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:00:00.898+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T13:00:00.901+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T13:00:06.910+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-08-01T13:00:06.910+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-01T13:00:06.911+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 01/08/2025@13:00:06+0700
2025-08-01T13:00:06.928+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 01/08/2025@13:00:00+0700 to 01/08/2025@13:15:00+0700
2025-08-01T13:00:06.929+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 01/08/2025@13:00:00+0700 to 01/08/2025@13:15:00+0700
2025-08-01T13:00:06.929+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T13:00:06.930+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:01:04.057+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:02:00.160+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-08-01T13:02:00.182+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T13:02:06.188+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:03:03.292+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:03:59.374+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T13:03:59.377+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T13:04:06.388+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:05:02.477+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:05:02.479+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T13:06:03.574+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T13:06:03.578+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T13:06:05.588+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:07:06.679+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:08:02.771+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T13:08:02.776+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T13:08:04.786+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:09:06.888+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:10:02.986+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T13:10:02.996+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T13:10:04.002+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:10:04.002+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T13:11:06.101+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:12:03.202+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-08-01T13:12:03.207+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T13:12:03.208+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:13:06.312+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:14:02.402+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T13:14:02.404+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T13:14:02.405+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:15:05.509+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:15:05.511+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T13:15:05.511+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-01T13:15:05.511+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 01/08/2025@13:15:05+0700
2025-08-01T13:15:05.526+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 01/08/2025@13:15:00+0700 to 01/08/2025@13:30:00+0700
2025-08-01T13:15:05.527+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 01/08/2025@13:15:00+0700 to 01/08/2025@13:30:00+0700
2025-08-01T13:16:01.647+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-08-01T13:16:01.655+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T13:16:06.660+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:17:04.760+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:18:00.846+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T13:18:00.850+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T13:18:06.859+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:19:03.970+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:20:00.142+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T13:20:00.150+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T13:20:06.163+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:20:06.164+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T13:21:03.342+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:21:59.532+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T13:21:59.535+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T13:22:06.565+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:23:02.734+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:23:58.888+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T13:23:58.893+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T13:24:05.906+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:25:02.011+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:25:02.013+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T13:26:03.137+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-08-01T13:26:03.141+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T13:26:05.148+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:27:06.250+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:28:03.357+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T13:28:03.364+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T13:28:04.369+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:29:06.469+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:30:03.584+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T13:30:03.597+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T13:30:03.598+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:30:03.598+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T13:30:03.598+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-01T13:30:03.598+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 01/08/2025@13:30:03+0700
2025-08-01T13:30:03.610+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 01/08/2025@13:30:00+0700 to 01/08/2025@13:45:00+0700
2025-08-01T13:30:03.610+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 01/08/2025@13:30:00+0700 to 01/08/2025@13:45:00+0700
2025-08-01T13:31:06.706+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:32:02.799+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T13:32:02.805+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T13:32:02.805+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:33:05.913+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:34:02.015+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T13:34:02.020+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T13:34:07.025+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:35:05.118+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T13:35:05.121+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:36:01.222+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T13:36:01.228+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T13:36:06.232+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:37:04.335+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:38:00.446+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T13:38:00.456+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T13:38:06.465+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:39:03.560+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:39:59.651+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T13:39:59.654+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T13:40:06.669+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:40:06.671+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T13:41:02.769+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:41:58.881+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-08-01T13:41:58.885+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T13:42:05.901+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:43:06.988+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:44:03.085+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T13:44:03.090+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T13:44:05.097+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:45:06.182+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-01T13:45:06.184+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 01/08/2025@13:45:06+0700
2025-08-01T13:45:06.203+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 01/08/2025@13:45:00+0700 to 01/08/2025@14:00:00+0700
2025-08-01T13:45:06.203+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 01/08/2025@13:45:00+0700 to 01/08/2025@14:00:00+0700
2025-08-01T13:45:06.203+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:45:06.204+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T13:46:03.392+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-08-01T13:46:03.398+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T13:46:04.401+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:47:06.497+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:48:03.616+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T13:48:03.623+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T13:48:03.623+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:49:06.728+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:50:02.819+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T13:50:02.821+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T13:50:02.822+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:50:02.822+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T13:51:05.926+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:52:02.023+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T13:52:02.030+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T13:52:02.031+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:53:05.125+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:54:01.216+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T13:54:01.219+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T13:54:06.222+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:55:04.314+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:55:04.314+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T13:56:00.404+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-08-01T13:56:00.408+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T13:56:06.418+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:57:03.520+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:57:59.615+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T13:57:59.620+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T13:58:06.632+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:59:02.725+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T13:59:58.811+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T13:59:58.816+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T14:00:05.825+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:00:05.826+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T14:00:05.833+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at 14 PM every day
2025-08-01T14:00:05.834+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 01/08/2025@14:00:05+0700
2025-08-01T14:00:05.852+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 01/08/2025@14:00:00+0700 to 01/08/2025@14:15:00+0700
2025-08-01T14:00:05.852+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 01/08/2025@14:00:00+0700 to 01/08/2025@14:15:00+0700
2025-08-01T14:00:05.853+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-01T14:00:05.853+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-08-01T14:01:06.954+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:02:03.071+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-08-01T14:02:03.078+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T14:02:05.084+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:03:06.170+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:04:03.275+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T14:04:03.280+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T14:04:04.285+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:05:06.394+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:05:06.396+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T14:06:03.489+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T14:06:03.493+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T14:06:03.493+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:07:06.605+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:08:02.708+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T14:08:02.713+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T14:08:02.714+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:09:05.821+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:10:01.934+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T14:10:01.941+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T14:10:06.946+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:10:06.947+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T14:11:05.029+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:12:01.119+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-08-01T14:12:01.121+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T14:12:06.125+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:13:04.232+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:14:00.324+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T14:14:00.329+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T14:14:06.338+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:15:03.432+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:15:03.433+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T14:15:03.433+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-01T14:15:03.434+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 01/08/2025@14:15:03+0700
2025-08-01T14:15:03.450+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 01/08/2025@14:15:00+0700 to 01/08/2025@14:30:00+0700
2025-08-01T14:15:03.450+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 01/08/2025@14:15:00+0700 to 01/08/2025@14:30:00+0700
2025-08-01T14:15:59.552+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T14:15:59.556+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T14:16:06.570+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:17:02.626+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:18:03.713+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T14:18:03.720+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T14:18:05.730+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:19:06.826+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:20:02.926+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T14:20:02.929+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T14:20:04.938+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T14:20:04.939+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:21:06.025+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:22:03.117+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T14:22:03.125+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T14:22:04.129+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:23:06.221+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:24:03.316+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T14:24:03.321+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T14:24:03.322+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:25:06.438+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:25:06.439+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T14:26:02.524+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-08-01T14:26:02.527+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T14:26:02.527+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:27:05.635+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:28:01.732+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T14:28:01.740+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T14:28:06.744+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:29:04.835+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:30:00.924+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T14:30:00.926+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T14:30:06.934+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 01/08/2025@14:30:06+0700
2025-08-01T14:30:06.946+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 01/08/2025@14:30:00+0700 to 01/08/2025@14:45:00+0700
2025-08-01T14:30:06.947+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 01/08/2025@14:30:00+0700 to 01/08/2025@14:45:00+0700
2025-08-01T14:30:06.947+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-01T14:30:06.948+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:30:06.948+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T14:31:04.039+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:32:00.060+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-08-01T14:32:00.067+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T14:32:06.077+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:33:03.169+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:33:59.255+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T14:33:59.258+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T14:34:06.269+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:35:02.366+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T14:35:02.370+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:36:03.493+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T14:36:03.502+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T14:36:05.511+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:37:06.591+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:38:03.683+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T14:38:03.687+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T14:38:04.691+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:39:06.790+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:40:02.882+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T14:40:02.895+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T14:40:03.896+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T14:40:03.897+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:41:06.990+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:42:03.090+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-08-01T14:42:03.097+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T14:42:03.098+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:43:06.193+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:44:02.288+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T14:44:02.292+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T14:44:02.295+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:45:05.397+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 01/08/2025@14:45:05+0700
2025-08-01T14:45:05.422+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 01/08/2025@14:45:00+0700 to 01/08/2025@15:00:00+0700
2025-08-01T14:45:05.422+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 01/08/2025@14:45:00+0700 to 01/08/2025@15:00:00+0700
2025-08-01T14:45:05.422+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-01T14:45:05.422+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T14:45:05.422+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:46:01.533+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-08-01T14:46:01.543+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T14:46:06.548+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:47:04.655+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:48:00.750+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T14:48:00.758+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T14:48:06.766+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:49:03.853+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:49:59.954+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T14:49:59.963+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T14:50:06.977+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T14:50:06.979+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:51:03.054+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:51:59.129+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T14:51:59.133+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T14:52:06.140+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:53:02.221+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:54:03.313+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T14:54:03.319+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T14:54:05.326+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:55:06.407+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T14:55:06.409+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:56:03.500+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-08-01T14:56:03.505+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T14:56:04.507+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:57:06.580+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:58:03.690+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T14:58:03.702+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T14:58:03.703+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T14:59:06.811+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:00:02.911+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T15:00:02.916+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T15:00:02.917+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-08-01T15:00:02.918+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-01T15:00:02.919+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 01/08/2025@15:00:02+0700
2025-08-01T15:00:02.943+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 01/08/2025@15:00:00+0700 to 01/08/2025@15:15:00+0700
2025-08-01T15:00:02.943+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 01/08/2025@15:00:00+0700 to 01/08/2025@15:15:00+0700
2025-08-01T15:00:02.943+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T15:00:02.944+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:00:02.944+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 3 hour
2025-08-01T15:01:06.044+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:02:02.168+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-08-01T15:02:02.174+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T15:02:02.175+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:03:05.272+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:04:01.370+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T15:04:01.375+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T15:04:06.380+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:05:04.469+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T15:05:04.472+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:06:00.568+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T15:06:00.575+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T15:06:06.586+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:07:03.681+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:07:59.785+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T15:07:59.788+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T15:08:06.799+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:09:02.898+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:09:58.999+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T15:09:59.006+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T15:10:06.027+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T15:10:06.030+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:11:02.183+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:12:03.324+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-08-01T15:12:03.338+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T15:12:05.352+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:13:06.547+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:14:02.707+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T15:14:02.711+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T15:14:04.731+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:15:06.824+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-01T15:15:06.826+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 01/08/2025@15:15:06+0700
2025-08-01T15:15:06.856+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 01/08/2025@15:15:00+0700 to 01/08/2025@15:30:00+0700
2025-08-01T15:15:06.856+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 01/08/2025@15:15:00+0700 to 01/08/2025@15:30:00+0700
2025-08-01T15:15:06.856+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T15:15:06.856+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:16:02.969+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-08-01T15:16:02.976+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T15:16:03.978+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:17:06.068+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:18:03.192+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T15:18:03.198+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T15:18:03.198+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:19:06.297+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:20:02.382+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T15:20:02.387+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T15:20:02.388+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:20:02.388+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T15:21:05.483+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:22:01.575+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T15:22:01.586+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T15:22:06.593+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:23:04.691+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:24:00.795+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T15:24:00.800+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T15:24:06.811+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:25:03.890+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:25:03.891+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T15:26:00.000+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-08-01T15:26:00.016+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T15:26:07.032+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:27:03.128+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:27:59.213+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T15:27:59.215+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T15:28:06.222+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:29:02.304+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:30:03.399+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T15:30:03.403+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T15:30:05.410+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:30:05.411+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T15:30:05.411+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 01/08/2025@15:30:05+0700
2025-08-01T15:30:05.423+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 01/08/2025@15:30:00+0700 to 01/08/2025@15:45:00+0700
2025-08-01T15:30:05.423+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 01/08/2025@15:30:00+0700 to 01/08/2025@15:45:00+0700
2025-08-01T15:30:05.424+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-01T15:31:06.506+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:32:03.607+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-08-01T15:32:03.613+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T15:32:04.615+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:33:06.749+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:34:02.856+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T15:34:02.860+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T15:34:03.865+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:35:06.956+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:35:06.956+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T15:36:03.060+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T15:36:03.065+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T15:36:03.067+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:37:06.169+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:38:02.257+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T15:38:02.262+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T15:38:02.263+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:39:05.370+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:40:01.483+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T15:40:01.490+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T15:40:06.496+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:40:06.496+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T15:41:04.594+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:42:00.697+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-08-01T15:42:00.700+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T15:42:06.705+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:43:03.801+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:43:59.912+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T15:43:59.920+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T15:44:06.935+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:45:03.033+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:45:03.034+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T15:45:03.034+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-01T15:45:03.035+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 01/08/2025@15:45:03+0700
2025-08-01T15:45:03.055+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 01/08/2025@15:45:00+0700 to 01/08/2025@16:00:00+0700
2025-08-01T15:45:03.055+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 01/08/2025@15:45:00+0700 to 01/08/2025@16:00:00+0700
2025-08-01T15:45:59.161+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T15:45:59.167+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T15:46:06.177+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:47:02.276+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:48:03.383+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T15:48:03.389+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T15:48:05.397+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:49:06.515+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:50:03.625+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T15:50:03.632+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T15:50:04.638+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:50:04.639+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T15:51:06.736+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:52:02.821+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T15:52:02.822+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T15:52:03.828+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:53:06.921+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:54:03.015+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T15:54:03.021+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T15:54:03.022+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:55:06.111+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:55:06.111+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T15:56:02.192+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-08-01T15:56:02.193+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T15:56:02.195+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:57:05.292+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:58:01.396+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T15:58:01.403+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T15:58:06.409+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T15:59:04.507+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:00:00.605+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T16:00:00.607+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T16:00:06.616+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:00:06.617+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T16:00:06.617+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-01T16:00:06.617+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 01/08/2025@16:00:06+0700
2025-08-01T16:00:06.633+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 01/08/2025@16:00:00+0700 to 01/08/2025@16:15:00+0700
2025-08-01T16:00:06.633+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 01/08/2025@16:00:00+0700 to 01/08/2025@16:15:00+0700
2025-08-01T16:00:06.633+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-08-01T16:01:03.737+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:01:59.830+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-08-01T16:01:59.837+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T16:02:06.849+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:03:02.929+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:03:59.021+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T16:03:59.025+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T16:04:06.035+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:05:02.129+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:05:02.130+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T16:06:03.253+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T16:06:03.261+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T16:06:05.272+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:07:06.360+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:08:03.468+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T16:08:03.472+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T16:08:04.477+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:09:06.574+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:10:03.678+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T16:10:03.684+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T16:10:03.684+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:10:03.686+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T16:11:06.787+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:12:02.885+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-08-01T16:12:02.912+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T16:12:02.912+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:13:06.016+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:14:02.104+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T16:14:02.112+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T16:14:02.113+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:15:05.223+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:15:05.224+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T16:15:05.225+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-01T16:15:05.225+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 01/08/2025@16:15:05+0700
2025-08-01T16:15:05.242+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 01/08/2025@16:15:00+0700 to 01/08/2025@16:30:00+0700
2025-08-01T16:15:05.242+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 01/08/2025@16:15:00+0700 to 01/08/2025@16:30:00+0700
2025-08-01T16:16:01.338+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T16:16:01.346+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T16:16:06.352+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:17:04.460+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:18:00.565+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T16:18:00.570+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T16:18:06.579+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:19:03.663+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:19:59.748+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T16:19:59.749+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T16:20:06.762+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:20:06.763+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T16:21:02.847+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:21:58.947+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T16:21:58.951+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T16:22:05.964+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:23:02.058+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:24:03.144+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T16:24:03.150+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T16:24:05.156+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:25:06.256+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:25:06.257+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T16:26:03.359+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-08-01T16:26:03.363+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T16:26:04.366+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:27:06.469+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:28:03.576+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T16:28:03.580+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T16:28:03.580+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:29:06.670+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:30:02.752+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T16:30:02.757+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T16:30:02.757+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:30:02.758+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T16:30:02.758+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 01/08/2025@16:30:02+0700
2025-08-01T16:30:02.769+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 01/08/2025@16:30:00+0700 to 01/08/2025@16:45:00+0700
2025-08-01T16:30:02.769+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 01/08/2025@16:30:00+0700 to 01/08/2025@16:45:00+0700
2025-08-01T16:30:02.769+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-01T16:31:05.863+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:32:01.981+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-08-01T16:32:01.987+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T16:32:06.993+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:33:05.105+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:34:01.191+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T16:34:01.196+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T16:34:06.202+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:35:04.303+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:35:04.303+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T16:36:00.401+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T16:36:00.409+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T16:36:06.416+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:37:03.507+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:37:59.596+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T16:37:59.602+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T16:38:06.613+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:39:02.695+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:40:03.815+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T16:40:03.828+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T16:40:05.840+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T16:40:05.841+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:41:06.935+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:42:03.045+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-08-01T16:42:03.053+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T16:42:05.061+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:43:06.138+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:44:03.265+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T16:44:03.290+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T16:44:04.296+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:45:06.390+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-01T16:45:06.395+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 01/08/2025@16:45:06+0700
2025-08-01T16:45:06.453+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 01/08/2025@16:45:00+0700 to 01/08/2025@17:00:00+0700
2025-08-01T16:45:06.453+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 01/08/2025@16:45:00+0700 to 01/08/2025@17:00:00+0700
2025-08-01T16:45:06.453+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:45:06.453+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T16:46:03.565+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-08-01T16:46:03.574+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T16:46:03.575+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:47:06.673+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:48:02.766+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T16:48:02.773+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T16:48:02.774+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:49:05.863+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:50:01.999+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T16:50:02.008+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T16:50:07.014+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:50:07.015+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T16:51:05.132+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:52:01.222+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T16:52:01.228+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T16:52:06.232+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:53:04.322+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:54:00.434+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T16:54:00.441+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T16:54:06.446+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:55:03.544+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:55:03.546+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T16:55:59.630+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-08-01T16:55:59.635+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T16:56:06.648+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:57:02.733+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:58:03.870+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T16:58:03.880+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T16:58:05.890+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T16:59:06.983+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T17:00:03.070+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T17:00:03.075+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T17:00:05.084+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T17:00:05.086+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T17:00:05.087+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 01/08/2025@17:00:05+0700
2025-08-01T17:00:05.142+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 01/08/2025@17:00:00+0700 to 01/08/2025@17:15:00+0700
2025-08-01T17:00:05.143+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 01/08/2025@17:00:00+0700 to 01/08/2025@17:15:00+0700
2025-08-01T17:00:05.143+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-01T17:00:05.143+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-08-01T17:01:06.237+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T17:02:03.355+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 0
2025-08-01T17:02:03.376+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T17:02:04.379+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T17:03:06.467+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T17:04:03.599+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 0
2025-08-01T17:04:03.636+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T17:04:03.637+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T17:05:06.778+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T17:05:06.782+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T17:06:02.866+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T17:06:02.871+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T17:06:02.872+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T17:07:05.964+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T17:08:02.125+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T17:08:02.138+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T17:08:02.139+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T17:09:05.247+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T17:10:01.348+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T17:10:01.351+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T17:10:06.356+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T17:10:06.356+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T17:11:04.452+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T17:12:00.567+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-08-01T17:12:00.576+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T17:12:06.585+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T17:13:03.687+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T17:13:59.790+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 2
2025-08-01T17:13:59.817+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T17:14:06.829+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T17:15:02.923+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T17:15:02.927+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-01T17:15:02.927+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-01T17:15:02.938+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 01/08/2025@17:15:02+0700
2025-08-01T17:15:02.991+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 01/08/2025@17:15:00+0700 to 01/08/2025@17:30:00+0700
2025-08-01T17:15:02.991+07:00  INFO 61422 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 01/08/2025@17:15:00+0700 to 01/08/2025@17:30:00+0700
2025-08-01T17:15:59.116+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-08-01T17:15:59.159+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T17:16:06.170+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T17:17:02.265+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T17:18:03.383+07:00  INFO 61422 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-01T17:18:03.392+07:00  INFO 61422 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T17:18:05.403+07:00  INFO 61422 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T17:18:36.785+07:00  INFO 61422 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@71142a7c{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-08-01T17:18:36.791+07:00  INFO 61422 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-08-01T17:18:36.791+07:00  INFO 61422 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-08-01T17:18:36.791+07:00  INFO 61422 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-08-01T17:18:36.792+07:00  INFO 61422 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-08-01T17:18:36.792+07:00  INFO 61422 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-08-01T17:18:36.792+07:00  INFO 61422 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-08-01T17:18:36.792+07:00  INFO 61422 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-08-01T17:18:36.792+07:00  INFO 61422 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-08-01T17:18:36.792+07:00  INFO 61422 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-08-01T17:18:36.792+07:00  INFO 61422 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-08-01T17:18:36.792+07:00  INFO 61422 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-08-01T17:18:36.792+07:00  INFO 61422 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-08-01T17:18:36.792+07:00  INFO 61422 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-08-01T17:18:36.792+07:00  INFO 61422 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-08-01T17:18:36.792+07:00  INFO 61422 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-08-01T17:18:36.816+07:00  INFO 61422 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T17:18:36.896+07:00  INFO 61422 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-08-01T17:18:36.902+07:00  INFO 61422 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-08-01T17:18:36.945+07:00  INFO 61422 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-01T17:18:36.953+07:00  INFO 61422 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-01T17:18:36.961+07:00  INFO 61422 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-01T17:18:36.963+07:00  INFO 61422 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-08-01T17:18:36.965+07:00  INFO 61422 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-08-01T17:18:36.966+07:00  INFO 61422 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-08-01T17:18:36.966+07:00  INFO 61422 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-08-01T17:18:36.966+07:00  INFO 61422 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-08-01T17:18:37.105+07:00  INFO 61422 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-08-01T17:18:37.105+07:00  INFO 61422 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-08-01T17:18:37.106+07:00  INFO 61422 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-08-01T17:18:37.106+07:00  INFO 61422 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-08-01T17:18:37.106+07:00  INFO 61422 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-08-01T17:18:37.110+07:00  INFO 61422 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@8fe054{STOPPING}[12.0.15,sto=0]
2025-08-01T17:18:37.117+07:00  INFO 61422 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-01T17:18:37.120+07:00  INFO 61422 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@283a5c2b{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.15421219078323286818/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@6701fe26{STOPPED}}
