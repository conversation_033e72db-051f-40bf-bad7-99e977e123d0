2025-08-04T00:00:06.277+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:00:06.322+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T00:00:06.323+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at 00:00 AM every day
2025-08-04T00:00:06.323+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-08-04T00:00:06.323+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 3 hour
2025-08-04T00:00:06.323+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-04T00:00:06.324+07:00  INFO 83697 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 04/08/2025@00:00:06+0700
2025-08-04T00:00:06.333+07:00  INFO 83697 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 04/08/2025@00:00:00+0700 to 04/08/2025@00:15:00+0700
2025-08-04T00:00:06.333+07:00  INFO 83697 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 04/08/2025@00:00:00+0700 to 04/08/2025@00:15:00+0700
2025-08-04T00:01:02.445+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:01:51.543+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 15
2025-08-04T00:01:51.551+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T00:02:05.575+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:03:06.679+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:03:50.774+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 1
2025-08-04T00:03:50.777+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T00:04:04.808+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:05:06.910+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:05:06.911+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T00:05:50.032+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T00:05:50.039+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T00:06:04.062+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:07:06.215+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:07:49.314+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 2
2025-08-04T00:07:49.331+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T00:08:03.352+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:09:06.468+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:09:48.531+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-08-04T00:09:48.535+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T00:10:02.559+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:10:02.560+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T00:11:05.684+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:11:52.786+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-08-04T00:11:52.796+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T00:12:06.818+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:13:04.914+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:13:52.011+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T00:13:52.018+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T00:14:06.045+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:15:04.115+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T00:15:04.116+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:15:04.117+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-04T00:15:04.118+07:00  INFO 83697 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 04/08/2025@00:15:04+0700
2025-08-04T00:15:04.141+07:00  INFO 83697 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 04/08/2025@00:15:00+0700 to 04/08/2025@00:30:00+0700
2025-08-04T00:15:04.141+07:00  INFO 83697 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 04/08/2025@00:15:00+0700 to 04/08/2025@00:30:00+0700
2025-08-04T00:15:52.240+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-08-04T00:15:52.249+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T00:16:06.276+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:17:03.369+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:17:52.493+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T00:17:52.509+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T00:18:06.528+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:19:02.603+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:19:51.687+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T00:19:51.690+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T00:20:05.709+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:20:05.710+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T00:21:06.845+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:21:50.984+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T00:21:50.998+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T00:22:05.023+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:23:06.130+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:23:50.203+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T00:23:50.205+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T00:24:04.233+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:25:06.334+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:25:06.334+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T00:25:49.415+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-08-04T00:25:49.422+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T00:26:03.449+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:27:06.560+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:27:48.687+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T00:27:48.691+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T00:28:02.741+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:29:05.941+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:29:48.016+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T00:29:48.028+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T00:30:02.050+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:30:02.051+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T00:30:02.051+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-04T00:30:02.052+07:00  INFO 83697 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 04/08/2025@00:30:02+0700
2025-08-04T00:30:02.069+07:00  INFO 83697 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 04/08/2025@00:30:00+0700 to 04/08/2025@00:45:00+0700
2025-08-04T00:30:02.070+07:00  INFO 83697 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 04/08/2025@00:30:00+0700 to 04/08/2025@00:45:00+0700
2025-08-04T00:31:05.166+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:31:52.247+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-08-04T00:31:52.255+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T00:32:06.280+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:33:04.386+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:33:52.466+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T00:33:52.471+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T00:34:06.495+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:35:03.603+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:35:03.604+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T00:35:52.713+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T00:35:52.721+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T00:36:06.745+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:37:02.833+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:37:51.923+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T00:37:51.926+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T00:38:05.951+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:39:02.044+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:39:51.137+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T00:39:51.144+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T00:40:05.174+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:40:05.174+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T00:41:06.271+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:41:50.372+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-08-04T00:41:50.385+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T00:42:04.410+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:43:06.516+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:43:49.616+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T00:43:49.623+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T00:44:03.651+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:45:06.741+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:45:06.745+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T00:45:06.746+07:00  INFO 83697 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 04/08/2025@00:45:06+0700
2025-08-04T00:45:06.770+07:00  INFO 83697 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 04/08/2025@00:45:00+0700 to 04/08/2025@01:00:00+0700
2025-08-04T00:45:06.770+07:00  INFO 83697 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 04/08/2025@00:45:00+0700 to 04/08/2025@01:00:00+0700
2025-08-04T00:45:06.770+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-04T00:45:48.847+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T00:45:48.851+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T00:46:02.870+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:47:05.982+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:47:48.122+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T00:47:48.136+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T00:48:02.160+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:49:05.264+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:49:52.430+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T00:49:52.449+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T00:50:06.470+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:50:06.472+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T00:51:04.574+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:51:52.667+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T00:51:52.673+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T00:52:06.701+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:53:03.796+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:53:51.876+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T00:53:51.882+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T00:54:06.908+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:55:03.000+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:55:03.005+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T00:55:52.174+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-08-04T00:55:52.184+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T00:56:06.209+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:57:02.311+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:57:51.386+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T00:57:51.389+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T00:58:05.413+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:59:06.521+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T00:59:50.641+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T00:59:50.646+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T01:00:04.678+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:00:04.680+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T01:00:04.680+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-08-04T01:00:04.680+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-04T01:00:04.681+07:00  INFO 83697 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 04/08/2025@01:00:04+0700
2025-08-04T01:00:04.701+07:00  INFO 83697 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 04/08/2025@01:00:00+0700 to 04/08/2025@01:15:00+0700
2025-08-04T01:00:04.702+07:00  INFO 83697 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 04/08/2025@01:00:00+0700 to 04/08/2025@01:15:00+0700
2025-08-04T01:01:06.819+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:01:49.901+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T01:01:49.904+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T01:02:03.929+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:03:05.995+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:03:49.110+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T01:03:49.137+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T01:04:03.162+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:05:06.286+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T01:05:06.291+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:05:48.375+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T01:05:48.379+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T01:06:02.404+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:07:05.519+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:07:52.616+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T01:07:52.624+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T01:08:06.649+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:09:04.783+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:09:51.863+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T01:09:51.867+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T01:10:06.899+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:10:06.901+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T01:11:03.991+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:11:52.116+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-08-04T01:11:52.125+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T01:12:06.150+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:13:03.256+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:13:52.350+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T01:13:52.355+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T01:14:06.378+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:15:02.466+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-04T01:15:02.468+07:00  INFO 83697 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 04/08/2025@01:15:02+0700
2025-08-04T01:15:02.481+07:00  INFO 83697 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 04/08/2025@01:15:00+0700 to 04/08/2025@01:30:00+0700
2025-08-04T01:15:02.482+07:00  INFO 83697 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 04/08/2025@01:15:00+0700 to 04/08/2025@01:30:00+0700
2025-08-04T01:15:02.482+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:15:02.482+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T01:15:51.576+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T01:15:51.579+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T01:16:05.600+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:17:06.719+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:17:50.810+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T01:17:50.816+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T01:18:04.837+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:19:06.943+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:19:50.018+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T01:19:50.025+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T01:20:04.052+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:20:04.054+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T01:21:06.170+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:21:49.269+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T01:21:49.276+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T01:22:03.303+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:23:06.413+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:23:48.476+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T01:23:48.480+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T01:24:02.506+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:25:05.616+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:25:05.618+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T01:25:52.688+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-08-04T01:25:52.694+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T01:26:06.720+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:27:04.835+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:27:51.906+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T01:27:51.910+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T01:28:06.955+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:29:04.064+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:29:52.165+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T01:29:52.175+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T01:30:06.196+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:30:06.199+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T01:30:06.200+07:00  INFO 83697 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 04/08/2025@01:30:06+0700
2025-08-04T01:30:06.218+07:00  INFO 83697 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 04/08/2025@01:30:00+0700 to 04/08/2025@01:45:00+0700
2025-08-04T01:30:06.218+07:00  INFO 83697 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 04/08/2025@01:30:00+0700 to 04/08/2025@01:45:00+0700
2025-08-04T01:30:06.219+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-04T01:31:03.324+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:31:52.450+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-08-04T01:31:52.461+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T01:32:06.484+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:33:02.577+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:33:51.663+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T01:33:51.668+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T01:34:05.692+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:35:06.791+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:35:06.793+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T01:35:50.872+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T01:35:50.878+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T01:36:04.904+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:37:06.999+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:37:50.069+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T01:37:50.071+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T01:38:04.094+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:39:06.192+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:39:49.278+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T01:39:49.286+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T01:40:03.314+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:40:03.315+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T01:41:06.443+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:41:48.507+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-08-04T01:41:48.510+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T01:42:02.535+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:43:05.633+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:43:52.723+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T01:43:52.728+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T01:44:06.750+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:45:04.852+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:45:04.854+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T01:45:04.854+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-04T01:45:04.855+07:00  INFO 83697 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 04/08/2025@01:45:04+0700
2025-08-04T01:45:04.870+07:00  INFO 83697 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 04/08/2025@01:45:00+0700 to 04/08/2025@02:00:00+0700
2025-08-04T01:45:04.871+07:00  INFO 83697 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 04/08/2025@01:45:00+0700 to 04/08/2025@02:00:00+0700
2025-08-04T01:45:51.953+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T01:45:51.961+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T01:46:06.993+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:47:04.101+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:47:52.203+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T01:47:52.213+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T01:48:06.237+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:49:03.339+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:49:52.439+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T01:49:52.449+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T01:50:06.469+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:50:06.470+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T01:51:02.558+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:51:51.642+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T01:51:51.645+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T01:52:05.663+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:53:06.763+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:53:50.848+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T01:53:50.856+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T01:54:04.881+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:55:06.978+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:55:06.980+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T01:55:50.059+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-08-04T01:55:50.063+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T01:56:04.088+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:57:06.202+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:57:49.295+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T01:57:49.303+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T01:58:03.324+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:59:06.430+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T01:59:48.505+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T01:59:48.512+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T02:00:02.517+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:00:02.517+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T02:00:02.518+07:00  INFO 83697 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 04/08/2025@02:00:02+0700
2025-08-04T02:00:02.537+07:00  INFO 83697 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 04/08/2025@02:00:00+0700 to 04/08/2025@02:15:00+0700
2025-08-04T02:00:02.537+07:00  INFO 83697 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 04/08/2025@02:00:00+0700 to 04/08/2025@02:15:00+0700
2025-08-04T02:00:02.537+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-04T02:00:02.538+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-08-04T02:01:05.621+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:01:52.699+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-08-04T02:01:52.704+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T02:02:06.729+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:03:04.822+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:03:51.883+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T02:03:51.885+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T02:04:06.916+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:05:04.014+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T02:05:04.014+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:05:52.102+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T02:05:52.109+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T02:06:06.133+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:07:03.212+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:07:52.293+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T02:07:52.299+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T02:08:06.320+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:09:02.419+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:09:51.505+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T02:09:51.508+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T02:10:05.531+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:10:05.532+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T02:11:06.630+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:11:50.707+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-08-04T02:11:50.712+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T02:12:04.735+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:13:06.831+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:13:49.910+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T02:13:49.912+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T02:14:03.930+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:15:06.031+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-04T02:15:06.032+07:00  INFO 83697 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 04/08/2025@02:15:06+0700
2025-08-04T02:15:06.039+07:00  INFO 83697 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 04/08/2025@02:15:00+0700 to 04/08/2025@02:30:00+0700
2025-08-04T02:15:06.039+07:00  INFO 83697 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 04/08/2025@02:15:00+0700 to 04/08/2025@02:30:00+0700
2025-08-04T02:15:06.039+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:15:06.040+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T02:15:49.116+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-08-04T02:15:49.124+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T02:16:03.148+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:17:06.258+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:17:48.325+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T02:17:48.328+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T02:18:02.349+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:19:05.461+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:19:52.535+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T02:19:52.542+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T02:20:06.564+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:20:06.566+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T02:21:04.676+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:21:52.743+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T02:21:52.747+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T02:22:06.770+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:23:03.865+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:23:51.949+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T02:23:51.952+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T02:24:06.979+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:25:03.070+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:25:03.072+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T02:25:52.152+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-08-04T02:25:52.156+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T02:26:06.179+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:27:02.262+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:27:51.351+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T02:27:51.354+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T02:28:05.372+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:29:06.465+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:29:50.564+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T02:29:50.571+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T02:30:04.602+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-04T02:30:04.602+07:00  INFO 83697 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 04/08/2025@02:30:04+0700
2025-08-04T02:30:04.611+07:00  INFO 83697 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 04/08/2025@02:30:00+0700 to 04/08/2025@02:45:00+0700
2025-08-04T02:30:04.611+07:00  INFO 83697 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 04/08/2025@02:30:00+0700 to 04/08/2025@02:45:00+0700
2025-08-04T02:30:04.612+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:30:04.612+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T02:31:06.716+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:31:49.783+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T02:31:49.790+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T02:32:03.813+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:33:06.927+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:33:48.991+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T02:33:48.998+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T02:34:03.019+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:35:06.122+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:35:06.124+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T02:35:48.194+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T02:35:48.197+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T02:36:02.223+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:37:05.328+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:37:52.419+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T02:37:52.427+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T02:38:06.456+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:39:04.592+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:39:52.726+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T02:39:52.729+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T02:40:06.773+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:40:06.773+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T02:41:03.925+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:41:52.000+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-08-04T02:41:52.003+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T02:42:06.028+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:43:03.116+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:43:52.201+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T02:43:52.207+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T02:44:06.225+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:45:02.320+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:45:02.321+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T02:45:02.322+07:00  INFO 83697 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 04/08/2025@02:45:02+0700
2025-08-04T02:45:02.331+07:00  INFO 83697 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 04/08/2025@02:45:00+0700 to 04/08/2025@03:00:00+0700
2025-08-04T02:45:02.332+07:00  INFO 83697 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 04/08/2025@02:45:00+0700 to 04/08/2025@03:00:00+0700
2025-08-04T02:45:02.332+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-04T02:45:51.426+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T02:45:51.435+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T02:46:05.465+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:47:06.560+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:47:50.648+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T02:47:50.657+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T02:48:04.679+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:49:06.782+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:49:49.853+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T02:49:49.855+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T02:50:03.877+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:50:03.878+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T02:51:06.981+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:51:49.062+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T02:51:49.067+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T02:52:03.087+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:53:06.187+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:53:48.249+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T02:53:48.251+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T02:54:02.274+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:55:05.380+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:55:05.381+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T02:55:52.464+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-08-04T02:55:52.471+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T02:56:06.489+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:57:04.593+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:57:52.702+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T02:57:52.709+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T02:58:06.732+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:59:03.807+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T02:59:52.888+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T02:59:52.893+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T03:00:06.917+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:00:06.917+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T03:00:06.917+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-04T03:00:06.918+07:00  INFO 83697 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 04/08/2025@03:00:06+0700
2025-08-04T03:00:06.928+07:00  INFO 83697 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 04/08/2025@03:00:00+0700 to 04/08/2025@03:15:00+0700
2025-08-04T03:00:06.928+07:00  INFO 83697 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 04/08/2025@03:00:00+0700 to 04/08/2025@03:15:00+0700
2025-08-04T03:00:06.928+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-08-04T03:00:06.929+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 3 hour
2025-08-04T03:01:03.022+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:01:52.103+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T03:01:52.110+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T03:02:06.138+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:03:02.229+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:03:51.322+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T03:03:51.332+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T03:04:05.364+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:05:06.457+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:05:06.459+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T03:05:50.533+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T03:05:50.536+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T03:06:04.561+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:07:06.692+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:07:49.790+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T03:07:49.794+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T03:08:03.815+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:09:06.916+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:09:48.986+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T03:09:48.989+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T03:10:03.007+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:10:03.008+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T03:11:06.115+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:11:48.199+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-08-04T03:11:48.209+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T03:12:02.232+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:13:05.317+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:13:52.388+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T03:13:52.395+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T03:14:06.417+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:15:04.508+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:15:04.509+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T03:15:04.509+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-04T03:15:04.510+07:00  INFO 83697 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 04/08/2025@03:15:04+0700
2025-08-04T03:15:04.523+07:00  INFO 83697 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 04/08/2025@03:15:00+0700 to 04/08/2025@03:30:00+0700
2025-08-04T03:15:04.523+07:00  INFO 83697 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 04/08/2025@03:15:00+0700 to 04/08/2025@03:30:00+0700
2025-08-04T03:15:52.603+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-08-04T03:15:52.617+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T03:16:06.638+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:17:03.766+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:17:52.868+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T03:17:52.874+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T03:18:06.900+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:19:03.003+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:19:52.103+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T03:19:52.105+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T03:20:06.128+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:20:06.128+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T03:21:02.215+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:21:51.295+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T03:21:51.302+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T03:22:05.330+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:23:06.458+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:23:50.536+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T03:23:50.553+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T03:24:04.580+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:25:06.680+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:25:06.682+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T03:25:49.777+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-08-04T03:25:49.781+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T03:26:03.804+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:27:06.910+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:27:49.018+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T03:27:49.022+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T03:28:03.059+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:29:06.259+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:29:48.401+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T03:29:48.408+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T03:30:02.453+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:30:02.453+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T03:30:02.454+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-04T03:30:02.455+07:00  INFO 83697 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 04/08/2025@03:30:02+0700
2025-08-04T03:30:02.465+07:00  INFO 83697 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 04/08/2025@03:30:00+0700 to 04/08/2025@03:45:00+0700
2025-08-04T03:30:02.465+07:00  INFO 83697 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 04/08/2025@03:30:00+0700 to 04/08/2025@03:45:00+0700
2025-08-04T03:31:05.561+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:31:52.636+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-08-04T03:31:52.643+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T03:32:06.666+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:33:04.762+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:33:52.851+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T03:33:52.856+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T03:34:06.880+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:35:03.968+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:35:03.970+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T03:35:52.054+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T03:35:52.056+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T03:36:06.081+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:37:03.179+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:37:52.277+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T03:37:52.286+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T03:38:06.311+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:39:02.395+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:39:51.478+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T03:39:51.482+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T03:40:05.508+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:40:05.509+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T03:41:06.604+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:41:50.686+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-08-04T03:41:50.693+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T03:42:04.725+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:43:06.835+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:43:49.906+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T03:43:49.909+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T03:44:03.927+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:45:06.021+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:45:06.022+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T03:45:07.027+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-04T03:45:07.028+07:00  INFO 83697 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 04/08/2025@03:45:07+0700
2025-08-04T03:45:07.042+07:00  INFO 83697 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 04/08/2025@03:45:00+0700 to 04/08/2025@04:00:00+0700
2025-08-04T03:45:07.043+07:00  INFO 83697 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 04/08/2025@03:45:00+0700 to 04/08/2025@04:00:00+0700
2025-08-04T03:45:49.118+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-08-04T03:45:49.127+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T03:46:03.152+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:47:06.242+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:47:48.313+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T03:47:48.318+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T03:48:02.343+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:49:05.434+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:49:52.522+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T03:49:52.531+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T03:50:06.556+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:50:06.557+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T03:51:04.660+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:51:52.737+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 6, expire count 0
2025-08-04T03:51:52.739+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T03:52:06.761+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:53:03.859+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:53:51.939+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T03:53:51.941+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T03:54:06.966+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:55:03.063+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T03:55:03.065+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:55:52.146+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 1
2025-08-04T03:55:52.154+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T03:56:06.179+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:57:02.277+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:57:51.358+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-08-04T03:57:51.361+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T03:58:05.382+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:59:03.211+07:00  INFO 83697 --- [qtp1972874802-1051] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0sabmh4pxx58unhgnqu5f7n5x4
2025-08-04T03:59:03.327+07:00  INFO 83697 --- [qtp1972874802-1051] n.d.module.session.ClientSessionManager  : Add a client session id = node0sabmh4pxx58unhgnqu5f7n5x4, token = 18223b569ea22f282b0eddc7c857a754
2025-08-04T03:59:03.335+07:00  INFO 83697 --- [qtp1972874802-1051] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T03:59:03.344+07:00  INFO 83697 --- [qtp1972874802-611] n.d.m.c.s.http.SystemRPCController       : Run the script from 127.0.0.1, by user nhat.le
2025-08-04T03:59:03.344+07:00  INFO 83697 --- [qtp1972874802-611] n.d.m.c.s.http.SystemRPCController       : Script Model: 
{
  "scriptDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/src/main/groovy",
  "scriptDataDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/data",
  "scripts" : [ ],
  "script" : "migration/server/hr/CheckAccount.groovy"
}
2025-08-04T03:59:06.470+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T03:59:50.564+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 4
2025-08-04T03:59:50.581+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T04:00:04.603+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-04T04:00:04.604+07:00  INFO 83697 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 04/08/2025@04:00:04+0700
2025-08-04T04:00:04.616+07:00  INFO 83697 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 04/08/2025@04:00:00+0700 to 04/08/2025@04:15:00+0700
2025-08-04T04:00:04.616+07:00  INFO 83697 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 04/08/2025@04:00:00+0700 to 04/08/2025@04:15:00+0700
2025-08-04T04:00:04.617+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T04:00:04.617+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T04:00:04.617+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-08-04T04:01:06.720+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T04:01:49.803+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-08-04T04:01:49.812+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T04:02:03.838+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T04:03:06.946+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T04:03:49.031+07:00  INFO 83697 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T04:03:49.036+07:00  INFO 83697 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T04:04:03.063+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T04:05:06.165+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T04:05:06.166+07:00  INFO 83697 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T04:05:27.625+07:00  INFO 83697 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@5aa571db{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-08-04T04:05:27.626+07:00  INFO 83697 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-08-04T04:05:27.626+07:00  INFO 83697 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-08-04T04:05:27.626+07:00  INFO 83697 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-08-04T04:05:27.626+07:00  INFO 83697 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-08-04T04:05:27.626+07:00  INFO 83697 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-08-04T04:05:27.626+07:00  INFO 83697 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-08-04T04:05:27.626+07:00  INFO 83697 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-08-04T04:05:27.626+07:00  INFO 83697 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-08-04T04:05:27.626+07:00  INFO 83697 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-08-04T04:05:27.626+07:00  INFO 83697 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-08-04T04:05:27.627+07:00  INFO 83697 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-08-04T04:05:27.627+07:00  INFO 83697 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-08-04T04:05:27.627+07:00  INFO 83697 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-08-04T04:05:27.627+07:00  INFO 83697 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-08-04T04:05:27.627+07:00  INFO 83697 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-08-04T04:05:27.640+07:00  INFO 83697 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T04:05:27.700+07:00  INFO 83697 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-08-04T04:05:27.705+07:00  INFO 83697 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-08-04T04:05:27.729+07:00  INFO 83697 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-04T04:05:27.730+07:00  INFO 83697 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-04T04:05:27.731+07:00  INFO 83697 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-04T04:05:27.731+07:00  INFO 83697 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-08-04T04:05:27.732+07:00  INFO 83697 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-08-04T04:05:27.732+07:00  INFO 83697 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-08-04T04:05:27.732+07:00  INFO 83697 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-08-04T04:05:27.732+07:00  INFO 83697 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-08-04T04:05:27.874+07:00  INFO 83697 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-08-04T04:05:27.875+07:00  INFO 83697 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-08-04T04:05:27.875+07:00  INFO 83697 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-08-04T04:05:27.875+07:00  INFO 83697 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-08-04T04:05:27.875+07:00  INFO 83697 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-08-04T04:05:27.877+07:00  INFO 83697 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@33cee31a{STOPPING}[12.0.15,sto=0]
2025-08-04T04:05:27.880+07:00  INFO 83697 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-04T04:05:27.882+07:00  INFO 83697 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@22d82fe8{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.4225080948789362293/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@74c74c99{STOPPED}}
