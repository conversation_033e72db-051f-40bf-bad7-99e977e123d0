2025-08-04T14:50:49.723+07:00  INFO 65144 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 65144 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-08-04T14:50:49.724+07:00  INFO 65144 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-08-04T14:50:50.417+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T14:50:50.483+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 63 ms. Found 22 JPA repository interfaces.
2025-08-04T14:50:50.492+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T14:50:50.493+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-08-04T14:50:50.494+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T14:50:50.535+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 41 ms. Found 9 JPA repository interfaces.
2025-08-04T14:50:50.536+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T14:50:50.540+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 3 JPA repository interfaces.
2025-08-04T14:50:50.541+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T14:50:50.545+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-08-04T14:50:50.556+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T14:50:50.561+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 1 JPA repository interface.
2025-08-04T14:50:50.570+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T14:50:50.574+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 5 JPA repository interfaces.
2025-08-04T14:50:50.578+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T14:50:50.580+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-08-04T14:50:50.580+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T14:50:50.581+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-08-04T14:50:50.585+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T14:50:50.592+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 10 JPA repository interfaces.
2025-08-04T14:50:50.596+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T14:50:50.599+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-08-04T14:50:50.599+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T14:50:50.603+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-08-04T14:50:50.604+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T14:50:50.611+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-08-04T14:50:50.611+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T14:50:50.614+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-08-04T14:50:50.614+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T14:50:50.614+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-08-04T14:50:50.614+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T14:50:50.615+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-08-04T14:50:50.615+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T14:50:50.620+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-08-04T14:50:50.620+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T14:50:50.622+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-08-04T14:50:50.622+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T14:50:50.622+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-08-04T14:50:50.622+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T14:50:50.632+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 19 JPA repository interfaces.
2025-08-04T14:50:50.641+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T14:50:50.646+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 8 JPA repository interfaces.
2025-08-04T14:50:50.647+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T14:50:50.649+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-08-04T14:50:50.649+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T14:50:50.653+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-08-04T14:50:50.654+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T14:50:50.659+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-08-04T14:50:50.659+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T14:50:50.663+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-08-04T14:50:50.663+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T14:50:50.666+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-08-04T14:50:50.666+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T14:50:50.671+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-08-04T14:50:50.671+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T14:50:50.679+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 14 JPA repository interfaces.
2025-08-04T14:50:50.679+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T14:50:50.691+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 11 ms. Found 20 JPA repository interfaces.
2025-08-04T14:50:50.691+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T14:50:50.692+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-08-04T14:50:50.698+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T14:50:50.698+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-08-04T14:50:50.698+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T14:50:50.705+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-08-04T14:50:50.706+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T14:50:50.742+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 35 ms. Found 69 JPA repository interfaces.
2025-08-04T14:50:50.742+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T14:50:50.743+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-08-04T14:50:50.748+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T14:50:50.751+07:00  INFO 65144 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-08-04T14:50:50.954+07:00  INFO 65144 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-08-04T14:50:50.958+07:00  INFO 65144 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-08-04T14:50:51.225+07:00  WARN 65144 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-08-04T14:50:51.435+07:00  INFO 65144 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-08-04T14:50:51.438+07:00  INFO 65144 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-08-04T14:50:51.451+07:00  INFO 65144 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-08-04T14:50:51.451+07:00  INFO 65144 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1628 ms
2025-08-04T14:50:51.505+07:00  WARN 65144 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-04T14:50:51.505+07:00  INFO 65144 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-08-04T14:50:51.601+07:00  INFO 65144 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@61236349
2025-08-04T14:50:51.602+07:00  INFO 65144 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-08-04T14:50:51.607+07:00  WARN 65144 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-04T14:50:51.607+07:00  INFO 65144 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-08-04T14:50:51.612+07:00  INFO 65144 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@3df920
2025-08-04T14:50:51.613+07:00  INFO 65144 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-08-04T14:50:51.613+07:00  WARN 65144 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-04T14:50:51.613+07:00  INFO 65144 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-04T14:50:52.075+07:00  INFO 65144 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@1f3c3291
2025-08-04T14:50:52.075+07:00  INFO 65144 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-04T14:50:52.075+07:00  WARN 65144 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-04T14:50:52.075+07:00  INFO 65144 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-08-04T14:50:52.126+07:00  INFO 65144 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@2fad48b7
2025-08-04T14:50:52.126+07:00  INFO 65144 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-08-04T14:50:52.127+07:00  WARN 65144 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-04T14:50:52.127+07:00  INFO 65144 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-08-04T14:50:52.151+07:00  INFO 65144 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@2d4e6bb2
2025-08-04T14:50:52.151+07:00  INFO 65144 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-08-04T14:50:52.152+07:00  INFO 65144 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '****************************************************', '***********************************************', '**********************************************'
2025-08-04T14:50:52.213+07:00  INFO 65144 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-08-04T14:50:52.215+07:00  INFO 65144 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@65dac812{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.3199363854414744550/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@7de4bd8f{STARTED}}
2025-08-04T14:50:52.216+07:00  INFO 65144 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@65dac812{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.3199363854414744550/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@7de4bd8f{STARTED}}
2025-08-04T14:50:52.217+07:00  INFO 65144 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@47b6f580{STARTING}[12.0.15,sto=0] @3181ms
2025-08-04T14:50:52.280+07:00  INFO 65144 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-04T14:50:52.310+07:00  INFO 65144 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-08-04T14:50:52.326+07:00  INFO 65144 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-04T14:50:52.448+07:00  INFO 65144 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-04T14:50:52.472+07:00  WARN 65144 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-04T14:50:53.104+07:00  INFO 65144 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-04T14:50:53.114+07:00  INFO 65144 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@1f7d3d82] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-08-04T14:50:53.234+07:00  INFO 65144 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-04T14:50:53.425+07:00  INFO 65144 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.kpi", "net.datatp.module.graphapi", "net.datatp.module.monitor.activity", "net.datatp.module.wfms", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "net.datatp.module.okr", "cloud.datatp.module.fleet", "net.datatp.module.core.template", "cloud.datatp.module.pegasus.shipment", "cloud.datatp.gps", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "cloud.datatp.tms", "net.datatp.module.groovy", "net.datatp.module.company.tmpl", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "net.datatp.module.resource", "cloud.datatp.vendor", "net.datatp.module.hr", "net.datatp.module.company.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "net.datatp.module.core.print", "net.datatp.module.chatbot", "cloud.datatp.module.odoo", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.accounting", "net.datatp.module.zalo" ]
2025-08-04T14:50:53.427+07:00  INFO 65144 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-08-04T14:50:53.433+07:00  INFO 65144 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-04T14:50:53.435+07:00  INFO 65144 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-04T14:50:53.462+07:00  INFO 65144 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-04T14:50:53.465+07:00  WARN 65144 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-04T14:50:55.450+07:00  INFO 65144 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-04T14:50:55.451+07:00  INFO 65144 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@3ce47681] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-08-04T14:50:55.628+07:00  WARN 65144 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-08-04T14:50:55.628+07:00  WARN 65144 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-08-04T14:50:55.633+07:00  WARN 65144 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-08-04T14:50:55.633+07:00  WARN 65144 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-08-04T14:50:55.650+07:00  WARN 65144 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-08-04T14:50:55.650+07:00  WARN 65144 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-08-04T14:50:56.023+07:00  INFO 65144 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-04T14:50:56.030+07:00  INFO 65144 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-04T14:50:56.032+07:00  INFO 65144 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-04T14:50:56.052+07:00  INFO 65144 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-04T14:50:56.054+07:00  WARN 65144 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-04T14:50:56.530+07:00  INFO 65144 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-04T14:50:56.530+07:00  INFO 65144 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@74c5328c] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-08-04T14:50:56.583+07:00  WARN 65144 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-08-04T14:50:56.584+07:00  WARN 65144 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-08-04T14:50:56.970+07:00  INFO 65144 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-04T14:50:57.000+07:00  INFO 65144 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-08-04T14:50:57.005+07:00  INFO 65144 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-08-04T14:50:57.005+07:00  INFO 65144 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-08-04T14:50:57.012+07:00  WARN 65144 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-08-04T14:50:57.138+07:00  INFO 65144 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-08-04T14:50:57.603+07:00  INFO 65144 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-08-04T14:50:57.607+07:00  INFO 65144 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-08-04T14:50:57.643+07:00  INFO 65144 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-08-04T14:50:57.681+07:00  INFO 65144 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-08-04T14:50:57.740+07:00  INFO 65144 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-08-04T14:50:57.767+07:00  INFO 65144 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-08-04T14:50:57.793+07:00  INFO 65144 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 184070592ms : this is harmless.
2025-08-04T14:50:57.802+07:00  INFO 65144 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-08-04T14:50:57.805+07:00  INFO 65144 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-08-04T14:50:57.816+07:00  INFO 65144 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 866302396ms : this is harmless.
2025-08-04T14:50:57.817+07:00  INFO 65144 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-08-04T14:50:57.830+07:00  INFO 65144 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-08-04T14:50:57.831+07:00  INFO 65144 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-08-04T14:51:00.061+07:00  INFO 65144 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-08-04T14:51:00.061+07:00  INFO 65144 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-08-04T14:51:00.061+07:00  WARN 65144 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-08-04T14:51:00.174+07:00  INFO 65144 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 04/08/2025@14:45:00+0700 to 04/08/2025@15:00:00+0700
2025-08-04T14:51:00.174+07:00  INFO 65144 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 04/08/2025@14:45:00+0700 to 04/08/2025@15:00:00+0700
2025-08-04T14:51:00.598+07:00  INFO 65144 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-08-04T14:51:00.598+07:00  INFO 65144 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-08-04T14:51:00.598+07:00  WARN 65144 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-08-04T14:51:00.808+07:00  INFO 65144 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-08-04T14:51:00.808+07:00  INFO 65144 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-08-04T14:51:00.808+07:00  INFO 65144 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-08-04T14:51:00.808+07:00  INFO 65144 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-08-04T14:51:00.808+07:00  INFO 65144 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-08-04T14:51:02.473+07:00  WARN 65144 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 05b8b8ac-6d9c-43db-bb4c-3cc54b211ef5

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-04T14:51:02.476+07:00  INFO 65144 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-04T14:51:02.775+07:00  INFO 65144 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-08-04T14:51:02.775+07:00  INFO 65144 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-08-04T14:51:02.775+07:00  INFO 65144 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-08-04T14:51:02.775+07:00  INFO 65144 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-08-04T14:51:02.775+07:00  INFO 65144 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-08-04T14:51:02.775+07:00  INFO 65144 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-08-04T14:51:02.775+07:00  INFO 65144 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-08-04T14:51:02.776+07:00  INFO 65144 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-08-04T14:51:02.776+07:00  INFO 65144 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-08-04T14:51:02.776+07:00  INFO 65144 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-08-04T14:51:02.776+07:00  INFO 65144 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-08-04T14:51:02.776+07:00  INFO 65144 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-08-04T14:51:02.776+07:00  INFO 65144 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-08-04T14:51:02.776+07:00  INFO 65144 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-08-04T14:51:02.777+07:00  INFO 65144 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-08-04T14:51:02.831+07:00  INFO 65144 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-04T14:51:02.831+07:00  INFO 65144 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-04T14:51:02.832+07:00  INFO 65144 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-08-04T14:51:02.840+07:00  INFO 65144 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@78207354{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-08-04T14:51:02.841+07:00  INFO 65144 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-08-04T14:51:02.842+07:00  INFO 65144 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-08-04T14:51:02.870+07:00  INFO 65144 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-08-04T14:51:02.871+07:00  INFO 65144 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-08-04T14:51:02.876+07:00  INFO 65144 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 13.464 seconds (process running for 13.84)
2025-08-04T14:51:04.870+07:00  INFO 65144 --- [qtp736402572-36] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-08-04T14:51:12.898+07:00  INFO 65144 --- [qtp736402572-35] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01edrxftz438ff17thjqezdo03o0
2025-08-04T14:51:12.898+07:00  INFO 65144 --- [qtp736402572-40] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0mbuvpa5juyxn1i37y2ut2nf5x1
2025-08-04T14:51:12.961+07:00  INFO 65144 --- [qtp736402572-40] n.d.module.session.ClientSessionManager  : Add a client session id = node0mbuvpa5juyxn1i37y2ut2nf5x1, token = f6a0550846900fb971c852713bb78016
2025-08-04T14:51:12.961+07:00  INFO 65144 --- [qtp736402572-35] n.d.module.session.ClientSessionManager  : Add a client session id = node01edrxftz438ff17thjqezdo03o0, token = f6a0550846900fb971c852713bb78016
2025-08-04T14:51:13.384+07:00  INFO 65144 --- [qtp736402572-40] n.d.m.c.a.CompanyAuthenticationService   : User max.vnhph is logged in successfully system
2025-08-04T14:51:13.390+07:00  INFO 65144 --- [qtp736402572-35] n.d.m.c.a.CompanyAuthenticationService   : User max.vnhph is logged in successfully system
2025-08-04T14:51:21.493+07:00  INFO 65144 --- [qtp736402572-60] n.d.module.session.ClientSessionManager  : Add a client session id = node0mbuvpa5juyxn1i37y2ut2nf5x1, token = f6a0550846900fb971c852713bb78016
2025-08-04T14:51:21.500+07:00  INFO 65144 --- [qtp736402572-60] n.d.m.c.a.CompanyAuthenticationService   : User max.vnhph is logged in successfully system
2025-08-04T14:51:21.513+07:00  INFO 65144 --- [qtp736402572-41] n.d.module.session.ClientSessionManager  : Add a client session id = node0mbuvpa5juyxn1i37y2ut2nf5x1, token = f6a0550846900fb971c852713bb78016
2025-08-04T14:51:21.521+07:00  INFO 65144 --- [qtp736402572-41] n.d.m.c.a.CompanyAuthenticationService   : User max.vnhph is logged in successfully system
2025-08-04T14:52:05.900+07:00  INFO 65144 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T14:52:05.905+07:00  INFO 65144 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T14:52:05.914+07:00  INFO 65144 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T14:52:54.781+07:00  INFO 65144 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@78207354{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-08-04T14:52:54.784+07:00  INFO 65144 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-08-04T14:52:54.784+07:00  INFO 65144 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-08-04T14:52:54.784+07:00  INFO 65144 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-08-04T14:52:54.784+07:00  INFO 65144 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-08-04T14:52:54.785+07:00  INFO 65144 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-08-04T14:52:54.785+07:00  INFO 65144 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-08-04T14:52:54.785+07:00  INFO 65144 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-08-04T14:52:54.785+07:00  INFO 65144 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-08-04T14:52:54.785+07:00  INFO 65144 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-08-04T14:52:54.785+07:00  INFO 65144 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-08-04T14:52:54.785+07:00  INFO 65144 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-08-04T14:52:54.785+07:00  INFO 65144 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-08-04T14:52:54.785+07:00  INFO 65144 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-08-04T14:52:54.785+07:00  INFO 65144 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-08-04T14:52:54.785+07:00  INFO 65144 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-08-04T14:52:54.801+07:00  INFO 65144 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T14:52:54.964+07:00  INFO 65144 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-08-04T14:52:54.972+07:00  INFO 65144 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-08-04T14:52:55.013+07:00  INFO 65144 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-04T14:52:55.014+07:00  INFO 65144 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-04T14:52:55.021+07:00  INFO 65144 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-04T14:52:55.021+07:00  INFO 65144 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-08-04T14:52:55.026+07:00  INFO 65144 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-08-04T14:52:55.027+07:00  INFO 65144 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-08-04T14:52:55.031+07:00  INFO 65144 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-08-04T14:52:55.031+07:00  INFO 65144 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-08-04T14:52:55.170+07:00  INFO 65144 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-08-04T14:52:55.170+07:00  INFO 65144 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-08-04T14:52:55.171+07:00  INFO 65144 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-08-04T14:52:55.171+07:00  INFO 65144 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-08-04T14:52:55.172+07:00  INFO 65144 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-08-04T14:52:55.174+07:00  INFO 65144 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@47b6f580{STOPPING}[12.0.15,sto=0]
2025-08-04T14:52:55.176+07:00  INFO 65144 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-04T14:52:55.178+07:00  INFO 65144 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@65dac812{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.3199363854414744550/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@7de4bd8f{STOPPED}}
