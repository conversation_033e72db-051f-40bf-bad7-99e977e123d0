2025-08-01T10:26:06.409+07:00  INFO 57538 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 57538 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-08-01T10:26:06.409+07:00  INFO 57538 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-08-01T10:26:07.124+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:26:07.191+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 63 ms. Found 22 JPA repository interfaces.
2025-08-01T10:26:07.203+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:26:07.204+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-08-01T10:26:07.204+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:26:07.211+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 9 JPA repository interfaces.
2025-08-01T10:26:07.212+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:26:07.251+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 38 ms. Found 3 JPA repository interfaces.
2025-08-01T10:26:07.251+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:26:07.255+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-08-01T10:26:07.267+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:26:07.272+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 1 JPA repository interface.
2025-08-01T10:26:07.282+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:26:07.286+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 5 JPA repository interfaces.
2025-08-01T10:26:07.290+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:26:07.293+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-08-01T10:26:07.293+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:26:07.293+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-08-01T10:26:07.298+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:26:07.304+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 10 JPA repository interfaces.
2025-08-01T10:26:07.308+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:26:07.311+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-08-01T10:26:07.311+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:26:07.315+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-08-01T10:26:07.316+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:26:07.323+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-08-01T10:26:07.323+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:26:07.326+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-08-01T10:26:07.326+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:26:07.326+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-08-01T10:26:07.326+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:26:07.327+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-08-01T10:26:07.327+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:26:07.331+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-08-01T10:26:07.331+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:26:07.332+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-08-01T10:26:07.332+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:26:07.333+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-08-01T10:26:07.333+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:26:07.342+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 19 JPA repository interfaces.
2025-08-01T10:26:07.352+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:26:07.357+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 8 JPA repository interfaces.
2025-08-01T10:26:07.358+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:26:07.360+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-08-01T10:26:07.360+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:26:07.365+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-08-01T10:26:07.365+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:26:07.370+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 9 JPA repository interfaces.
2025-08-01T10:26:07.370+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:26:07.374+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-08-01T10:26:07.374+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:26:07.377+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 5 JPA repository interfaces.
2025-08-01T10:26:07.378+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:26:07.382+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-08-01T10:26:07.382+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:26:07.391+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 14 JPA repository interfaces.
2025-08-01T10:26:07.391+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:26:07.403+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 11 ms. Found 20 JPA repository interfaces.
2025-08-01T10:26:07.403+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:26:07.404+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-08-01T10:26:07.409+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:26:07.410+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-08-01T10:26:07.410+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:26:07.417+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-08-01T10:26:07.419+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:26:07.455+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 35 ms. Found 69 JPA repository interfaces.
2025-08-01T10:26:07.455+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:26:07.456+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-08-01T10:26:07.461+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-01T10:26:07.463+07:00  INFO 57538 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-08-01T10:26:07.677+07:00  INFO 57538 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-08-01T10:26:07.681+07:00  INFO 57538 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-08-01T10:26:07.961+07:00  WARN 57538 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-08-01T10:26:08.161+07:00  INFO 57538 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-08-01T10:26:08.163+07:00  INFO 57538 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-08-01T10:26:08.174+07:00  INFO 57538 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-08-01T10:26:08.175+07:00  INFO 57538 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1641 ms
2025-08-01T10:26:08.228+07:00  WARN 57538 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-01T10:26:08.228+07:00  INFO 57538 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-08-01T10:26:08.396+07:00  INFO 57538 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@29617475
2025-08-01T10:26:08.397+07:00  INFO 57538 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-08-01T10:26:08.402+07:00  WARN 57538 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-01T10:26:08.402+07:00  INFO 57538 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-08-01T10:26:08.409+07:00  INFO 57538 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@12de62aa
2025-08-01T10:26:08.409+07:00  INFO 57538 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-08-01T10:26:08.409+07:00  WARN 57538 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-01T10:26:08.409+07:00  INFO 57538 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-01T10:26:08.841+07:00  INFO 57538 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@64bcf689
2025-08-01T10:26:08.841+07:00  INFO 57538 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-01T10:26:08.841+07:00  WARN 57538 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-01T10:26:08.841+07:00  INFO 57538 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-08-01T10:26:08.850+07:00  INFO 57538 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@21584b99
2025-08-01T10:26:08.850+07:00  INFO 57538 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-08-01T10:26:08.850+07:00  WARN 57538 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-01T10:26:08.850+07:00  INFO 57538 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-08-01T10:26:08.858+07:00  INFO 57538 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@56b4951a
2025-08-01T10:26:08.858+07:00  INFO 57538 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-08-01T10:26:08.858+07:00  INFO 57538 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '****************************************************', '***********************************************', '**********************************************'
2025-08-01T10:26:08.911+07:00  INFO 57538 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-08-01T10:26:08.913+07:00  INFO 57538 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@7505ef47{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.1366672937543224676/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@68fe866b{STARTED}}
2025-08-01T10:26:08.914+07:00  INFO 57538 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@7505ef47{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.1366672937543224676/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@68fe866b{STARTED}}
2025-08-01T10:26:08.915+07:00  INFO 57538 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@22ca5fe1{STARTING}[12.0.15,sto=0] @3095ms
2025-08-01T10:26:08.970+07:00  INFO 57538 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-01T10:26:09.000+07:00  INFO 57538 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-08-01T10:26:09.014+07:00  INFO 57538 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-01T10:26:09.133+07:00  INFO 57538 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-01T10:26:09.164+07:00  WARN 57538 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-01T10:26:09.776+07:00  INFO 57538 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-01T10:26:09.787+07:00  INFO 57538 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@212c969a] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-08-01T10:26:09.906+07:00  INFO 57538 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-01T10:26:10.082+07:00  INFO 57538 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.graphapi", "net.datatp.module.kpi", "net.datatp.module.monitor.activity", "net.datatp.module.wfms", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "cloud.datatp.module.fleet", "net.datatp.module.okr", "net.datatp.module.core.template", "cloud.datatp.gps", "cloud.datatp.module.pegasus.shipment", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "net.datatp.module.company.tmpl", "net.datatp.module.groovy", "cloud.datatp.tms", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "cloud.datatp.vendor", "net.datatp.module.resource", "net.datatp.module.hr", "net.datatp.module.company.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "cloud.datatp.module.odoo", "net.datatp.module.core.print", "net.datatp.module.chatbot", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.accounting", "net.datatp.module.zalo" ]
2025-08-01T10:26:10.084+07:00  INFO 57538 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-08-01T10:26:10.090+07:00  INFO 57538 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-01T10:26:10.091+07:00  INFO 57538 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-01T10:26:10.116+07:00  INFO 57538 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-01T10:26:10.123+07:00  WARN 57538 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-01T10:26:12.142+07:00  INFO 57538 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-01T10:26:12.143+07:00  INFO 57538 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@10e41bde] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-08-01T10:26:12.353+07:00  WARN 57538 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-08-01T10:26:12.353+07:00  WARN 57538 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-08-01T10:26:12.366+07:00  WARN 57538 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-08-01T10:26:12.366+07:00  WARN 57538 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-08-01T10:26:12.381+07:00  WARN 57538 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-08-01T10:26:12.381+07:00  WARN 57538 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-08-01T10:26:12.847+07:00  INFO 57538 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-01T10:26:12.853+07:00  INFO 57538 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-01T10:26:12.854+07:00  INFO 57538 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-01T10:26:12.867+07:00  INFO 57538 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-01T10:26:12.871+07:00  WARN 57538 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-01T10:26:13.376+07:00  INFO 57538 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-01T10:26:13.376+07:00  INFO 57538 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@5032ce3b] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-08-01T10:26:13.470+07:00  WARN 57538 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-08-01T10:26:13.470+07:00  WARN 57538 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-08-01T10:26:13.835+07:00  INFO 57538 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-01T10:26:13.868+07:00  INFO 57538 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-08-01T10:26:13.873+07:00  INFO 57538 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-08-01T10:26:13.873+07:00  INFO 57538 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-08-01T10:26:13.880+07:00  WARN 57538 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-08-01T10:26:14.020+07:00  INFO 57538 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-08-01T10:26:14.514+07:00  INFO 57538 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-08-01T10:26:14.517+07:00  INFO 57538 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-08-01T10:26:14.552+07:00  INFO 57538 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-08-01T10:26:14.598+07:00  INFO 57538 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-08-01T10:26:14.613+07:00  INFO 57538 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-08-01T10:26:14.642+07:00  INFO 57538 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-08-01T10:26:14.669+07:00  INFO 57538 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 256626742ms : this is harmless.
2025-08-01T10:26:14.678+07:00  INFO 57538 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-08-01T10:26:14.681+07:00  INFO 57538 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-08-01T10:26:14.697+07:00  INFO 57538 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 591218887ms : this is harmless.
2025-08-01T10:26:14.699+07:00  INFO 57538 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-08-01T10:26:14.713+07:00  INFO 57538 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-08-01T10:26:14.713+07:00  INFO 57538 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-08-01T10:26:17.315+07:00  INFO 57538 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-08-01T10:26:17.315+07:00  INFO 57538 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-08-01T10:26:17.315+07:00  WARN 57538 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-08-01T10:26:17.584+07:00  INFO 57538 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-08-01T10:26:17.584+07:00  INFO 57538 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-08-01T10:26:17.585+07:00  WARN 57538 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-08-01T10:26:17.863+07:00  INFO 57538 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-08-01T10:26:17.863+07:00  INFO 57538 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-08-01T10:26:17.863+07:00  INFO 57538 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-08-01T10:26:17.863+07:00  INFO 57538 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-08-01T10:26:17.863+07:00  INFO 57538 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-08-01T10:26:18.416+07:00  INFO 57538 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 01/08/2025@10:15:00+0700 to 01/08/2025@10:30:00+0700
2025-08-01T10:26:18.416+07:00  INFO 57538 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 01/08/2025@10:15:00+0700 to 01/08/2025@10:30:00+0700
2025-08-01T10:26:19.989+07:00  WARN 57538 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 3d21492b-6539-4acd-8a71-496f6e54d403

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-01T10:26:19.992+07:00  INFO 57538 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-01T10:26:20.326+07:00  INFO 57538 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-08-01T10:26:20.326+07:00  INFO 57538 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-08-01T10:26:20.327+07:00  INFO 57538 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-08-01T10:26:20.327+07:00  INFO 57538 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-08-01T10:26:20.327+07:00  INFO 57538 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-08-01T10:26:20.327+07:00  INFO 57538 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-08-01T10:26:20.327+07:00  INFO 57538 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-08-01T10:26:20.327+07:00  INFO 57538 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-08-01T10:26:20.327+07:00  INFO 57538 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-08-01T10:26:20.327+07:00  INFO 57538 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-08-01T10:26:20.327+07:00  INFO 57538 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-08-01T10:26:20.327+07:00  INFO 57538 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-08-01T10:26:20.330+07:00  INFO 57538 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-08-01T10:26:20.330+07:00  INFO 57538 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-08-01T10:26:20.330+07:00  INFO 57538 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-08-01T10:26:20.348+07:00  INFO 57538 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-01T10:26:20.348+07:00  INFO 57538 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-01T10:26:20.349+07:00  INFO 57538 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-08-01T10:26:20.359+07:00  INFO 57538 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@3e6520f3{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-08-01T10:26:20.360+07:00  INFO 57538 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-08-01T10:26:20.361+07:00  INFO 57538 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-08-01T10:26:20.392+07:00  INFO 57538 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-08-01T10:26:20.392+07:00  INFO 57538 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-08-01T10:26:20.399+07:00  INFO 57538 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 14.316 seconds (process running for 14.58)
2025-08-01T10:26:31.941+07:00  INFO 57538 --- [qtp791217259-35] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node04cbi2hmt3lb6hhc6q9pnzh7r0
2025-08-01T10:26:31.941+07:00  INFO 57538 --- [qtp791217259-37] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0cmt8zftvo88ykmhn4o4vhnfb1
2025-08-01T10:26:32.058+07:00  INFO 57538 --- [qtp791217259-37] n.d.module.session.ClientSessionManager  : Add a client session id = node0cmt8zftvo88ykmhn4o4vhnfb1, token = 9e57ccec8e98a2e2c2d25e3766e4540f
2025-08-01T10:26:32.058+07:00  INFO 57538 --- [qtp791217259-35] n.d.module.session.ClientSessionManager  : Add a client session id = node04cbi2hmt3lb6hhc6q9pnzh7r0, token = 9e57ccec8e98a2e2c2d25e3766e4540f
2025-08-01T10:26:32.534+07:00  INFO 57538 --- [qtp791217259-35] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-01T10:26:32.541+07:00  INFO 57538 --- [qtp791217259-37] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-01T10:26:35.562+07:00 ERROR 57538 --- [qtp791217259-39] n.datatp.lib.executable.ExecutableUnit   : Error: 

org.springframework.jdbc.BadSqlGrammarException: PreparedStatementCallback; bad SQL grammar [
        SELECT 
          cell."id"                      AS id,
          cell."status"                  AS status,
          cell."internal_issue_count"    AS internal_issue_count,
          cell."external_issue_count"    AS external_issue_count,
          cell."job_tracking_id"         AS job_tracking_id,
          cell."job_tracking_project_id" AS job_tracking_project_id,
          cell."job_tracking_column_id"  AS job_tracking_column_id,
          cell."start_time"              AS start_time,
          cell."end_time"                AS end_time,
          cell."string_value"            AS string_value,
          cell."double_value"            AS double_value,
          cell."boolean_value"           AS boolean_value,
          cell."date_value"              AS date_value,
          cell."version"                 AS version,
          col."name"                     AS name,
          col."data_type"                AS data_type,
          col."data_input_type"          AS data_input_type
        FROM lgc_job_tracking_cell cell
        JOIN lgc_job_tracking_column col ON lgc_job_tracking_column.id = lgc_job_tracking_cell.job_tracking_column_id
        WHERE 1=1
           AND cell.job_tracking_id IN (247779, 247777, 247776, 247774, 247746, 247745, 247744, 247729, 247725, 247708, 247707, 247706, 247700, 247699, 247698, 247695, 247680, 247678, 247654, 247653, 247652, 247641, 247640, 247631, 247630, 247628, 247627, 247626, 247625, 247624, 247623, 247609, 247601, 247585, 247584, 247574, 247570, 247569, 247562, 247560, 247559, 247549, 247535, 247534, 247533, 247532, 247531, 247529, 247528, 247526, 247509, 247503, 247500, 247491, 247461, 247455, 247451, 247449, 247445, 247388, 247387, 247386, 247384, 247383, 247382, 247381, 247380, 247379, 247378, 247374, 247364, 247363, 247354, 247345, 247344, 247343, 247333, 247332, 247331, 247330, 247305, 247272, 247271, 247265, 247264, 247243, 247242, 247241, 247234, 247233, 247218, 247188, 247185, 247182, 247180, 247178, 247147, 247138, 247137, 247135, 247134, 247132, 247131, 247128, 247127, 247126, 247125, 247123, 247122, 247121, 247104, 247100, 247030, 246763, 246762, 246738, 246737, 246736, 246735, 246734, 246733, 246732, 246731, 246730, 246729, 246728, 246715, 246714, 246713, 246712, 246711, 246710, 246692, 246663, 246661, 246644, 246643, 246633, 246632, 246631, 246630, 246629, 246628, 246627, 246626, 246625, 246624, 246623, 246622, 246621, 246620, 246619, 246618, 246617, 246616, 246615, 246614, 246613, 246612, 246611, 246610, 246609, 246608, 246606, 246605, 246604, 246603, 246602, 246601, 246600, 246599, 246598, 246597, 246596, 246595, 246594, 246593, 246592, 246591, 246590, 246589, 246588, 246587, 246586, 246585, 246584, 246583, 246582, 246581, 246580, 246579, 246578, 246577, 246576, 246575, 246574, 246573, 246572, 246571, 246570, 246569, 246568, 246567, 246566, 246565, 246564, 246563, 246562, 246561, 246559, 246558, 246557, 246556, 246555, 246554, 246553, 246552, 246551, 246550, 246549, 246548, 246547, 246546, 246542, 246541, 246540, 246539, 246538, 246537, 246536, 246535, 246534, 246533, 246532, 246531, 246528, 246527, 246525, 246524, 246523, 246522, 246521, 246520, 246519, 246518, 246517, 246515, 246514, 246513, 246512, 246511, 246510, 246508, 246507, 246506, 246505, 246504, 246503, 246502, 246501, 246500, 246499, 246498, 246497, 246496, 246495, 246494, 246493, 246492, 246491, 246490, 246489, 246488, 246487, 246486, 246485, 246484, 246483, 246482, 246481, 246480, 246479, 246478, 246477, 246476, 246475, 246474, 246473, 246472, 246471, 246470, 246469, 246460, 246456, 246455, 246454, 246453, 246452, 246451, 246450, 246449, 246448, 246447, 246446, 246445, 246444, 246443, 246442, 246441, 246440, 246439, 246438, 246437, 246436, 246435, 246434, 246433, 246432, 246414, 246413, 246412, 246411, 246410, 246409, 246408, 246407, 246402, 246400, 246399, 246391, 246390, 246389, 246388, 246387, 246386, 246368, 246367, 246366, 246365, 246364, 246363, 246362, 246361, 246360, 246359, 246358, 246357, 246356, 246355, 246354, 246353, 246352, 246351, 246350, 246349, 246342, 246341, 246340, 246339, 246337, 246336, 246335, 246334, 246333, 246332, 246331, 246330, 246329, 246328, 246327, 246326, 246325, 246324, 246323, 246322, 246321, 246320, 246319, 246318, 246317, 246316, 246315, 246314, 246313, 246312, 246311, 246310, 246309, 246308, 246307, 246306, 246305, 246304, 246291, 246290, 246289, 246288, 246287, 246286, 246285, 246284, 246283, 246282, 246281, 246280, 246279, 246278, 246277, 246276, 246275, 246274, 246273, 246272, 246271, 246270, 246269, 246268, 246267, 246266, 246265, 246264, 246263, 246262, 246261, 246255, 246254, 246253, 246252, 246251, 246250, 246249, 246247, 246246, 246245, 246234, 246204, 246203, 246202, 246201, 246200, 246193, 246192, 246179, 246156, 246150, 246145, 246133, 246132, 246128, 246125, 246123, 246122, 246121, 246120, 246095, 246094, 246093, 246092, 246091, 246090, 246089, 246088, 246087, 246086, 246081, 246080, 246079, 246078, 246077, 246076, 246075, 246074, 246073, 246072, 246071, 246070, 246069, 246068, 246067, 246066, 246065, 246064, 246063, 246062, 246061, 246060, 246059, 246058, 246057, 246056, 246055, 246054, 246053, 246052, 246051, 246050, 246049, 246048, 246047, 246046, 246045, 246044, 246043, 246042, 246041, 246040, 246039, 246038, 246037, 246036, 246035, 246034, 246033, 246032, 246031, 246030, 246029, 246028, 246027, 246026, 246025, 246024, 246023, 246022, 246021, 246020, 246019, 246018, 246017, 246016, 245989, 245988, 245987, 245986, 245985, 245984, 245983, 245982, 245981, 245980, 245978, 245977, 245934, 245933, 245932, 245931, 245930, 245929, 245928, 245927, 245926, 245925, 245924, 245923, 245922, 245921, 245920, 245919, 245918, 245917, 245916, 245915, 245914, 245913, 245912, 245911, 245910, 245909, 245908, 245907, 245906, 245905, 245904, 245903, 245902, 245901, 245900, 245899, 245898, 245897, 245896, 245895, 245894, 245893, 245892, 245891, 245890, 245889, 245888, 245887, 245886, 245885, 245884, 245883, 245882, 245881, 245880, 245879, 245878, 245877, 245876, 245875, 245874, 245873, 245872, 245871, 245870, 245869, 245868, 245867, 245866, 245865, 245864, 245863, 245862, 245861, 245860, 245859, 245858, 245857, 245856, 245855, 245854, 245853, 245852, 245851, 245850, 245849, 245848, 245847, 245846, 245845, 245844, 245843, 245842, 245841, 245840, 245839, 245838, 245837, 245836, 245835, 245834, 245833, 245832, 245831, 245830, 245829, 245828, 245827, 245826, 245825, 245824, 245823, 245822, 245821, 245820, 245819, 245818, 245817, 245816, 245815, 245814, 245813, 245812, 245811, 245810, 245809, 245808, 245807, 245806, 245805, 245804, 245803, 245802, 245801, 245800, 245799, 245798, 245797, 245796, 245795, 245794, 245793, 245792, 245791, 245790, 245789, 245788, 245787, 245786, 245785, 245784, 245783, 245782, 245781, 245780, 245779, 245778, 245777, 245776, 245775, 245774, 245773, 245772, 245771, 245770, 245769, 245768, 245767, 245766, 245765, 245764, 245763, 245762, 245761, 245760, 245759, 245758, 245757, 245756, 245755, 245754, 245753, 245752, 245751, 245750, 245749, 245748, 245747, 245746, 245745, 245744, 245743, 245742, 245741, 245740, 245739, 245738, 245737, 245736, 245735, 245734, 245733, 245732, 245731, 245730, 245729, 245728, 245727, 245726, 245725, 245724, 245723, 245722, 245721, 245720, 245719, 245718, 245717, 245716, 245715, 245714, 245713, 245712, 245711, 245710, 245709, 245708, 245707, 245706, 245705, 245704, 245703, 245702, 245690, 245686, 245680, 245679, 245678, 245677, 245676, 245675, 245674, 245673, 245672, 245671, 245670, 245669, 245668, 245667, 245666, 245665, 245664, 245663, 245662, 245661, 245660, 245659, 245658, 245657, 245656, 245655, 245654, 245653, 245652, 245651, 245650, 245649, 245648, 245647, 245646, 245645, 245644, 245643, 245642, 245641, 245640, 245589, 245588, 245586, 245582, 245581, 245580, 245579, 245578, 245577, 245576, 245575, 245574, 245573, 245572, 245571, 245570, 245569, 245568, 245567, 245566, 245565, 245564, 245563, 245562, 245561, 245560, 245559, 245558, 245557, 245556, 245555, 245554, 245553, 245552, 245551, 245550, 245549, 245548, 245547, 245546, 245545, 245544, 245543, 245542, 245541, 245540, 245539, 245538, 245537, 245536, 245535, 245533, 245530, 245527, 245526, 245525, 245524, 245523, 245522, 245521, 245520, 245519, 245518, 245517, 245516, 245515, 245514, 245513, 245512, 245511, 245510, 245509, 245508, 245507, 245506, 245505, 245504, 245503, 245502, 245501, 245500, 245499, 245498, 245497, 245496, 245495, 245494, 245493, 245492, 245491, 245490, 245489, 245488, 245487, 245486, 245485, 245484, 245483, 245482, 245481, 245480, 245479, 245478, 245477, 245476, 245475, 245474, 245473, 245472, 245471, 245470, 245469, 245468, 245467, 245466, 245465, 245464, 245463, 245462, 245461, 245460, 245459, 245458, 245457, 245456, 245449, 245447, 245444, 245437, 245387, 245361, 245360, 245359, 245358, 245357, 245356, 245355, 245349, 245348, 245347, 245339, 245326, 245325, 245324, 245274, 245268, 245267, 245255, 245251, 245213, 245212, 245200, 245168, 245167, 245166, 245165, 245164, 245156, 245155, 245152, 245149, 245148, 245145, 245142, 245140, 245139, 245137, 245118, 245117, 245116, 245098, 245083, 245078, 245065, 245040, 245037, 245036, 245035, 245026, 245023, 245021, 245016, 245015, 245002, 245001, 245000, 244976, 244963, 244951, 244945, 244944, 244939, 244935, 244934, 244928, 244927, 244926, 244925, 244924, 244923, 244922, 244921, 244920, 244919, 244918, 244917, 244916, 244909, 244906, 244895, 244894, 244878, 244800, 244764, 244748, 244742, 244740, 244739, 244738, 244737, 244736, 244735, 244734, 244733, 244732, 244731, 244730, 244729, 244728, 244727, 244726, 244725, 244724, 244723, 244722, 244716, 244707, 244706, 244705, 244704, 244702, 244697, 244696, 244695, 244694, 244693, 244687, 244686, 244677, 244676, 244675, 244669, 244664, 244658, 244657, 244651, 244623, 244613, 244608, 244606, 244603, 244593, 244573, 244561, 244560, 244556, 244555, 244554, 244553, 244552, 244549, 244544, 244541, 244510, 244486, 244480, 244479, 244478, 244477, 244476, 244475, 244474, 244473, 244472, 244471, 244470, 244469, 244468, 244467, 244466, 244465, 244464, 244463, 244462, 244461, 244460, 244459, 244458, 244457, 244456, 244455, 244454, 244453, 244452, 244451, 244450, 244449, 244448, 244447, 244446, 244445, 244444, 244443, 244442, 244441, 244440, 244439, 244438, 244437, 244436, 244435, 244434, 244433, 244432, 244387, 244375, 244365, 244335, 244328, 244327, 244284, 244193, 244191, 244178, 244177, 244176, 244174, 244155, 244154, 244151, 244150, 244149, 244148, 244146, 244145, 244144, 244143, 244133, 244119, 244117, 244088, 244079, 244078, 244077, 244076, 244075, 244073, 244072, 244062, 243976, 243975, 243971, 243961, 243948, 243938, 243937, 243918, 243917, 243915, 243914, 243913, 243912, 243905, 243904, 243903, 243902, 243901, 243900, 243899, 243898, 243897, 243896, 243895, 243894, 243893, 243892, 243891, 243890, 243889, 243888, 243887, 243886, 243885, 243884, 243883, 243882, 243881, 243880, 243879, 243878, 243877, 243876, 243875, 243874, 243873, 243872, 243871, 243870, 243869, 243868, 243867, 243866, 243865, 243864, 243863, 243862, 243861, 243860, 243859, 243858, 243857, 243856, 243855, 243854, 243853, 243852, 243851, 243850, 243849, 243848, 243847, 243846, 243845, 243844, 243843, 243842, 243841, 243840, 243839, 243838, 243837, 243836, 243835, 243834, 243833, 243832, 243831, 243830, 243829, 243828, 243827, 243826, 243825, 243824, 243823, 243822, 243821, 243820, 243819, 243818, 243817, 243816, 243815, 243814, 243813, 243812, 243811, 243810, 243809, 243808, 243807, 243806, 243805, 243804, 243803, 243802, 243801, 243800, 243799, 243798, 243797, 243796, 243795, 243794, 243793, 243792, 243791, 243790, 243789, 243788, 243787, 243786, 243785, 243784, 243783, 243782, 243781, 243780, 243779, 243778, 243777, 243776, 243775, 243774, 243773, 243772, 243771, 243770, 243769, 243768, 243767, 243766, 243765, 243764, 243763, 243762, 243761, 243760, 243759, 243758, 243757, 243756, 243755, 243754, 243753, 243752, 243751, 243750, 243749, 243748, 243747, 243746, 243745, 243744, 243743, 243742, 243741, 243740, 243739, 243738, 243737, 243736, 243735, 243734, 243733, 243732, 243731, 243730, 243729, 243728, 243727, 243726, 243707, 243705, 243704, 243703, 243702, 243701, 243700, 243699, 243698, 243697, 243696, 243695, 243694, 243693, 243692, 243691, 243690, 243689, 243661, 243660, 243658, 243657, 243656, 243654, 243607, 243605, 243604, 243597, 243586, 243531, 243527, 243449, 243405, 243397, 243390, 243388, 243387, 243386, 243385, 243382, 243381, 243379, 243376, 243375, 243370, 243235, 243234, 243233, 243232, 243231, 243230, 243229, 243228, 243227, 243226, 243225, 243224, 243223, 243222, 243221, 243220, 243219, 243217, 243216, 243215, 243214, 243213, 243212, 243211, 243210, 243209, 243208, 243097, 243096, 243095, 243094, 243093, 243054, 243049, 243044, 243043, 243041, 243038, 243034, 243033, 243032, 243022, 243006, 243001, 242984, 242973, 242962, 242943, 242938, 242937, 242911, 242909, 242907, 242897, 242896, 242895, 242892, 242887, 242876, 242875, 242874, 242863, 242862, 242853, 242851, 242819, 242818, 242817, 242812, 242803, 242798, 242797, 242796, 242795, 242794, 242793, 242773, 242772, 242771, 242770, 242769, 242767, 242764, 242748, 242747, 242746, 242745, 242744, 242743, 242742, 242741, 242740, 242739, 242738, 242737, 242736, 242735, 242734, 242733, 242732, 242731, 242730, 242729, 242728, 242727, 242726, 242725, 242724, 242696, 242687, 242669, 242668, 242665, 242642, 242641, 242637, 242636, 242627, 242625, 242623, 242622, 242621, 242620, 242604, 242595, 242594, 242593, 242592, 242591, 242573, 242572, 242570, 242569, 242566, 242565, 242563, 242555, 242544, 242543, 242536, 242534, 242533, 242522, 242498, 242479, 242467, 242451, 242450, 242449, 242428, 242427, 242425, 242423, 242422, 242396, 242395, 242394, 242390, 242326, 242323, 242322, 242321, 242320, 242289, 242288, 242287, 242285, 242284, 242283, 242282, 242281, 242279, 242254, 242249, 242248, 242241, 242238, 242220, 242219, 242218, 242203, 242202, 242201, 242199, 242193, 242186, 242177, 242147, 242141, 242140, 242109, 242108, 242103, 242097, 242096, 242084, 242083, 242082, 242081, 242079, 242071, 242070, 242069, 242068, 242067, 242066, 242065, 242063, 242061, 242060, 242059, 242025, 241983, 241979, 241971, 241968, 241967, 241966, 241965, 241955, 241951, 241912, 241911, 241910, 241909, 241908, 241907, 241906, 241893, 241883, 241882, 241843, 241806, 241777, 241774, 241773, 241772, 241769, 241768, 241687, 241683, 241681, 241678, 241676, 241672, 241664, 241659, 241647, 241624, 241623, 241546, 241545, 241538, 241532, 241531, 241470, 241464, 241463, 241462, 241461, 241460, 241459, 241458, 241457, 241456, 241455, 241454, 241453, 241452, 241451, 241450, 241449, 241448, 241447, 241446, 241445, 241444, 241443, 241442, 241441, 241440, 241439, 241438, 241437, 241436, 241435, 241434, 241433, 241432, 241431, 241430, 241429, 241428, 241427, 241426, 241425, 241424, 241423, 241422, 241421, 241420, 241419, 241418, 241417, 241416, 241415, 241414, 241413, 241412, 241411, 241410, 241409, 241408, 241407, 241406, 241405, 241404, 241403, 241402, 241401, 241400, 241399, 241398, 241397, 241396, 241395, 241394, 241393, 241392, 241391, 241390, 241389, 241388, 241387, 241386, 241385, 241384, 241383, 241382, 241381, 241380, 241379, 241378, 241377, 241376, 241375, 241374, 241373, 241372, 241371, 241370, 241369, 241368, 241367, 241366, 241365, 241364, 241363, 241362, 241361, 241360, 241359, 241358, 241357, 241356, 241355, 241354, 241353, 241352, 241351, 241350, 241349, 241348, 241347, 241346, 241345, 241344, 241343, 241342, 241341, 241340, 241339, 241338, 241337, 241336, 241335, 241334, 241333, 241332, 241331, 241330, 241329, 241328, 241327, 241326, 241325, 241324, 241323, 241322, 241321, 241320, 241319, 241318, 241317, 241316, 241315, 241314, 241313, 241312, 241311, 241310, 241309, 241308, 241307, 241306, 241305, 241304, 241303, 241302, 241301, 241300, 241299, 241298, 241297, 241296, 241295, 241294, 241293, 241292, 241291, 241290, 241289, 241288, 241287, 241286, 241285, 241284, 241283, 241282, 241281, 241280, 241279, 241278, 241277, 241276, 241275, 241274, 241273, 241272, 241271, 241270, 241269, 241268, 241267, 241266, 241265, 241264, 241263, 241262, 241261, 241260, 241259, 241258, 241257, 241256, 241255, 241254, 241253, 241252, 241251, 241250, 241249, 241248, 241247, 241246, 241245, 241244, 241243, 241242, 241241, 241240, 241239, 241238, 241237, 241236, 241235, 241234, 241233, 241232, 241231, 241230, 241229, 241228, 241227, 241226, 241225, 241224, 241223, 241222, 241221, 241220, 241219, 241218, 241217, 241216, 241215, 241214, 241213, 241212, 241211, 241210, 241209, 241208, 241207, 241206, 241205, 241204, 241203, 241202, 241201, 241200, 241199, 241198, 241197, 241196, 241195, 241194, 241193, 241192, 241191, 241190, 241189, 241188, 241187, 241186, 241185, 241184, 241183, 241145, 241095, 241094, 241093, 241092, 241091, 241090, 241089, 241088, 241087, 241086, 241085, 241084, 241083, 241082, 241081, 241080, 241079, 241078, 241077, 241076, 241075, 241074, 241073, 241072, 241071, 241070, 241069, 241068, 241067, 241066, 241065, 241064, 241063, 241062, 241061, 241060, 241059, 241058, 241057, 241056, 241055, 241054, 241053, 241052, 241051, 241050, 241049, 241048, 241047, 241046, 241045, 241044, 241043, 241042, 241041, 241040, 241039, 241038, 241037, 241036, 241035, 241034, 241033, 241032, 241031, 241030, 241029, 241028, 241027, 241026, 241025, 241024, 241023, 241022, 241021, 241020, 240996, 240995, 240987, 240979, 240977, 240969, 240968, 240927, 240924, 240914, 240912, 240911, 240910, 240909, 240908, 240907, 240906, 240905, 240904, 240903, 240902, 240901, 240900, 240899, 240898, 240897, 240896, 240895, 240894, 240893, 240892, 240891, 240890, 240889, 240888, 240887, 240886, 240885, 240884, 240883, 240882, 240881, 240880, 240879, 240878, 240875, 240856, 240853, 240852, 240851, 240844, 240837, 240834, 240823, 240790, 240777, 240776, 240775, 240772, 240771, 240768, 240767, 240702, 240675, 240674, 240673, 240672, 240666, 240665, 240664, 240663, 240646, 240619, 240617, 240612, 240607, 240600, 240590, 240583, 240555, 240554, 240553, 240537, 240536, 240535, 240534, 240533, 240532, 240517, 240516, 240515, 240514, 240513, 240512, 240511, 240510, 240509, 240508, 240507, 240477, 240473, 240465, 240453, 240448, 240445, 240444, 240443, 240442, 240441, 240440, 240439, 240434, 240417, 240406, 240405, 240390, 240372, 240335, 240334, 240332, 240325, 240319, 240304, 240303, 240302, 240301, 240300, 240299, 240298, 240297, 240293, 240277, 240275, 240274, 240270, 240269, 240210, 240208, 240207, 240206, 240205, 240204, 240203, 240202, 240201, 240200, 240199, 240198, 240197, 240196, 240195, 240194, 240193, 240192, 240191, 240190, 240189, 240188, 240166, 240124, 240123, 240122, 240121, 240118, 240104, 240103, 240102, 240101, 240100, 240099, 240098, 240097, 240096, 240080, 240077, 240076, 240057, 240051, 240047, 240043, 240031, 240030, 240025, 240006, 239920, 239891, 239889, 239819, 239806, 239805, 239804, 239803, 239802, 239783, 239775, 239764, 239759, 239758, 239757, 239756, 239744, 239743, 239742, 239741, 239738, 239737, 239681, 239680, 239679, 239678, 239677, 239657, 239654, 239634, 239630, 239627, 239626, 239619, 239618, 239617, 239616, 239613, 239609, 239608, 239607, 239601, 239600, 239598, 239597, 239596, 239584, 239583, 239563, 239559, 239516, 239512, 239511, 239510, 239509, 239508, 239507, 239495, 239494, 239493, 239492, 239491, 239490, 239489, 239488, 239487, 239486, 239485, 239484, 239483, 239482, 239481, 239480, 239479, 239478, 239477, 239476, 239475, 239474, 239473, 239472, 239471, 239470, 239469, 239468, 239467, 239383, 239382, 239381, 239380, 239379, 239378, 239377, 239376, 239370, 239369, 239345, 239321, 239320, 239319, 239318, 239317, 239316, 239315, 239314, 239302, 239298, 239297, 239296, 239276, 239265, 239256, 239253, 239249, 239246, 239226, 239223, 239196, 239195, 239194, 239193, 239192, 239180, 239175, 239174, 239169, 239168, 239165, 239156, 239146, 239145, 239144, 239137, 239136, 239096, 239091, 239088, 239076, 239067, 239052, 239040, 239039, 239038, 239036, 239018, 239015, 239011, 239001, 239000, 238998, 238993, 238990, 238981, 238976, 238954, 238915, 238913, 238906, 238905, 238904, 238903, 238895, 238892, 238891, 238888, 238887, 238886, 238885, 238869, 238866, 238863, 238862, 238861, 238860, 238859, 238858, 238857, 238856, 238855, 238854, 238839, 238767, 238742, 238739, 238737, 238736, 238721, 238716, 238715, 238714, 238713, 238712, 238711, 238708, 238703, 238652, 238633, 238631, 238630, 238627, 238625, 238624, 238623, 238622, 238611, 238610, 238609, 238606, 238603, 238598, 238597, 238596, 238595, 238594, 238593, 238592, 238591, 238590, 238589, 238588, 238587, 238586, 238585, 238584, 238583, 238582, 238581, 238580, 238579, 238577, 238576, 238575, 238574, 238573, 238572, 238571, 238569, 238561, 238558, 238554, 238551, 238543, 238533, 238532, 238527, 238519, 238518, 238505, 238502, 238495, 238492, 238491, 238490, 238489, 238486, 238479, 238474, 238468, 238467, 238463, 238461, 238459, 238458, 238457, 238456, 238448, 238443, 238440, 238439, 238433, 238421, 238386, 238383, 238380, 238359, 238358, 238336, 238320, 238312, 238311, 238304, 238296, 238230, 238224, 238223, 238222, 238212, 238198, 238194, 238192, 238191, 238190, 238189, 238174, 238154, 238140, 238120, 238102, 238096, 238095, 238094, 238093, 238092, 238090, 238076, 238074, 238073, 238066, 238056, 238053, 238049, 238048, 238047, 238045, 238009, 238008, 238002, 237993, 237977, 237976, 237975, 237966, 237965, 237964, 237963, 237962, 237961, 237960, 237959, 237958, 237957, 237956, 237955, 237954, 237953, 237952, 237950, 237949, 237947, 237946, 237944, 237943, 237941, 237940, 237939, 237934, 237933, 237931, 237930, 237929, 237928, 237915, 237910, 237898, 237890, 237889, 237888, 237887, 237886, 237885, 237884, 237883, 237882, 237881, 237880, 237879, 237878, 237877, 237876, 237875, 237874, 237873, 237872, 237871, 237870, 237869, 237868, 237867, 237866, 237843, 237842, 237828, 237824, 237823, 237822, 237820, 237816, 237814, 237813, 237812, 237811, 237810, 237809, 237806, 237803, 237801, 237800, 237799, 237798, 237797, 237792, 237791, 237790, 237789, 237788, 237787, 237786, 237785, 237784, 237783, 237782, 237781, 237780, 237779, 237766, 237765, 237764, 237760, 237759, 237757, 237756, 237755, 237754, 237753, 237752, 237750, 237749, 237748, 237747, 237746, 237745, 237744, 237743, 237738, 237735, 237730, 237729, 237728, 237727, 237726, 237725, 237724, 237712, 237711, 237710, 237709, 237707, 237706, 237705, 237701, 237700, 237699, 237698, 237697, 237681, 237674, 237673, 237669, 237667, 237661, 237658, 237657, 237650, 237634, 237627, 237626, 237611, 237607, 237604, 237529, 237508, 237470, 237451, 237448, 237345, 237343, 237342, 237341, 237323, 237322, 237315, 237292, 237281, 237262, 237217, 237216, 237212, 237210, 237195, 237194, 237193, 237174, 237173, 237172, 237171, 237141, 237130, 237051, 237032, 237031, 237030, 236950, 236922, 236921, 236920, 236913, 236912, 236910, 236856, 236832, 236831, 236830, 236829, 236828, 236827, 236826, 236825, 236823, 236822, 236821, 236820, 236819, 236818, 236817, 236816, 236815, 236757, 236753, 236714, 236713, 236712, 236711, 236710, 236709, 236708, 236707, 236706, 236705, 236704, 236703, 236702, 236701, 236700, 236699, 236698, 236697, 236696, 236695, 236692, 236691, 236690, 236689, 236688, 236687, 236686, 236685, 236684, 236683, 236682, 236681, 236680, 236679, 236678, 236677, 236676, 236675, 236674, 236673, 236666, 236665, 236664, 236663, 236661, 236660, 236659, 236658, 236657, 236656, 236655, 236654, 236653, 236652, 236651, 236650, 236649, 236648, 236647, 236646, 236645, 236644, 236643, 236642, 236641, 236640, 236639, 236638, 236637, 236636, 236635, 236634, 236632, 236631, 236630, 236629, 236628, 236627, 236626, 236625, 236624, 236623, 236622, 236621, 236620, 236619, 236618, 236617, 236616, 236615, 236614, 236613, 236612, 236611, 236610, 236609, 236608, 236607, 236606, 236605, 236604, 236603, 236602, 236601, 236600, 236599, 236598, 236597, 236596, 236595, 236594, 236593, 236592, 236583, 236582, 236581, 236580, 236579, 236578, 236577, 236576, 236575, 236574, 236567, 236566, 236565, 236564, 236563, 236562, 236561, 236560, 236559, 236558, 236557, 236556, 236542, 236541, 236540, 236539, 236538, 236537, 236535, 236534, 236533, 236532, 236531, 236530, 236529, 236528, 236527, 236526, 236525, 236524, 236523, 236522, 236521, 236520, 236519, 236509, 236501, 236481, 236317, 236316, 236307, 236306, 236305, 236304, 236303, 236302, 236301, 236300, 236299, 236298, 236297, 236296, 236295, 236294, 236293, 236292, 236291, 236290, 236289, 236288, 236287, 236286, 236285, 236284, 236283, 236282, 236281, 236280, 236279, 236278, 236277, 236276, 236275, 236274, 236273, 236272, 236271, 236270, 236269, 236268, 236263, 236259, 236248, 236247, 236245, 236241, 236236, 236235, 236234, 236233, 236232, 236231, 236230, 236229, 236228, 236227, 236226, 236225, 236224, 236223, 236222, 236221, 236220, 236219, 236218, 236217, 236216, 236215, 236214, 236213, 236212, 236211, 236209, 236208, 236192, 236150, 236149, 236148, 236147, 236146, 236145, 236144, 236142, 236141, 236140, 236139, 236138, 236137, 236136, 236135, 236134, 236133, 236132, 236131, 236130, 236129, 236128, 236127, 236126, 236125, 236124, 236123, 236122, 236121, 236120, 236119, 236118, 236117, 236116, 236115, 236114, 236113, 236112, 236111, 236110, 236109, 236108, 236107, 236106, 236105, 236104, 236103, 236102, 236101, 236100, 236099, 236098, 236097, 236096, 236095, 236094, 236093, 236092, 236091, 236090, 236089, 236088, 236087, 236086, 236085, 236084, 236083, 236082, 236081, 236080, 236079, 236078, 236077, 236076, 236075, 236074, 236073, 236072, 236071, 236070, 236069, 236068, 236067, 236066, 236065, 236064, 236063, 236062, 236061, 236060, 236059, 236058, 236057, 236056, 236055, 236054, 236053, 236052, 236051, 236050, 236049, 236048, 236047, 236046, 236045, 236044, 236043, 236042, 236041, 236040, 236039, 236038, 236037, 236036, 236035, 236033, 236022, 236019, 236017, 236013, 236011, 235998, 235997, 235996, 235995, 235994, 235993, 235992, 235991, 235969, 235968, 235963, 235962, 235961, 235960, 235959, 235958, 235957, 235956, 235955, 235954, 235953, 235952, 235951, 235950, 235949, 235948, 235947, 235946, 235945, 235944, 235943, 235942, 235941, 235940, 235939, 235938, 235937, 235936, 235935, 235934, 235933, 235932, 235931, 235930, 235929, 235928, 235927, 235926, 235925, 235924, 235923, 235922, 235921, 235920, 235919, 235918, 235917, 235916, 235915, 235914, 235913, 235912, 235911, 235910, 235909, 235908, 235907, 235906, 235905, 235904, 235903, 235902, 235901, 235900, 235899, 235898, 235897, 235896, 235895, 235894, 235893, 235892, 235891, 235890, 235889, 235888, 235887, 235886, 235885, 235884, 235883, 235882, 235881, 235880, 235879, 235878, 235877, 235876, 235875, 235866, 235864, 235863, 235862, 235861, 235860, 235852, 235844, 235827, 235826, 235825, 235824, 235823, 235822, 235821, 235819, 235815, 235814, 235813, 235812, 235810, 235795, 235793, 235785, 235784, 235779, 235776, 235768, 235761, 235752, 235750, 235747, 235746, 235745, 235703, 235684, 235681, 235680, 235661, 235658, 235655, 235645, 235644, 235643, 235640, 235635, 235619, 235568, 235567, 235566, 235561, 235542, 235541, 235521, 235519, 235518, 235515, 235448, 235422, 235413, 235412, 235400, 235396, 235340, 235336, 235335, 235332, 235331, 235329, 235285, 235262, 235237, 235235, 235226, 235213, 235211, 235210, 235209, 235208, 235207, 235206, 235205, 235204, 235203, 235202, 235201, 235200, 235199, 235198, 235196, 235195, 235194, 235193, 235192, 235191, 235190, 235189, 235188, 235187, 235186, 235185, 235184, 235183, 235182, 235181, 235180, 235179, 235178, 235177, 235176, 235175, 235174, 235173, 235172, 235171, 235170, 235169, 235168, 235167, 235166, 235165, 235164, 235163, 235162, 235161, 235160, 235159, 235158, 235157, 235156, 235155, 235154, 235153, 235152, 235151, 235150, 235149, 235148, 235147, 235146, 235145, 235144, 235143, 235142, 235141, 235140, 235139, 235138, 235137, 235136, 235135, 235134, 235133, 235132, 235131, 235130, 235129, 235128, 235127, 235126, 235125, 235124, 235123, 235122, 235121, 235120, 235119, 235118, 235117, 235116, 235115, 235114, 235113, 235112, 235111, 235110, 235109, 235108, 235107, 235106, 235105, 235104, 235103, 235102, 235101, 235100, 235099, 235098, 235097, 235096, 235095, 235094, 235093, 235092, 235091, 235090, 235089, 235088, 235087, 235086, 235085, 235084, 235083, 235082, 235081, 235080, 235079, 235078, 235077, 235076, 235075, 235074, 235073, 235072, 235071, 235070, 235069, 235068, 235067, 235066, 235065, 235064, 235063, 235062, 235061, 235060, 235059, 235058, 235057, 235056, 235055, 235054, 235050, 235049, 235048, 235047, 235046, 235045, 235044, 235043, 235042, 235041, 235040, 235039, 235038, 235037, 235036, 235035, 235034, 235033, 235032, 235031, 235030, 235029, 235028, 235027, 235026, 235025, 235024, 235023, 235022, 235021, 235020, 235019, 235018, 235017, 235016, 235015, 235014, 235013, 235012, 235011, 235010, 235009, 235008, 235007, 235006, 235005, 235004, 235003, 235002, 235001, 235000, 234999, 234998, 234997, 234996, 234995, 234994, 234993, 234980, 234967, 234966, 234964, 234963, 234962, 234961, 234960, 234958, 234956, 234953, 234941, 234929, 234909, 234894, 234884, 234883, 234882, 234880, 234879, 234876, 234875, 234874, 234873, 234872, 234870, 234864, 234768, 234761, 234760, 234755, 234748, 234738, 234734, 234733, 234732, 234728, 234719, 234704, 234703, 234702, 234701, 234700, 234699, 234698, 234697, 234696, 234695, 234689, 234688, 234687, 234686, 234685, 234684, 234683, 234682, 234670, 234669, 234667, 234661, 234649, 234648, 234634, 234622, 234606, 234595, 234585, 234584, 234569, 234564, 234563, 234560, 234544, 234542, 234541, 234499, 234498, 234497, 234496, 234495, 234494, 234493, 234492, 234486, 234484, 234473, 234472, 234471, 234470, 234469, 234468, 234467, 234466, 234465, 234464, 234463, 234462, 234461, 234460, 234459, 234458, 234457, 234456, 234455, 234454, 234453, 234452, 234451, 234450, 234449, 234448, 234447, 234446, 234436, 234426, 234425, 234424, 234409, 234407, 234383, 234380, 234377, 234376, 234366, 234358, 234339, 234336, 234335, 234333, 234329, 234327, 234320, 234319, 234318, 234307, 234306, 234305, 234301, 234296, 234293, 234290, 234284, 234272, 234263, 234262, 234260, 234259, 234221, 234220, 234219, 234218, 234194, 234178, 234177, 234176, 234175, 234174, 234167, 234156, 234148, 234133, 234132, 234131, 234125, 234093, 234090, 234081, 234079, 234078, 234066, 234064, 234062, 234060, 234059, 234058, 234053, 234050, 234019, 233967, 233945, 233944, 233943, 233942, 233941, 233940, 233939, 233938, 233937, 233936, 233935, 233934, 233933, 233932, 233931, 233930, 233929, 233928, 233927, 233926, 233925, 233924, 233923, 233922, 233917, 233908, 233905, 233897, 233895, 233893, 233889, 233882, 233864, 233863, 233862, 233861, 233860, 233859, 233858, 233857, 233856, 233855, 233854, 233853, 233852, 233851, 233850, 233845, 233838, 233837, 233836, 233835, 233834, 233833, 233813, 233812, 233811, 233810, 233798, 233796, 233795, 233794, 233775, 233772, 233771, 233768, 233765, 233764, 233763, 233762, 233760, 233756, 233749, 233748, 233734, 233728, 233725, 233723, 233711, 233707, 233706, 233705, 233704, 233703, 233702, 233701, 233700, 233699, 233698, 233695, 233693, 233692, 233691, 233689, 233685, 233683, 233658, 233635, 233632, 233627, 233626, 233599, 233598, 233481, 233480, 233479, 233478, 233468, 233467, 233460, 233458, 233455, 233454, 233436, 233410, 233409, 233402, 233392, 233387, 233378, 233283, 233282, 233277, 233276, 233275, 233274, 233273, 233272, 233271, 233270, 233269, 233268, 233267, 233266, 233265, 233264, 233263, 233262, 233261, 233241, 233240, 233239, 233238, 233237, 233236, 233235, 233231, 233225, 233208, 233207, 233206, 233203, 233194, 233179, 233178, 233174, 233173, 233161, 233138, 233122, 233121, 233109, 233085, 233074, 233073, 233072, 233071, 233070, 233069, 233068, 233067, 233066, 233065, 233064, 233063, 233062, 233058, 233057, 233045, 233040, 233039, 233031, 233026, 233025, 233024, 233021, 233012, 233011, 233010, 233009, 232982, 232956, 232954, 232953, 232947, 232946, 232936, 232814, 232813, 232812, 232805, 232799, 232779, 232778, 232776, 232775, 232773, 232755, 232747, 232739, 232738, 232737, 232727, 232718, 232689, 232676, 232665, 232664, 232663, 232662, 232661, 232660, 232659, 232658, 232657, 232656, 232655, 232654, 232653, 232652, 232651, 232650, 232648, 232646, 232645, 232644, 232643, 232642, 232641, 232640, 232639, 232638, 232637, 232636, 232635, 232634, 232633, 232632, 232631, 232630, 232629, 232628, 232627, 232626, 232625, 232624, 232623, 232622, 232621, 232620, 232619, 232618, 232617, 232616, 232615, 232614, 232613, 232612, 232611, 232610, 232609, 232608, 232607, 232606, 232605, 232604, 232603, 232602, 232601, 232600, 232599, 232598, 232597, 232596, 232595, 232594, 232593, 232592, 232591, 232590, 232589, 232588, 232587, 232586, 232585, 232584, 232583, 232582, 232581, 232580, 232579, 232578, 232577, 232576, 232575, 232574, 232573, 232572, 232571, 232570, 232569, 232568, 232567, 232566, 232565, 232564, 232563, 232562, 232561, 232560, 232559, 232558, 232557, 232556, 232555, 232554, 232553, 232552, 232551, 232550, 232549, 232548, 232547, 232546, 232545, 232544, 232543, 232542, 232541, 232540, 232539, 232538, 232537, 232536, 232535, 232534, 232531, 232530, 232528, 232470, 232458, 232455, 232454, 232453, 232452, 232451, 232450, 232447, 232446, 232445, 232444, 232439, 232438, 232436, 232430, 232422, 232421, 232399, 232366, 232356, 232348, 232249, 232160, 232159, 232158, 232157, 232156, 232155, 232154, 232153, 232126, 232124, 232123, 232122, 232119, 232118, 232116, 232108, 232092, 232091, 232090, 232089, 232088, 232087, 232086, 232085, 232084, 232083, 232082, 232081, 232076, 232074, 232053, 232004, 231990, 231936, 231935, 231928, 231927, 231926, 231925, 231924, 231923, 231922, 231921, 231915, 231913, 231888, 231880, 231825, 231824, 231823, 231822, 231821, 231819, 231818, 231817, 231816, 231815, 231814, 231802, 231801, 231800, 231799, 231798, 231797, 231796, 231771, 231770, 231769, 231768, 231767, 231766, 231765, 231751, 231749, 231668, 231667, 231666, 231665, 231664, 231655, 231653, 231652, 231651, 231650, 231649, 231648, 231647, 231646, 231645, 231644, 231643, 231642, 231641, 231640, 231639, 231638, 231637, 231636, 231635, 231634, 231633, 231632, 231631, 231630, 231629, 231628, 231627, 231626, 231625, 231624, 231623, 231622, 231621, 231620, 231619, 231618, 231617, 231616, 231615, 231614, 231613, 231612, 231611, 231610, 231609, 231608, 231607, 231606, 231605, 231604, 231603, 231602, 231601, 231600, 231599, 231598, 231597, 231596, 231595, 231594, 231593, 231592, 231591, 231590, 231589, 231588, 231587, 231586, 231585, 231584, 231583, 231582, 231581, 231580, 231579, 231578, 231577, 231576, 231564, 231563, 231513, 231512, 231511, 231510, 231509, 231434, 231433, 231423, 231421, 231418, 231417, 231416, 231415, 231414, 231413, 231412, 231411, 231410, 231409, 231408, 231407, 231406, 231405, 231404, 231403, 231402, 231401, 231400, 231399, 231398, 231397, 231396, 231395, 231394, 231393, 231392, 231391, 231390, 231389, 231388, 231344, 231343, 231318, 231316, 231315, 231312, 231309, 231308, 231306, 231305, 231304, 231288, 231287, 231286, 231285, 231282, 231281, 231280, 231279, 231278, 231277, 231271, 231270, 231269, 231268, 231267, 231266, 231265, 231264, 231263, 231254, 231244, 231243, 231235, 231223, 231221, 231214, 231212, 231211, 231210, 231209, 231208, 231207, 231206, 231205, 231204, 231203, 231202, 231201, 231200, 231199, 231198, 231197, 231195, 231194, 231193, 231192, 231191, 231190, 231189, 231188, 231187, 231186, 231185, 231184, 231183, 231182, 231181, 231180, 231179, 231178, 231177, 231176, 231175, 231174, 231173, 231172, 231171, 231170, 231169, 231168, 231167, 231166, 231165, 231164, 231163, 231162, 231161, 231160, 231159, 231158, 231153, 231152, 231151, 231150, 231149, 231148, 231147, 231146, 231145, 231144, 231143, 231142, 231141, 231140, 231139, 231138, 231137, 231132, 231131, 231130, 231129, 231128, 231127, 231126, 231125, 231124, 231123, 231122, 231121, 231120, 231119, 231118, 231117, 231116, 231115, 231114, 231113, 231112, 231111, 231110, 231109, 231108, 231107, 231106, 231105, 231104, 231103, 231102, 231101, 231100, 231095, 231094, 231093, 231092, 231091, 231090, 231089, 231084, 231083, 231082, 231081, 231080, 231079, 231078, 231077, 231076, 231075, 231074, 231073, 231072, 231071, 231070, 231069, 231068, 231067, 231066, 231065, 231064, 231063, 231062, 231061, 231060, 231059, 231058, 231057, 231056, 231055, 231054, 231053, 231052, 231051, 231050, 231049, 231048, 231047, 231046, 231045, 231044, 231043, 231042, 231041, 231040, 231039, 231037, 231036, 231035, 231034, 231033, 231032, 231031, 231030, 231029, 231028, 231026, 231025, 231024, 231023, 231022, 231021, 231020, 231019, 231018, 231017, 231016, 231015, 231014, 231013, 231012, 231011, 231010, 231009, 231008, 231007, 231006, 231005, 231004, 231003, 231002, 231001, 231000, 230999, 230998, 230997, 230996, 230995, 230994, 230993, 230992, 230991, 230990, 230989, 230988, 230987, 230986, 230985, 230984, 230983, 230982, 230981, 230980, 230979, 230978, 230977, 230976, 230975, 230974, 230973, 230972, 230971, 230970, 230969, 230968, 230967, 230966, 230965, 230964, 230963, 230962, 230961, 230960, 230959, 230958, 230957, 230956, 230955, 230954, 230953, 230952, 230951, 230950, 230949, 230948, 230947, 230946, 230945, 230944, 230943, 230942, 230941, 230940, 230939, 230938, 230937, 230936, 230935, 230934, 230933, 230932, 230931, 230930, 230929, 230928, 230927, 230926, 230925, 230924, 230923, 230922, 230921, 230920, 230919, 230918, 230917, 230916, 230915, 230914, 230913, 230912, 230857, 230856, 230854, 230847, 230843, 230842, 230835, 230834, 230833, 230832, 230831, 230824, 230822, 230818, 230815, 230814, 230797, 230772, 230771, 230769, 230768, 230764, 230763, 230762, 230754, 230750, 230741, 230740, 230739, 230736, 230727, 230721, 230711, 230709, 230706, 230705, 230701, 230700, 230699, 230692, 230689, 230688, 230685, 230683, 230652, 230630, 230628, 230626, 230625, 230595, 230592, 230589, 230577, 230575, 230568, 230560, 230554, 230553, 230552, 230547, 230539, 230483, 230482, 230481, 230480, 230479, 230478, 230477, 230476, 230475, 230474, 230473, 230472, 230471, 230470, 230469, 230468, 230467, 230466, 230465, 230464, 230463, 230462, 230461, 230460, 230459, 230458, 230457, 230456, 230455, 230454, 230453, 230452, 230451, 230450, 230449, 230448, 230447, 230446, 230445, 230444, 230443, 230442, 230441, 230440, 230439, 230438, 230437, 230436, 230435, 230434, 230430, 230425, 230423, 230412, 230411, 230399, 230396, 230395, 230394, 230392, 230390, 230381, 230380, 230379, 230378, 230375, 230374, 230361, 230359, 230342, 230339, 230332, 230331, 230314, 230303, 230302, 230284, 230265, 230261, 230252, 230247, 230245, 230232, 230195, 230194, 230193, 230192, 230191, 230190, 230189, 230188, 230187, 230186, 230185, 230184, 230171, 230169, 230160, 230130, 230129, 230128, 230126, 230125, 230122, 230101, 230100, 230090, 230089, 230088, 230087, 230085, 230084, 230078, 230076, 230073, 230066, 230019, 230015, 230007, 230006, 230005, 230004, 229991, 229981, 229980, 229973, 229970, 229965, 229939, 229938, 229933, 229927, 229925, 229924, 229923, 229897, 229895, 229893, 229887, 229885, 229884, 229883, 229880, 229879, 229878, 229877, 229856, 229824, 229823, 229822, 229821, 229820, 229819, 229774, 229769, 229763, 229762, 229760, 229736, 229729, 229727, 229726, 229725, 229724, 229720, 229702, 229697, 229691, 229682, 229673, 229672, 229671, 229670, 229669, 229668, 229667, 229666, 229665, 229664, 229663, 229662, 229661, 229660, 229659, 229658, 229657, 229656, 229655, 229654, 229653, 229652, 229651, 229650, 229649, 229648, 229647, 229646, 229645, 229644, 229643, 229642, 229641, 229640, 229639, 229638, 229637, 229636, 229635, 229634, 229633, 229632, 229631, 229630, 229629, 229628, 229627, 229626, 229625, 229624, 229623, 229622, 229621, 229620, 229619, 229618, 229617, 229616, 229615, 229614, 229613, 229612, 229611, 229610, 229609, 229608, 229607, 229606, 229605, 229604, 229603, 229602, 229601, 229600, 229599, 229598, 229597, 229596, 229595, 229594, 229593, 229592, 229591, 229590, 229589, 229588, 229587, 229586, 229585, 229584, 229583, 229582, 229581, 229580, 229579, 229578, 229577, 229576, 229575, 229574, 229573, 229572, 229571, 229570, 229569, 229568, 229567, 229566, 229565, 229564, 229560, 229528, 229527, 229512, 229511, 229495, 229487, 229486, 229480, 229478, 229469, 229467, 229466, 229465, 229464, 229455, 229452, 229423, 229421, 229418, 229394, 229352, 229351, 229350, 229349, 229348, 229347, 229340, 229339, 229338, 229302, 229296, 229295, 229294, 229293, 229286, 229269, 229237, 229225, 229224, 229221, 229215, 229208, 229207, 229206, 229205, 229204, 229203, 229163, 229162, 229056, 229055, 228982, 228981, 228967, 228966, 228965, 228963, 228956, 228955, 228954, 228953, 228952, 228935, 228928, 228927, 228926, 228925, 228924, 228923, 228922, 228921, 228920, 228919, 228918, 228917, 228916, 228915, 228914, 228913, 228912, 228911, 228910, 228909, 228908, 228907, 228906, 228905, 228904, 228903, 228902, 228901, 228900, 228899, 228898, 228897, 228896, 228895, 228894, 228893)
           AND col.name IN ('agent', 'source', '4.3.1', '4.3.2', 'eta', 'etd', 'preAlertDn', 'agentPriceChecking', 'cdsDate', 'consignee', 'agent-key-date', 'saleman', 'vesselNameVoy', 'volume', '1.3.1', '1.1', '1.2', '10.1', 'mblNo', '1.3', '3.1', '1.4', '3.2', '5.1', '5.2', '5.3', '7.1', '5.4', '9.1', 'etaTransitPort', '9.2', '9.3', 'status', 'falseReason', '9.4', '9.5', 'note', '9.6', 'containerNo', '9.7', 'pod', 'subjectEmail', 'combineMbl', 'loadGateIn', 'mblTelex', 'etdTransitPort', 'hblTelex', 'pol', 'shipmentType', 'combine-done', 'jobNo', 'cutOff', 'shipmentDate', 'jobType', 'jobCreatedDate', 'costDemDest', 'agentFeedback', '11.5', 'cfs', '11.4', 'carrier', '1.4.1', '11.3', '11.2', 'hblNo', '11.1', '2.1', '2.2', '2.3', '4.1', '4.2', '4.3', '6.1', '4.4', '4.5', '8.1', '8.2', '11.9', '11.8', '11.7', '11.6')
          -- AND  col.data_type = :dataType
          -- AND  col.name = :colunmName
      ]
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:112)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:116)
	at org.springframework.jdbc.core.JdbcTemplate.translateException(JdbcTemplate.java:1556)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:677)
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:723)
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:748)
	at org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate.query(NamedParameterJdbcTemplate.java:178)
	at org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate.query(NamedParameterJdbcTemplate.java:186)
	at net.datatp.module.data.db.repository.DAOTemplate.query(DAOTemplate.java:175)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at net.datatp.module.data.db.repository.DAOTemplatePrimary$$SpringCGLIB$$0.query(<generated>)
	at net.datatp.module.data.db.SqlQueryUnitManager$QueryContext.query(SqlQueryUnitManager.java:78)
	at net.datatp.module.data.db.DAOService.searchDbRecords(DAOService.java:135)
	at cloud.datatp.jobtracking.JobTrackingLogicExecutor$SearchJobTracking.execute(JobTrackingLogicExecutor.java:71)
	at cloud.datatp.jobtracking.JobTrackingLogicExecutor$SearchJobTracking.execute(JobTrackingLogicExecutor.java:31)
	at net.datatp.lib.executable.ExecutableUnit.doExecute(ExecutableUnit.java:30)
	at net.datatp.lib.executable.Executor.execute(Executor.java:34)
	at net.datatp.module.service.ExecutableUnitManager.execute(ExecutableUnitManager.java:61)
	at cloud.datatp.jobtracking.JobTrackingLogic.searchJobTrackings(JobTrackingLogic.java:124)
	at cloud.datatp.jobtracking.JobTrackingService.searchJobTrackings(JobTrackingService.java:143)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at cloud.datatp.jobtracking.JobTrackingService$$SpringCGLIB$$0.searchJobTrackings(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:158)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:143)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.postgresql.util.PSQLException: ERROR: invalid reference to FROM-clause entry for table "lgc_job_tracking_column"
  Hint: Perhaps you meant to reference the table alias "col".
  Position: 1091
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2725)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2412)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:371)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:502)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:419)
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:194)
	at org.postgresql.jdbc.PgPreparedStatement.executeQuery(PgPreparedStatement.java:137)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeQuery(ProxyPreparedStatement.java:52)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeQuery(HikariProxyPreparedStatement.java)
	at org.springframework.jdbc.core.JdbcTemplate$1.doInPreparedStatement(JdbcTemplate.java:732)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:658)
	... 132 common frames omitted

2025-08-01T10:26:35.562+07:00 ERROR 57538 --- [qtp791217259-41] n.datatp.lib.executable.ExecutableUnit   : Error: 

org.springframework.jdbc.BadSqlGrammarException: PreparedStatementCallback; bad SQL grammar [
        SELECT 
          cell."id"                      AS id,
          cell."status"                  AS status,
          cell."internal_issue_count"    AS internal_issue_count,
          cell."external_issue_count"    AS external_issue_count,
          cell."job_tracking_id"         AS job_tracking_id,
          cell."job_tracking_project_id" AS job_tracking_project_id,
          cell."job_tracking_column_id"  AS job_tracking_column_id,
          cell."start_time"              AS start_time,
          cell."end_time"                AS end_time,
          cell."string_value"            AS string_value,
          cell."double_value"            AS double_value,
          cell."boolean_value"           AS boolean_value,
          cell."date_value"              AS date_value,
          cell."version"                 AS version,
          col."name"                     AS name,
          col."data_type"                AS data_type,
          col."data_input_type"          AS data_input_type
        FROM lgc_job_tracking_cell cell
        JOIN lgc_job_tracking_column col ON lgc_job_tracking_column.id = lgc_job_tracking_cell.job_tracking_column_id
        WHERE 1=1
           AND cell.job_tracking_id IN (247779, 247777, 247776, 247774, 247746, 247745, 247744, 247729, 247725, 247708, 247707, 247706, 247700, 247699, 247698, 247695, 247680, 247678, 247654, 247653, 247652, 247641, 247640, 247631, 247630, 247628, 247627, 247626, 247625, 247624, 247623, 247609, 247601, 247585, 247584, 247574, 247570, 247569, 247562, 247560, 247559, 247549, 247535, 247534, 247533, 247532, 247531, 247529, 247528, 247526, 247509, 247503, 247500, 247491, 247461, 247455, 247451, 247449, 247445, 247388, 247387, 247386, 247384, 247383, 247382, 247381, 247380, 247379, 247378, 247374, 247364, 247363, 247354, 247345, 247344, 247343, 247333, 247332, 247331, 247330, 247305, 247272, 247271, 247265, 247264, 247243, 247242, 247241, 247234, 247233, 247218, 247188, 247185, 247182, 247180, 247178, 247147, 247138, 247137, 247135, 247134, 247132, 247131, 247128, 247127, 247126, 247125, 247123, 247122, 247121, 247104, 247100, 247030, 246763, 246762, 246738, 246737, 246736, 246735, 246734, 246733, 246732, 246731, 246730, 246729, 246728, 246715, 246714, 246713, 246712, 246711, 246710, 246692, 246663, 246661, 246644, 246643, 246633, 246632, 246631, 246630, 246629, 246628, 246627, 246626, 246625, 246624, 246623, 246622, 246621, 246620, 246619, 246618, 246617, 246616, 246615, 246614, 246613, 246612, 246611, 246610, 246609, 246608, 246606, 246605, 246604, 246603, 246602, 246601, 246600, 246599, 246598, 246597, 246596, 246595, 246594, 246593, 246592, 246591, 246590, 246589, 246588, 246587, 246586, 246585, 246584, 246583, 246582, 246581, 246580, 246579, 246578, 246577, 246576, 246575, 246574, 246573, 246572, 246571, 246570, 246569, 246568, 246567, 246566, 246565, 246564, 246563, 246562, 246561, 246559, 246558, 246557, 246556, 246555, 246554, 246553, 246552, 246551, 246550, 246549, 246548, 246547, 246546, 246542, 246541, 246540, 246539, 246538, 246537, 246536, 246535, 246534, 246533, 246532, 246531, 246528, 246527, 246525, 246524, 246523, 246522, 246521, 246520, 246519, 246518, 246517, 246515, 246514, 246513, 246512, 246511, 246510, 246508, 246507, 246506, 246505, 246504, 246503, 246502, 246501, 246500, 246499, 246498, 246497, 246496, 246495, 246494, 246493, 246492, 246491, 246490, 246489, 246488, 246487, 246486, 246485, 246484, 246483, 246482, 246481, 246480, 246479, 246478, 246477, 246476, 246475, 246474, 246473, 246472, 246471, 246470, 246469, 246460, 246456, 246455, 246454, 246453, 246452, 246451, 246450, 246449, 246448, 246447, 246446, 246445, 246444, 246443, 246442, 246441, 246440, 246439, 246438, 246437, 246436, 246435, 246434, 246433, 246432, 246414, 246413, 246412, 246411, 246410, 246409, 246408, 246407, 246402, 246400, 246399, 246391, 246390, 246389, 246388, 246387, 246386, 246368, 246367, 246366, 246365, 246364, 246363, 246362, 246361, 246360, 246359, 246358, 246357, 246356, 246355, 246354, 246353, 246352, 246351, 246350, 246349, 246342, 246341, 246340, 246339, 246337, 246336, 246335, 246334, 246333, 246332, 246331, 246330, 246329, 246328, 246327, 246326, 246325, 246324, 246323, 246322, 246321, 246320, 246319, 246318, 246317, 246316, 246315, 246314, 246313, 246312, 246311, 246310, 246309, 246308, 246307, 246306, 246305, 246304, 246291, 246290, 246289, 246288, 246287, 246286, 246285, 246284, 246283, 246282, 246281, 246280, 246279, 246278, 246277, 246276, 246275, 246274, 246273, 246272, 246271, 246270, 246269, 246268, 246267, 246266, 246265, 246264, 246263, 246262, 246261, 246255, 246254, 246253, 246252, 246251, 246250, 246249, 246247, 246246, 246245, 246234, 246204, 246203, 246202, 246201, 246200, 246193, 246192, 246179, 246156, 246150, 246145, 246133, 246132, 246128, 246125, 246123, 246122, 246121, 246120, 246095, 246094, 246093, 246092, 246091, 246090, 246089, 246088, 246087, 246086, 246081, 246080, 246079, 246078, 246077, 246076, 246075, 246074, 246073, 246072, 246071, 246070, 246069, 246068, 246067, 246066, 246065, 246064, 246063, 246062, 246061, 246060, 246059, 246058, 246057, 246056, 246055, 246054, 246053, 246052, 246051, 246050, 246049, 246048, 246047, 246046, 246045, 246044, 246043, 246042, 246041, 246040, 246039, 246038, 246037, 246036, 246035, 246034, 246033, 246032, 246031, 246030, 246029, 246028, 246027, 246026, 246025, 246024, 246023, 246022, 246021, 246020, 246019, 246018, 246017, 246016, 245989, 245988, 245987, 245986, 245985, 245984, 245983, 245982, 245981, 245980, 245978, 245977, 245934, 245933, 245932, 245931, 245930, 245929, 245928, 245927, 245926, 245925, 245924, 245923, 245922, 245921, 245920, 245919, 245918, 245917, 245916, 245915, 245914, 245913, 245912, 245911, 245910, 245909, 245908, 245907, 245906, 245905, 245904, 245903, 245902, 245901, 245900, 245899, 245898, 245897, 245896, 245895, 245894, 245893, 245892, 245891, 245890, 245889, 245888, 245887, 245886, 245885, 245884, 245883, 245882, 245881, 245880, 245879, 245878, 245877, 245876, 245875, 245874, 245873, 245872, 245871, 245870, 245869, 245868, 245867, 245866, 245865, 245864, 245863, 245862, 245861, 245860, 245859, 245858, 245857, 245856, 245855, 245854, 245853, 245852, 245851, 245850, 245849, 245848, 245847, 245846, 245845, 245844, 245843, 245842, 245841, 245840, 245839, 245838, 245837, 245836, 245835, 245834, 245833, 245832, 245831, 245830, 245829, 245828, 245827, 245826, 245825, 245824, 245823, 245822, 245821, 245820, 245819, 245818, 245817, 245816, 245815, 245814, 245813, 245812, 245811, 245810, 245809, 245808, 245807, 245806, 245805, 245804, 245803, 245802, 245801, 245800, 245799, 245798, 245797, 245796, 245795, 245794, 245793, 245792, 245791, 245790, 245789, 245788, 245787, 245786, 245785, 245784, 245783, 245782, 245781, 245780, 245779, 245778, 245777, 245776, 245775, 245774, 245773, 245772, 245771, 245770, 245769, 245768, 245767, 245766, 245765, 245764, 245763, 245762, 245761, 245760, 245759, 245758, 245757, 245756, 245755, 245754, 245753, 245752, 245751, 245750, 245749, 245748, 245747, 245746, 245745, 245744, 245743, 245742, 245741, 245740, 245739, 245738, 245737, 245736, 245735, 245734, 245733, 245732, 245731, 245730, 245729, 245728, 245727, 245726, 245725, 245724, 245723, 245722, 245721, 245720, 245719, 245718, 245717, 245716, 245715, 245714, 245713, 245712, 245711, 245710, 245709, 245708, 245707, 245706, 245705, 245704, 245703, 245702, 245690, 245686, 245680, 245679, 245678, 245677, 245676, 245675, 245674, 245673, 245672, 245671, 245670, 245669, 245668, 245667, 245666, 245665, 245664, 245663, 245662, 245661, 245660, 245659, 245658, 245657, 245656, 245655, 245654, 245653, 245652, 245651, 245650, 245649, 245648, 245647, 245646, 245645, 245644, 245643, 245642, 245641, 245640, 245589, 245588, 245586, 245582, 245581, 245580, 245579, 245578, 245577, 245576, 245575, 245574, 245573, 245572, 245571, 245570, 245569, 245568, 245567, 245566, 245565, 245564, 245563, 245562, 245561, 245560, 245559, 245558, 245557, 245556, 245555, 245554, 245553, 245552, 245551, 245550, 245549, 245548, 245547, 245546, 245545, 245544, 245543, 245542, 245541, 245540, 245539, 245538, 245537, 245536, 245535, 245533, 245530, 245527, 245526, 245525, 245524, 245523, 245522, 245521, 245520, 245519, 245518, 245517, 245516, 245515, 245514, 245513, 245512, 245511, 245510, 245509, 245508, 245507, 245506, 245505, 245504, 245503, 245502, 245501, 245500, 245499, 245498, 245497, 245496, 245495, 245494, 245493, 245492, 245491, 245490, 245489, 245488, 245487, 245486, 245485, 245484, 245483, 245482, 245481, 245480, 245479, 245478, 245477, 245476, 245475, 245474, 245473, 245472, 245471, 245470, 245469, 245468, 245467, 245466, 245465, 245464, 245463, 245462, 245461, 245460, 245459, 245458, 245457, 245456, 245449, 245447, 245444, 245437, 245387, 245361, 245360, 245359, 245358, 245357, 245356, 245355, 245349, 245348, 245347, 245339, 245326, 245325, 245324, 245274, 245268, 245267, 245255, 245251, 245213, 245212, 245200, 245168, 245167, 245166, 245165, 245164, 245156, 245155, 245152, 245149, 245148, 245145, 245142, 245140, 245139, 245137, 245118, 245117, 245116, 245098, 245083, 245078, 245065, 245040, 245037, 245036, 245035, 245026, 245023, 245021, 245016, 245015, 245002, 245001, 245000, 244976, 244963, 244951, 244945, 244944, 244939, 244935, 244934, 244928, 244927, 244926, 244925, 244924, 244923, 244922, 244921, 244920, 244919, 244918, 244917, 244916, 244909, 244906, 244895, 244894, 244878, 244800, 244764, 244748, 244742, 244740, 244739, 244738, 244737, 244736, 244735, 244734, 244733, 244732, 244731, 244730, 244729, 244728, 244727, 244726, 244725, 244724, 244723, 244722, 244716, 244707, 244706, 244705, 244704, 244702, 244697, 244696, 244695, 244694, 244693, 244687, 244686, 244677, 244676, 244675, 244669, 244664, 244658, 244657, 244651, 244623, 244613, 244608, 244606, 244603, 244593, 244573, 244561, 244560, 244556, 244555, 244554, 244553, 244552, 244549, 244544, 244541, 244510, 244486, 244480, 244479, 244478, 244477, 244476, 244475, 244474, 244473, 244472, 244471, 244470, 244469, 244468, 244467, 244466, 244465, 244464, 244463, 244462, 244461, 244460, 244459, 244458, 244457, 244456, 244455, 244454, 244453, 244452, 244451, 244450, 244449, 244448, 244447, 244446, 244445, 244444, 244443, 244442, 244441, 244440, 244439, 244438, 244437, 244436, 244435, 244434, 244433, 244432, 244387, 244375, 244365, 244335, 244328, 244327, 244284, 244193, 244191, 244178, 244177, 244176, 244174, 244155, 244154, 244151, 244150, 244149, 244148, 244146, 244145, 244144, 244143, 244133, 244119, 244117, 244088, 244079, 244078, 244077, 244076, 244075, 244073, 244072, 244062, 243976, 243975, 243971, 243961, 243948, 243938, 243937, 243918, 243917, 243915, 243914, 243913, 243912, 243905, 243904, 243903, 243902, 243901, 243900, 243899, 243898, 243897, 243896, 243895, 243894, 243893, 243892, 243891, 243890, 243889, 243888, 243887, 243886, 243885, 243884, 243883, 243882, 243881, 243880, 243879, 243878, 243877, 243876, 243875, 243874, 243873, 243872, 243871, 243870, 243869, 243868, 243867, 243866, 243865, 243864, 243863, 243862, 243861, 243860, 243859, 243858, 243857, 243856, 243855, 243854, 243853, 243852, 243851, 243850, 243849, 243848, 243847, 243846, 243845, 243844, 243843, 243842, 243841, 243840, 243839, 243838, 243837, 243836, 243835, 243834, 243833, 243832, 243831, 243830, 243829, 243828, 243827, 243826, 243825, 243824, 243823, 243822, 243821, 243820, 243819, 243818, 243817, 243816, 243815, 243814, 243813, 243812, 243811, 243810, 243809, 243808, 243807, 243806, 243805, 243804, 243803, 243802, 243801, 243800, 243799, 243798, 243797, 243796, 243795, 243794, 243793, 243792, 243791, 243790, 243789, 243788, 243787, 243786, 243785, 243784, 243783, 243782, 243781, 243780, 243779, 243778, 243777, 243776, 243775, 243774, 243773, 243772, 243771, 243770, 243769, 243768, 243767, 243766, 243765, 243764, 243763, 243762, 243761, 243760, 243759, 243758, 243757, 243756, 243755, 243754, 243753, 243752, 243751, 243750, 243749, 243748, 243747, 243746, 243745, 243744, 243743, 243742, 243741, 243740, 243739, 243738, 243737, 243736, 243735, 243734, 243733, 243732, 243731, 243730, 243729, 243728, 243727, 243726, 243707, 243705, 243704, 243703, 243702, 243701, 243700, 243699, 243698, 243697, 243696, 243695, 243694, 243693, 243692, 243691, 243690, 243689, 243661, 243660, 243658, 243657, 243656, 243654, 243607, 243605, 243604, 243597, 243586, 243531, 243527, 243449, 243405, 243397, 243390, 243388, 243387, 243386, 243385, 243382, 243381, 243379, 243376, 243375, 243370, 243235, 243234, 243233, 243232, 243231, 243230, 243229, 243228, 243227, 243226, 243225, 243224, 243223, 243222, 243221, 243220, 243219, 243217, 243216, 243215, 243214, 243213, 243212, 243211, 243210, 243209, 243208, 243097, 243096, 243095, 243094, 243093, 243054, 243049, 243044, 243043, 243041, 243038, 243034, 243033, 243032, 243022, 243006, 243001, 242984, 242973, 242962, 242943, 242938, 242937, 242911, 242909, 242907, 242897, 242896, 242895, 242892, 242887, 242876, 242875, 242874, 242863, 242862, 242853, 242851, 242819, 242818, 242817, 242812, 242803, 242798, 242797, 242796, 242795, 242794, 242793, 242773, 242772, 242771, 242770, 242769, 242767, 242764, 242748, 242747, 242746, 242745, 242744, 242743, 242742, 242741, 242740, 242739, 242738, 242737, 242736, 242735, 242734, 242733, 242732, 242731, 242730, 242729, 242728, 242727, 242726, 242725, 242724, 242696, 242687, 242669, 242668, 242665, 242642, 242641, 242637, 242636, 242627, 242625, 242623, 242622, 242621, 242620, 242604, 242595, 242594, 242593, 242592, 242591, 242573, 242572, 242570, 242569, 242566, 242565, 242563, 242555, 242544, 242543, 242536, 242534, 242533, 242522, 242498, 242479, 242467, 242451, 242450, 242449, 242428, 242427, 242425, 242423, 242422, 242396, 242395, 242394, 242390, 242326, 242323, 242322, 242321, 242320, 242289, 242288, 242287, 242285, 242284, 242283, 242282, 242281, 242279, 242254, 242249, 242248, 242241, 242238, 242220, 242219, 242218, 242203, 242202, 242201, 242199, 242193, 242186, 242177, 242147, 242141, 242140, 242109, 242108, 242103, 242097, 242096, 242084, 242083, 242082, 242081, 242079, 242071, 242070, 242069, 242068, 242067, 242066, 242065, 242063, 242061, 242060, 242059, 242025, 241983, 241979, 241971, 241968, 241967, 241966, 241965, 241955, 241951, 241912, 241911, 241910, 241909, 241908, 241907, 241906, 241893, 241883, 241882, 241843, 241806, 241777, 241774, 241773, 241772, 241769, 241768, 241687, 241683, 241681, 241678, 241676, 241672, 241664, 241659, 241647, 241624, 241623, 241546, 241545, 241538, 241532, 241531, 241470, 241464, 241463, 241462, 241461, 241460, 241459, 241458, 241457, 241456, 241455, 241454, 241453, 241452, 241451, 241450, 241449, 241448, 241447, 241446, 241445, 241444, 241443, 241442, 241441, 241440, 241439, 241438, 241437, 241436, 241435, 241434, 241433, 241432, 241431, 241430, 241429, 241428, 241427, 241426, 241425, 241424, 241423, 241422, 241421, 241420, 241419, 241418, 241417, 241416, 241415, 241414, 241413, 241412, 241411, 241410, 241409, 241408, 241407, 241406, 241405, 241404, 241403, 241402, 241401, 241400, 241399, 241398, 241397, 241396, 241395, 241394, 241393, 241392, 241391, 241390, 241389, 241388, 241387, 241386, 241385, 241384, 241383, 241382, 241381, 241380, 241379, 241378, 241377, 241376, 241375, 241374, 241373, 241372, 241371, 241370, 241369, 241368, 241367, 241366, 241365, 241364, 241363, 241362, 241361, 241360, 241359, 241358, 241357, 241356, 241355, 241354, 241353, 241352, 241351, 241350, 241349, 241348, 241347, 241346, 241345, 241344, 241343, 241342, 241341, 241340, 241339, 241338, 241337, 241336, 241335, 241334, 241333, 241332, 241331, 241330, 241329, 241328, 241327, 241326, 241325, 241324, 241323, 241322, 241321, 241320, 241319, 241318, 241317, 241316, 241315, 241314, 241313, 241312, 241311, 241310, 241309, 241308, 241307, 241306, 241305, 241304, 241303, 241302, 241301, 241300, 241299, 241298, 241297, 241296, 241295, 241294, 241293, 241292, 241291, 241290, 241289, 241288, 241287, 241286, 241285, 241284, 241283, 241282, 241281, 241280, 241279, 241278, 241277, 241276, 241275, 241274, 241273, 241272, 241271, 241270, 241269, 241268, 241267, 241266, 241265, 241264, 241263, 241262, 241261, 241260, 241259, 241258, 241257, 241256, 241255, 241254, 241253, 241252, 241251, 241250, 241249, 241248, 241247, 241246, 241245, 241244, 241243, 241242, 241241, 241240, 241239, 241238, 241237, 241236, 241235, 241234, 241233, 241232, 241231, 241230, 241229, 241228, 241227, 241226, 241225, 241224, 241223, 241222, 241221, 241220, 241219, 241218, 241217, 241216, 241215, 241214, 241213, 241212, 241211, 241210, 241209, 241208, 241207, 241206, 241205, 241204, 241203, 241202, 241201, 241200, 241199, 241198, 241197, 241196, 241195, 241194, 241193, 241192, 241191, 241190, 241189, 241188, 241187, 241186, 241185, 241184, 241183, 241145, 241095, 241094, 241093, 241092, 241091, 241090, 241089, 241088, 241087, 241086, 241085, 241084, 241083, 241082, 241081, 241080, 241079, 241078, 241077, 241076, 241075, 241074, 241073, 241072, 241071, 241070, 241069, 241068, 241067, 241066, 241065, 241064, 241063, 241062, 241061, 241060, 241059, 241058, 241057, 241056, 241055, 241054, 241053, 241052, 241051, 241050, 241049, 241048, 241047, 241046, 241045, 241044, 241043, 241042, 241041, 241040, 241039, 241038, 241037, 241036, 241035, 241034, 241033, 241032, 241031, 241030, 241029, 241028, 241027, 241026, 241025, 241024, 241023, 241022, 241021, 241020, 240996, 240995, 240987, 240979, 240977, 240969, 240968, 240927, 240924, 240914, 240912, 240911, 240910, 240909, 240908, 240907, 240906, 240905, 240904, 240903, 240902, 240901, 240900, 240899, 240898, 240897, 240896, 240895, 240894, 240893, 240892, 240891, 240890, 240889, 240888, 240887, 240886, 240885, 240884, 240883, 240882, 240881, 240880, 240879, 240878, 240875, 240856, 240853, 240852, 240851, 240844, 240837, 240834, 240823, 240790, 240777, 240776, 240775, 240772, 240771, 240768, 240767, 240702, 240675, 240674, 240673, 240672, 240666, 240665, 240664, 240663, 240646, 240619, 240617, 240612, 240607, 240600, 240590, 240583, 240555, 240554, 240553, 240537, 240536, 240535, 240534, 240533, 240532, 240517, 240516, 240515, 240514, 240513, 240512, 240511, 240510, 240509, 240508, 240507, 240477, 240473, 240465, 240453, 240448, 240445, 240444, 240443, 240442, 240441, 240440, 240439, 240434, 240417, 240406, 240405, 240390, 240372, 240335, 240334, 240332, 240325, 240319, 240304, 240303, 240302, 240301, 240300, 240299, 240298, 240297, 240293, 240277, 240275, 240274, 240270, 240269, 240210, 240208, 240207, 240206, 240205, 240204, 240203, 240202, 240201, 240200, 240199, 240198, 240197, 240196, 240195, 240194, 240193, 240192, 240191, 240190, 240189, 240188, 240166, 240124, 240123, 240122, 240121, 240118, 240104, 240103, 240102, 240101, 240100, 240099, 240098, 240097, 240096, 240080, 240077, 240076, 240057, 240051, 240047, 240043, 240031, 240030, 240025, 240006, 239920, 239891, 239889, 239819, 239806, 239805, 239804, 239803, 239802, 239783, 239775, 239764, 239759, 239758, 239757, 239756, 239744, 239743, 239742, 239741, 239738, 239737, 239681, 239680, 239679, 239678, 239677, 239657, 239654, 239634, 239630, 239627, 239626, 239619, 239618, 239617, 239616, 239613, 239609, 239608, 239607, 239601, 239600, 239598, 239597, 239596, 239584, 239583, 239563, 239559, 239516, 239512, 239511, 239510, 239509, 239508, 239507, 239495, 239494, 239493, 239492, 239491, 239490, 239489, 239488, 239487, 239486, 239485, 239484, 239483, 239482, 239481, 239480, 239479, 239478, 239477, 239476, 239475, 239474, 239473, 239472, 239471, 239470, 239469, 239468, 239467, 239383, 239382, 239381, 239380, 239379, 239378, 239377, 239376, 239370, 239369, 239345, 239321, 239320, 239319, 239318, 239317, 239316, 239315, 239314, 239302, 239298, 239297, 239296, 239276, 239265, 239256, 239253, 239249, 239246, 239226, 239223, 239196, 239195, 239194, 239193, 239192, 239180, 239175, 239174, 239169, 239168, 239165, 239156, 239146, 239145, 239144, 239137, 239136, 239096, 239091, 239088, 239076, 239067, 239052, 239040, 239039, 239038, 239036, 239018, 239015, 239011, 239001, 239000, 238998, 238993, 238990, 238981, 238976, 238954, 238915, 238913, 238906, 238905, 238904, 238903, 238895, 238892, 238891, 238888, 238887, 238886, 238885, 238869, 238866, 238863, 238862, 238861, 238860, 238859, 238858, 238857, 238856, 238855, 238854, 238839, 238767, 238742, 238739, 238737, 238736, 238721, 238716, 238715, 238714, 238713, 238712, 238711, 238708, 238703, 238652, 238633, 238631, 238630, 238627, 238625, 238624, 238623, 238622, 238611, 238610, 238609, 238606, 238603, 238598, 238597, 238596, 238595, 238594, 238593, 238592, 238591, 238590, 238589, 238588, 238587, 238586, 238585, 238584, 238583, 238582, 238581, 238580, 238579, 238577, 238576, 238575, 238574, 238573, 238572, 238571, 238569, 238561, 238558, 238554, 238551, 238543, 238533, 238532, 238527, 238519, 238518, 238505, 238502, 238495, 238492, 238491, 238490, 238489, 238486, 238479, 238474, 238468, 238467, 238463, 238461, 238459, 238458, 238457, 238456, 238448, 238443, 238440, 238439, 238433, 238421, 238386, 238383, 238380, 238359, 238358, 238336, 238320, 238312, 238311, 238304, 238296, 238230, 238224, 238223, 238222, 238212, 238198, 238194, 238192, 238191, 238190, 238189, 238174, 238154, 238140, 238120, 238102, 238096, 238095, 238094, 238093, 238092, 238090, 238076, 238074, 238073, 238066, 238056, 238053, 238049, 238048, 238047, 238045, 238009, 238008, 238002, 237993, 237977, 237976, 237975, 237966, 237965, 237964, 237963, 237962, 237961, 237960, 237959, 237958, 237957, 237956, 237955, 237954, 237953, 237952, 237950, 237949, 237947, 237946, 237944, 237943, 237941, 237940, 237939, 237934, 237933, 237931, 237930, 237929, 237928, 237915, 237910, 237898, 237890, 237889, 237888, 237887, 237886, 237885, 237884, 237883, 237882, 237881, 237880, 237879, 237878, 237877, 237876, 237875, 237874, 237873, 237872, 237871, 237870, 237869, 237868, 237867, 237866, 237843, 237842, 237828, 237824, 237823, 237822, 237820, 237816, 237814, 237813, 237812, 237811, 237810, 237809, 237806, 237803, 237801, 237800, 237799, 237798, 237797, 237792, 237791, 237790, 237789, 237788, 237787, 237786, 237785, 237784, 237783, 237782, 237781, 237780, 237779, 237766, 237765, 237764, 237760, 237759, 237757, 237756, 237755, 237754, 237753, 237752, 237750, 237749, 237748, 237747, 237746, 237745, 237744, 237743, 237738, 237735, 237730, 237729, 237728, 237727, 237726, 237725, 237724, 237712, 237711, 237710, 237709, 237707, 237706, 237705, 237701, 237700, 237699, 237698, 237697, 237681, 237674, 237673, 237669, 237667, 237661, 237658, 237657, 237650, 237634, 237627, 237626, 237611, 237607, 237604, 237529, 237508, 237470, 237451, 237448, 237345, 237343, 237342, 237341, 237323, 237322, 237315, 237292, 237281, 237262, 237217, 237216, 237212, 237210, 237195, 237194, 237193, 237174, 237173, 237172, 237171, 237141, 237130, 237051, 237032, 237031, 237030, 236950, 236922, 236921, 236920, 236913, 236912, 236910, 236856, 236832, 236831, 236830, 236829, 236828, 236827, 236826, 236825, 236823, 236822, 236821, 236820, 236819, 236818, 236817, 236816, 236815, 236757, 236753, 236714, 236713, 236712, 236711, 236710, 236709, 236708, 236707, 236706, 236705, 236704, 236703, 236702, 236701, 236700, 236699, 236698, 236697, 236696, 236695, 236692, 236691, 236690, 236689, 236688, 236687, 236686, 236685, 236684, 236683, 236682, 236681, 236680, 236679, 236678, 236677, 236676, 236675, 236674, 236673, 236666, 236665, 236664, 236663, 236661, 236660, 236659, 236658, 236657, 236656, 236655, 236654, 236653, 236652, 236651, 236650, 236649, 236648, 236647, 236646, 236645, 236644, 236643, 236642, 236641, 236640, 236639, 236638, 236637, 236636, 236635, 236634, 236632, 236631, 236630, 236629, 236628, 236627, 236626, 236625, 236624, 236623, 236622, 236621, 236620, 236619, 236618, 236617, 236616, 236615, 236614, 236613, 236612, 236611, 236610, 236609, 236608, 236607, 236606, 236605, 236604, 236603, 236602, 236601, 236600, 236599, 236598, 236597, 236596, 236595, 236594, 236593, 236592, 236583, 236582, 236581, 236580, 236579, 236578, 236577, 236576, 236575, 236574, 236567, 236566, 236565, 236564, 236563, 236562, 236561, 236560, 236559, 236558, 236557, 236556, 236542, 236541, 236540, 236539, 236538, 236537, 236535, 236534, 236533, 236532, 236531, 236530, 236529, 236528, 236527, 236526, 236525, 236524, 236523, 236522, 236521, 236520, 236519, 236509, 236501, 236481, 236317, 236316, 236307, 236306, 236305, 236304, 236303, 236302, 236301, 236300, 236299, 236298, 236297, 236296, 236295, 236294, 236293, 236292, 236291, 236290, 236289, 236288, 236287, 236286, 236285, 236284, 236283, 236282, 236281, 236280, 236279, 236278, 236277, 236276, 236275, 236274, 236273, 236272, 236271, 236270, 236269, 236268, 236263, 236259, 236248, 236247, 236245, 236241, 236236, 236235, 236234, 236233, 236232, 236231, 236230, 236229, 236228, 236227, 236226, 236225, 236224, 236223, 236222, 236221, 236220, 236219, 236218, 236217, 236216, 236215, 236214, 236213, 236212, 236211, 236209, 236208, 236192, 236150, 236149, 236148, 236147, 236146, 236145, 236144, 236142, 236141, 236140, 236139, 236138, 236137, 236136, 236135, 236134, 236133, 236132, 236131, 236130, 236129, 236128, 236127, 236126, 236125, 236124, 236123, 236122, 236121, 236120, 236119, 236118, 236117, 236116, 236115, 236114, 236113, 236112, 236111, 236110, 236109, 236108, 236107, 236106, 236105, 236104, 236103, 236102, 236101, 236100, 236099, 236098, 236097, 236096, 236095, 236094, 236093, 236092, 236091, 236090, 236089, 236088, 236087, 236086, 236085, 236084, 236083, 236082, 236081, 236080, 236079, 236078, 236077, 236076, 236075, 236074, 236073, 236072, 236071, 236070, 236069, 236068, 236067, 236066, 236065, 236064, 236063, 236062, 236061, 236060, 236059, 236058, 236057, 236056, 236055, 236054, 236053, 236052, 236051, 236050, 236049, 236048, 236047, 236046, 236045, 236044, 236043, 236042, 236041, 236040, 236039, 236038, 236037, 236036, 236035, 236033, 236022, 236019, 236017, 236013, 236011, 235998, 235997, 235996, 235995, 235994, 235993, 235992, 235991, 235969, 235968, 235963, 235962, 235961, 235960, 235959, 235958, 235957, 235956, 235955, 235954, 235953, 235952, 235951, 235950, 235949, 235948, 235947, 235946, 235945, 235944, 235943, 235942, 235941, 235940, 235939, 235938, 235937, 235936, 235935, 235934, 235933, 235932, 235931, 235930, 235929, 235928, 235927, 235926, 235925, 235924, 235923, 235922, 235921, 235920, 235919, 235918, 235917, 235916, 235915, 235914, 235913, 235912, 235911, 235910, 235909, 235908, 235907, 235906, 235905, 235904, 235903, 235902, 235901, 235900, 235899, 235898, 235897, 235896, 235895, 235894, 235893, 235892, 235891, 235890, 235889, 235888, 235887, 235886, 235885, 235884, 235883, 235882, 235881, 235880, 235879, 235878, 235877, 235876, 235875, 235866, 235864, 235863, 235862, 235861, 235860, 235852, 235844, 235827, 235826, 235825, 235824, 235823, 235822, 235821, 235819, 235815, 235814, 235813, 235812, 235810, 235795, 235793, 235785, 235784, 235779, 235776, 235768, 235761, 235752, 235750, 235747, 235746, 235745, 235703, 235684, 235681, 235680, 235661, 235658, 235655, 235645, 235644, 235643, 235640, 235635, 235619, 235568, 235567, 235566, 235561, 235542, 235541, 235521, 235519, 235518, 235515, 235448, 235422, 235413, 235412, 235400, 235396, 235340, 235336, 235335, 235332, 235331, 235329, 235285, 235262, 235237, 235235, 235226, 235213, 235211, 235210, 235209, 235208, 235207, 235206, 235205, 235204, 235203, 235202, 235201, 235200, 235199, 235198, 235196, 235195, 235194, 235193, 235192, 235191, 235190, 235189, 235188, 235187, 235186, 235185, 235184, 235183, 235182, 235181, 235180, 235179, 235178, 235177, 235176, 235175, 235174, 235173, 235172, 235171, 235170, 235169, 235168, 235167, 235166, 235165, 235164, 235163, 235162, 235161, 235160, 235159, 235158, 235157, 235156, 235155, 235154, 235153, 235152, 235151, 235150, 235149, 235148, 235147, 235146, 235145, 235144, 235143, 235142, 235141, 235140, 235139, 235138, 235137, 235136, 235135, 235134, 235133, 235132, 235131, 235130, 235129, 235128, 235127, 235126, 235125, 235124, 235123, 235122, 235121, 235120, 235119, 235118, 235117, 235116, 235115, 235114, 235113, 235112, 235111, 235110, 235109, 235108, 235107, 235106, 235105, 235104, 235103, 235102, 235101, 235100, 235099, 235098, 235097, 235096, 235095, 235094, 235093, 235092, 235091, 235090, 235089, 235088, 235087, 235086, 235085, 235084, 235083, 235082, 235081, 235080, 235079, 235078, 235077, 235076, 235075, 235074, 235073, 235072, 235071, 235070, 235069, 235068, 235067, 235066, 235065, 235064, 235063, 235062, 235061, 235060, 235059, 235058, 235057, 235056, 235055, 235054, 235050, 235049, 235048, 235047, 235046, 235045, 235044, 235043, 235042, 235041, 235040, 235039, 235038, 235037, 235036, 235035, 235034, 235033, 235032, 235031, 235030, 235029, 235028, 235027, 235026, 235025, 235024, 235023, 235022, 235021, 235020, 235019, 235018, 235017, 235016, 235015, 235014, 235013, 235012, 235011, 235010, 235009, 235008, 235007, 235006, 235005, 235004, 235003, 235002, 235001, 235000, 234999, 234998, 234997, 234996, 234995, 234994, 234993, 234980, 234967, 234966, 234964, 234963, 234962, 234961, 234960, 234958, 234956, 234953, 234941, 234929, 234909, 234894, 234884, 234883, 234882, 234880, 234879, 234876, 234875, 234874, 234873, 234872, 234870, 234864, 234768, 234761, 234760, 234755, 234748, 234738, 234734, 234733, 234732, 234728, 234719, 234704, 234703, 234702, 234701, 234700, 234699, 234698, 234697, 234696, 234695, 234689, 234688, 234687, 234686, 234685, 234684, 234683, 234682, 234670, 234669, 234667, 234661, 234649, 234648, 234634, 234622, 234606, 234595, 234585, 234584, 234569, 234564, 234563, 234560, 234544, 234542, 234541, 234499, 234498, 234497, 234496, 234495, 234494, 234493, 234492, 234486, 234484, 234473, 234472, 234471, 234470, 234469, 234468, 234467, 234466, 234465, 234464, 234463, 234462, 234461, 234460, 234459, 234458, 234457, 234456, 234455, 234454, 234453, 234452, 234451, 234450, 234449, 234448, 234447, 234446, 234436, 234426, 234425, 234424, 234409, 234407, 234383, 234380, 234377, 234376, 234366, 234358, 234339, 234336, 234335, 234333, 234329, 234327, 234320, 234319, 234318, 234307, 234306, 234305, 234301, 234296, 234293, 234290, 234284, 234272, 234263, 234262, 234260, 234259, 234221, 234220, 234219, 234218, 234194, 234178, 234177, 234176, 234175, 234174, 234167, 234156, 234148, 234133, 234132, 234131, 234125, 234093, 234090, 234081, 234079, 234078, 234066, 234064, 234062, 234060, 234059, 234058, 234053, 234050, 234019, 233967, 233945, 233944, 233943, 233942, 233941, 233940, 233939, 233938, 233937, 233936, 233935, 233934, 233933, 233932, 233931, 233930, 233929, 233928, 233927, 233926, 233925, 233924, 233923, 233922, 233917, 233908, 233905, 233897, 233895, 233893, 233889, 233882, 233864, 233863, 233862, 233861, 233860, 233859, 233858, 233857, 233856, 233855, 233854, 233853, 233852, 233851, 233850, 233845, 233838, 233837, 233836, 233835, 233834, 233833, 233813, 233812, 233811, 233810, 233798, 233796, 233795, 233794, 233775, 233772, 233771, 233768, 233765, 233764, 233763, 233762, 233760, 233756, 233749, 233748, 233734, 233728, 233725, 233723, 233711, 233707, 233706, 233705, 233704, 233703, 233702, 233701, 233700, 233699, 233698, 233695, 233693, 233692, 233691, 233689, 233685, 233683, 233658, 233635, 233632, 233627, 233626, 233599, 233598, 233481, 233480, 233479, 233478, 233468, 233467, 233460, 233458, 233455, 233454, 233436, 233410, 233409, 233402, 233392, 233387, 233378, 233283, 233282, 233277, 233276, 233275, 233274, 233273, 233272, 233271, 233270, 233269, 233268, 233267, 233266, 233265, 233264, 233263, 233262, 233261, 233241, 233240, 233239, 233238, 233237, 233236, 233235, 233231, 233225, 233208, 233207, 233206, 233203, 233194, 233179, 233178, 233174, 233173, 233161, 233138, 233122, 233121, 233109, 233085, 233074, 233073, 233072, 233071, 233070, 233069, 233068, 233067, 233066, 233065, 233064, 233063, 233062, 233058, 233057, 233045, 233040, 233039, 233031, 233026, 233025, 233024, 233021, 233012, 233011, 233010, 233009, 232982, 232956, 232954, 232953, 232947, 232946, 232936, 232814, 232813, 232812, 232805, 232799, 232779, 232778, 232776, 232775, 232773, 232755, 232747, 232739, 232738, 232737, 232727, 232718, 232689, 232676, 232665, 232664, 232663, 232662, 232661, 232660, 232659, 232658, 232657, 232656, 232655, 232654, 232653, 232652, 232651, 232650, 232648, 232646, 232645, 232644, 232643, 232642, 232641, 232640, 232639, 232638, 232637, 232636, 232635, 232634, 232633, 232632, 232631, 232630, 232629, 232628, 232627, 232626, 232625, 232624, 232623, 232622, 232621, 232620, 232619, 232618, 232617, 232616, 232615, 232614, 232613, 232612, 232611, 232610, 232609, 232608, 232607, 232606, 232605, 232604, 232603, 232602, 232601, 232600, 232599, 232598, 232597, 232596, 232595, 232594, 232593, 232592, 232591, 232590, 232589, 232588, 232587, 232586, 232585, 232584, 232583, 232582, 232581, 232580, 232579, 232578, 232577, 232576, 232575, 232574, 232573, 232572, 232571, 232570, 232569, 232568, 232567, 232566, 232565, 232564, 232563, 232562, 232561, 232560, 232559, 232558, 232557, 232556, 232555, 232554, 232553, 232552, 232551, 232550, 232549, 232548, 232547, 232546, 232545, 232544, 232543, 232542, 232541, 232540, 232539, 232538, 232537, 232536, 232535, 232534, 232531, 232530, 232528, 232470, 232458, 232455, 232454, 232453, 232452, 232451, 232450, 232447, 232446, 232445, 232444, 232439, 232438, 232436, 232430, 232422, 232421, 232399, 232366, 232356, 232348, 232249, 232160, 232159, 232158, 232157, 232156, 232155, 232154, 232153, 232126, 232124, 232123, 232122, 232119, 232118, 232116, 232108, 232092, 232091, 232090, 232089, 232088, 232087, 232086, 232085, 232084, 232083, 232082, 232081, 232076, 232074, 232053, 232004, 231990, 231936, 231935, 231928, 231927, 231926, 231925, 231924, 231923, 231922, 231921, 231915, 231913, 231888, 231880, 231825, 231824, 231823, 231822, 231821, 231819, 231818, 231817, 231816, 231815, 231814, 231802, 231801, 231800, 231799, 231798, 231797, 231796, 231771, 231770, 231769, 231768, 231767, 231766, 231765, 231751, 231749, 231668, 231667, 231666, 231665, 231664, 231655, 231653, 231652, 231651, 231650, 231649, 231648, 231647, 231646, 231645, 231644, 231643, 231642, 231641, 231640, 231639, 231638, 231637, 231636, 231635, 231634, 231633, 231632, 231631, 231630, 231629, 231628, 231627, 231626, 231625, 231624, 231623, 231622, 231621, 231620, 231619, 231618, 231617, 231616, 231615, 231614, 231613, 231612, 231611, 231610, 231609, 231608, 231607, 231606, 231605, 231604, 231603, 231602, 231601, 231600, 231599, 231598, 231597, 231596, 231595, 231594, 231593, 231592, 231591, 231590, 231589, 231588, 231587, 231586, 231585, 231584, 231583, 231582, 231581, 231580, 231579, 231578, 231577, 231576, 231564, 231563, 231513, 231512, 231511, 231510, 231509, 231434, 231433, 231423, 231421, 231418, 231417, 231416, 231415, 231414, 231413, 231412, 231411, 231410, 231409, 231408, 231407, 231406, 231405, 231404, 231403, 231402, 231401, 231400, 231399, 231398, 231397, 231396, 231395, 231394, 231393, 231392, 231391, 231390, 231389, 231388, 231344, 231343, 231318, 231316, 231315, 231312, 231309, 231308, 231306, 231305, 231304, 231288, 231287, 231286, 231285, 231282, 231281, 231280, 231279, 231278, 231277, 231271, 231270, 231269, 231268, 231267, 231266, 231265, 231264, 231263, 231254, 231244, 231243, 231235, 231223, 231221, 231214, 231212, 231211, 231210, 231209, 231208, 231207, 231206, 231205, 231204, 231203, 231202, 231201, 231200, 231199, 231198, 231197, 231195, 231194, 231193, 231192, 231191, 231190, 231189, 231188, 231187, 231186, 231185, 231184, 231183, 231182, 231181, 231180, 231179, 231178, 231177, 231176, 231175, 231174, 231173, 231172, 231171, 231170, 231169, 231168, 231167, 231166, 231165, 231164, 231163, 231162, 231161, 231160, 231159, 231158, 231153, 231152, 231151, 231150, 231149, 231148, 231147, 231146, 231145, 231144, 231143, 231142, 231141, 231140, 231139, 231138, 231137, 231132, 231131, 231130, 231129, 231128, 231127, 231126, 231125, 231124, 231123, 231122, 231121, 231120, 231119, 231118, 231117, 231116, 231115, 231114, 231113, 231112, 231111, 231110, 231109, 231108, 231107, 231106, 231105, 231104, 231103, 231102, 231101, 231100, 231095, 231094, 231093, 231092, 231091, 231090, 231089, 231084, 231083, 231082, 231081, 231080, 231079, 231078, 231077, 231076, 231075, 231074, 231073, 231072, 231071, 231070, 231069, 231068, 231067, 231066, 231065, 231064, 231063, 231062, 231061, 231060, 231059, 231058, 231057, 231056, 231055, 231054, 231053, 231052, 231051, 231050, 231049, 231048, 231047, 231046, 231045, 231044, 231043, 231042, 231041, 231040, 231039, 231037, 231036, 231035, 231034, 231033, 231032, 231031, 231030, 231029, 231028, 231026, 231025, 231024, 231023, 231022, 231021, 231020, 231019, 231018, 231017, 231016, 231015, 231014, 231013, 231012, 231011, 231010, 231009, 231008, 231007, 231006, 231005, 231004, 231003, 231002, 231001, 231000, 230999, 230998, 230997, 230996, 230995, 230994, 230993, 230992, 230991, 230990, 230989, 230988, 230987, 230986, 230985, 230984, 230983, 230982, 230981, 230980, 230979, 230978, 230977, 230976, 230975, 230974, 230973, 230972, 230971, 230970, 230969, 230968, 230967, 230966, 230965, 230964, 230963, 230962, 230961, 230960, 230959, 230958, 230957, 230956, 230955, 230954, 230953, 230952, 230951, 230950, 230949, 230948, 230947, 230946, 230945, 230944, 230943, 230942, 230941, 230940, 230939, 230938, 230937, 230936, 230935, 230934, 230933, 230932, 230931, 230930, 230929, 230928, 230927, 230926, 230925, 230924, 230923, 230922, 230921, 230920, 230919, 230918, 230917, 230916, 230915, 230914, 230913, 230912, 230857, 230856, 230854, 230847, 230843, 230842, 230835, 230834, 230833, 230832, 230831, 230824, 230822, 230818, 230815, 230814, 230797, 230772, 230771, 230769, 230768, 230764, 230763, 230762, 230754, 230750, 230741, 230740, 230739, 230736, 230727, 230721, 230711, 230709, 230706, 230705, 230701, 230700, 230699, 230692, 230689, 230688, 230685, 230683, 230652, 230630, 230628, 230626, 230625, 230595, 230592, 230589, 230577, 230575, 230568, 230560, 230554, 230553, 230552, 230547, 230539, 230483, 230482, 230481, 230480, 230479, 230478, 230477, 230476, 230475, 230474, 230473, 230472, 230471, 230470, 230469, 230468, 230467, 230466, 230465, 230464, 230463, 230462, 230461, 230460, 230459, 230458, 230457, 230456, 230455, 230454, 230453, 230452, 230451, 230450, 230449, 230448, 230447, 230446, 230445, 230444, 230443, 230442, 230441, 230440, 230439, 230438, 230437, 230436, 230435, 230434, 230430, 230425, 230423, 230412, 230411, 230399, 230396, 230395, 230394, 230392, 230390, 230381, 230380, 230379, 230378, 230375, 230374, 230361, 230359, 230342, 230339, 230332, 230331, 230314, 230303, 230302, 230284, 230265, 230261, 230252, 230247, 230245, 230232, 230195, 230194, 230193, 230192, 230191, 230190, 230189, 230188, 230187, 230186, 230185, 230184, 230171, 230169, 230160, 230130, 230129, 230128, 230126, 230125, 230122, 230101, 230100, 230090, 230089, 230088, 230087, 230085, 230084, 230078, 230076, 230073, 230066, 230019, 230015, 230007, 230006, 230005, 230004, 229991, 229981, 229980, 229973, 229970, 229965, 229939, 229938, 229933, 229927, 229925, 229924, 229923, 229897, 229895, 229893, 229887, 229885, 229884, 229883, 229880, 229879, 229878, 229877, 229856, 229824, 229823, 229822, 229821, 229820, 229819, 229774, 229769, 229763, 229762, 229760, 229736, 229729, 229727, 229726, 229725, 229724, 229720, 229702, 229697, 229691, 229682, 229673, 229672, 229671, 229670, 229669, 229668, 229667, 229666, 229665, 229664, 229663, 229662, 229661, 229660, 229659, 229658, 229657, 229656, 229655, 229654, 229653, 229652, 229651, 229650, 229649, 229648, 229647, 229646, 229645, 229644, 229643, 229642, 229641, 229640, 229639, 229638, 229637, 229636, 229635, 229634, 229633, 229632, 229631, 229630, 229629, 229628, 229627, 229626, 229625, 229624, 229623, 229622, 229621, 229620, 229619, 229618, 229617, 229616, 229615, 229614, 229613, 229612, 229611, 229610, 229609, 229608, 229607, 229606, 229605, 229604, 229603, 229602, 229601, 229600, 229599, 229598, 229597, 229596, 229595, 229594, 229593, 229592, 229591, 229590, 229589, 229588, 229587, 229586, 229585, 229584, 229583, 229582, 229581, 229580, 229579, 229578, 229577, 229576, 229575, 229574, 229573, 229572, 229571, 229570, 229569, 229568, 229567, 229566, 229565, 229564, 229560, 229528, 229527, 229512, 229511, 229495, 229487, 229486, 229480, 229478, 229469, 229467, 229466, 229465, 229464, 229455, 229452, 229423, 229421, 229418, 229394, 229352, 229351, 229350, 229349, 229348, 229347, 229340, 229339, 229338, 229302, 229296, 229295, 229294, 229293, 229286, 229269, 229237, 229225, 229224, 229221, 229215, 229208, 229207, 229206, 229205, 229204, 229203, 229163, 229162, 229056, 229055, 228982, 228981, 228967, 228966, 228965, 228963, 228956, 228955, 228954, 228953, 228952, 228935, 228928, 228927, 228926, 228925, 228924, 228923, 228922, 228921, 228920, 228919, 228918, 228917, 228916, 228915, 228914, 228913, 228912, 228911, 228910, 228909, 228908, 228907, 228906, 228905, 228904, 228903, 228902, 228901, 228900, 228899, 228898, 228897, 228896, 228895, 228894, 228893)
           AND col.name IN ('agent', 'source', '4.3.1', '4.3.2', 'eta', 'etd', 'preAlertDn', 'agentPriceChecking', 'cdsDate', 'consignee', 'agent-key-date', 'saleman', 'vesselNameVoy', 'volume', '1.3.1', '1.1', '1.2', '10.1', 'mblNo', '1.3', '3.1', '1.4', '3.2', '5.1', '5.2', '5.3', '7.1', '5.4', '9.1', 'etaTransitPort', '9.2', '9.3', 'status', 'falseReason', '9.4', '9.5', 'note', '9.6', 'containerNo', '9.7', 'pod', 'subjectEmail', 'combineMbl', 'loadGateIn', 'mblTelex', 'etdTransitPort', 'hblTelex', 'pol', 'shipmentType', 'combine-done', 'jobNo', 'cutOff', 'shipmentDate', 'jobType', 'jobCreatedDate', 'costDemDest', 'agentFeedback', '11.5', 'cfs', '11.4', 'carrier', '1.4.1', '11.3', '11.2', 'hblNo', '11.1', '2.1', '2.2', '2.3', '4.1', '4.2', '4.3', '6.1', '4.4', '4.5', '8.1', '8.2', '11.9', '11.8', '11.7', '11.6')
          -- AND  col.data_type = :dataType
          -- AND  col.name = :colunmName
      ]
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:112)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107)
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:116)
	at org.springframework.jdbc.core.JdbcTemplate.translateException(JdbcTemplate.java:1556)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:677)
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:723)
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:748)
	at org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate.query(NamedParameterJdbcTemplate.java:178)
	at org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate.query(NamedParameterJdbcTemplate.java:186)
	at net.datatp.module.data.db.repository.DAOTemplate.query(DAOTemplate.java:175)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at net.datatp.module.data.db.repository.DAOTemplatePrimary$$SpringCGLIB$$0.query(<generated>)
	at net.datatp.module.data.db.SqlQueryUnitManager$QueryContext.query(SqlQueryUnitManager.java:78)
	at net.datatp.module.data.db.DAOService.searchDbRecords(DAOService.java:135)
	at cloud.datatp.jobtracking.JobTrackingLogicExecutor$SearchJobTracking.execute(JobTrackingLogicExecutor.java:71)
	at cloud.datatp.jobtracking.JobTrackingLogicExecutor$SearchJobTracking.execute(JobTrackingLogicExecutor.java:31)
	at net.datatp.lib.executable.ExecutableUnit.doExecute(ExecutableUnit.java:30)
	at net.datatp.lib.executable.Executor.execute(Executor.java:34)
	at net.datatp.module.service.ExecutableUnitManager.execute(ExecutableUnitManager.java:61)
	at cloud.datatp.jobtracking.JobTrackingLogic.searchJobTrackings(JobTrackingLogic.java:124)
	at cloud.datatp.jobtracking.JobTrackingService.searchJobTrackings(JobTrackingService.java:143)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at cloud.datatp.jobtracking.JobTrackingService$$SpringCGLIB$$0.searchJobTrackings(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:158)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:143)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.postgresql.util.PSQLException: ERROR: invalid reference to FROM-clause entry for table "lgc_job_tracking_column"
  Hint: Perhaps you meant to reference the table alias "col".
  Position: 1091
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2725)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2412)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:371)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:502)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:419)
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:194)
	at org.postgresql.jdbc.PgPreparedStatement.executeQuery(PgPreparedStatement.java:137)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeQuery(ProxyPreparedStatement.java:52)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeQuery(HikariProxyPreparedStatement.java)
	at org.springframework.jdbc.core.JdbcTemplate$1.doInPreparedStatement(JdbcTemplate.java:732)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:658)
	... 132 common frames omitted

2025-08-01T10:26:35.574+07:00 ERROR 57538 --- [qtp791217259-41] n.d.m.monitor.call.EndpointCallContext   : Start call with component JobTrackingService, method searchJobTrackings, arguments
[ {
  "tenantId" : "default",
  "companyId" : 8,
  "companyParentId" : 4,
  "companyCode" : "beehph",
  "companyLabel" : "Bee HPH",
  "companyFullName" : "BEE LOGISTICS CORPORATION - HAI PHONG BRANCH OFFICE",
  "loginId" : "nhat.le",
  "accountId" : 2089,
  "token" : "9e57ccec8e98a2e2c2d25e3766e4540f",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node04cbi2hmt3lb6hhc6q9pnzh7r0",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : "Employee",
  "allowAccessCompanies" : {
    "bee" : {
      "companyId" : 4,
      "companyParentId" : 0,
      "companyCode" : "bee",
      "companyLabel" : "Bee Corp",
      "companyFullName" : null
    },
    "beescs" : {
      "companyId" : 12,
      "companyParentId" : 1,
      "companyCode" : "beescs",
      "companyLabel" : "BEESCS",
      "companyFullName" : null
    },
    "beehan" : {
      "companyId" : 16,
      "companyParentId" : 4,
      "companyCode" : "beehan",
      "companyLabel" : "Bee HAN",
      "companyFullName" : null
    },
    "beehph" : {
      "companyId" : 8,
      "companyParentId" : 4,
      "companyCode" : "beehph",
      "companyLabel" : "Bee HPH",
      "companyFullName" : null
    },
    "beehcm" : {
      "companyId" : 17,
      "companyParentId" : 4,
      "companyCode" : "beehcm",
      "companyLabel" : "Bee HCM",
      "companyFullName" : null
    },
    "beedad" : {
      "companyId" : 18,
      "companyParentId" : 4,
      "companyCode" : "beedad",
      "companyLabel" : "Bee DAD",
      "companyFullName" : null
    },
    "datatp" : {
      "companyId" : 10,
      "companyParentId" : 1,
      "companyCode" : "datatp",
      "companyLabel" : "DataTP Cloud",
      "companyFullName" : null
    }
  },
  "featurePermissions" : [ {
    "id" : 6989,
    "appId" : 2,
    "appModule" : "user",
    "appName" : "my-space",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 11332,
    "appId" : 68,
    "appModule" : "document",
    "appName" : "company-document",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 8423,
    "appId" : 15,
    "appModule" : "logistics",
    "appName" : "user-logistics-prices",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 983,
    "appId" : 5,
    "appModule" : "admin",
    "appName" : "admin-space",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 1302,
    "appId" : 26,
    "appModule" : "okr",
    "appName" : "okr",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 1356,
    "appId" : 10,
    "appModule" : "partner",
    "appName" : "partner",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 1576,
    "appId" : 13,
    "appModule" : "logistics",
    "appName" : "company-logistics-settings",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 5515,
    "appId" : 64,
    "appModule" : "job-tracking",
    "appName" : "job-tracking",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 1580,
    "appId" : 16,
    "appModule" : "logistics",
    "appName" : "user-logistics-sales",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 5031,
    "appId" : 27,
    "appModule" : "logistics",
    "appName" : "user-partner-logistics-crm",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 2139,
    "appId" : 18,
    "appModule" : "system",
    "appName" : "system",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 2758,
    "appId" : 19,
    "appModule" : "sample",
    "appName" : "reactjs-lib",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 2835,
    "appId" : 21,
    "appModule" : "tms",
    "appName" : "user-tms-app",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 3852,
    "appId" : 4,
    "appModule" : "communication",
    "appName" : "communication",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 4484,
    "appId" : 61,
    "appModule" : "tms",
    "appName" : "user-tms-ops",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 4487,
    "appId" : 62,
    "appModule" : "tms",
    "appName" : "user-tms-round-used",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 5726,
    "appId" : 9,
    "appModule" : "company",
    "appName" : "company-hr",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 11732,
    "appId" : 70,
    "appModule" : "hr",
    "appName" : "company-kpi",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 988,
    "appId" : 6,
    "appModule" : "company",
    "appName" : "company-space",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 12977,
    "appId" : 67,
    "appModule" : "tms",
    "appName" : "user-vehicle-trip",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 12978,
    "appId" : 25,
    "appModule" : "tms",
    "appName" : "user-vehicle-fleets",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 13064,
    "appId" : 7,
    "appModule" : "company",
    "appName" : "company-asset",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 14281,
    "appId" : 80,
    "appModule" : "logistics",
    "appName" : "company-logistics-sales",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 13102,
    "appId" : 79,
    "appModule" : "company",
    "appName" : "user-asset",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 14455,
    "appId" : 3,
    "appModule" : "project",
    "appName" : "project",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  } ],
  "attributes" : { },
  "clientId" : "default:nhat.le"
}, {
  "parentId" : 0,
  "id" : 8,
  "code" : "beehph",
  "label" : "Bee HPH",
  "fullName" : "BEE LOGISTICS CORPORATION - HAI PHONG BRANCH OFFICE"
}, {
  "params" : {
    "jobTrackingProjectId" : 1,
    "withPermission" : true,
    "accessColumns" : [ "agent", "source", "4.3.1", "4.3.2", "eta", "etd", "preAlertDn", "agentPriceChecking", "cdsDate", "consignee", "agent-key-date", "saleman", "vesselNameVoy", "volume", "1.3.1", "1.1", "1.2", "10.1", "mblNo", "1.3", "3.1", "1.4", "3.2", "5.1", "5.2", "5.3", "7.1", "5.4", "9.1", "etaTransitPort", "9.2", "9.3", "status", "falseReason", "9.4", "9.5", "note", "9.6", "containerNo", "9.7", "pod", "subjectEmail", "combineMbl", "loadGateIn", "mblTelex", "etdTransitPort", "hblTelex", "pol", "shipmentType", "combine-done", "jobNo", "cutOff", "shipmentDate", "jobType", "jobCreatedDate", "costDemDest", "agentFeedback", "11.5", "cfs", "11.4", "carrier", "1.4.1", "11.3", "11.2", "hblNo", "11.1", "2.1", "2.2", "2.3", "4.1", "4.2", "4.3", "6.1", "4.4", "4.5", "8.1", "8.2", "11.9", "11.8", "11.7", "11.6" ],
    "companyId" : 8,
    "jobTrackingIds" : [ 247779, 247777, 247776, 247774, 247746, 247745, 247744, 247729, 247725, 247708, 247707, 247706, 247700, 247699, 247698, 247695, 247680, 247678, 247654, 247653, 247652, 247641, 247640, 247631, 247630, 247628, 247627, 247626, 247625, 247624, 247623, 247609, 247601, 247585, 247584, 247574, 247570, 247569, 247562, 247560, 247559, 247549, 247535, 247534, 247533, 247532, 247531, 247529, 247528, 247526, 247509, 247503, 247500, 247491, 247461, 247455, 247451, 247449, 247445, 247388, 247387, 247386, 247384, 247383, 247382, 247381, 247380, 247379, 247378, 247374, 247364, 247363, 247354, 247345, 247344, 247343, 247333, 247332, 247331, 247330, 247305, 247272, 247271, 247265, 247264, 247243, 247242, 247241, 247234, 247233, 247218, 247188, 247185, 247182, 247180, 247178, 247147, 247138, 247137, 247135, 247134, 247132, 247131, 247128, 247127, 247126, 247125, 247123, 247122, 247121, 247104, 247100, 247030, 246763, 246762, 246738, 246737, 246736, 246735, 246734, 246733, 246732, 246731, 246730, 246729, 246728, 246715, 246714, 246713, 246712, 246711, 246710, 246692, 246663, 246661, 246644, 246643, 246633, 246632, 246631, 246630, 246629, 246628, 246627, 246626, 246625, 246624, 246623, 246622, 246621, 246620, 246619, 246618, 246617, 246616, 246615, 246614, 246613, 246612, 246611, 246610, 246609, 246608, 246606, 246605, 246604, 246603, 246602, 246601, 246600, 246599, 246598, 246597, 246596, 246595, 246594, 246593, 246592, 246591, 246590, 246589, 246588, 246587, 246586, 246585, 246584, 246583, 246582, 246581, 246580, 246579, 246578, 246577, 246576, 246575, 246574, 246573, 246572, 246571, 246570, 246569, 246568, 246567, 246566, 246565, 246564, 246563, 246562, 246561, 246559, 246558, 246557, 246556, 246555, 246554, 246553, 246552, 246551, 246550, 246549, 246548, 246547, 246546, 246542, 246541, 246540, 246539, 246538, 246537, 246536, 246535, 246534, 246533, 246532, 246531, 246528, 246527, 246525, 246524, 246523, 246522, 246521, 246520, 246519, 246518, 246517, 246515, 246514, 246513, 246512, 246511, 246510, 246508, 246507, 246506, 246505, 246504, 246503, 246502, 246501, 246500, 246499, 246498, 246497, 246496, 246495, 246494, 246493, 246492, 246491, 246490, 246489, 246488, 246487, 246486, 246485, 246484, 246483, 246482, 246481, 246480, 246479, 246478, 246477, 246476, 246475, 246474, 246473, 246472, 246471, 246470, 246469, 246460, 246456, 246455, 246454, 246453, 246452, 246451, 246450, 246449, 246448, 246447, 246446, 246445, 246444, 246443, 246442, 246441, 246440, 246439, 246438, 246437, 246436, 246435, 246434, 246433, 246432, 246414, 246413, 246412, 246411, 246410, 246409, 246408, 246407, 246402, 246400, 246399, 246391, 246390, 246389, 246388, 246387, 246386, 246368, 246367, 246366, 246365, 246364, 246363, 246362, 246361, 246360, 246359, 246358, 246357, 246356, 246355, 246354, 246353, 246352, 246351, 246350, 246349, 246342, 246341, 246340, 246339, 246337, 246336, 246335, 246334, 246333, 246332, 246331, 246330, 246329, 246328, 246327, 246326, 246325, 246324, 246323, 246322, 246321, 246320, 246319, 246318, 246317, 246316, 246315, 246314, 246313, 246312, 246311, 246310, 246309, 246308, 246307, 246306, 246305, 246304, 246291, 246290, 246289, 246288, 246287, 246286, 246285, 246284, 246283, 246282, 246281, 246280, 246279, 246278, 246277, 246276, 246275, 246274, 246273, 246272, 246271, 246270, 246269, 246268, 246267, 246266, 246265, 246264, 246263, 246262, 246261, 246255, 246254, 246253, 246252, 246251, 246250, 246249, 246247, 246246, 246245, 246234, 246204, 246203, 246202, 246201, 246200, 246193, 246192, 246179, 246156, 246150, 246145, 246133, 246132, 246128, 246125, 246123, 246122, 246121, 246120, 246095, 246094, 246093, 246092, 246091, 246090, 246089, 246088, 246087, 246086, 246081, 246080, 246079, 246078, 246077, 246076, 246075, 246074, 246073, 246072, 246071, 246070, 246069, 246068, 246067, 246066, 246065, 246064, 246063, 246062, 246061, 246060, 246059, 246058, 246057, 246056, 246055, 246054, 246053, 246052, 246051, 246050, 246049, 246048, 246047, 246046, 246045, 246044, 246043, 246042, 246041, 246040, 246039, 246038, 246037, 246036, 246035, 246034, 246033, 246032, 246031, 246030, 246029, 246028, 246027, 246026, 246025, 246024, 246023, 246022, 246021, 246020, 246019, 246018, 246017, 246016, 245989, 245988, 245987, 245986, 245985, 245984, 245983, 245982, 245981, 245980, 245978, 245977, 245934, 245933, 245932, 245931, 245930, 245929, 245928, 245927, 245926, 245925, 245924, 245923, 245922, 245921, 245920, 245919, 245918, 245917, 245916, 245915, 245914, 245913, 245912, 245911, 245910, 245909, 245908, 245907, 245906, 245905, 245904, 245903, 245902, 245901, 245900, 245899, 245898, 245897, 245896, 245895, 245894, 245893, 245892, 245891, 245890, 245889, 245888, 245887, 245886, 245885, 245884, 245883, 245882, 245881, 245880, 245879, 245878, 245877, 245876, 245875, 245874, 245873, 245872, 245871, 245870, 245869, 245868, 245867, 245866, 245865, 245864, 245863, 245862, 245861, 245860, 245859, 245858, 245857, 245856, 245855, 245854, 245853, 245852, 245851, 245850, 245849, 245848, 245847, 245846, 245845, 245844, 245843, 245842, 245841, 245840, 245839, 245838, 245837, 245836, 245835, 245834, 245833, 245832, 245831, 245830, 245829, 245828, 245827, 245826, 245825, 245824, 245823, 245822, 245821, 245820, 245819, 245818, 245817, 245816, 245815, 245814, 245813, 245812, 245811, 245810, 245809, 245808, 245807, 245806, 245805, 245804, 245803, 245802, 245801, 245800, 245799, 245798, 245797, 245796, 245795, 245794, 245793, 245792, 245791, 245790, 245789, 245788, 245787, 245786, 245785, 245784, 245783, 245782, 245781, 245780, 245779, 245778, 245777, 245776, 245775, 245774, 245773, 245772, 245771, 245770, 245769, 245768, 245767, 245766, 245765, 245764, 245763, 245762, 245761, 245760, 245759, 245758, 245757, 245756, 245755, 245754, 245753, 245752, 245751, 245750, 245749, 245748, 245747, 245746, 245745, 245744, 245743, 245742, 245741, 245740, 245739, 245738, 245737, 245736, 245735, 245734, 245733, 245732, 245731, 245730, 245729, 245728, 245727, 245726, 245725, 245724, 245723, 245722, 245721, 245720, 245719, 245718, 245717, 245716, 245715, 245714, 245713, 245712, 245711, 245710, 245709, 245708, 245707, 245706, 245705, 245704, 245703, 245702, 245690, 245686, 245680, 245679, 245678, 245677, 245676, 245675, 245674, 245673, 245672, 245671, 245670, 245669, 245668, 245667, 245666, 245665, 245664, 245663, 245662, 245661, 245660, 245659, 245658, 245657, 245656, 245655, 245654, 245653, 245652, 245651, 245650, 245649, 245648, 245647, 245646, 245645, 245644, 245643, 245642, 245641, 245640, 245589, 245588, 245586, 245582, 245581, 245580, 245579, 245578, 245577, 245576, 245575, 245574, 245573, 245572, 245571, 245570, 245569, 245568, 245567, 245566, 245565, 245564, 245563, 245562, 245561, 245560, 245559, 245558, 245557, 245556, 245555, 245554, 245553, 245552, 245551, 245550, 245549, 245548, 245547, 245546, 245545, 245544, 245543, 245542, 245541, 245540, 245539, 245538, 245537, 245536, 245535, 245533, 245530, 245527, 245526, 245525, 245524, 245523, 245522, 245521, 245520, 245519, 245518, 245517, 245516, 245515, 245514, 245513, 245512, 245511, 245510, 245509, 245508, 245507, 245506, 245505, 245504, 245503, 245502, 245501, 245500, 245499, 245498, 245497, 245496, 245495, 245494, 245493, 245492, 245491, 245490, 245489, 245488, 245487, 245486, 245485, 245484, 245483, 245482, 245481, 245480, 245479, 245478, 245477, 245476, 245475, 245474, 245473, 245472, 245471, 245470, 245469, 245468, 245467, 245466, 245465, 245464, 245463, 245462, 245461, 245460, 245459, 245458, 245457, 245456, 245449, 245447, 245444, 245437, 245387, 245361, 245360, 245359, 245358, 245357, 245356, 245355, 245349, 245348, 245347, 245339, 245326, 245325, 245324, 245274, 245268, 245267, 245255, 245251, 245213, 245212, 245200, 245168, 245167, 245166, 245165, 245164, 245156, 245155, 245152, 245149, 245148, 245145, 245142, 245140, 245139, 245137, 245118, 245117, 245116, 245098, 245083, 245078, 245065, 245040, 245037, 245036, 245035, 245026, 245023, 245021, 245016, 245015, 245002, 245001, 245000, 244976, 244963, 244951, 244945, 244944, 244939, 244935, 244934, 244928, 244927, 244926, 244925, 244924, 244923, 244922, 244921, 244920, 244919, 244918, 244917, 244916, 244909, 244906, 244895, 244894, 244878, 244800, 244764, 244748, 244742, 244740, 244739, 244738, 244737, 244736, 244735, 244734, 244733, 244732, 244731, 244730, 244729, 244728, 244727, 244726, 244725, 244724, 244723, 244722, 244716, 244707, 244706, 244705, 244704, 244702, 244697, 244696, 244695, 244694, 244693, 244687, 244686, 244677, 244676, 244675, 244669, 244664, 244658, 244657, 244651, 244623, 244613, 244608, 244606, 244603, 244593, 244573, 244561, 244560, 244556, 244555, 244554, 244553, 244552, 244549, 244544, 244541, 244510, 244486, 244480, 244479, 244478, 244477, 244476, 244475, 244474, 244473, 244472, 244471, 244470, 244469, 244468, 244467, 244466, 244465, 244464, 244463, 244462, 244461, 244460, 244459, 244458, 244457, 244456, 244455, 244454, 244453, 244452, 244451, 244450, 244449, 244448, 244447, 244446, 244445, 244444, 244443, 244442, 244441, 244440, 244439, 244438, 244437, 244436, 244435, 244434, 244433, 244432, 244387, 244375, 244365, 244335, 244328, 244327, 244284, 244193, 244191, 244178, 244177, 244176, 244174, 244155, 244154, 244151, 244150, 244149, 244148, 244146, 244145, 244144, 244143, 244133, 244119, 244117, 244088, 244079, 244078, 244077, 244076, 244075, 244073, 244072, 244062, 243976, 243975, 243971, 243961, 243948, 243938, 243937, 243918, 243917, 243915, 243914, 243913, 243912, 243905, 243904, 243903, 243902, 243901, 243900, 243899, 243898, 243897, 243896, 243895, 243894, 243893, 243892, 243891, 243890, 243889, 243888, 243887, 243886, 243885, 243884, 243883, 243882, 243881, 243880, 243879, 243878, 243877, 243876, 243875, 243874, 243873, 243872, 243871, 243870, 243869, 243868, 243867, 243866, 243865, 243864, 243863, 243862, 243861, 243860, 243859, 243858, 243857, 243856, 243855, 243854, 243853, 243852, 243851, 243850, 243849, 243848, 243847, 243846, 243845, 243844, 243843, 243842, 243841, 243840, 243839, 243838, 243837, 243836, 243835, 243834, 243833, 243832, 243831, 243830, 243829, 243828, 243827, 243826, 243825, 243824, 243823, 243822, 243821, 243820, 243819, 243818, 243817, 243816, 243815, 243814, 243813, 243812, 243811, 243810, 243809, 243808, 243807, 243806, 243805, 243804, 243803, 243802, 243801, 243800, 243799, 243798, 243797, 243796, 243795, 243794, 243793, 243792, 243791, 243790, 243789, 243788, 243787, 243786, 243785, 243784, 243783, 243782, 243781, 243780, 243779, 243778, 243777, 243776, 243775, 243774, 243773, 243772, 243771, 243770, 243769, 243768, 243767, 243766, 243765, 243764, 243763, 243762, 243761, 243760, 243759, 243758, 243757, 243756, 243755, 243754, 243753, 243752, 243751, 243750, 243749, 243748, 243747, 243746, 243745, 243744, 243743, 243742, 243741, 243740, 243739, 243738, 243737, 243736, 243735, 243734, 243733, 243732, 243731, 243730, 243729, 243728, 243727, 243726, 243707, 243705, 243704, 243703, 243702, 243701, 243700, 243699, 243698, 243697, 243696, 243695, 243694, 243693, 243692, 243691, 243690, 243689, 243661, 243660, 243658, 243657, 243656, 243654, 243607, 243605, 243604, 243597, 243586, 243531, 243527, 243449, 243405, 243397, 243390, 243388, 243387, 243386, 243385, 243382, 243381, 243379, 243376, 243375, 243370, 243235, 243234, 243233, 243232, 243231, 243230, 243229, 243228, 243227, 243226, 243225, 243224, 243223, 243222, 243221, 243220, 243219, 243217, 243216, 243215, 243214, 243213, 243212, 243211, 243210, 243209, 243208, 243097, 243096, 243095, 243094, 243093, 243054, 243049, 243044, 243043, 243041, 243038, 243034, 243033, 243032, 243022, 243006, 243001, 242984, 242973, 242962, 242943, 242938, 242937, 242911, 242909, 242907, 242897, 242896, 242895, 242892, 242887, 242876, 242875, 242874, 242863, 242862, 242853, 242851, 242819, 242818, 242817, 242812, 242803, 242798, 242797, 242796, 242795, 242794, 242793, 242773, 242772, 242771, 242770, 242769, 242767, 242764, 242748, 242747, 242746, 242745, 242744, 242743, 242742, 242741, 242740, 242739, 242738, 242737, 242736, 242735, 242734, 242733, 242732, 242731, 242730, 242729, 242728, 242727, 242726, 242725, 242724, 242696, 242687, 242669, 242668, 242665, 242642, 242641, 242637, 242636, 242627, 242625, 242623, 242622, 242621, 242620, 242604, 242595, 242594, 242593, 242592, 242591, 242573, 242572, 242570, 242569, 242566, 242565, 242563, 242555, 242544, 242543, 242536, 242534, 242533, 242522, 242498, 242479, 242467, 242451, 242450, 242449, 242428, 242427, 242425, 242423, 242422, 242396, 242395, 242394, 242390, 242326, 242323, 242322, 242321, 242320, 242289, 242288, 242287, 242285, 242284, 242283, 242282, 242281, 242279, 242254, 242249, 242248, 242241, 242238, 242220, 242219, 242218, 242203, 242202, 242201, 242199, 242193, 242186, 242177, 242147, 242141, 242140, 242109, 242108, 242103, 242097, 242096, 242084, 242083, 242082, 242081, 242079, 242071, 242070, 242069, 242068, 242067, 242066, 242065, 242063, 242061, 242060, 242059, 242025, 241983, 241979, 241971, 241968, 241967, 241966, 241965, 241955, 241951, 241912, 241911, 241910, 241909, 241908, 241907, 241906, 241893, 241883, 241882, 241843, 241806, 241777, 241774, 241773, 241772, 241769, 241768, 241687, 241683, 241681, 241678, 241676, 241672, 241664, 241659, 241647, 241624, 241623, 241546, 241545, 241538, 241532, 241531, 241470, 241464, 241463, 241462, 241461, 241460, 241459, 241458, 241457, 241456, 241455, 241454, 241453, 241452, 241451, 241450, 241449, 241448, 241447, 241446, 241445, 241444, 241443, 241442, 241441, 241440, 241439, 241438, 241437, 241436, 241435, 241434, 241433, 241432, 241431, 241430, 241429, 241428, 241427, 241426, 241425, 241424, 241423, 241422, 241421, 241420, 241419, 241418, 241417, 241416, 241415, 241414, 241413, 241412, 241411, 241410, 241409, 241408, 241407, 241406, 241405, 241404, 241403, 241402, 241401, 241400, 241399, 241398, 241397, 241396, 241395, 241394, 241393, 241392, 241391, 241390, 241389, 241388, 241387, 241386, 241385, 241384, 241383, 241382, 241381, 241380, 241379, 241378, 241377, 241376, 241375, 241374, 241373, 241372, 241371, 241370, 241369, 241368, 241367, 241366, 241365, 241364, 241363, 241362, 241361, 241360, 241359, 241358, 241357, 241356, 241355, 241354, 241353, 241352, 241351, 241350, 241349, 241348, 241347, 241346, 241345, 241344, 241343, 241342, 241341, 241340, 241339, 241338, 241337, 241336, 241335, 241334, 241333, 241332, 241331, 241330, 241329, 241328, 241327, 241326, 241325, 241324, 241323, 241322, 241321, 241320, 241319, 241318, 241317, 241316, 241315, 241314, 241313, 241312, 241311, 241310, 241309, 241308, 241307, 241306, 241305, 241304, 241303, 241302, 241301, 241300, 241299, 241298, 241297, 241296, 241295, 241294, 241293, 241292, 241291, 241290, 241289, 241288, 241287, 241286, 241285, 241284, 241283, 241282, 241281, 241280, 241279, 241278, 241277, 241276, 241275, 241274, 241273, 241272, 241271, 241270, 241269, 241268, 241267, 241266, 241265, 241264, 241263, 241262, 241261, 241260, 241259, 241258, 241257, 241256, 241255, 241254, 241253, 241252, 241251, 241250, 241249, 241248, 241247, 241246, 241245, 241244, 241243, 241242, 241241, 241240, 241239, 241238, 241237, 241236, 241235, 241234, 241233, 241232, 241231, 241230, 241229, 241228, 241227, 241226, 241225, 241224, 241223, 241222, 241221, 241220, 241219, 241218, 241217, 241216, 241215, 241214, 241213, 241212, 241211, 241210, 241209, 241208, 241207, 241206, 241205, 241204, 241203, 241202, 241201, 241200, 241199, 241198, 241197, 241196, 241195, 241194, 241193, 241192, 241191, 241190, 241189, 241188, 241187, 241186, 241185, 241184, 241183, 241145, 241095, 241094, 241093, 241092, 241091, 241090, 241089, 241088, 241087, 241086, 241085, 241084, 241083, 241082, 241081, 241080, 241079, 241078, 241077, 241076, 241075, 241074, 241073, 241072, 241071, 241070, 241069, 241068, 241067, 241066, 241065, 241064, 241063, 241062, 241061, 241060, 241059, 241058, 241057, 241056, 241055, 241054, 241053, 241052, 241051, 241050, 241049, 241048, 241047, 241046, 241045, 241044, 241043, 241042, 241041, 241040, 241039, 241038, 241037, 241036, 241035, 241034, 241033, 241032, 241031, 241030, 241029, 241028, 241027, 241026, 241025, 241024, 241023, 241022, 241021, 241020, 240996, 240995, 240987, 240979, 240977, 240969, 240968, 240927, 240924, 240914, 240912, 240911, 240910, 240909, 240908, 240907, 240906, 240905, 240904, 240903, 240902, 240901, 240900, 240899, 240898, 240897, 240896, 240895, 240894, 240893, 240892, 240891, 240890, 240889, 240888, 240887, 240886, 240885, 240884, 240883, 240882, 240881, 240880, 240879, 240878, 240875, 240856, 240853, 240852, 240851, 240844, 240837, 240834, 240823, 240790, 240777, 240776, 240775, 240772, 240771, 240768, 240767, 240702, 240675, 240674, 240673, 240672, 240666, 240665, 240664, 240663, 240646, 240619, 240617, 240612, 240607, 240600, 240590, 240583, 240555, 240554, 240553, 240537, 240536, 240535, 240534, 240533, 240532, 240517, 240516, 240515, 240514, 240513, 240512, 240511, 240510, 240509, 240508, 240507, 240477, 240473, 240465, 240453, 240448, 240445, 240444, 240443, 240442, 240441, 240440, 240439, 240434, 240417, 240406, 240405, 240390, 240372, 240335, 240334, 240332, 240325, 240319, 240304, 240303, 240302, 240301, 240300, 240299, 240298, 240297, 240293, 240277, 240275, 240274, 240270, 240269, 240210, 240208, 240207, 240206, 240205, 240204, 240203, 240202, 240201, 240200, 240199, 240198, 240197, 240196, 240195, 240194, 240193, 240192, 240191, 240190, 240189, 240188, 240166, 240124, 240123, 240122, 240121, 240118, 240104, 240103, 240102, 240101, 240100, 240099, 240098, 240097, 240096, 240080, 240077, 240076, 240057, 240051, 240047, 240043, 240031, 240030, 240025, 240006, 239920, 239891, 239889, 239819, 239806, 239805, 239804, 239803, 239802, 239783, 239775, 239764, 239759, 239758, 239757, 239756, 239744, 239743, 239742, 239741, 239738, 239737, 239681, 239680, 239679, 239678, 239677, 239657, 239654, 239634, 239630, 239627, 239626, 239619, 239618, 239617, 239616, 239613, 239609, 239608, 239607, 239601, 239600, 239598, 239597, 239596, 239584, 239583, 239563, 239559, 239516, 239512, 239511, 239510, 239509, 239508, 239507, 239495, 239494, 239493, 239492, 239491, 239490, 239489, 239488, 239487, 239486, 239485, 239484, 239483, 239482, 239481, 239480, 239479, 239478, 239477, 239476, 239475, 239474, 239473, 239472, 239471, 239470, 239469, 239468, 239467, 239383, 239382, 239381, 239380, 239379, 239378, 239377, 239376, 239370, 239369, 239345, 239321, 239320, 239319, 239318, 239317, 239316, 239315, 239314, 239302, 239298, 239297, 239296, 239276, 239265, 239256, 239253, 239249, 239246, 239226, 239223, 239196, 239195, 239194, 239193, 239192, 239180, 239175, 239174, 239169, 239168, 239165, 239156, 239146, 239145, 239144, 239137, 239136, 239096, 239091, 239088, 239076, 239067, 239052, 239040, 239039, 239038, 239036, 239018, 239015, 239011, 239001, 239000, 238998, 238993, 238990, 238981, 238976, 238954, 238915, 238913, 238906, 238905, 238904, 238903, 238895, 238892, 238891, 238888, 238887, 238886, 238885, 238869, 238866, 238863, 238862, 238861, 238860, 238859, 238858, 238857, 238856, 238855, 238854, 238839, 238767, 238742, 238739, 238737, 238736, 238721, 238716, 238715, 238714, 238713, 238712, 238711, 238708, 238703, 238652, 238633, 238631, 238630, 238627, 238625, 238624, 238623, 238622, 238611, 238610, 238609, 238606, 238603, 238598, 238597, 238596, 238595, 238594, 238593, 238592, 238591, 238590, 238589, 238588, 238587, 238586, 238585, 238584, 238583, 238582, 238581, 238580, 238579, 238577, 238576, 238575, 238574, 238573, 238572, 238571, 238569, 238561, 238558, 238554, 238551, 238543, 238533, 238532, 238527, 238519, 238518, 238505, 238502, 238495, 238492, 238491, 238490, 238489, 238486, 238479, 238474, 238468, 238467, 238463, 238461, 238459, 238458, 238457, 238456, 238448, 238443, 238440, 238439, 238433, 238421, 238386, 238383, 238380, 238359, 238358, 238336, 238320, 238312, 238311, 238304, 238296, 238230, 238224, 238223, 238222, 238212, 238198, 238194, 238192, 238191, 238190, 238189, 238174, 238154, 238140, 238120, 238102, 238096, 238095, 238094, 238093, 238092, 238090, 238076, 238074, 238073, 238066, 238056, 238053, 238049, 238048, 238047, 238045, 238009, 238008, 238002, 237993, 237977, 237976, 237975, 237966, 237965, 237964, 237963, 237962, 237961, 237960, 237959, 237958, 237957, 237956, 237955, 237954, 237953, 237952, 237950, 237949, 237947, 237946, 237944, 237943, 237941, 237940, 237939, 237934, 237933, 237931, 237930, 237929, 237928, 237915, 237910, 237898, 237890, 237889, 237888, 237887, 237886, 237885, 237884, 237883, 237882, 237881, 237880, 237879, 237878, 237877, 237876, 237875, 237874, 237873, 237872, 237871, 237870, 237869, 237868, 237867, 237866, 237843, 237842, 237828, 237824, 237823, 237822, 237820, 237816, 237814, 237813, 237812, 237811, 237810, 237809, 237806, 237803, 237801, 237800, 237799, 237798, 237797, 237792, 237791, 237790, 237789, 237788, 237787, 237786, 237785, 237784, 237783, 237782, 237781, 237780, 237779, 237766, 237765, 237764, 237760, 237759, 237757, 237756, 237755, 237754, 237753, 237752, 237750, 237749, 237748, 237747, 237746, 237745, 237744, 237743, 237738, 237735, 237730, 237729, 237728, 237727, 237726, 237725, 237724, 237712, 237711, 237710, 237709, 237707, 237706, 237705, 237701, 237700, 237699, 237698, 237697, 237681, 237674, 237673, 237669, 237667, 237661, 237658, 237657, 237650, 237634, 237627, 237626, 237611, 237607, 237604, 237529, 237508, 237470, 237451, 237448, 237345, 237343, 237342, 237341, 237323, 237322, 237315, 237292, 237281, 237262, 237217, 237216, 237212, 237210, 237195, 237194, 237193, 237174, 237173, 237172, 237171, 237141, 237130, 237051, 237032, 237031, 237030, 236950, 236922, 236921, 236920, 236913, 236912, 236910, 236856, 236832, 236831, 236830, 236829, 236828, 236827, 236826, 236825, 236823, 236822, 236821, 236820, 236819, 236818, 236817, 236816, 236815, 236757, 236753, 236714, 236713, 236712, 236711, 236710, 236709, 236708, 236707, 236706, 236705, 236704, 236703, 236702, 236701, 236700, 236699, 236698, 236697, 236696, 236695, 236692, 236691, 236690, 236689, 236688, 236687, 236686, 236685, 236684, 236683, 236682, 236681, 236680, 236679, 236678, 236677, 236676, 236675, 236674, 236673, 236666, 236665, 236664, 236663, 236661, 236660, 236659, 236658, 236657, 236656, 236655, 236654, 236653, 236652, 236651, 236650, 236649, 236648, 236647, 236646, 236645, 236644, 236643, 236642, 236641, 236640, 236639, 236638, 236637, 236636, 236635, 236634, 236632, 236631, 236630, 236629, 236628, 236627, 236626, 236625, 236624, 236623, 236622, 236621, 236620, 236619, 236618, 236617, 236616, 236615, 236614, 236613, 236612, 236611, 236610, 236609, 236608, 236607, 236606, 236605, 236604, 236603, 236602, 236601, 236600, 236599, 236598, 236597, 236596, 236595, 236594, 236593, 236592, 236583, 236582, 236581, 236580, 236579, 236578, 236577, 236576, 236575, 236574, 236567, 236566, 236565, 236564, 236563, 236562, 236561, 236560, 236559, 236558, 236557, 236556, 236542, 236541, 236540, 236539, 236538, 236537, 236535, 236534, 236533, 236532, 236531, 236530, 236529, 236528, 236527, 236526, 236525, 236524, 236523, 236522, 236521, 236520, 236519, 236509, 236501, 236481, 236317, 236316, 236307, 236306, 236305, 236304, 236303, 236302, 236301, 236300, 236299, 236298, 236297, 236296, 236295, 236294, 236293, 236292, 236291, 236290, 236289, 236288, 236287, 236286, 236285, 236284, 236283, 236282, 236281, 236280, 236279, 236278, 236277, 236276, 236275, 236274, 236273, 236272, 236271, 236270, 236269, 236268, 236263, 236259, 236248, 236247, 236245, 236241, 236236, 236235, 236234, 236233, 236232, 236231, 236230, 236229, 236228, 236227, 236226, 236225, 236224, 236223, 236222, 236221, 236220, 236219, 236218, 236217, 236216, 236215, 236214, 236213, 236212, 236211, 236209, 236208, 236192, 236150, 236149, 236148, 236147, 236146, 236145, 236144, 236142, 236141, 236140, 236139, 236138, 236137, 236136, 236135, 236134, 236133, 236132, 236131, 236130, 236129, 236128, 236127, 236126, 236125, 236124, 236123, 236122, 236121, 236120, 236119, 236118, 236117, 236116, 236115, 236114, 236113, 236112, 236111, 236110, 236109, 236108, 236107, 236106, 236105, 236104, 236103, 236102, 236101, 236100, 236099, 236098, 236097, 236096, 236095, 236094, 236093, 236092, 236091, 236090, 236089, 236088, 236087, 236086, 236085, 236084, 236083, 236082, 236081, 236080, 236079, 236078, 236077, 236076, 236075, 236074, 236073, 236072, 236071, 236070, 236069, 236068, 236067, 236066, 236065, 236064, 236063, 236062, 236061, 236060, 236059, 236058, 236057, 236056, 236055, 236054, 236053, 236052, 236051, 236050, 236049, 236048, 236047, 236046, 236045, 236044, 236043, 236042, 236041, 236040, 236039, 236038, 236037, 236036, 236035, 236033, 236022, 236019, 236017, 236013, 236011, 235998, 235997, 235996, 235995, 235994, 235993, 235992, 235991, 235969, 235968, 235963, 235962, 235961, 235960, 235959, 235958, 235957, 235956, 235955, 235954, 235953, 235952, 235951, 235950, 235949, 235948, 235947, 235946, 235945, 235944, 235943, 235942, 235941, 235940, 235939, 235938, 235937, 235936, 235935, 235934, 235933, 235932, 235931, 235930, 235929, 235928, 235927, 235926, 235925, 235924, 235923, 235922, 235921, 235920, 235919, 235918, 235917, 235916, 235915, 235914, 235913, 235912, 235911, 235910, 235909, 235908, 235907, 235906, 235905, 235904, 235903, 235902, 235901, 235900, 235899, 235898, 235897, 235896, 235895, 235894, 235893, 235892, 235891, 235890, 235889, 235888, 235887, 235886, 235885, 235884, 235883, 235882, 235881, 235880, 235879, 235878, 235877, 235876, 235875, 235866, 235864, 235863, 235862, 235861, 235860, 235852, 235844, 235827, 235826, 235825, 235824, 235823, 235822, 235821, 235819, 235815, 235814, 235813, 235812, 235810, 235795, 235793, 235785, 235784, 235779, 235776, 235768, 235761, 235752, 235750, 235747, 235746, 235745, 235703, 235684, 235681, 235680, 235661, 235658, 235655, 235645, 235644, 235643, 235640, 235635, 235619, 235568, 235567, 235566, 235561, 235542, 235541, 235521, 235519, 235518, 235515, 235448, 235422, 235413, 235412, 235400, 235396, 235340, 235336, 235335, 235332, 235331, 235329, 235285, 235262, 235237, 235235, 235226, 235213, 235211, 235210, 235209, 235208, 235207, 235206, 235205, 235204, 235203, 235202, 235201, 235200, 235199, 235198, 235196, 235195, 235194, 235193, 235192, 235191, 235190, 235189, 235188, 235187, 235186, 235185, 235184, 235183, 235182, 235181, 235180, 235179, 235178, 235177, 235176, 235175, 235174, 235173, 235172, 235171, 235170, 235169, 235168, 235167, 235166, 235165, 235164, 235163, 235162, 235161, 235160, 235159, 235158, 235157, 235156, 235155, 235154, 235153, 235152, 235151, 235150, 235149, 235148, 235147, 235146, 235145, 235144, 235143, 235142, 235141, 235140, 235139, 235138, 235137, 235136, 235135, 235134, 235133, 235132, 235131, 235130, 235129, 235128, 235127, 235126, 235125, 235124, 235123, 235122, 235121, 235120, 235119, 235118, 235117, 235116, 235115, 235114, 235113, 235112, 235111, 235110, 235109, 235108, 235107, 235106, 235105, 235104, 235103, 235102, 235101, 235100, 235099, 235098, 235097, 235096, 235095, 235094, 235093, 235092, 235091, 235090, 235089, 235088, 235087, 235086, 235085, 235084, 235083, 235082, 235081, 235080, 235079, 235078, 235077, 235076, 235075, 235074, 235073, 235072, 235071, 235070, 235069, 235068, 235067, 235066, 235065, 235064, 235063, 235062, 235061, 235060, 235059, 235058, 235057, 235056, 235055, 235054, 235050, 235049, 235048, 235047, 235046, 235045, 235044, 235043, 235042, 235041, 235040, 235039, 235038, 235037, 235036, 235035, 235034, 235033, 235032, 235031, 235030, 235029, 235028, 235027, 235026, 235025, 235024, 235023, 235022, 235021, 235020, 235019, 235018, 235017, 235016, 235015, 235014, 235013, 235012, 235011, 235010, 235009, 235008, 235007, 235006, 235005, 235004, 235003, 235002, 235001, 235000, 234999, 234998, 234997, 234996, 234995, 234994, 234993, 234980, 234967, 234966, 234964, 234963, 234962, 234961, 234960, 234958, 234956, 234953, 234941, 234929, 234909, 234894, 234884, 234883, 234882, 234880, 234879, 234876, 234875, 234874, 234873, 234872, 234870, 234864, 234768, 234761, 234760, 234755, 234748, 234738, 234734, 234733, 234732, 234728, 234719, 234704, 234703, 234702, 234701, 234700, 234699, 234698, 234697, 234696, 234695, 234689, 234688, 234687, 234686, 234685, 234684, 234683, 234682, 234670, 234669, 234667, 234661, 234649, 234648, 234634, 234622, 234606, 234595, 234585, 234584, 234569, 234564, 234563, 234560, 234544, 234542, 234541, 234499, 234498, 234497, 234496, 234495, 234494, 234493, 234492, 234486, 234484, 234473, 234472, 234471, 234470, 234469, 234468, 234467, 234466, 234465, 234464, 234463, 234462, 234461, 234460, 234459, 234458, 234457, 234456, 234455, 234454, 234453, 234452, 234451, 234450, 234449, 234448, 234447, 234446, 234436, 234426, 234425, 234424, 234409, 234407, 234383, 234380, 234377, 234376, 234366, 234358, 234339, 234336, 234335, 234333, 234329, 234327, 234320, 234319, 234318, 234307, 234306, 234305, 234301, 234296, 234293, 234290, 234284, 234272, 234263, 234262, 234260, 234259, 234221, 234220, 234219, 234218, 234194, 234178, 234177, 234176, 234175, 234174, 234167, 234156, 234148, 234133, 234132, 234131, 234125, 234093, 234090, 234081, 234079, 234078, 234066, 234064, 234062, 234060, 234059, 234058, 234053, 234050, 234019, 233967, 233945, 233944, 233943, 233942, 233941, 233940, 233939, 233938, 233937, 233936, 233935, 233934, 233933, 233932, 233931, 233930, 233929, 233928, 233927, 233926, 233925, 233924, 233923, 233922, 233917, 233908, 233905, 233897, 233895, 233893, 233889, 233882, 233864, 233863, 233862, 233861, 233860, 233859, 233858, 233857, 233856, 233855, 233854, 233853, 233852, 233851, 233850, 233845, 233838, 233837, 233836, 233835, 233834, 233833, 233813, 233812, 233811, 233810, 233798, 233796, 233795, 233794, 233775, 233772, 233771, 233768, 233765, 233764, 233763, 233762, 233760, 233756, 233749, 233748, 233734, 233728, 233725, 233723, 233711, 233707, 233706, 233705, 233704, 233703, 233702, 233701, 233700, 233699, 233698, 233695, 233693, 233692, 233691, 233689, 233685, 233683, 233658, 233635, 233632, 233627, 233626, 233599, 233598, 233481, 233480, 233479, 233478, 233468, 233467, 233460, 233458, 233455, 233454, 233436, 233410, 233409, 233402, 233392, 233387, 233378, 233283, 233282, 233277, 233276, 233275, 233274, 233273, 233272, 233271, 233270, 233269, 233268, 233267, 233266, 233265, 233264, 233263, 233262, 233261, 233241, 233240, 233239, 233238, 233237, 233236, 233235, 233231, 233225, 233208, 233207, 233206, 233203, 233194, 233179, 233178, 233174, 233173, 233161, 233138, 233122, 233121, 233109, 233085, 233074, 233073, 233072, 233071, 233070, 233069, 233068, 233067, 233066, 233065, 233064, 233063, 233062, 233058, 233057, 233045, 233040, 233039, 233031, 233026, 233025, 233024, 233021, 233012, 233011, 233010, 233009, 232982, 232956, 232954, 232953, 232947, 232946, 232936, 232814, 232813, 232812, 232805, 232799, 232779, 232778, 232776, 232775, 232773, 232755, 232747, 232739, 232738, 232737, 232727, 232718, 232689, 232676, 232665, 232664, 232663, 232662, 232661, 232660, 232659, 232658, 232657, 232656, 232655, 232654, 232653, 232652, 232651, 232650, 232648, 232646, 232645, 232644, 232643, 232642, 232641, 232640, 232639, 232638, 232637, 232636, 232635, 232634, 232633, 232632, 232631, 232630, 232629, 232628, 232627, 232626, 232625, 232624, 232623, 232622, 232621, 232620, 232619, 232618, 232617, 232616, 232615, 232614, 232613, 232612, 232611, 232610, 232609, 232608, 232607, 232606, 232605, 232604, 232603, 232602, 232601, 232600, 232599, 232598, 232597, 232596, 232595, 232594, 232593, 232592, 232591, 232590, 232589, 232588, 232587, 232586, 232585, 232584, 232583, 232582, 232581, 232580, 232579, 232578, 232577, 232576, 232575, 232574, 232573, 232572, 232571, 232570, 232569, 232568, 232567, 232566, 232565, 232564, 232563, 232562, 232561, 232560, 232559, 232558, 232557, 232556, 232555, 232554, 232553, 232552, 232551, 232550, 232549, 232548, 232547, 232546, 232545, 232544, 232543, 232542, 232541, 232540, 232539, 232538, 232537, 232536, 232535, 232534, 232531, 232530, 232528, 232470, 232458, 232455, 232454, 232453, 232452, 232451, 232450, 232447, 232446, 232445, 232444, 232439, 232438, 232436, 232430, 232422, 232421, 232399, 232366, 232356, 232348, 232249, 232160, 232159, 232158, 232157, 232156, 232155, 232154, 232153, 232126, 232124, 232123, 232122, 232119, 232118, 232116, 232108, 232092, 232091, 232090, 232089, 232088, 232087, 232086, 232085, 232084, 232083, 232082, 232081, 232076, 232074, 232053, 232004, 231990, 231936, 231935, 231928, 231927, 231926, 231925, 231924, 231923, 231922, 231921, 231915, 231913, 231888, 231880, 231825, 231824, 231823, 231822, 231821, 231819, 231818, 231817, 231816, 231815, 231814, 231802, 231801, 231800, 231799, 231798, 231797, 231796, 231771, 231770, 231769, 231768, 231767, 231766, 231765, 231751, 231749, 231668, 231667, 231666, 231665, 231664, 231655, 231653, 231652, 231651, 231650, 231649, 231648, 231647, 231646, 231645, 231644, 231643, 231642, 231641, 231640, 231639, 231638, 231637, 231636, 231635, 231634, 231633, 231632, 231631, 231630, 231629, 231628, 231627, 231626, 231625, 231624, 231623, 231622, 231621, 231620, 231619, 231618, 231617, 231616, 231615, 231614, 231613, 231612, 231611, 231610, 231609, 231608, 231607, 231606, 231605, 231604, 231603, 231602, 231601, 231600, 231599, 231598, 231597, 231596, 231595, 231594, 231593, 231592, 231591, 231590, 231589, 231588, 231587, 231586, 231585, 231584, 231583, 231582, 231581, 231580, 231579, 231578, 231577, 231576, 231564, 231563, 231513, 231512, 231511, 231510, 231509, 231434, 231433, 231423, 231421, 231418, 231417, 231416, 231415, 231414, 231413, 231412, 231411, 231410, 231409, 231408, 231407, 231406, 231405, 231404, 231403, 231402, 231401, 231400, 231399, 231398, 231397, 231396, 231395, 231394, 231393, 231392, 231391, 231390, 231389, 231388, 231344, 231343, 231318, 231316, 231315, 231312, 231309, 231308, 231306, 231305, 231304, 231288, 231287, 231286, 231285, 231282, 231281, 231280, 231279, 231278, 231277, 231271, 231270, 231269, 231268, 231267, 231266, 231265, 231264, 231263, 231254, 231244, 231243, 231235, 231223, 231221, 231214, 231212, 231211, 231210, 231209, 231208, 231207, 231206, 231205, 231204, 231203, 231202, 231201, 231200, 231199, 231198, 231197, 231195, 231194, 231193, 231192, 231191, 231190, 231189, 231188, 231187, 231186, 231185, 231184, 231183, 231182, 231181, 231180, 231179, 231178, 231177, 231176, 231175, 231174, 231173, 231172, 231171, 231170, 231169, 231168, 231167, 231166, 231165, 231164, 231163, 231162, 231161, 231160, 231159, 231158, 231153, 231152, 231151, 231150, 231149, 231148, 231147, 231146, 231145, 231144, 231143, 231142, 231141, 231140, 231139, 231138, 231137, 231132, 231131, 231130, 231129, 231128, 231127, 231126, 231125, 231124, 231123, 231122, 231121, 231120, 231119, 231118, 231117, 231116, 231115, 231114, 231113, 231112, 231111, 231110, 231109, 231108, 231107, 231106, 231105, 231104, 231103, 231102, 231101, 231100, 231095, 231094, 231093, 231092, 231091, 231090, 231089, 231084, 231083, 231082, 231081, 231080, 231079, 231078, 231077, 231076, 231075, 231074, 231073, 231072, 231071, 231070, 231069, 231068, 231067, 231066, 231065, 231064, 231063, 231062, 231061, 231060, 231059, 231058, 231057, 231056, 231055, 231054, 231053, 231052, 231051, 231050, 231049, 231048, 231047, 231046, 231045, 231044, 231043, 231042, 231041, 231040, 231039, 231037, 231036, 231035, 231034, 231033, 231032, 231031, 231030, 231029, 231028, 231026, 231025, 231024, 231023, 231022, 231021, 231020, 231019, 231018, 231017, 231016, 231015, 231014, 231013, 231012, 231011, 231010, 231009, 231008, 231007, 231006, 231005, 231004, 231003, 231002, 231001, 231000, 230999, 230998, 230997, 230996, 230995, 230994, 230993, 230992, 230991, 230990, 230989, 230988, 230987, 230986, 230985, 230984, 230983, 230982, 230981, 230980, 230979, 230978, 230977, 230976, 230975, 230974, 230973, 230972, 230971, 230970, 230969, 230968, 230967, 230966, 230965, 230964, 230963, 230962, 230961, 230960, 230959, 230958, 230957, 230956, 230955, 230954, 230953, 230952, 230951, 230950, 230949, 230948, 230947, 230946, 230945, 230944, 230943, 230942, 230941, 230940, 230939, 230938, 230937, 230936, 230935, 230934, 230933, 230932, 230931, 230930, 230929, 230928, 230927, 230926, 230925, 230924, 230923, 230922, 230921, 230920, 230919, 230918, 230917, 230916, 230915, 230914, 230913, 230912, 230857, 230856, 230854, 230847, 230843, 230842, 230835, 230834, 230833, 230832, 230831, 230824, 230822, 230818, 230815, 230814, 230797, 230772, 230771, 230769, 230768, 230764, 230763, 230762, 230754, 230750, 230741, 230740, 230739, 230736, 230727, 230721, 230711, 230709, 230706, 230705, 230701, 230700, 230699, 230692, 230689, 230688, 230685, 230683, 230652, 230630, 230628, 230626, 230625, 230595, 230592, 230589, 230577, 230575, 230568, 230560, 230554, 230553, 230552, 230547, 230539, 230483, 230482, 230481, 230480, 230479, 230478, 230477, 230476, 230475, 230474, 230473, 230472, 230471, 230470, 230469, 230468, 230467, 230466, 230465, 230464, 230463, 230462, 230461, 230460, 230459, 230458, 230457, 230456, 230455, 230454, 230453, 230452, 230451, 230450, 230449, 230448, 230447, 230446, 230445, 230444, 230443, 230442, 230441, 230440, 230439, 230438, 230437, 230436, 230435, 230434, 230430, 230425, 230423, 230412, 230411, 230399, 230396, 230395, 230394, 230392, 230390, 230381, 230380, 230379, 230378, 230375, 230374, 230361, 230359, 230342, 230339, 230332, 230331, 230314, 230303, 230302, 230284, 230265, 230261, 230252, 230247, 230245, 230232, 230195, 230194, 230193, 230192, 230191, 230190, 230189, 230188, 230187, 230186, 230185, 230184, 230171, 230169, 230160, 230130, 230129, 230128, 230126, 230125, 230122, 230101, 230100, 230090, 230089, 230088, 230087, 230085, 230084, 230078, 230076, 230073, 230066, 230019, 230015, 230007, 230006, 230005, 230004, 229991, 229981, 229980, 229973, 229970, 229965, 229939, 229938, 229933, 229927, 229925, 229924, 229923, 229897, 229895, 229893, 229887, 229885, 229884, 229883, 229880, 229879, 229878, 229877, 229856, 229824, 229823, 229822, 229821, 229820, 229819, 229774, 229769, 229763, 229762, 229760, 229736, 229729, 229727, 229726, 229725, 229724, 229720, 229702, 229697, 229691, 229682, 229673, 229672, 229671, 229670, 229669, 229668, 229667, 229666, 229665, 229664, 229663, 229662, 229661, 229660, 229659, 229658, 229657, 229656, 229655, 229654, 229653, 229652, 229651, 229650, 229649, 229648, 229647, 229646, 229645, 229644, 229643, 229642, 229641, 229640, 229639, 229638, 229637, 229636, 229635, 229634, 229633, 229632, 229631, 229630, 229629, 229628, 229627, 229626, 229625, 229624, 229623, 229622, 229621, 229620, 229619, 229618, 229617, 229616, 229615, 229614, 229613, 229612, 229611, 229610, 229609, 229608, 229607, 229606, 229605, 229604, 229603, 229602, 229601, 229600, 229599, 229598, 229597, 229596, 229595, 229594, 229593, 229592, 229591, 229590, 229589, 229588, 229587, 229586, 229585, 229584, 229583, 229582, 229581, 229580, 229579, 229578, 229577, 229576, 229575, 229574, 229573, 229572, 229571, 229570, 229569, 229568, 229567, 229566, 229565, 229564, 229560, 229528, 229527, 229512, 229511, 229495, 229487, 229486, 229480, 229478, 229469, 229467, 229466, 229465, 229464, 229455, 229452, 229423, 229421, 229418, 229394, 229352, 229351, 229350, 229349, 229348, 229347, 229340, 229339, 229338, 229302, 229296, 229295, 229294, 229293, 229286, 229269, 229237, 229225, 229224, 229221, 229215, 229208, 229207, 229206, 229205, 229204, 229203, 229163, 229162, 229056, 229055, 228982, 228981, 228967, 228966, 228965, 228963, 228956, 228955, 228954, 228953, 228952, 228935, 228928, 228927, 228926, 228925, 228924, 228923, 228922, 228921, 228920, 228919, 228918, 228917, 228916, 228915, 228914, 228913, 228912, 228911, 228910, 228909, 228908, 228907, 228906, 228905, 228904, 228903, 228902, 228901, 228900, 228899, 228898, 228897, 228896, 228895, 228894, 228893 ]
  },
  "filters" : [ {
    "name" : "search",
    "filterType" : "String",
    "filterValue" : "",
    "required" : true
  } ],
  "optionFilters" : [ {
    "name" : "storageState",
    "filterType" : "NotSet",
    "required" : true,
    "multiple" : true,
    "options" : [ "ACTIVE", "ARCHIVED" ],
    "selectOptions" : [ "ACTIVE" ]
  } ],
  "rangeFilters" : [ ],
  "maxReturn" : 5000
} ]
2025-08-01T10:26:35.581+07:00 ERROR 57538 --- [qtp791217259-41] n.d.m.monitor.call.EndpointCallService   : Unknown Error when handle the endpoint JobTrackingService/searchJobTrackings
2025-08-01T10:26:35.574+07:00 ERROR 57538 --- [qtp791217259-39] n.d.m.monitor.call.EndpointCallContext   : Start call with component JobTrackingService, method searchJobTrackings, arguments
[ {
  "tenantId" : "default",
  "companyId" : 8,
  "companyParentId" : 4,
  "companyCode" : "beehph",
  "companyLabel" : "Bee HPH",
  "companyFullName" : "BEE LOGISTICS CORPORATION - HAI PHONG BRANCH OFFICE",
  "loginId" : "nhat.le",
  "accountId" : 2089,
  "token" : "9e57ccec8e98a2e2c2d25e3766e4540f",
  "tokenId" : null,
  "remoteIp" : "",
  "sessionId" : "node04cbi2hmt3lb6hhc6q9pnzh7r0",
  "deviceInfo" : {
    "deviceType" : "Computer"
  },
  "accessType" : "Employee",
  "allowAccessCompanies" : {
    "bee" : {
      "companyId" : 4,
      "companyParentId" : 0,
      "companyCode" : "bee",
      "companyLabel" : "Bee Corp",
      "companyFullName" : null
    },
    "beescs" : {
      "companyId" : 12,
      "companyParentId" : 1,
      "companyCode" : "beescs",
      "companyLabel" : "BEESCS",
      "companyFullName" : null
    },
    "beehan" : {
      "companyId" : 16,
      "companyParentId" : 4,
      "companyCode" : "beehan",
      "companyLabel" : "Bee HAN",
      "companyFullName" : null
    },
    "beehph" : {
      "companyId" : 8,
      "companyParentId" : 4,
      "companyCode" : "beehph",
      "companyLabel" : "Bee HPH",
      "companyFullName" : null
    },
    "beehcm" : {
      "companyId" : 17,
      "companyParentId" : 4,
      "companyCode" : "beehcm",
      "companyLabel" : "Bee HCM",
      "companyFullName" : null
    },
    "beedad" : {
      "companyId" : 18,
      "companyParentId" : 4,
      "companyCode" : "beedad",
      "companyLabel" : "Bee DAD",
      "companyFullName" : null
    },
    "datatp" : {
      "companyId" : 10,
      "companyParentId" : 1,
      "companyCode" : "datatp",
      "companyLabel" : "DataTP Cloud",
      "companyFullName" : null
    }
  },
  "featurePermissions" : [ {
    "id" : 6989,
    "appId" : 2,
    "appModule" : "user",
    "appName" : "my-space",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 11332,
    "appId" : 68,
    "appModule" : "document",
    "appName" : "company-document",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 8423,
    "appId" : 15,
    "appModule" : "logistics",
    "appName" : "user-logistics-prices",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 983,
    "appId" : 5,
    "appModule" : "admin",
    "appName" : "admin-space",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 1302,
    "appId" : 26,
    "appModule" : "okr",
    "appName" : "okr",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 1356,
    "appId" : 10,
    "appModule" : "partner",
    "appName" : "partner",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 1576,
    "appId" : 13,
    "appModule" : "logistics",
    "appName" : "company-logistics-settings",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 5515,
    "appId" : 64,
    "appModule" : "job-tracking",
    "appName" : "job-tracking",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 1580,
    "appId" : 16,
    "appModule" : "logistics",
    "appName" : "user-logistics-sales",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 5031,
    "appId" : 27,
    "appModule" : "logistics",
    "appName" : "user-partner-logistics-crm",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 2139,
    "appId" : 18,
    "appModule" : "system",
    "appName" : "system",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 2758,
    "appId" : 19,
    "appModule" : "sample",
    "appName" : "reactjs-lib",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : null,
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 2835,
    "appId" : 21,
    "appModule" : "tms",
    "appName" : "user-tms-app",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 3852,
    "appId" : 4,
    "appModule" : "communication",
    "appName" : "communication",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 4484,
    "appId" : 61,
    "appModule" : "tms",
    "appName" : "user-tms-ops",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 4487,
    "appId" : 62,
    "appModule" : "tms",
    "appName" : "user-tms-round-used",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 5726,
    "appId" : 9,
    "appModule" : "company",
    "appName" : "company-hr",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 11732,
    "appId" : 70,
    "appModule" : "hr",
    "appName" : "company-kpi",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 988,
    "appId" : 6,
    "appModule" : "company",
    "appName" : "company-space",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 12977,
    "appId" : 67,
    "appModule" : "tms",
    "appName" : "user-vehicle-trip",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 12978,
    "appId" : 25,
    "appModule" : "tms",
    "appName" : "user-vehicle-fleets",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 13064,
    "appId" : 7,
    "appModule" : "company",
    "appName" : "company-asset",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Admin"
  }, {
    "id" : 14281,
    "appId" : 80,
    "appModule" : "logistics",
    "appName" : "company-logistics-sales",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Moderator"
  }, {
    "id" : 13102,
    "appId" : 79,
    "appModule" : "company",
    "appName" : "user-asset",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  }, {
    "id" : 14455,
    "appId" : 3,
    "appModule" : "project",
    "appName" : "project",
    "companyId" : 8,
    "loginId" : "nhat.le",
    "appRole" : null,
    "appRoleLabel" : null,
    "dataScope" : "Company",
    "accessType" : "Employee",
    "capability" : "Write"
  } ],
  "attributes" : { },
  "clientId" : "default:nhat.le"
}, {
  "parentId" : 0,
  "id" : 8,
  "code" : "beehph",
  "label" : "Bee HPH",
  "fullName" : "BEE LOGISTICS CORPORATION - HAI PHONG BRANCH OFFICE"
}, {
  "params" : {
    "jobTrackingProjectId" : 1,
    "withPermission" : true,
    "accessColumns" : [ "agent", "source", "4.3.1", "4.3.2", "eta", "etd", "preAlertDn", "agentPriceChecking", "cdsDate", "consignee", "agent-key-date", "saleman", "vesselNameVoy", "volume", "1.3.1", "1.1", "1.2", "10.1", "mblNo", "1.3", "3.1", "1.4", "3.2", "5.1", "5.2", "5.3", "7.1", "5.4", "9.1", "etaTransitPort", "9.2", "9.3", "status", "falseReason", "9.4", "9.5", "note", "9.6", "containerNo", "9.7", "pod", "subjectEmail", "combineMbl", "loadGateIn", "mblTelex", "etdTransitPort", "hblTelex", "pol", "shipmentType", "combine-done", "jobNo", "cutOff", "shipmentDate", "jobType", "jobCreatedDate", "costDemDest", "agentFeedback", "11.5", "cfs", "11.4", "carrier", "1.4.1", "11.3", "11.2", "hblNo", "11.1", "2.1", "2.2", "2.3", "4.1", "4.2", "4.3", "6.1", "4.4", "4.5", "8.1", "8.2", "11.9", "11.8", "11.7", "11.6" ],
    "companyId" : 8,
    "jobTrackingIds" : [ 247779, 247777, 247776, 247774, 247746, 247745, 247744, 247729, 247725, 247708, 247707, 247706, 247700, 247699, 247698, 247695, 247680, 247678, 247654, 247653, 247652, 247641, 247640, 247631, 247630, 247628, 247627, 247626, 247625, 247624, 247623, 247609, 247601, 247585, 247584, 247574, 247570, 247569, 247562, 247560, 247559, 247549, 247535, 247534, 247533, 247532, 247531, 247529, 247528, 247526, 247509, 247503, 247500, 247491, 247461, 247455, 247451, 247449, 247445, 247388, 247387, 247386, 247384, 247383, 247382, 247381, 247380, 247379, 247378, 247374, 247364, 247363, 247354, 247345, 247344, 247343, 247333, 247332, 247331, 247330, 247305, 247272, 247271, 247265, 247264, 247243, 247242, 247241, 247234, 247233, 247218, 247188, 247185, 247182, 247180, 247178, 247147, 247138, 247137, 247135, 247134, 247132, 247131, 247128, 247127, 247126, 247125, 247123, 247122, 247121, 247104, 247100, 247030, 246763, 246762, 246738, 246737, 246736, 246735, 246734, 246733, 246732, 246731, 246730, 246729, 246728, 246715, 246714, 246713, 246712, 246711, 246710, 246692, 246663, 246661, 246644, 246643, 246633, 246632, 246631, 246630, 246629, 246628, 246627, 246626, 246625, 246624, 246623, 246622, 246621, 246620, 246619, 246618, 246617, 246616, 246615, 246614, 246613, 246612, 246611, 246610, 246609, 246608, 246606, 246605, 246604, 246603, 246602, 246601, 246600, 246599, 246598, 246597, 246596, 246595, 246594, 246593, 246592, 246591, 246590, 246589, 246588, 246587, 246586, 246585, 246584, 246583, 246582, 246581, 246580, 246579, 246578, 246577, 246576, 246575, 246574, 246573, 246572, 246571, 246570, 246569, 246568, 246567, 246566, 246565, 246564, 246563, 246562, 246561, 246559, 246558, 246557, 246556, 246555, 246554, 246553, 246552, 246551, 246550, 246549, 246548, 246547, 246546, 246542, 246541, 246540, 246539, 246538, 246537, 246536, 246535, 246534, 246533, 246532, 246531, 246528, 246527, 246525, 246524, 246523, 246522, 246521, 246520, 246519, 246518, 246517, 246515, 246514, 246513, 246512, 246511, 246510, 246508, 246507, 246506, 246505, 246504, 246503, 246502, 246501, 246500, 246499, 246498, 246497, 246496, 246495, 246494, 246493, 246492, 246491, 246490, 246489, 246488, 246487, 246486, 246485, 246484, 246483, 246482, 246481, 246480, 246479, 246478, 246477, 246476, 246475, 246474, 246473, 246472, 246471, 246470, 246469, 246460, 246456, 246455, 246454, 246453, 246452, 246451, 246450, 246449, 246448, 246447, 246446, 246445, 246444, 246443, 246442, 246441, 246440, 246439, 246438, 246437, 246436, 246435, 246434, 246433, 246432, 246414, 246413, 246412, 246411, 246410, 246409, 246408, 246407, 246402, 246400, 246399, 246391, 246390, 246389, 246388, 246387, 246386, 246368, 246367, 246366, 246365, 246364, 246363, 246362, 246361, 246360, 246359, 246358, 246357, 246356, 246355, 246354, 246353, 246352, 246351, 246350, 246349, 246342, 246341, 246340, 246339, 246337, 246336, 246335, 246334, 246333, 246332, 246331, 246330, 246329, 246328, 246327, 246326, 246325, 246324, 246323, 246322, 246321, 246320, 246319, 246318, 246317, 246316, 246315, 246314, 246313, 246312, 246311, 246310, 246309, 246308, 246307, 246306, 246305, 246304, 246291, 246290, 246289, 246288, 246287, 246286, 246285, 246284, 246283, 246282, 246281, 246280, 246279, 246278, 246277, 246276, 246275, 246274, 246273, 246272, 246271, 246270, 246269, 246268, 246267, 246266, 246265, 246264, 246263, 246262, 246261, 246255, 246254, 246253, 246252, 246251, 246250, 246249, 246247, 246246, 246245, 246234, 246204, 246203, 246202, 246201, 246200, 246193, 246192, 246179, 246156, 246150, 246145, 246133, 246132, 246128, 246125, 246123, 246122, 246121, 246120, 246095, 246094, 246093, 246092, 246091, 246090, 246089, 246088, 246087, 246086, 246081, 246080, 246079, 246078, 246077, 246076, 246075, 246074, 246073, 246072, 246071, 246070, 246069, 246068, 246067, 246066, 246065, 246064, 246063, 246062, 246061, 246060, 246059, 246058, 246057, 246056, 246055, 246054, 246053, 246052, 246051, 246050, 246049, 246048, 246047, 246046, 246045, 246044, 246043, 246042, 246041, 246040, 246039, 246038, 246037, 246036, 246035, 246034, 246033, 246032, 246031, 246030, 246029, 246028, 246027, 246026, 246025, 246024, 246023, 246022, 246021, 246020, 246019, 246018, 246017, 246016, 245989, 245988, 245987, 245986, 245985, 245984, 245983, 245982, 245981, 245980, 245978, 245977, 245934, 245933, 245932, 245931, 245930, 245929, 245928, 245927, 245926, 245925, 245924, 245923, 245922, 245921, 245920, 245919, 245918, 245917, 245916, 245915, 245914, 245913, 245912, 245911, 245910, 245909, 245908, 245907, 245906, 245905, 245904, 245903, 245902, 245901, 245900, 245899, 245898, 245897, 245896, 245895, 245894, 245893, 245892, 245891, 245890, 245889, 245888, 245887, 245886, 245885, 245884, 245883, 245882, 245881, 245880, 245879, 245878, 245877, 245876, 245875, 245874, 245873, 245872, 245871, 245870, 245869, 245868, 245867, 245866, 245865, 245864, 245863, 245862, 245861, 245860, 245859, 245858, 245857, 245856, 245855, 245854, 245853, 245852, 245851, 245850, 245849, 245848, 245847, 245846, 245845, 245844, 245843, 245842, 245841, 245840, 245839, 245838, 245837, 245836, 245835, 245834, 245833, 245832, 245831, 245830, 245829, 245828, 245827, 245826, 245825, 245824, 245823, 245822, 245821, 245820, 245819, 245818, 245817, 245816, 245815, 245814, 245813, 245812, 245811, 245810, 245809, 245808, 245807, 245806, 245805, 245804, 245803, 245802, 245801, 245800, 245799, 245798, 245797, 245796, 245795, 245794, 245793, 245792, 245791, 245790, 245789, 245788, 245787, 245786, 245785, 245784, 245783, 245782, 245781, 245780, 245779, 245778, 245777, 245776, 245775, 245774, 245773, 245772, 245771, 245770, 245769, 245768, 245767, 245766, 245765, 245764, 245763, 245762, 245761, 245760, 245759, 245758, 245757, 245756, 245755, 245754, 245753, 245752, 245751, 245750, 245749, 245748, 245747, 245746, 245745, 245744, 245743, 245742, 245741, 245740, 245739, 245738, 245737, 245736, 245735, 245734, 245733, 245732, 245731, 245730, 245729, 245728, 245727, 245726, 245725, 245724, 245723, 245722, 245721, 245720, 245719, 245718, 245717, 245716, 245715, 245714, 245713, 245712, 245711, 245710, 245709, 245708, 245707, 245706, 245705, 245704, 245703, 245702, 245690, 245686, 245680, 245679, 245678, 245677, 245676, 245675, 245674, 245673, 245672, 245671, 245670, 245669, 245668, 245667, 245666, 245665, 245664, 245663, 245662, 245661, 245660, 245659, 245658, 245657, 245656, 245655, 245654, 245653, 245652, 245651, 245650, 245649, 245648, 245647, 245646, 245645, 245644, 245643, 245642, 245641, 245640, 245589, 245588, 245586, 245582, 245581, 245580, 245579, 245578, 245577, 245576, 245575, 245574, 245573, 245572, 245571, 245570, 245569, 245568, 245567, 245566, 245565, 245564, 245563, 245562, 245561, 245560, 245559, 245558, 245557, 245556, 245555, 245554, 245553, 245552, 245551, 245550, 245549, 245548, 245547, 245546, 245545, 245544, 245543, 245542, 245541, 245540, 245539, 245538, 245537, 245536, 245535, 245533, 245530, 245527, 245526, 245525, 245524, 245523, 245522, 245521, 245520, 245519, 245518, 245517, 245516, 245515, 245514, 245513, 245512, 245511, 245510, 245509, 245508, 245507, 245506, 245505, 245504, 245503, 245502, 245501, 245500, 245499, 245498, 245497, 245496, 245495, 245494, 245493, 245492, 245491, 245490, 245489, 245488, 245487, 245486, 245485, 245484, 245483, 245482, 245481, 245480, 245479, 245478, 245477, 245476, 245475, 245474, 245473, 245472, 245471, 245470, 245469, 245468, 245467, 245466, 245465, 245464, 245463, 245462, 245461, 245460, 245459, 245458, 245457, 245456, 245449, 245447, 245444, 245437, 245387, 245361, 245360, 245359, 245358, 245357, 245356, 245355, 245349, 245348, 245347, 245339, 245326, 245325, 245324, 245274, 245268, 245267, 245255, 245251, 245213, 245212, 245200, 245168, 245167, 245166, 245165, 245164, 245156, 245155, 245152, 245149, 245148, 245145, 245142, 245140, 245139, 245137, 245118, 245117, 245116, 245098, 245083, 245078, 245065, 245040, 245037, 245036, 245035, 245026, 245023, 245021, 245016, 245015, 245002, 245001, 245000, 244976, 244963, 244951, 244945, 244944, 244939, 244935, 244934, 244928, 244927, 244926, 244925, 244924, 244923, 244922, 244921, 244920, 244919, 244918, 244917, 244916, 244909, 244906, 244895, 244894, 244878, 244800, 244764, 244748, 244742, 244740, 244739, 244738, 244737, 244736, 244735, 244734, 244733, 244732, 244731, 244730, 244729, 244728, 244727, 244726, 244725, 244724, 244723, 244722, 244716, 244707, 244706, 244705, 244704, 244702, 244697, 244696, 244695, 244694, 244693, 244687, 244686, 244677, 244676, 244675, 244669, 244664, 244658, 244657, 244651, 244623, 244613, 244608, 244606, 244603, 244593, 244573, 244561, 244560, 244556, 244555, 244554, 244553, 244552, 244549, 244544, 244541, 244510, 244486, 244480, 244479, 244478, 244477, 244476, 244475, 244474, 244473, 244472, 244471, 244470, 244469, 244468, 244467, 244466, 244465, 244464, 244463, 244462, 244461, 244460, 244459, 244458, 244457, 244456, 244455, 244454, 244453, 244452, 244451, 244450, 244449, 244448, 244447, 244446, 244445, 244444, 244443, 244442, 244441, 244440, 244439, 244438, 244437, 244436, 244435, 244434, 244433, 244432, 244387, 244375, 244365, 244335, 244328, 244327, 244284, 244193, 244191, 244178, 244177, 244176, 244174, 244155, 244154, 244151, 244150, 244149, 244148, 244146, 244145, 244144, 244143, 244133, 244119, 244117, 244088, 244079, 244078, 244077, 244076, 244075, 244073, 244072, 244062, 243976, 243975, 243971, 243961, 243948, 243938, 243937, 243918, 243917, 243915, 243914, 243913, 243912, 243905, 243904, 243903, 243902, 243901, 243900, 243899, 243898, 243897, 243896, 243895, 243894, 243893, 243892, 243891, 243890, 243889, 243888, 243887, 243886, 243885, 243884, 243883, 243882, 243881, 243880, 243879, 243878, 243877, 243876, 243875, 243874, 243873, 243872, 243871, 243870, 243869, 243868, 243867, 243866, 243865, 243864, 243863, 243862, 243861, 243860, 243859, 243858, 243857, 243856, 243855, 243854, 243853, 243852, 243851, 243850, 243849, 243848, 243847, 243846, 243845, 243844, 243843, 243842, 243841, 243840, 243839, 243838, 243837, 243836, 243835, 243834, 243833, 243832, 243831, 243830, 243829, 243828, 243827, 243826, 243825, 243824, 243823, 243822, 243821, 243820, 243819, 243818, 243817, 243816, 243815, 243814, 243813, 243812, 243811, 243810, 243809, 243808, 243807, 243806, 243805, 243804, 243803, 243802, 243801, 243800, 243799, 243798, 243797, 243796, 243795, 243794, 243793, 243792, 243791, 243790, 243789, 243788, 243787, 243786, 243785, 243784, 243783, 243782, 243781, 243780, 243779, 243778, 243777, 243776, 243775, 243774, 243773, 243772, 243771, 243770, 243769, 243768, 243767, 243766, 243765, 243764, 243763, 243762, 243761, 243760, 243759, 243758, 243757, 243756, 243755, 243754, 243753, 243752, 243751, 243750, 243749, 243748, 243747, 243746, 243745, 243744, 243743, 243742, 243741, 243740, 243739, 243738, 243737, 243736, 243735, 243734, 243733, 243732, 243731, 243730, 243729, 243728, 243727, 243726, 243707, 243705, 243704, 243703, 243702, 243701, 243700, 243699, 243698, 243697, 243696, 243695, 243694, 243693, 243692, 243691, 243690, 243689, 243661, 243660, 243658, 243657, 243656, 243654, 243607, 243605, 243604, 243597, 243586, 243531, 243527, 243449, 243405, 243397, 243390, 243388, 243387, 243386, 243385, 243382, 243381, 243379, 243376, 243375, 243370, 243235, 243234, 243233, 243232, 243231, 243230, 243229, 243228, 243227, 243226, 243225, 243224, 243223, 243222, 243221, 243220, 243219, 243217, 243216, 243215, 243214, 243213, 243212, 243211, 243210, 243209, 243208, 243097, 243096, 243095, 243094, 243093, 243054, 243049, 243044, 243043, 243041, 243038, 243034, 243033, 243032, 243022, 243006, 243001, 242984, 242973, 242962, 242943, 242938, 242937, 242911, 242909, 242907, 242897, 242896, 242895, 242892, 242887, 242876, 242875, 242874, 242863, 242862, 242853, 242851, 242819, 242818, 242817, 242812, 242803, 242798, 242797, 242796, 242795, 242794, 242793, 242773, 242772, 242771, 242770, 242769, 242767, 242764, 242748, 242747, 242746, 242745, 242744, 242743, 242742, 242741, 242740, 242739, 242738, 242737, 242736, 242735, 242734, 242733, 242732, 242731, 242730, 242729, 242728, 242727, 242726, 242725, 242724, 242696, 242687, 242669, 242668, 242665, 242642, 242641, 242637, 242636, 242627, 242625, 242623, 242622, 242621, 242620, 242604, 242595, 242594, 242593, 242592, 242591, 242573, 242572, 242570, 242569, 242566, 242565, 242563, 242555, 242544, 242543, 242536, 242534, 242533, 242522, 242498, 242479, 242467, 242451, 242450, 242449, 242428, 242427, 242425, 242423, 242422, 242396, 242395, 242394, 242390, 242326, 242323, 242322, 242321, 242320, 242289, 242288, 242287, 242285, 242284, 242283, 242282, 242281, 242279, 242254, 242249, 242248, 242241, 242238, 242220, 242219, 242218, 242203, 242202, 242201, 242199, 242193, 242186, 242177, 242147, 242141, 242140, 242109, 242108, 242103, 242097, 242096, 242084, 242083, 242082, 242081, 242079, 242071, 242070, 242069, 242068, 242067, 242066, 242065, 242063, 242061, 242060, 242059, 242025, 241983, 241979, 241971, 241968, 241967, 241966, 241965, 241955, 241951, 241912, 241911, 241910, 241909, 241908, 241907, 241906, 241893, 241883, 241882, 241843, 241806, 241777, 241774, 241773, 241772, 241769, 241768, 241687, 241683, 241681, 241678, 241676, 241672, 241664, 241659, 241647, 241624, 241623, 241546, 241545, 241538, 241532, 241531, 241470, 241464, 241463, 241462, 241461, 241460, 241459, 241458, 241457, 241456, 241455, 241454, 241453, 241452, 241451, 241450, 241449, 241448, 241447, 241446, 241445, 241444, 241443, 241442, 241441, 241440, 241439, 241438, 241437, 241436, 241435, 241434, 241433, 241432, 241431, 241430, 241429, 241428, 241427, 241426, 241425, 241424, 241423, 241422, 241421, 241420, 241419, 241418, 241417, 241416, 241415, 241414, 241413, 241412, 241411, 241410, 241409, 241408, 241407, 241406, 241405, 241404, 241403, 241402, 241401, 241400, 241399, 241398, 241397, 241396, 241395, 241394, 241393, 241392, 241391, 241390, 241389, 241388, 241387, 241386, 241385, 241384, 241383, 241382, 241381, 241380, 241379, 241378, 241377, 241376, 241375, 241374, 241373, 241372, 241371, 241370, 241369, 241368, 241367, 241366, 241365, 241364, 241363, 241362, 241361, 241360, 241359, 241358, 241357, 241356, 241355, 241354, 241353, 241352, 241351, 241350, 241349, 241348, 241347, 241346, 241345, 241344, 241343, 241342, 241341, 241340, 241339, 241338, 241337, 241336, 241335, 241334, 241333, 241332, 241331, 241330, 241329, 241328, 241327, 241326, 241325, 241324, 241323, 241322, 241321, 241320, 241319, 241318, 241317, 241316, 241315, 241314, 241313, 241312, 241311, 241310, 241309, 241308, 241307, 241306, 241305, 241304, 241303, 241302, 241301, 241300, 241299, 241298, 241297, 241296, 241295, 241294, 241293, 241292, 241291, 241290, 241289, 241288, 241287, 241286, 241285, 241284, 241283, 241282, 241281, 241280, 241279, 241278, 241277, 241276, 241275, 241274, 241273, 241272, 241271, 241270, 241269, 241268, 241267, 241266, 241265, 241264, 241263, 241262, 241261, 241260, 241259, 241258, 241257, 241256, 241255, 241254, 241253, 241252, 241251, 241250, 241249, 241248, 241247, 241246, 241245, 241244, 241243, 241242, 241241, 241240, 241239, 241238, 241237, 241236, 241235, 241234, 241233, 241232, 241231, 241230, 241229, 241228, 241227, 241226, 241225, 241224, 241223, 241222, 241221, 241220, 241219, 241218, 241217, 241216, 241215, 241214, 241213, 241212, 241211, 241210, 241209, 241208, 241207, 241206, 241205, 241204, 241203, 241202, 241201, 241200, 241199, 241198, 241197, 241196, 241195, 241194, 241193, 241192, 241191, 241190, 241189, 241188, 241187, 241186, 241185, 241184, 241183, 241145, 241095, 241094, 241093, 241092, 241091, 241090, 241089, 241088, 241087, 241086, 241085, 241084, 241083, 241082, 241081, 241080, 241079, 241078, 241077, 241076, 241075, 241074, 241073, 241072, 241071, 241070, 241069, 241068, 241067, 241066, 241065, 241064, 241063, 241062, 241061, 241060, 241059, 241058, 241057, 241056, 241055, 241054, 241053, 241052, 241051, 241050, 241049, 241048, 241047, 241046, 241045, 241044, 241043, 241042, 241041, 241040, 241039, 241038, 241037, 241036, 241035, 241034, 241033, 241032, 241031, 241030, 241029, 241028, 241027, 241026, 241025, 241024, 241023, 241022, 241021, 241020, 240996, 240995, 240987, 240979, 240977, 240969, 240968, 240927, 240924, 240914, 240912, 240911, 240910, 240909, 240908, 240907, 240906, 240905, 240904, 240903, 240902, 240901, 240900, 240899, 240898, 240897, 240896, 240895, 240894, 240893, 240892, 240891, 240890, 240889, 240888, 240887, 240886, 240885, 240884, 240883, 240882, 240881, 240880, 240879, 240878, 240875, 240856, 240853, 240852, 240851, 240844, 240837, 240834, 240823, 240790, 240777, 240776, 240775, 240772, 240771, 240768, 240767, 240702, 240675, 240674, 240673, 240672, 240666, 240665, 240664, 240663, 240646, 240619, 240617, 240612, 240607, 240600, 240590, 240583, 240555, 240554, 240553, 240537, 240536, 240535, 240534, 240533, 240532, 240517, 240516, 240515, 240514, 240513, 240512, 240511, 240510, 240509, 240508, 240507, 240477, 240473, 240465, 240453, 240448, 240445, 240444, 240443, 240442, 240441, 240440, 240439, 240434, 240417, 240406, 240405, 240390, 240372, 240335, 240334, 240332, 240325, 240319, 240304, 240303, 240302, 240301, 240300, 240299, 240298, 240297, 240293, 240277, 240275, 240274, 240270, 240269, 240210, 240208, 240207, 240206, 240205, 240204, 240203, 240202, 240201, 240200, 240199, 240198, 240197, 240196, 240195, 240194, 240193, 240192, 240191, 240190, 240189, 240188, 240166, 240124, 240123, 240122, 240121, 240118, 240104, 240103, 240102, 240101, 240100, 240099, 240098, 240097, 240096, 240080, 240077, 240076, 240057, 240051, 240047, 240043, 240031, 240030, 240025, 240006, 239920, 239891, 239889, 239819, 239806, 239805, 239804, 239803, 239802, 239783, 239775, 239764, 239759, 239758, 239757, 239756, 239744, 239743, 239742, 239741, 239738, 239737, 239681, 239680, 239679, 239678, 239677, 239657, 239654, 239634, 239630, 239627, 239626, 239619, 239618, 239617, 239616, 239613, 239609, 239608, 239607, 239601, 239600, 239598, 239597, 239596, 239584, 239583, 239563, 239559, 239516, 239512, 239511, 239510, 239509, 239508, 239507, 239495, 239494, 239493, 239492, 239491, 239490, 239489, 239488, 239487, 239486, 239485, 239484, 239483, 239482, 239481, 239480, 239479, 239478, 239477, 239476, 239475, 239474, 239473, 239472, 239471, 239470, 239469, 239468, 239467, 239383, 239382, 239381, 239380, 239379, 239378, 239377, 239376, 239370, 239369, 239345, 239321, 239320, 239319, 239318, 239317, 239316, 239315, 239314, 239302, 239298, 239297, 239296, 239276, 239265, 239256, 239253, 239249, 239246, 239226, 239223, 239196, 239195, 239194, 239193, 239192, 239180, 239175, 239174, 239169, 239168, 239165, 239156, 239146, 239145, 239144, 239137, 239136, 239096, 239091, 239088, 239076, 239067, 239052, 239040, 239039, 239038, 239036, 239018, 239015, 239011, 239001, 239000, 238998, 238993, 238990, 238981, 238976, 238954, 238915, 238913, 238906, 238905, 238904, 238903, 238895, 238892, 238891, 238888, 238887, 238886, 238885, 238869, 238866, 238863, 238862, 238861, 238860, 238859, 238858, 238857, 238856, 238855, 238854, 238839, 238767, 238742, 238739, 238737, 238736, 238721, 238716, 238715, 238714, 238713, 238712, 238711, 238708, 238703, 238652, 238633, 238631, 238630, 238627, 238625, 238624, 238623, 238622, 238611, 238610, 238609, 238606, 238603, 238598, 238597, 238596, 238595, 238594, 238593, 238592, 238591, 238590, 238589, 238588, 238587, 238586, 238585, 238584, 238583, 238582, 238581, 238580, 238579, 238577, 238576, 238575, 238574, 238573, 238572, 238571, 238569, 238561, 238558, 238554, 238551, 238543, 238533, 238532, 238527, 238519, 238518, 238505, 238502, 238495, 238492, 238491, 238490, 238489, 238486, 238479, 238474, 238468, 238467, 238463, 238461, 238459, 238458, 238457, 238456, 238448, 238443, 238440, 238439, 238433, 238421, 238386, 238383, 238380, 238359, 238358, 238336, 238320, 238312, 238311, 238304, 238296, 238230, 238224, 238223, 238222, 238212, 238198, 238194, 238192, 238191, 238190, 238189, 238174, 238154, 238140, 238120, 238102, 238096, 238095, 238094, 238093, 238092, 238090, 238076, 238074, 238073, 238066, 238056, 238053, 238049, 238048, 238047, 238045, 238009, 238008, 238002, 237993, 237977, 237976, 237975, 237966, 237965, 237964, 237963, 237962, 237961, 237960, 237959, 237958, 237957, 237956, 237955, 237954, 237953, 237952, 237950, 237949, 237947, 237946, 237944, 237943, 237941, 237940, 237939, 237934, 237933, 237931, 237930, 237929, 237928, 237915, 237910, 237898, 237890, 237889, 237888, 237887, 237886, 237885, 237884, 237883, 237882, 237881, 237880, 237879, 237878, 237877, 237876, 237875, 237874, 237873, 237872, 237871, 237870, 237869, 237868, 237867, 237866, 237843, 237842, 237828, 237824, 237823, 237822, 237820, 237816, 237814, 237813, 237812, 237811, 237810, 237809, 237806, 237803, 237801, 237800, 237799, 237798, 237797, 237792, 237791, 237790, 237789, 237788, 237787, 237786, 237785, 237784, 237783, 237782, 237781, 237780, 237779, 237766, 237765, 237764, 237760, 237759, 237757, 237756, 237755, 237754, 237753, 237752, 237750, 237749, 237748, 237747, 237746, 237745, 237744, 237743, 237738, 237735, 237730, 237729, 237728, 237727, 237726, 237725, 237724, 237712, 237711, 237710, 237709, 237707, 237706, 237705, 237701, 237700, 237699, 237698, 237697, 237681, 237674, 237673, 237669, 237667, 237661, 237658, 237657, 237650, 237634, 237627, 237626, 237611, 237607, 237604, 237529, 237508, 237470, 237451, 237448, 237345, 237343, 237342, 237341, 237323, 237322, 237315, 237292, 237281, 237262, 237217, 237216, 237212, 237210, 237195, 237194, 237193, 237174, 237173, 237172, 237171, 237141, 237130, 237051, 237032, 237031, 237030, 236950, 236922, 236921, 236920, 236913, 236912, 236910, 236856, 236832, 236831, 236830, 236829, 236828, 236827, 236826, 236825, 236823, 236822, 236821, 236820, 236819, 236818, 236817, 236816, 236815, 236757, 236753, 236714, 236713, 236712, 236711, 236710, 236709, 236708, 236707, 236706, 236705, 236704, 236703, 236702, 236701, 236700, 236699, 236698, 236697, 236696, 236695, 236692, 236691, 236690, 236689, 236688, 236687, 236686, 236685, 236684, 236683, 236682, 236681, 236680, 236679, 236678, 236677, 236676, 236675, 236674, 236673, 236666, 236665, 236664, 236663, 236661, 236660, 236659, 236658, 236657, 236656, 236655, 236654, 236653, 236652, 236651, 236650, 236649, 236648, 236647, 236646, 236645, 236644, 236643, 236642, 236641, 236640, 236639, 236638, 236637, 236636, 236635, 236634, 236632, 236631, 236630, 236629, 236628, 236627, 236626, 236625, 236624, 236623, 236622, 236621, 236620, 236619, 236618, 236617, 236616, 236615, 236614, 236613, 236612, 236611, 236610, 236609, 236608, 236607, 236606, 236605, 236604, 236603, 236602, 236601, 236600, 236599, 236598, 236597, 236596, 236595, 236594, 236593, 236592, 236583, 236582, 236581, 236580, 236579, 236578, 236577, 236576, 236575, 236574, 236567, 236566, 236565, 236564, 236563, 236562, 236561, 236560, 236559, 236558, 236557, 236556, 236542, 236541, 236540, 236539, 236538, 236537, 236535, 236534, 236533, 236532, 236531, 236530, 236529, 236528, 236527, 236526, 236525, 236524, 236523, 236522, 236521, 236520, 236519, 236509, 236501, 236481, 236317, 236316, 236307, 236306, 236305, 236304, 236303, 236302, 236301, 236300, 236299, 236298, 236297, 236296, 236295, 236294, 236293, 236292, 236291, 236290, 236289, 236288, 236287, 236286, 236285, 236284, 236283, 236282, 236281, 236280, 236279, 236278, 236277, 236276, 236275, 236274, 236273, 236272, 236271, 236270, 236269, 236268, 236263, 236259, 236248, 236247, 236245, 236241, 236236, 236235, 236234, 236233, 236232, 236231, 236230, 236229, 236228, 236227, 236226, 236225, 236224, 236223, 236222, 236221, 236220, 236219, 236218, 236217, 236216, 236215, 236214, 236213, 236212, 236211, 236209, 236208, 236192, 236150, 236149, 236148, 236147, 236146, 236145, 236144, 236142, 236141, 236140, 236139, 236138, 236137, 236136, 236135, 236134, 236133, 236132, 236131, 236130, 236129, 236128, 236127, 236126, 236125, 236124, 236123, 236122, 236121, 236120, 236119, 236118, 236117, 236116, 236115, 236114, 236113, 236112, 236111, 236110, 236109, 236108, 236107, 236106, 236105, 236104, 236103, 236102, 236101, 236100, 236099, 236098, 236097, 236096, 236095, 236094, 236093, 236092, 236091, 236090, 236089, 236088, 236087, 236086, 236085, 236084, 236083, 236082, 236081, 236080, 236079, 236078, 236077, 236076, 236075, 236074, 236073, 236072, 236071, 236070, 236069, 236068, 236067, 236066, 236065, 236064, 236063, 236062, 236061, 236060, 236059, 236058, 236057, 236056, 236055, 236054, 236053, 236052, 236051, 236050, 236049, 236048, 236047, 236046, 236045, 236044, 236043, 236042, 236041, 236040, 236039, 236038, 236037, 236036, 236035, 236033, 236022, 236019, 236017, 236013, 236011, 235998, 235997, 235996, 235995, 235994, 235993, 235992, 235991, 235969, 235968, 235963, 235962, 235961, 235960, 235959, 235958, 235957, 235956, 235955, 235954, 235953, 235952, 235951, 235950, 235949, 235948, 235947, 235946, 235945, 235944, 235943, 235942, 235941, 235940, 235939, 235938, 235937, 235936, 235935, 235934, 235933, 235932, 235931, 235930, 235929, 235928, 235927, 235926, 235925, 235924, 235923, 235922, 235921, 235920, 235919, 235918, 235917, 235916, 235915, 235914, 235913, 235912, 235911, 235910, 235909, 235908, 235907, 235906, 235905, 235904, 235903, 235902, 235901, 235900, 235899, 235898, 235897, 235896, 235895, 235894, 235893, 235892, 235891, 235890, 235889, 235888, 235887, 235886, 235885, 235884, 235883, 235882, 235881, 235880, 235879, 235878, 235877, 235876, 235875, 235866, 235864, 235863, 235862, 235861, 235860, 235852, 235844, 235827, 235826, 235825, 235824, 235823, 235822, 235821, 235819, 235815, 235814, 235813, 235812, 235810, 235795, 235793, 235785, 235784, 235779, 235776, 235768, 235761, 235752, 235750, 235747, 235746, 235745, 235703, 235684, 235681, 235680, 235661, 235658, 235655, 235645, 235644, 235643, 235640, 235635, 235619, 235568, 235567, 235566, 235561, 235542, 235541, 235521, 235519, 235518, 235515, 235448, 235422, 235413, 235412, 235400, 235396, 235340, 235336, 235335, 235332, 235331, 235329, 235285, 235262, 235237, 235235, 235226, 235213, 235211, 235210, 235209, 235208, 235207, 235206, 235205, 235204, 235203, 235202, 235201, 235200, 235199, 235198, 235196, 235195, 235194, 235193, 235192, 235191, 235190, 235189, 235188, 235187, 235186, 235185, 235184, 235183, 235182, 235181, 235180, 235179, 235178, 235177, 235176, 235175, 235174, 235173, 235172, 235171, 235170, 235169, 235168, 235167, 235166, 235165, 235164, 235163, 235162, 235161, 235160, 235159, 235158, 235157, 235156, 235155, 235154, 235153, 235152, 235151, 235150, 235149, 235148, 235147, 235146, 235145, 235144, 235143, 235142, 235141, 235140, 235139, 235138, 235137, 235136, 235135, 235134, 235133, 235132, 235131, 235130, 235129, 235128, 235127, 235126, 235125, 235124, 235123, 235122, 235121, 235120, 235119, 235118, 235117, 235116, 235115, 235114, 235113, 235112, 235111, 235110, 235109, 235108, 235107, 235106, 235105, 235104, 235103, 235102, 235101, 235100, 235099, 235098, 235097, 235096, 235095, 235094, 235093, 235092, 235091, 235090, 235089, 235088, 235087, 235086, 235085, 235084, 235083, 235082, 235081, 235080, 235079, 235078, 235077, 235076, 235075, 235074, 235073, 235072, 235071, 235070, 235069, 235068, 235067, 235066, 235065, 235064, 235063, 235062, 235061, 235060, 235059, 235058, 235057, 235056, 235055, 235054, 235050, 235049, 235048, 235047, 235046, 235045, 235044, 235043, 235042, 235041, 235040, 235039, 235038, 235037, 235036, 235035, 235034, 235033, 235032, 235031, 235030, 235029, 235028, 235027, 235026, 235025, 235024, 235023, 235022, 235021, 235020, 235019, 235018, 235017, 235016, 235015, 235014, 235013, 235012, 235011, 235010, 235009, 235008, 235007, 235006, 235005, 235004, 235003, 235002, 235001, 235000, 234999, 234998, 234997, 234996, 234995, 234994, 234993, 234980, 234967, 234966, 234964, 234963, 234962, 234961, 234960, 234958, 234956, 234953, 234941, 234929, 234909, 234894, 234884, 234883, 234882, 234880, 234879, 234876, 234875, 234874, 234873, 234872, 234870, 234864, 234768, 234761, 234760, 234755, 234748, 234738, 234734, 234733, 234732, 234728, 234719, 234704, 234703, 234702, 234701, 234700, 234699, 234698, 234697, 234696, 234695, 234689, 234688, 234687, 234686, 234685, 234684, 234683, 234682, 234670, 234669, 234667, 234661, 234649, 234648, 234634, 234622, 234606, 234595, 234585, 234584, 234569, 234564, 234563, 234560, 234544, 234542, 234541, 234499, 234498, 234497, 234496, 234495, 234494, 234493, 234492, 234486, 234484, 234473, 234472, 234471, 234470, 234469, 234468, 234467, 234466, 234465, 234464, 234463, 234462, 234461, 234460, 234459, 234458, 234457, 234456, 234455, 234454, 234453, 234452, 234451, 234450, 234449, 234448, 234447, 234446, 234436, 234426, 234425, 234424, 234409, 234407, 234383, 234380, 234377, 234376, 234366, 234358, 234339, 234336, 234335, 234333, 234329, 234327, 234320, 234319, 234318, 234307, 234306, 234305, 234301, 234296, 234293, 234290, 234284, 234272, 234263, 234262, 234260, 234259, 234221, 234220, 234219, 234218, 234194, 234178, 234177, 234176, 234175, 234174, 234167, 234156, 234148, 234133, 234132, 234131, 234125, 234093, 234090, 234081, 234079, 234078, 234066, 234064, 234062, 234060, 234059, 234058, 234053, 234050, 234019, 233967, 233945, 233944, 233943, 233942, 233941, 233940, 233939, 233938, 233937, 233936, 233935, 233934, 233933, 233932, 233931, 233930, 233929, 233928, 233927, 233926, 233925, 233924, 233923, 233922, 233917, 233908, 233905, 233897, 233895, 233893, 233889, 233882, 233864, 233863, 233862, 233861, 233860, 233859, 233858, 233857, 233856, 233855, 233854, 233853, 233852, 233851, 233850, 233845, 233838, 233837, 233836, 233835, 233834, 233833, 233813, 233812, 233811, 233810, 233798, 233796, 233795, 233794, 233775, 233772, 233771, 233768, 233765, 233764, 233763, 233762, 233760, 233756, 233749, 233748, 233734, 233728, 233725, 233723, 233711, 233707, 233706, 233705, 233704, 233703, 233702, 233701, 233700, 233699, 233698, 233695, 233693, 233692, 233691, 233689, 233685, 233683, 233658, 233635, 233632, 233627, 233626, 233599, 233598, 233481, 233480, 233479, 233478, 233468, 233467, 233460, 233458, 233455, 233454, 233436, 233410, 233409, 233402, 233392, 233387, 233378, 233283, 233282, 233277, 233276, 233275, 233274, 233273, 233272, 233271, 233270, 233269, 233268, 233267, 233266, 233265, 233264, 233263, 233262, 233261, 233241, 233240, 233239, 233238, 233237, 233236, 233235, 233231, 233225, 233208, 233207, 233206, 233203, 233194, 233179, 233178, 233174, 233173, 233161, 233138, 233122, 233121, 233109, 233085, 233074, 233073, 233072, 233071, 233070, 233069, 233068, 233067, 233066, 233065, 233064, 233063, 233062, 233058, 233057, 233045, 233040, 233039, 233031, 233026, 233025, 233024, 233021, 233012, 233011, 233010, 233009, 232982, 232956, 232954, 232953, 232947, 232946, 232936, 232814, 232813, 232812, 232805, 232799, 232779, 232778, 232776, 232775, 232773, 232755, 232747, 232739, 232738, 232737, 232727, 232718, 232689, 232676, 232665, 232664, 232663, 232662, 232661, 232660, 232659, 232658, 232657, 232656, 232655, 232654, 232653, 232652, 232651, 232650, 232648, 232646, 232645, 232644, 232643, 232642, 232641, 232640, 232639, 232638, 232637, 232636, 232635, 232634, 232633, 232632, 232631, 232630, 232629, 232628, 232627, 232626, 232625, 232624, 232623, 232622, 232621, 232620, 232619, 232618, 232617, 232616, 232615, 232614, 232613, 232612, 232611, 232610, 232609, 232608, 232607, 232606, 232605, 232604, 232603, 232602, 232601, 232600, 232599, 232598, 232597, 232596, 232595, 232594, 232593, 232592, 232591, 232590, 232589, 232588, 232587, 232586, 232585, 232584, 232583, 232582, 232581, 232580, 232579, 232578, 232577, 232576, 232575, 232574, 232573, 232572, 232571, 232570, 232569, 232568, 232567, 232566, 232565, 232564, 232563, 232562, 232561, 232560, 232559, 232558, 232557, 232556, 232555, 232554, 232553, 232552, 232551, 232550, 232549, 232548, 232547, 232546, 232545, 232544, 232543, 232542, 232541, 232540, 232539, 232538, 232537, 232536, 232535, 232534, 232531, 232530, 232528, 232470, 232458, 232455, 232454, 232453, 232452, 232451, 232450, 232447, 232446, 232445, 232444, 232439, 232438, 232436, 232430, 232422, 232421, 232399, 232366, 232356, 232348, 232249, 232160, 232159, 232158, 232157, 232156, 232155, 232154, 232153, 232126, 232124, 232123, 232122, 232119, 232118, 232116, 232108, 232092, 232091, 232090, 232089, 232088, 232087, 232086, 232085, 232084, 232083, 232082, 232081, 232076, 232074, 232053, 232004, 231990, 231936, 231935, 231928, 231927, 231926, 231925, 231924, 231923, 231922, 231921, 231915, 231913, 231888, 231880, 231825, 231824, 231823, 231822, 231821, 231819, 231818, 231817, 231816, 231815, 231814, 231802, 231801, 231800, 231799, 231798, 231797, 231796, 231771, 231770, 231769, 231768, 231767, 231766, 231765, 231751, 231749, 231668, 231667, 231666, 231665, 231664, 231655, 231653, 231652, 231651, 231650, 231649, 231648, 231647, 231646, 231645, 231644, 231643, 231642, 231641, 231640, 231639, 231638, 231637, 231636, 231635, 231634, 231633, 231632, 231631, 231630, 231629, 231628, 231627, 231626, 231625, 231624, 231623, 231622, 231621, 231620, 231619, 231618, 231617, 231616, 231615, 231614, 231613, 231612, 231611, 231610, 231609, 231608, 231607, 231606, 231605, 231604, 231603, 231602, 231601, 231600, 231599, 231598, 231597, 231596, 231595, 231594, 231593, 231592, 231591, 231590, 231589, 231588, 231587, 231586, 231585, 231584, 231583, 231582, 231581, 231580, 231579, 231578, 231577, 231576, 231564, 231563, 231513, 231512, 231511, 231510, 231509, 231434, 231433, 231423, 231421, 231418, 231417, 231416, 231415, 231414, 231413, 231412, 231411, 231410, 231409, 231408, 231407, 231406, 231405, 231404, 231403, 231402, 231401, 231400, 231399, 231398, 231397, 231396, 231395, 231394, 231393, 231392, 231391, 231390, 231389, 231388, 231344, 231343, 231318, 231316, 231315, 231312, 231309, 231308, 231306, 231305, 231304, 231288, 231287, 231286, 231285, 231282, 231281, 231280, 231279, 231278, 231277, 231271, 231270, 231269, 231268, 231267, 231266, 231265, 231264, 231263, 231254, 231244, 231243, 231235, 231223, 231221, 231214, 231212, 231211, 231210, 231209, 231208, 231207, 231206, 231205, 231204, 231203, 231202, 231201, 231200, 231199, 231198, 231197, 231195, 231194, 231193, 231192, 231191, 231190, 231189, 231188, 231187, 231186, 231185, 231184, 231183, 231182, 231181, 231180, 231179, 231178, 231177, 231176, 231175, 231174, 231173, 231172, 231171, 231170, 231169, 231168, 231167, 231166, 231165, 231164, 231163, 231162, 231161, 231160, 231159, 231158, 231153, 231152, 231151, 231150, 231149, 231148, 231147, 231146, 231145, 231144, 231143, 231142, 231141, 231140, 231139, 231138, 231137, 231132, 231131, 231130, 231129, 231128, 231127, 231126, 231125, 231124, 231123, 231122, 231121, 231120, 231119, 231118, 231117, 231116, 231115, 231114, 231113, 231112, 231111, 231110, 231109, 231108, 231107, 231106, 231105, 231104, 231103, 231102, 231101, 231100, 231095, 231094, 231093, 231092, 231091, 231090, 231089, 231084, 231083, 231082, 231081, 231080, 231079, 231078, 231077, 231076, 231075, 231074, 231073, 231072, 231071, 231070, 231069, 231068, 231067, 231066, 231065, 231064, 231063, 231062, 231061, 231060, 231059, 231058, 231057, 231056, 231055, 231054, 231053, 231052, 231051, 231050, 231049, 231048, 231047, 231046, 231045, 231044, 231043, 231042, 231041, 231040, 231039, 231037, 231036, 231035, 231034, 231033, 231032, 231031, 231030, 231029, 231028, 231026, 231025, 231024, 231023, 231022, 231021, 231020, 231019, 231018, 231017, 231016, 231015, 231014, 231013, 231012, 231011, 231010, 231009, 231008, 231007, 231006, 231005, 231004, 231003, 231002, 231001, 231000, 230999, 230998, 230997, 230996, 230995, 230994, 230993, 230992, 230991, 230990, 230989, 230988, 230987, 230986, 230985, 230984, 230983, 230982, 230981, 230980, 230979, 230978, 230977, 230976, 230975, 230974, 230973, 230972, 230971, 230970, 230969, 230968, 230967, 230966, 230965, 230964, 230963, 230962, 230961, 230960, 230959, 230958, 230957, 230956, 230955, 230954, 230953, 230952, 230951, 230950, 230949, 230948, 230947, 230946, 230945, 230944, 230943, 230942, 230941, 230940, 230939, 230938, 230937, 230936, 230935, 230934, 230933, 230932, 230931, 230930, 230929, 230928, 230927, 230926, 230925, 230924, 230923, 230922, 230921, 230920, 230919, 230918, 230917, 230916, 230915, 230914, 230913, 230912, 230857, 230856, 230854, 230847, 230843, 230842, 230835, 230834, 230833, 230832, 230831, 230824, 230822, 230818, 230815, 230814, 230797, 230772, 230771, 230769, 230768, 230764, 230763, 230762, 230754, 230750, 230741, 230740, 230739, 230736, 230727, 230721, 230711, 230709, 230706, 230705, 230701, 230700, 230699, 230692, 230689, 230688, 230685, 230683, 230652, 230630, 230628, 230626, 230625, 230595, 230592, 230589, 230577, 230575, 230568, 230560, 230554, 230553, 230552, 230547, 230539, 230483, 230482, 230481, 230480, 230479, 230478, 230477, 230476, 230475, 230474, 230473, 230472, 230471, 230470, 230469, 230468, 230467, 230466, 230465, 230464, 230463, 230462, 230461, 230460, 230459, 230458, 230457, 230456, 230455, 230454, 230453, 230452, 230451, 230450, 230449, 230448, 230447, 230446, 230445, 230444, 230443, 230442, 230441, 230440, 230439, 230438, 230437, 230436, 230435, 230434, 230430, 230425, 230423, 230412, 230411, 230399, 230396, 230395, 230394, 230392, 230390, 230381, 230380, 230379, 230378, 230375, 230374, 230361, 230359, 230342, 230339, 230332, 230331, 230314, 230303, 230302, 230284, 230265, 230261, 230252, 230247, 230245, 230232, 230195, 230194, 230193, 230192, 230191, 230190, 230189, 230188, 230187, 230186, 230185, 230184, 230171, 230169, 230160, 230130, 230129, 230128, 230126, 230125, 230122, 230101, 230100, 230090, 230089, 230088, 230087, 230085, 230084, 230078, 230076, 230073, 230066, 230019, 230015, 230007, 230006, 230005, 230004, 229991, 229981, 229980, 229973, 229970, 229965, 229939, 229938, 229933, 229927, 229925, 229924, 229923, 229897, 229895, 229893, 229887, 229885, 229884, 229883, 229880, 229879, 229878, 229877, 229856, 229824, 229823, 229822, 229821, 229820, 229819, 229774, 229769, 229763, 229762, 229760, 229736, 229729, 229727, 229726, 229725, 229724, 229720, 229702, 229697, 229691, 229682, 229673, 229672, 229671, 229670, 229669, 229668, 229667, 229666, 229665, 229664, 229663, 229662, 229661, 229660, 229659, 229658, 229657, 229656, 229655, 229654, 229653, 229652, 229651, 229650, 229649, 229648, 229647, 229646, 229645, 229644, 229643, 229642, 229641, 229640, 229639, 229638, 229637, 229636, 229635, 229634, 229633, 229632, 229631, 229630, 229629, 229628, 229627, 229626, 229625, 229624, 229623, 229622, 229621, 229620, 229619, 229618, 229617, 229616, 229615, 229614, 229613, 229612, 229611, 229610, 229609, 229608, 229607, 229606, 229605, 229604, 229603, 229602, 229601, 229600, 229599, 229598, 229597, 229596, 229595, 229594, 229593, 229592, 229591, 229590, 229589, 229588, 229587, 229586, 229585, 229584, 229583, 229582, 229581, 229580, 229579, 229578, 229577, 229576, 229575, 229574, 229573, 229572, 229571, 229570, 229569, 229568, 229567, 229566, 229565, 229564, 229560, 229528, 229527, 229512, 229511, 229495, 229487, 229486, 229480, 229478, 229469, 229467, 229466, 229465, 229464, 229455, 229452, 229423, 229421, 229418, 229394, 229352, 229351, 229350, 229349, 229348, 229347, 229340, 229339, 229338, 229302, 229296, 229295, 229294, 229293, 229286, 229269, 229237, 229225, 229224, 229221, 229215, 229208, 229207, 229206, 229205, 229204, 229203, 229163, 229162, 229056, 229055, 228982, 228981, 228967, 228966, 228965, 228963, 228956, 228955, 228954, 228953, 228952, 228935, 228928, 228927, 228926, 228925, 228924, 228923, 228922, 228921, 228920, 228919, 228918, 228917, 228916, 228915, 228914, 228913, 228912, 228911, 228910, 228909, 228908, 228907, 228906, 228905, 228904, 228903, 228902, 228901, 228900, 228899, 228898, 228897, 228896, 228895, 228894, 228893 ]
  },
  "filters" : [ {
    "name" : "search",
    "filterType" : "String",
    "filterValue" : "",
    "required" : true
  } ],
  "optionFilters" : [ {
    "name" : "storageState",
    "filterType" : "NotSet",
    "required" : true,
    "multiple" : true,
    "options" : [ "ACTIVE", "ARCHIVED" ],
    "selectOptions" : [ "ACTIVE" ]
  } ],
  "rangeFilters" : [ ],
  "maxReturn" : 5000
} ]
2025-08-01T10:26:35.584+07:00 ERROR 57538 --- [qtp791217259-39] n.d.m.monitor.call.EndpointCallService   : Unknown Error when handle the endpoint JobTrackingService/searchJobTrackings
2025-08-01T10:26:35.583+07:00 ERROR 57538 --- [qtp791217259-41] n.d.m.monitor.call.EndpointCallService   : Exception: 

net.datatp.util.error.RuntimeError: Cannot run script file SearchJobTracking
	at net.datatp.util.error.RuntimeError.UnknownError(RuntimeError.java:96)
	at net.datatp.lib.executable.ExecutableUnit.doExecute(ExecutableUnit.java:33)
	at net.datatp.lib.executable.Executor.execute(Executor.java:34)
	at net.datatp.module.service.ExecutableUnitManager.execute(ExecutableUnitManager.java:61)
	at cloud.datatp.jobtracking.JobTrackingLogic.searchJobTrackings(JobTrackingLogic.java:124)
	at cloud.datatp.jobtracking.JobTrackingService.searchJobTrackings(JobTrackingService.java:143)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at cloud.datatp.jobtracking.JobTrackingService$$SpringCGLIB$$0.searchJobTrackings(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:158)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:143)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)

2025-08-01T10:26:35.584+07:00 ERROR 57538 --- [qtp791217259-39] n.d.m.monitor.call.EndpointCallService   : Exception: 

net.datatp.util.error.RuntimeError: Cannot run script file SearchJobTracking
	at net.datatp.util.error.RuntimeError.UnknownError(RuntimeError.java:96)
	at net.datatp.lib.executable.ExecutableUnit.doExecute(ExecutableUnit.java:33)
	at net.datatp.lib.executable.Executor.execute(Executor.java:34)
	at net.datatp.module.service.ExecutableUnitManager.execute(ExecutableUnitManager.java:61)
	at cloud.datatp.jobtracking.JobTrackingLogic.searchJobTrackings(JobTrackingLogic.java:124)
	at cloud.datatp.jobtracking.JobTrackingService.searchJobTrackings(JobTrackingService.java:143)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:359)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:380)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:727)
	at cloud.datatp.jobtracking.JobTrackingService$$SpringCGLIB$$0.searchJobTrackings(<generated>)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at net.datatp.module.monitor.call.EndpointCallContext.doCall(EndpointCallContext.java:158)
	at net.datatp.module.monitor.call.EndpointCallContext.call(EndpointCallContext.java:143)
	at net.datatp.module.monitor.call.EndpointCallService.call(EndpointCallService.java:58)
	at net.datatp.module.core.security.http.RPCController.call(RPCController.java:93)
	at net.datatp.module.core.security.http.RPCController.privateCall(RPCController.java:49)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:986)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:891)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1088)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:978)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:520)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:587)
	at org.eclipse.jetty.ee10.servlet.ServletHolder.handle(ServletHolder.java:736)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$ChainEnd.doFilter(ServletHandler.java:1614)
	at org.eclipse.jetty.ee10.websocket.servlet.WebSocketUpgradeFilter.doFilter(WebSocketUpgradeFilter.java:195)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:108)
	at org.springframework.security.web.FilterChainProxy.lambda$doFilterInternal$3(FilterChainProxy.java:231)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:365)
	at org.springframework.security.web.access.intercept.AuthorizationFilter.doFilter(AuthorizationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:126)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:120)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:100)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:179)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:107)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:93)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:82)
	at org.springframework.security.web.context.SecurityContextHolderFilter.doFilter(SecurityContextHolderFilter.java:69)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:62)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.session.DisableEncodeUrlFilter.doFilterInternal(DisableEncodeUrlFilter.java:42)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:374)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:233)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:191)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.servlet.handler.HandlerMappingIntrospector.lambda$createCacheFilter$3(HandlerMappingIntrospector.java:243)
	at org.springframework.web.filter.CompositeFilter$VirtualFilterChain.doFilter(CompositeFilter.java:113)
	at org.springframework.web.filter.CompositeFilter.doFilter(CompositeFilter.java:74)
	at org.springframework.security.config.annotation.web.configuration.WebMvcSecurityConfiguration$CompositeFilterChainProxy.doFilter(WebMvcSecurityConfiguration.java:230)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:362)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:278)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.eclipse.jetty.ee10.servlet.FilterHolder.doFilter(FilterHolder.java:205)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$Chain.doFilter(ServletHandler.java:1586)
	at org.eclipse.jetty.ee10.servlet.ServletHandler$MappedServlet.handle(ServletHandler.java:1547)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.dispatch(ServletChannel.java:819)
	at org.eclipse.jetty.ee10.servlet.ServletChannel.handle(ServletChannel.java:436)
	at org.eclipse.jetty.ee10.servlet.ServletHandler.handle(ServletHandler.java:464)
	at org.eclipse.jetty.security.SecurityHandler.handle(SecurityHandler.java:575)
	at org.eclipse.jetty.ee10.servlet.SessionHandler.handle(SessionHandler.java:717)
	at org.eclipse.jetty.server.handler.ContextHandler.handle(ContextHandler.java:1060)
	at org.eclipse.jetty.server.handler.gzip.GzipHandler.handle(GzipHandler.java:611)
	at org.eclipse.jetty.server.Server.handle(Server.java:182)
	at org.eclipse.jetty.server.internal.HttpChannelState$HandlerInvoker.run(HttpChannelState.java:662)
	at org.eclipse.jetty.server.internal.HttpConnection.onFillable(HttpConnection.java:418)
	at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:322)
	at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:99)
	at org.eclipse.jetty.io.SelectableChannelEndPoint$1.run(SelectableChannelEndPoint.java:53)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.runTask(AdaptiveExecutionStrategy.java:478)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.consumeTask(AdaptiveExecutionStrategy.java:441)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.tryProduce(AdaptiveExecutionStrategy.java:293)
	at org.eclipse.jetty.util.thread.strategy.AdaptiveExecutionStrategy.run(AdaptiveExecutionStrategy.java:201)
	at org.eclipse.jetty.util.thread.ReservedThreadExecutor$ReservedThread.run(ReservedThreadExecutor.java:311)
	at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:979)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.doRunJob(QueuedThreadPool.java:1209)
	at org.eclipse.jetty.util.thread.QueuedThreadPool$Runner.run(QueuedThreadPool.java:1164)
	at java.base/java.lang.Thread.run(Thread.java:1583)

2025-08-01T10:27:02.408+07:00  INFO 57538 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-01T10:27:23.538+07:00  INFO 57538 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-08-01T10:27:23.594+07:00  INFO 57538 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T10:27:31.970+07:00  INFO 57538 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@3e6520f3{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-08-01T10:27:31.971+07:00  INFO 57538 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-08-01T10:27:31.971+07:00  INFO 57538 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-08-01T10:27:31.971+07:00  INFO 57538 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-08-01T10:27:31.971+07:00  INFO 57538 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-08-01T10:27:31.971+07:00  INFO 57538 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-08-01T10:27:31.971+07:00  INFO 57538 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-08-01T10:27:31.971+07:00  INFO 57538 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-08-01T10:27:31.971+07:00  INFO 57538 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-08-01T10:27:31.971+07:00  INFO 57538 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-08-01T10:27:31.971+07:00  INFO 57538 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-08-01T10:27:31.971+07:00  INFO 57538 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-08-01T10:27:31.971+07:00  INFO 57538 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-08-01T10:27:31.971+07:00  INFO 57538 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-08-01T10:27:31.971+07:00  INFO 57538 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-08-01T10:27:31.971+07:00  INFO 57538 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-08-01T10:27:31.987+07:00  INFO 57538 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-01T10:27:32.042+07:00  INFO 57538 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-08-01T10:27:32.050+07:00  INFO 57538 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-08-01T10:27:32.070+07:00  INFO 57538 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-01T10:27:32.071+07:00  INFO 57538 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-01T10:27:32.072+07:00  INFO 57538 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-01T10:27:32.072+07:00  INFO 57538 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-08-01T10:27:32.073+07:00  INFO 57538 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-08-01T10:27:32.073+07:00  INFO 57538 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-08-01T10:27:32.074+07:00  INFO 57538 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-08-01T10:27:32.074+07:00  INFO 57538 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-08-01T10:27:32.210+07:00  INFO 57538 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-08-01T10:27:32.210+07:00  INFO 57538 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-08-01T10:27:32.211+07:00  INFO 57538 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-08-01T10:27:32.211+07:00  INFO 57538 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-08-01T10:27:32.211+07:00  INFO 57538 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-08-01T10:27:32.213+07:00  INFO 57538 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@22ca5fe1{STOPPING}[12.0.15,sto=0]
2025-08-01T10:27:32.217+07:00  INFO 57538 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-01T10:27:32.218+07:00  INFO 57538 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@7505ef47{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.1366672937543224676/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@68fe866b{STOPPED}}
