2025-08-04T15:17:42.366+07:00  INFO 69743 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 69743 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-08-04T15:17:42.368+07:00  INFO 69743 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-08-04T15:17:43.104+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T15:17:43.168+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 60 ms. Found 22 JPA repository interfaces.
2025-08-04T15:17:43.177+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T15:17:43.179+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-08-04T15:17:43.179+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T15:17:43.187+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 9 JPA repository interfaces.
2025-08-04T15:17:43.188+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T15:17:43.225+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 36 ms. Found 3 JPA repository interfaces.
2025-08-04T15:17:43.225+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T15:17:43.229+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-08-04T15:17:43.240+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T15:17:43.245+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 1 JPA repository interface.
2025-08-04T15:17:43.256+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T15:17:43.261+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 5 JPA repository interfaces.
2025-08-04T15:17:43.264+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T15:17:43.267+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-08-04T15:17:43.267+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T15:17:43.268+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-08-04T15:17:43.273+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T15:17:43.280+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 10 JPA repository interfaces.
2025-08-04T15:17:43.286+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T15:17:43.288+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-08-04T15:17:43.289+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T15:17:43.292+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-08-04T15:17:43.294+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T15:17:43.301+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-08-04T15:17:43.301+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T15:17:43.304+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-08-04T15:17:43.304+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T15:17:43.305+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-08-04T15:17:43.305+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T15:17:43.306+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-08-04T15:17:43.306+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T15:17:43.310+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-08-04T15:17:43.310+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T15:17:43.312+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-08-04T15:17:43.312+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T15:17:43.312+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-08-04T15:17:43.312+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T15:17:43.323+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 19 JPA repository interfaces.
2025-08-04T15:17:43.333+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T15:17:43.340+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 8 JPA repository interfaces.
2025-08-04T15:17:43.340+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T15:17:43.343+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-08-04T15:17:43.343+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T15:17:43.348+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-08-04T15:17:43.348+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T15:17:43.354+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-08-04T15:17:43.354+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T15:17:43.358+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 6 JPA repository interfaces.
2025-08-04T15:17:43.359+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T15:17:43.362+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-08-04T15:17:43.362+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T15:17:43.366+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-08-04T15:17:43.367+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T15:17:43.375+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 14 JPA repository interfaces.
2025-08-04T15:17:43.376+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T15:17:43.388+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 12 ms. Found 20 JPA repository interfaces.
2025-08-04T15:17:43.388+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T15:17:43.389+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-08-04T15:17:43.395+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T15:17:43.396+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-08-04T15:17:43.396+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T15:17:43.403+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-08-04T15:17:43.405+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T15:17:43.449+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 43 ms. Found 69 JPA repository interfaces.
2025-08-04T15:17:43.449+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T15:17:43.450+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-08-04T15:17:43.455+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T15:17:43.458+07:00  INFO 69743 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-08-04T15:17:43.673+07:00  INFO 69743 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-08-04T15:17:43.677+07:00  INFO 69743 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-08-04T15:17:43.939+07:00  WARN 69743 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-08-04T15:17:44.132+07:00  INFO 69743 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-08-04T15:17:44.134+07:00  INFO 69743 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-08-04T15:17:44.146+07:00  INFO 69743 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-08-04T15:17:44.146+07:00  INFO 69743 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1660 ms
2025-08-04T15:17:44.202+07:00  WARN 69743 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-04T15:17:44.202+07:00  INFO 69743 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-08-04T15:17:44.299+07:00  INFO 69743 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@635f4be1
2025-08-04T15:17:44.300+07:00  INFO 69743 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-08-04T15:17:44.305+07:00  WARN 69743 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-04T15:17:44.305+07:00  INFO 69743 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-08-04T15:17:44.310+07:00  INFO 69743 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@2ea3f905
2025-08-04T15:17:44.310+07:00  INFO 69743 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-08-04T15:17:44.310+07:00  WARN 69743 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-04T15:17:44.310+07:00  INFO 69743 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-04T15:17:44.767+07:00  INFO 69743 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@64dd94ed
2025-08-04T15:17:44.767+07:00  INFO 69743 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-04T15:17:44.767+07:00  WARN 69743 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-04T15:17:44.767+07:00  INFO 69743 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-08-04T15:17:44.779+07:00  INFO 69743 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@67f25c
2025-08-04T15:17:44.779+07:00  INFO 69743 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-08-04T15:17:44.779+07:00  WARN 69743 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-04T15:17:44.779+07:00  INFO 69743 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-08-04T15:17:44.785+07:00  INFO 69743 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@43941806
2025-08-04T15:17:44.785+07:00  INFO 69743 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-08-04T15:17:44.785+07:00  INFO 69743 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '****************************************************', '***********************************************', '**********************************************'
2025-08-04T15:17:44.837+07:00  INFO 69743 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-08-04T15:17:44.839+07:00  INFO 69743 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@5068a2b1{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.13385377560270647409/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@3938e876{STARTED}}
2025-08-04T15:17:44.839+07:00  INFO 69743 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@5068a2b1{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.13385377560270647409/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@3938e876{STARTED}}
2025-08-04T15:17:44.841+07:00  INFO 69743 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@570cef1e{STARTING}[12.0.15,sto=0] @3168ms
2025-08-04T15:17:44.902+07:00  INFO 69743 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-04T15:17:44.930+07:00  INFO 69743 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-08-04T15:17:44.944+07:00  INFO 69743 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-04T15:17:45.072+07:00  INFO 69743 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-04T15:17:45.101+07:00  WARN 69743 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-04T15:17:45.741+07:00  INFO 69743 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-04T15:17:45.754+07:00  INFO 69743 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@2d83ed58] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-08-04T15:17:45.877+07:00  INFO 69743 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-04T15:17:46.065+07:00  INFO 69743 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.graphapi", "net.datatp.module.kpi", "net.datatp.module.monitor.activity", "net.datatp.module.wfms", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "net.datatp.module.okr", "cloud.datatp.module.fleet", "net.datatp.module.core.template", "cloud.datatp.gps", "cloud.datatp.module.pegasus.shipment", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "net.datatp.module.company.tmpl", "net.datatp.module.groovy", "cloud.datatp.tms", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "cloud.datatp.vendor", "net.datatp.module.resource", "net.datatp.module.company.hr", "net.datatp.module.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "cloud.datatp.module.odoo", "net.datatp.module.core.print", "net.datatp.module.chatbot", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.zalo", "net.datatp.module.accounting" ]
2025-08-04T15:17:46.067+07:00  INFO 69743 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-08-04T15:17:46.073+07:00  INFO 69743 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-04T15:17:46.074+07:00  INFO 69743 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-04T15:17:46.101+07:00  INFO 69743 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-04T15:17:46.105+07:00  WARN 69743 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-04T15:17:48.182+07:00  INFO 69743 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-04T15:17:48.183+07:00  INFO 69743 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@f71836a] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-08-04T15:17:48.366+07:00  WARN 69743 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-08-04T15:17:48.366+07:00  WARN 69743 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-08-04T15:17:48.374+07:00  WARN 69743 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-08-04T15:17:48.374+07:00  WARN 69743 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-08-04T15:17:48.390+07:00  WARN 69743 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-08-04T15:17:48.390+07:00  WARN 69743 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-08-04T15:17:48.851+07:00  INFO 69743 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-04T15:17:48.856+07:00  INFO 69743 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-04T15:17:48.858+07:00  INFO 69743 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-04T15:17:48.872+07:00  INFO 69743 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-04T15:17:48.876+07:00  WARN 69743 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-04T15:17:49.402+07:00  INFO 69743 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-04T15:17:49.403+07:00  INFO 69743 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@4ab99cbb] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-08-04T15:17:49.462+07:00  WARN 69743 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-08-04T15:17:49.462+07:00  WARN 69743 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-08-04T15:17:49.765+07:00  INFO 69743 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-04T15:17:49.798+07:00  INFO 69743 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-08-04T15:17:49.803+07:00  INFO 69743 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-08-04T15:17:49.803+07:00  INFO 69743 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-08-04T15:17:49.809+07:00  WARN 69743 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-08-04T15:17:49.948+07:00  INFO 69743 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-08-04T15:17:50.498+07:00  INFO 69743 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-08-04T15:17:50.501+07:00  INFO 69743 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-08-04T15:17:50.538+07:00  INFO 69743 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-08-04T15:17:50.576+07:00  INFO 69743 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-08-04T15:17:50.626+07:00  INFO 69743 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-08-04T15:17:50.655+07:00  INFO 69743 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-08-04T15:17:50.677+07:00  INFO 69743 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 185684809ms : this is harmless.
2025-08-04T15:17:50.686+07:00  INFO 69743 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-08-04T15:17:50.690+07:00  INFO 69743 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-08-04T15:17:50.703+07:00  INFO 69743 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 867916611ms : this is harmless.
2025-08-04T15:17:50.705+07:00  INFO 69743 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-08-04T15:17:50.721+07:00  INFO 69743 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-08-04T15:17:50.723+07:00  INFO 69743 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-08-04T15:17:53.120+07:00  INFO 69743 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-08-04T15:17:53.120+07:00  INFO 69743 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-08-04T15:17:53.121+07:00  WARN 69743 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-08-04T15:17:53.244+07:00  INFO 69743 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 04/08/2025@15:15:00+0700 to 04/08/2025@15:30:00+0700
2025-08-04T15:17:53.244+07:00  INFO 69743 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 04/08/2025@15:15:00+0700 to 04/08/2025@15:30:00+0700
2025-08-04T15:17:53.701+07:00  INFO 69743 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-08-04T15:17:53.701+07:00  INFO 69743 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-08-04T15:17:53.701+07:00  WARN 69743 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-08-04T15:17:53.935+07:00  INFO 69743 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-08-04T15:17:53.935+07:00  INFO 69743 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-08-04T15:17:53.935+07:00  INFO 69743 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-08-04T15:17:53.935+07:00  INFO 69743 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-08-04T15:17:53.935+07:00  INFO 69743 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-08-04T15:17:55.808+07:00  WARN 69743 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 8275476a-b529-4a9e-9b0c-f7708d0ee29e

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-04T15:17:55.812+07:00  INFO 69743 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-04T15:17:56.142+07:00  INFO 69743 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-08-04T15:17:56.143+07:00  INFO 69743 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-08-04T15:17:56.143+07:00  INFO 69743 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-08-04T15:17:56.143+07:00  INFO 69743 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-08-04T15:17:56.143+07:00  INFO 69743 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-08-04T15:17:56.143+07:00  INFO 69743 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-08-04T15:17:56.143+07:00  INFO 69743 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-08-04T15:17:56.143+07:00  INFO 69743 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-08-04T15:17:56.143+07:00  INFO 69743 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-08-04T15:17:56.143+07:00  INFO 69743 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-08-04T15:17:56.143+07:00  INFO 69743 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-08-04T15:17:56.143+07:00  INFO 69743 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-08-04T15:17:56.146+07:00  INFO 69743 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-08-04T15:17:56.146+07:00  INFO 69743 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-08-04T15:17:56.146+07:00  INFO 69743 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-08-04T15:17:56.222+07:00  INFO 69743 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-04T15:17:56.222+07:00  INFO 69743 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-04T15:17:56.223+07:00  INFO 69743 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-08-04T15:17:56.231+07:00  INFO 69743 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@33c179e9{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-08-04T15:17:56.232+07:00  INFO 69743 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-08-04T15:17:56.232+07:00  INFO 69743 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-08-04T15:17:56.255+07:00  INFO 69743 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-08-04T15:17:56.255+07:00  INFO 69743 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-08-04T15:17:56.261+07:00  INFO 69743 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 14.253 seconds (process running for 14.588)
2025-08-04T15:17:59.625+07:00  INFO 69743 --- [qtp1582527589-37] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node013imfkzwljylcqfodzs3bxdbk1
2025-08-04T15:17:59.625+07:00  INFO 69743 --- [qtp1582527589-35] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node04qsarl7ws78yq52n7mu3mtix0
2025-08-04T15:17:59.758+07:00  INFO 69743 --- [qtp1582527589-35] n.d.module.session.ClientSessionManager  : Add a client session id = node04qsarl7ws78yq52n7mu3mtix0, token = 228136614c4396bfd49f4ca505678a65
2025-08-04T15:17:59.759+07:00  INFO 69743 --- [qtp1582527589-37] n.d.module.session.ClientSessionManager  : Add a client session id = node013imfkzwljylcqfodzs3bxdbk1, token = 228136614c4396bfd49f4ca505678a65
2025-08-04T15:18:00.220+07:00  INFO 69743 --- [qtp1582527589-35] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:18:00.228+07:00  INFO 69743 --- [qtp1582527589-37] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:18:03.168+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T15:18:04.433+07:00  INFO 69743 --- [qtp1582527589-37] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-08-04T15:18:15.442+07:00  INFO 69743 --- [qtp1582527589-40] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-08-04T15:18:33.273+07:00  INFO 69743 --- [qtp1582527589-62] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0szwvvl7hnwbd8rm64fbgk5013
2025-08-04T15:18:33.274+07:00  INFO 69743 --- [qtp1582527589-64] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0199naent08mr3s0oud2m7nkyi2
2025-08-04T15:18:33.306+07:00  INFO 69743 --- [qtp1582527589-64] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 7275453a845c475081363f25b1a68466
2025-08-04T15:18:33.306+07:00  INFO 69743 --- [qtp1582527589-62] n.d.module.session.ClientSessionManager  : Add a client session id = node0szwvvl7hnwbd8rm64fbgk5013, token = 7275453a845c475081363f25b1a68466
2025-08-04T15:18:33.313+07:00  INFO 69743 --- [qtp1582527589-62] n.d.m.c.a.CompanyAuthenticationService   : User edgar.vnhph is logged in successfully system
2025-08-04T15:18:33.313+07:00  INFO 69743 --- [qtp1582527589-64] n.d.m.c.a.CompanyAuthenticationService   : User edgar.vnhph is logged in successfully system
2025-08-04T15:18:59.285+07:00  INFO 69743 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 10, expire count 0
2025-08-04T15:18:59.300+07:00  INFO 69743 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T15:19:06.313+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T15:19:06.431+07:00  INFO 69743 --- [qtp1582527589-37] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 7275453a845c475081363f25b1a68466
2025-08-04T15:19:06.434+07:00  INFO 69743 --- [qtp1582527589-64] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 7275453a845c475081363f25b1a68466
2025-08-04T15:19:06.444+07:00  INFO 69743 --- [qtp1582527589-37] n.d.m.c.a.CompanyAuthenticationService   : User edgar.vnhph is logged in successfully system
2025-08-04T15:19:06.449+07:00  INFO 69743 --- [qtp1582527589-64] n.d.m.c.a.CompanyAuthenticationService   : User edgar.vnhph is logged in successfully system
2025-08-04T15:19:30.606+07:00  INFO 69743 --- [qtp1582527589-35] n.d.module.session.ClientSessionManager  : Add a client session id = node013imfkzwljylcqfodzs3bxdbk1, token = 228136614c4396bfd49f4ca505678a65
2025-08-04T15:19:30.659+07:00  INFO 69743 --- [qtp1582527589-35] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:19:30.667+07:00  INFO 69743 --- [qtp1582527589-39] n.d.module.session.ClientSessionManager  : Add a client session id = node013imfkzwljylcqfodzs3bxdbk1, token = 228136614c4396bfd49f4ca505678a65
2025-08-04T15:19:30.700+07:00  INFO 69743 --- [qtp1582527589-39] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:19:31.433+07:00  INFO 69743 --- [qtp1582527589-36] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 7275453a845c475081363f25b1a68466
2025-08-04T15:19:31.434+07:00  INFO 69743 --- [qtp1582527589-61] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 7275453a845c475081363f25b1a68466
2025-08-04T15:19:31.439+07:00  INFO 69743 --- [qtp1582527589-36] n.d.m.c.a.CompanyAuthenticationService   : User edgar.vnhph is logged in successfully system
2025-08-04T15:19:31.439+07:00  INFO 69743 --- [qtp1582527589-61] n.d.m.c.a.CompanyAuthenticationService   : User edgar.vnhph is logged in successfully system
2025-08-04T15:19:32.443+07:00  INFO 69743 --- [qtp1582527589-64] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 7275453a845c475081363f25b1a68466
2025-08-04T15:19:32.449+07:00  INFO 69743 --- [qtp1582527589-64] n.d.m.c.a.CompanyAuthenticationService   : User edgar.vnhph is logged in successfully system
2025-08-04T15:19:32.472+07:00  INFO 69743 --- [qtp1582527589-67] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 7275453a845c475081363f25b1a68466
2025-08-04T15:19:32.477+07:00  INFO 69743 --- [qtp1582527589-67] n.d.m.c.a.CompanyAuthenticationService   : User edgar.vnhph is logged in successfully system
2025-08-04T15:20:02.416+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T15:20:02.426+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T15:20:58.565+07:00  INFO 69743 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 13, expire count 0
2025-08-04T15:20:58.578+07:00  INFO 69743 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T15:21:05.592+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T15:22:06.669+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T15:23:02.806+07:00  INFO 69743 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 7, expire count 0
2025-08-04T15:23:02.812+07:00  INFO 69743 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T15:23:04.819+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T15:24:06.908+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T15:25:03.016+07:00  INFO 69743 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-08-04T15:25:03.027+07:00  INFO 69743 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T15:25:04.034+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T15:25:04.035+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T15:26:06.172+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T15:27:02.320+07:00  INFO 69743 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T15:27:02.324+07:00  INFO 69743 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T15:27:03.335+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T15:27:44.862+07:00  INFO 69743 --- [Scheduler-2145867337-1] n.d.m.session.AppHttpSessionListener     : The session node04qsarl7ws78yq52n7mu3mtix0 is destroyed.
2025-08-04T15:27:44.864+07:00  INFO 69743 --- [Scheduler-2145867337-1] n.d.m.session.AppHttpSessionListener     : The session node0szwvvl7hnwbd8rm64fbgk5013 is destroyed.
2025-08-04T15:28:06.552+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T15:28:32.590+07:00  INFO 69743 --- [qtp1582527589-39] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-08-04T15:28:39.418+07:00  INFO 69743 --- [qtp1582527589-66] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-08-04T15:28:40.444+07:00  INFO 69743 --- [qtp1582527589-74] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-08-04T15:29:02.757+07:00  INFO 69743 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 8
2025-08-04T15:29:02.791+07:00  INFO 69743 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T15:29:02.791+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T15:29:33.607+07:00  INFO 69743 --- [qtp1582527589-66] n.d.module.session.ClientSessionManager  : Add a client session id = node013imfkzwljylcqfodzs3bxdbk1, token = 228136614c4396bfd49f4ca505678a65
2025-08-04T15:29:33.608+07:00  INFO 69743 --- [qtp1582527589-34] n.d.module.session.ClientSessionManager  : Add a client session id = node013imfkzwljylcqfodzs3bxdbk1, token = 228136614c4396bfd49f4ca505678a65
2025-08-04T15:29:33.626+07:00  INFO 69743 --- [qtp1582527589-66] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:29:33.626+07:00  INFO 69743 --- [qtp1582527589-34] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:29:41.538+07:00  INFO 69743 --- [qtp1582527589-74] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 7275453a845c475081363f25b1a68466
2025-08-04T15:29:41.541+07:00  INFO 69743 --- [qtp1582527589-72] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 7275453a845c475081363f25b1a68466
2025-08-04T15:29:41.547+07:00  INFO 69743 --- [qtp1582527589-72] n.d.m.c.a.CompanyAuthenticationService   : User edgar.vnhph is logged in successfully system
2025-08-04T15:29:41.547+07:00  INFO 69743 --- [qtp1582527589-74] n.d.m.c.a.CompanyAuthenticationService   : User edgar.vnhph is logged in successfully system
2025-08-04T15:29:43.429+07:00  INFO 69743 --- [qtp1582527589-61] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 7275453a845c475081363f25b1a68466
2025-08-04T15:29:43.430+07:00  INFO 69743 --- [qtp1582527589-74] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 7275453a845c475081363f25b1a68466
2025-08-04T15:29:43.439+07:00  INFO 69743 --- [qtp1582527589-61] n.d.m.c.a.CompanyAuthenticationService   : User edgar.vnhph is logged in successfully system
2025-08-04T15:29:43.457+07:00  INFO 69743 --- [qtp1582527589-74] n.d.m.c.a.CompanyAuthenticationService   : User edgar.vnhph is logged in successfully system
2025-08-04T15:30:05.994+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T15:30:05.996+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T15:30:05.998+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-04T15:30:06.003+07:00  INFO 69743 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 04/08/2025@15:30:06+0700
2025-08-04T15:30:06.042+07:00  INFO 69743 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 04/08/2025@15:30:00+0700 to 04/08/2025@15:45:00+0700
2025-08-04T15:30:06.043+07:00  INFO 69743 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 04/08/2025@15:30:00+0700 to 04/08/2025@15:45:00+0700
2025-08-04T15:31:02.201+07:00  INFO 69743 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 8, expire count 6
2025-08-04T15:31:02.211+07:00  INFO 69743 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T15:31:02.211+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T15:31:49.797+07:00  INFO 69743 --- [qtp1582527589-39] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le logout successfully 
2025-08-04T15:31:51.929+07:00  INFO 69743 --- [qtp1582527589-71] n.d.module.session.ClientSessionManager  : Add a client session id = node013imfkzwljylcqfodzs3bxdbk1, token = caf174a6741170d6b0db872f1f34f339
2025-08-04T15:31:51.936+07:00  INFO 69743 --- [qtp1582527589-71] n.d.m.c.a.CompanyAuthenticationService   : User liam.vnhph is logged in successfully system
2025-08-04T15:32:05.301+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T15:33:01.469+07:00  INFO 69743 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 8, expire count 0
2025-08-04T15:33:01.478+07:00  INFO 69743 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T15:33:06.486+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T15:34:04.588+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T15:34:09.316+07:00  INFO 69743 --- [qtp1582527589-66] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 7275453a845c475081363f25b1a68466
2025-08-04T15:34:09.332+07:00  INFO 69743 --- [qtp1582527589-66] n.d.m.c.a.CompanyAuthenticationService   : User edgar.vnhph is logged in successfully system
2025-08-04T15:34:09.348+07:00  INFO 69743 --- [qtp1582527589-69] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 7275453a845c475081363f25b1a68466
2025-08-04T15:34:09.382+07:00  INFO 69743 --- [qtp1582527589-69] n.d.m.c.a.CompanyAuthenticationService   : User edgar.vnhph is logged in successfully system
2025-08-04T15:34:09.465+07:00  INFO 69743 --- [qtp1582527589-61] n.d.module.session.ClientSessionManager  : Add a client session id = node013imfkzwljylcqfodzs3bxdbk1, token = caf174a6741170d6b0db872f1f34f339
2025-08-04T15:34:09.469+07:00  INFO 69743 --- [qtp1582527589-66] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 7275453a845c475081363f25b1a68466
2025-08-04T15:34:09.473+07:00  INFO 69743 --- [qtp1582527589-61] n.d.m.c.a.CompanyAuthenticationService   : User liam.vnhph is logged in successfully system
2025-08-04T15:34:09.482+07:00  INFO 69743 --- [qtp1582527589-66] n.d.m.c.a.CompanyAuthenticationService   : User edgar.vnhph is logged in successfully system
2025-08-04T15:34:09.572+07:00  INFO 69743 --- [qtp1582527589-99] n.d.module.session.ClientSessionManager  : Add a client session id = node013imfkzwljylcqfodzs3bxdbk1, token = caf174a6741170d6b0db872f1f34f339
2025-08-04T15:34:09.572+07:00  INFO 69743 --- [qtp1582527589-109] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 7275453a845c475081363f25b1a68466
2025-08-04T15:34:09.588+07:00  INFO 69743 --- [qtp1582527589-99] n.d.m.c.a.CompanyAuthenticationService   : User liam.vnhph is logged in successfully system
2025-08-04T15:34:09.591+07:00  INFO 69743 --- [qtp1582527589-109] n.d.m.c.a.CompanyAuthenticationService   : User edgar.vnhph is logged in successfully system
2025-08-04T15:34:52.675+07:00  INFO 69743 --- [qtp1582527589-39] n.d.m.data.db.util.DBConnectionUtil      : Execute SQL: 
 DELETE FROM kpi_item WHERE id IN (:ids)
2025-08-04T15:35:00.748+07:00  INFO 69743 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 16, expire count 0
2025-08-04T15:35:00.757+07:00  INFO 69743 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T15:35:06.768+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T15:35:06.770+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T15:36:00.452+07:00  INFO 69743 --- [qtp1582527589-39] n.d.module.session.ClientSessionManager  : Add a client session id = node013imfkzwljylcqfodzs3bxdbk1, token = caf174a6741170d6b0db872f1f34f339
2025-08-04T15:36:00.457+07:00  INFO 69743 --- [qtp1582527589-37] n.d.module.session.ClientSessionManager  : Add a client session id = node013imfkzwljylcqfodzs3bxdbk1, token = caf174a6741170d6b0db872f1f34f339
2025-08-04T15:36:00.485+07:00  INFO 69743 --- [qtp1582527589-39] n.d.m.c.a.CompanyAuthenticationService   : User liam.vnhph is logged in successfully system
2025-08-04T15:36:00.491+07:00  INFO 69743 --- [qtp1582527589-37] n.d.m.c.a.CompanyAuthenticationService   : User liam.vnhph is logged in successfully system
2025-08-04T15:36:01.499+07:00  INFO 69743 --- [qtp1582527589-37] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 7275453a845c475081363f25b1a68466
2025-08-04T15:36:01.500+07:00  INFO 69743 --- [qtp1582527589-61] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 7275453a845c475081363f25b1a68466
2025-08-04T15:36:01.502+07:00  INFO 69743 --- [qtp1582527589-69] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 7275453a845c475081363f25b1a68466
2025-08-04T15:36:01.504+07:00  INFO 69743 --- [qtp1582527589-66] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 7275453a845c475081363f25b1a68466
2025-08-04T15:36:01.505+07:00  INFO 69743 --- [qtp1582527589-61] n.d.m.c.a.CompanyAuthenticationService   : User edgar.vnhph is logged in successfully system
2025-08-04T15:36:01.508+07:00  INFO 69743 --- [qtp1582527589-37] n.d.m.c.a.CompanyAuthenticationService   : User edgar.vnhph is logged in successfully system
2025-08-04T15:36:01.517+07:00  INFO 69743 --- [qtp1582527589-69] n.d.m.c.a.CompanyAuthenticationService   : User edgar.vnhph is logged in successfully system
2025-08-04T15:36:01.538+07:00  INFO 69743 --- [qtp1582527589-66] n.d.m.c.a.CompanyAuthenticationService   : User edgar.vnhph is logged in successfully system
2025-08-04T15:36:03.870+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T15:36:11.590+07:00  INFO 69743 --- [qtp1582527589-37] n.d.module.session.ClientSessionManager  : Add a client session id = node013imfkzwljylcqfodzs3bxdbk1, token = caf174a6741170d6b0db872f1f34f339
2025-08-04T15:36:11.592+07:00  INFO 69743 --- [qtp1582527589-61] n.d.module.session.ClientSessionManager  : Add a client session id = node013imfkzwljylcqfodzs3bxdbk1, token = caf174a6741170d6b0db872f1f34f339
2025-08-04T15:36:11.603+07:00  INFO 69743 --- [qtp1582527589-37] n.d.m.c.a.CompanyAuthenticationService   : User liam.vnhph is logged in successfully system
2025-08-04T15:36:11.603+07:00  INFO 69743 --- [qtp1582527589-61] n.d.m.c.a.CompanyAuthenticationService   : User liam.vnhph is logged in successfully system
2025-08-04T15:36:12.510+07:00  INFO 69743 --- [qtp1582527589-37] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 7275453a845c475081363f25b1a68466
2025-08-04T15:36:12.529+07:00  INFO 69743 --- [qtp1582527589-71] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 7275453a845c475081363f25b1a68466
2025-08-04T15:36:12.532+07:00  INFO 69743 --- [qtp1582527589-37] n.d.m.c.a.CompanyAuthenticationService   : User edgar.vnhph is logged in successfully system
2025-08-04T15:36:12.577+07:00  INFO 69743 --- [qtp1582527589-71] n.d.m.c.a.CompanyAuthenticationService   : User edgar.vnhph is logged in successfully system
2025-08-04T15:36:13.502+07:00  INFO 69743 --- [qtp1582527589-34] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 7275453a845c475081363f25b1a68466
2025-08-04T15:36:13.503+07:00  INFO 69743 --- [qtp1582527589-61] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 7275453a845c475081363f25b1a68466
2025-08-04T15:36:13.523+07:00  INFO 69743 --- [qtp1582527589-61] n.d.m.c.a.CompanyAuthenticationService   : User edgar.vnhph is logged in successfully system
2025-08-04T15:36:13.523+07:00  INFO 69743 --- [qtp1582527589-34] n.d.m.c.a.CompanyAuthenticationService   : User edgar.vnhph is logged in successfully system
2025-08-04T15:36:33.010+07:00  INFO 69743 --- [qtp1582527589-34] n.d.module.session.ClientSessionManager  : Add a client session id = node013imfkzwljylcqfodzs3bxdbk1, token = caf174a6741170d6b0db872f1f34f339
2025-08-04T15:36:33.011+07:00  INFO 69743 --- [qtp1582527589-69] n.d.module.session.ClientSessionManager  : Add a client session id = node013imfkzwljylcqfodzs3bxdbk1, token = caf174a6741170d6b0db872f1f34f339
2025-08-04T15:36:33.024+07:00  INFO 69743 --- [qtp1582527589-69] n.d.m.c.a.CompanyAuthenticationService   : User liam.vnhph is logged in successfully system
2025-08-04T15:36:33.024+07:00  INFO 69743 --- [qtp1582527589-34] n.d.m.c.a.CompanyAuthenticationService   : User liam.vnhph is logged in successfully system
2025-08-04T15:36:33.465+07:00  INFO 69743 --- [qtp1582527589-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 7275453a845c475081363f25b1a68466
2025-08-04T15:36:33.465+07:00  INFO 69743 --- [qtp1582527589-61] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 7275453a845c475081363f25b1a68466
2025-08-04T15:36:33.470+07:00  INFO 69743 --- [qtp1582527589-35] n.d.m.c.a.CompanyAuthenticationService   : User edgar.vnhph is logged in successfully system
2025-08-04T15:36:33.470+07:00  INFO 69743 --- [qtp1582527589-61] n.d.m.c.a.CompanyAuthenticationService   : User edgar.vnhph is logged in successfully system
2025-08-04T15:36:33.496+07:00  INFO 69743 --- [qtp1582527589-110] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 7275453a845c475081363f25b1a68466
2025-08-04T15:36:33.499+07:00  INFO 69743 --- [qtp1582527589-78] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 7275453a845c475081363f25b1a68466
2025-08-04T15:36:33.509+07:00  INFO 69743 --- [qtp1582527589-110] n.d.m.c.a.CompanyAuthenticationService   : User edgar.vnhph is logged in successfully system
2025-08-04T15:36:33.509+07:00  INFO 69743 --- [qtp1582527589-78] n.d.m.c.a.CompanyAuthenticationService   : User edgar.vnhph is logged in successfully system
2025-08-04T15:37:00.050+07:00  INFO 69743 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 8, expire count 0
2025-08-04T15:37:00.055+07:00  INFO 69743 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T15:37:05.021+07:00  INFO 69743 --- [qtp1582527589-110] n.d.module.session.ClientSessionManager  : Add a client session id = node013imfkzwljylcqfodzs3bxdbk1, token = caf174a6741170d6b0db872f1f34f339
2025-08-04T15:37:05.022+07:00  INFO 69743 --- [qtp1582527589-61] n.d.module.session.ClientSessionManager  : Add a client session id = node013imfkzwljylcqfodzs3bxdbk1, token = caf174a6741170d6b0db872f1f34f339
2025-08-04T15:37:05.037+07:00  INFO 69743 --- [qtp1582527589-61] n.d.m.c.a.CompanyAuthenticationService   : User liam.vnhph is logged in successfully system
2025-08-04T15:37:05.037+07:00  INFO 69743 --- [qtp1582527589-110] n.d.m.c.a.CompanyAuthenticationService   : User liam.vnhph is logged in successfully system
2025-08-04T15:37:05.470+07:00  INFO 69743 --- [qtp1582527589-61] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 7275453a845c475081363f25b1a68466
2025-08-04T15:37:05.474+07:00  INFO 69743 --- [qtp1582527589-39] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 7275453a845c475081363f25b1a68466
2025-08-04T15:37:05.474+07:00  INFO 69743 --- [qtp1582527589-110] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 7275453a845c475081363f25b1a68466
2025-08-04T15:37:05.478+07:00  INFO 69743 --- [qtp1582527589-61] n.d.m.c.a.CompanyAuthenticationService   : User edgar.vnhph is logged in successfully system
2025-08-04T15:37:05.490+07:00  INFO 69743 --- [qtp1582527589-110] n.d.m.c.a.CompanyAuthenticationService   : User edgar.vnhph is logged in successfully system
2025-08-04T15:37:05.490+07:00  INFO 69743 --- [qtp1582527589-39] n.d.m.c.a.CompanyAuthenticationService   : User edgar.vnhph is logged in successfully system
2025-08-04T15:37:05.525+07:00  INFO 69743 --- [qtp1582527589-41] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 7275453a845c475081363f25b1a68466
2025-08-04T15:37:05.535+07:00  INFO 69743 --- [qtp1582527589-41] n.d.m.c.a.CompanyAuthenticationService   : User edgar.vnhph is logged in successfully system
2025-08-04T15:37:06.066+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T15:37:48.120+07:00  INFO 69743 --- [qtp1582527589-41] n.d.module.session.ClientSessionManager  : Add a client session id = node013imfkzwljylcqfodzs3bxdbk1, token = caf174a6741170d6b0db872f1f34f339
2025-08-04T15:37:48.123+07:00  INFO 69743 --- [qtp1582527589-61] n.d.module.session.ClientSessionManager  : Add a client session id = node013imfkzwljylcqfodzs3bxdbk1, token = caf174a6741170d6b0db872f1f34f339
2025-08-04T15:37:48.138+07:00  INFO 69743 --- [qtp1582527589-41] n.d.m.c.a.CompanyAuthenticationService   : User liam.vnhph is logged in successfully system
2025-08-04T15:37:48.138+07:00  INFO 69743 --- [qtp1582527589-61] n.d.m.c.a.CompanyAuthenticationService   : User liam.vnhph is logged in successfully system
2025-08-04T15:37:59.775+07:00  INFO 69743 --- [qtp1582527589-73] n.d.m.c.a.CompanyAuthenticationService   : User edgar.vnhph logout successfully 
2025-08-04T15:38:03.091+07:00  INFO 69743 --- [qtp1582527589-41] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 40625ad3bcacc9c205f28b541233d381
2025-08-04T15:38:03.102+07:00  INFO 69743 --- [qtp1582527589-41] n.d.m.c.a.CompanyAuthenticationService   : User max.vnhph is logged in successfully system
2025-08-04T15:38:03.170+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T15:38:08.463+07:00  INFO 69743 --- [qtp1582527589-69] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-08-04T15:38:59.300+07:00  INFO 69743 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 16, expire count 0
2025-08-04T15:38:59.309+07:00  INFO 69743 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T15:39:06.321+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T15:39:10.490+07:00  INFO 69743 --- [qtp1582527589-71] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 40625ad3bcacc9c205f28b541233d381
2025-08-04T15:39:10.491+07:00  INFO 69743 --- [qtp1582527589-78] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 40625ad3bcacc9c205f28b541233d381
2025-08-04T15:39:10.499+07:00  INFO 69743 --- [qtp1582527589-78] n.d.m.c.a.CompanyAuthenticationService   : User max.vnhph is logged in successfully system
2025-08-04T15:39:10.499+07:00  INFO 69743 --- [qtp1582527589-71] n.d.m.c.a.CompanyAuthenticationService   : User max.vnhph is logged in successfully system
2025-08-04T15:40:02.409+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T15:40:02.412+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T15:40:37.020+07:00  INFO 69743 --- [qtp1582527589-109] n.d.module.session.ClientSessionManager  : Add a client session id = node013imfkzwljylcqfodzs3bxdbk1, token = caf174a6741170d6b0db872f1f34f339
2025-08-04T15:40:37.022+07:00  INFO 69743 --- [qtp1582527589-34] n.d.module.session.ClientSessionManager  : Add a client session id = node013imfkzwljylcqfodzs3bxdbk1, token = caf174a6741170d6b0db872f1f34f339
2025-08-04T15:40:37.038+07:00  INFO 69743 --- [qtp1582527589-34] n.d.m.c.a.CompanyAuthenticationService   : User liam.vnhph is logged in successfully system
2025-08-04T15:40:37.038+07:00  INFO 69743 --- [qtp1582527589-109] n.d.m.c.a.CompanyAuthenticationService   : User liam.vnhph is logged in successfully system
2025-08-04T15:40:58.572+07:00  INFO 69743 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 14, expire count 5
2025-08-04T15:40:58.608+07:00  INFO 69743 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T15:41:05.623+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T15:42:06.715+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T15:42:23.528+07:00  INFO 69743 --- [qtp1582527589-34] n.d.m.c.a.CompanyAuthenticationService   : User max.vnhph logout successfully 
2025-08-04T15:42:26.318+07:00  INFO 69743 --- [qtp1582527589-77] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:42:26.328+07:00  INFO 69743 --- [qtp1582527589-77] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:43:02.860+07:00  INFO 69743 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 24, expire count 8
2025-08-04T15:43:02.874+07:00  INFO 69743 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T15:43:04.880+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T15:43:13.481+07:00  INFO 69743 --- [qtp1582527589-77] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-08-04T15:43:33.129+07:00  INFO 69743 --- [qtp1582527589-41] n.d.module.session.ClientSessionManager  : Add a client session id = node013imfkzwljylcqfodzs3bxdbk1, token = caf174a6741170d6b0db872f1f34f339
2025-08-04T15:43:33.131+07:00  INFO 69743 --- [qtp1582527589-71] n.d.module.session.ClientSessionManager  : Add a client session id = node013imfkzwljylcqfodzs3bxdbk1, token = caf174a6741170d6b0db872f1f34f339
2025-08-04T15:43:33.145+07:00  INFO 69743 --- [qtp1582527589-41] n.d.m.c.a.CompanyAuthenticationService   : User liam.vnhph is logged in successfully system
2025-08-04T15:43:33.145+07:00  INFO 69743 --- [qtp1582527589-71] n.d.m.c.a.CompanyAuthenticationService   : User liam.vnhph is logged in successfully system
2025-08-04T15:43:33.604+07:00  INFO 69743 --- [qtp1582527589-34] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:43:33.654+07:00  INFO 69743 --- [qtp1582527589-117] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:43:33.657+07:00  INFO 69743 --- [qtp1582527589-34] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:43:33.662+07:00  INFO 69743 --- [qtp1582527589-117] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:43:34.474+07:00  INFO 69743 --- [qtp1582527589-34] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:43:34.474+07:00  INFO 69743 --- [qtp1582527589-77] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:43:34.485+07:00  INFO 69743 --- [qtp1582527589-34] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:43:34.484+07:00  INFO 69743 --- [qtp1582527589-77] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:43:42.523+07:00  INFO 69743 --- [qtp1582527589-41] n.d.module.session.ClientSessionManager  : Add a client session id = node013imfkzwljylcqfodzs3bxdbk1, token = caf174a6741170d6b0db872f1f34f339
2025-08-04T15:43:42.527+07:00  INFO 69743 --- [qtp1582527589-39] n.d.module.session.ClientSessionManager  : Add a client session id = node013imfkzwljylcqfodzs3bxdbk1, token = caf174a6741170d6b0db872f1f34f339
2025-08-04T15:43:42.553+07:00  INFO 69743 --- [qtp1582527589-39] n.d.m.c.a.CompanyAuthenticationService   : User liam.vnhph is logged in successfully system
2025-08-04T15:43:42.553+07:00  INFO 69743 --- [qtp1582527589-41] n.d.m.c.a.CompanyAuthenticationService   : User liam.vnhph is logged in successfully system
2025-08-04T15:43:43.499+07:00  INFO 69743 --- [qtp1582527589-109] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:43:43.500+07:00  INFO 69743 --- [qtp1582527589-117] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:43:43.504+07:00  INFO 69743 --- [qtp1582527589-66] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:43:43.510+07:00  INFO 69743 --- [qtp1582527589-109] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:43:43.511+07:00  INFO 69743 --- [qtp1582527589-117] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:43:43.515+07:00  INFO 69743 --- [qtp1582527589-66] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:43:43.580+07:00  INFO 69743 --- [qtp1582527589-73] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:43:43.799+07:00  INFO 69743 --- [qtp1582527589-73] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:43:50.610+07:00  INFO 69743 --- [qtp1582527589-41] n.d.module.session.ClientSessionManager  : Add a client session id = node013imfkzwljylcqfodzs3bxdbk1, token = caf174a6741170d6b0db872f1f34f339
2025-08-04T15:43:50.613+07:00  INFO 69743 --- [qtp1582527589-117] n.d.module.session.ClientSessionManager  : Add a client session id = node013imfkzwljylcqfodzs3bxdbk1, token = caf174a6741170d6b0db872f1f34f339
2025-08-04T15:43:50.633+07:00  INFO 69743 --- [qtp1582527589-41] n.d.m.c.a.CompanyAuthenticationService   : User liam.vnhph is logged in successfully system
2025-08-04T15:43:50.638+07:00  INFO 69743 --- [qtp1582527589-117] n.d.m.c.a.CompanyAuthenticationService   : User liam.vnhph is logged in successfully system
2025-08-04T15:43:51.485+07:00  INFO 69743 --- [qtp1582527589-66] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:43:51.485+07:00  INFO 69743 --- [qtp1582527589-109] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:43:51.486+07:00  INFO 69743 --- [qtp1582527589-77] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:43:51.487+07:00  INFO 69743 --- [qtp1582527589-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:43:51.493+07:00  INFO 69743 --- [qtp1582527589-109] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:43:51.495+07:00  INFO 69743 --- [qtp1582527589-66] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:43:51.496+07:00  INFO 69743 --- [qtp1582527589-77] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:43:51.503+07:00  INFO 69743 --- [qtp1582527589-35] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:44:00.479+07:00  INFO 69743 --- [qtp1582527589-73] n.d.module.session.ClientSessionManager  : Add a client session id = node013imfkzwljylcqfodzs3bxdbk1, token = caf174a6741170d6b0db872f1f34f339
2025-08-04T15:44:00.485+07:00  INFO 69743 --- [qtp1582527589-34] n.d.module.session.ClientSessionManager  : Add a client session id = node013imfkzwljylcqfodzs3bxdbk1, token = caf174a6741170d6b0db872f1f34f339
2025-08-04T15:44:00.534+07:00  INFO 69743 --- [qtp1582527589-34] n.d.m.c.a.CompanyAuthenticationService   : User liam.vnhph is logged in successfully system
2025-08-04T15:44:00.534+07:00  INFO 69743 --- [qtp1582527589-73] n.d.m.c.a.CompanyAuthenticationService   : User liam.vnhph is logged in successfully system
2025-08-04T15:44:01.486+07:00  INFO 69743 --- [qtp1582527589-99] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:44:01.490+07:00  INFO 69743 --- [qtp1582527589-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:44:01.498+07:00  INFO 69743 --- [qtp1582527589-35] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:44:01.499+07:00  INFO 69743 --- [qtp1582527589-99] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:44:02.469+07:00  INFO 69743 --- [qtp1582527589-77] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:44:02.469+07:00  INFO 69743 --- [qtp1582527589-117] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:44:02.475+07:00  INFO 69743 --- [qtp1582527589-117] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:44:02.476+07:00  INFO 69743 --- [qtp1582527589-77] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:44:07.013+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T15:44:36.392+07:00  INFO 69743 --- [qtp1582527589-78] n.d.module.session.ClientSessionManager  : Add a client session id = node013imfkzwljylcqfodzs3bxdbk1, token = caf174a6741170d6b0db872f1f34f339
2025-08-04T15:44:36.393+07:00  INFO 69743 --- [qtp1582527589-117] n.d.module.session.ClientSessionManager  : Add a client session id = node013imfkzwljylcqfodzs3bxdbk1, token = caf174a6741170d6b0db872f1f34f339
2025-08-04T15:44:36.414+07:00  INFO 69743 --- [qtp1582527589-117] n.d.m.c.a.CompanyAuthenticationService   : User liam.vnhph is logged in successfully system
2025-08-04T15:44:36.416+07:00  INFO 69743 --- [qtp1582527589-78] n.d.m.c.a.CompanyAuthenticationService   : User liam.vnhph is logged in successfully system
2025-08-04T15:44:37.478+07:00  INFO 69743 --- [qtp1582527589-66] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:44:37.479+07:00  INFO 69743 --- [qtp1582527589-41] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:44:37.484+07:00  INFO 69743 --- [qtp1582527589-117] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:44:37.498+07:00  INFO 69743 --- [qtp1582527589-77] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:44:37.502+07:00  INFO 69743 --- [qtp1582527589-66] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:44:37.503+07:00  INFO 69743 --- [qtp1582527589-41] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:44:37.503+07:00  INFO 69743 --- [qtp1582527589-117] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:44:37.519+07:00  INFO 69743 --- [qtp1582527589-77] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:45:03.156+07:00  INFO 69743 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 12, expire count 5
2025-08-04T15:45:03.168+07:00  INFO 69743 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T15:45:04.174+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T15:45:04.174+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T15:45:04.175+07:00  INFO 69743 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 04/08/2025@15:45:04+0700
2025-08-04T15:45:04.211+07:00  INFO 69743 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 04/08/2025@15:45:00+0700 to 04/08/2025@16:00:00+0700
2025-08-04T15:45:04.212+07:00  INFO 69743 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 04/08/2025@15:45:00+0700 to 04/08/2025@16:00:00+0700
2025-08-04T15:45:04.212+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-04T15:45:08.450+07:00  INFO 69743 --- [qtp1582527589-73] n.d.module.session.ClientSessionManager  : Add a client session id = node013imfkzwljylcqfodzs3bxdbk1, token = caf174a6741170d6b0db872f1f34f339
2025-08-04T15:45:08.452+07:00  INFO 69743 --- [qtp1582527589-77] n.d.module.session.ClientSessionManager  : Add a client session id = node013imfkzwljylcqfodzs3bxdbk1, token = caf174a6741170d6b0db872f1f34f339
2025-08-04T15:45:08.460+07:00  INFO 69743 --- [qtp1582527589-77] n.d.m.c.a.CompanyAuthenticationService   : User liam.vnhph is logged in successfully system
2025-08-04T15:45:08.460+07:00  INFO 69743 --- [qtp1582527589-73] n.d.m.c.a.CompanyAuthenticationService   : User liam.vnhph is logged in successfully system
2025-08-04T15:45:09.479+07:00  INFO 69743 --- [qtp1582527589-77] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:45:09.485+07:00  INFO 69743 --- [qtp1582527589-41] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:45:09.499+07:00  INFO 69743 --- [qtp1582527589-99] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:45:09.500+07:00  INFO 69743 --- [qtp1582527589-117] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:45:09.502+07:00  INFO 69743 --- [qtp1582527589-77] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:45:09.502+07:00  INFO 69743 --- [qtp1582527589-41] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:45:09.504+07:00  INFO 69743 --- [qtp1582527589-99] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:45:09.506+07:00  INFO 69743 --- [qtp1582527589-117] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:45:49.639+07:00  INFO 69743 --- [qtp1582527589-73] n.d.module.session.ClientSessionManager  : Add a client session id = node013imfkzwljylcqfodzs3bxdbk1, token = caf174a6741170d6b0db872f1f34f339
2025-08-04T15:45:49.642+07:00  INFO 69743 --- [qtp1582527589-35] n.d.module.session.ClientSessionManager  : Add a client session id = node013imfkzwljylcqfodzs3bxdbk1, token = caf174a6741170d6b0db872f1f34f339
2025-08-04T15:45:49.655+07:00  INFO 69743 --- [qtp1582527589-35] n.d.m.c.a.CompanyAuthenticationService   : User liam.vnhph is logged in successfully system
2025-08-04T15:45:49.655+07:00  INFO 69743 --- [qtp1582527589-73] n.d.m.c.a.CompanyAuthenticationService   : User liam.vnhph is logged in successfully system
2025-08-04T15:45:50.494+07:00  INFO 69743 --- [qtp1582527589-66] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:45:50.495+07:00  INFO 69743 --- [qtp1582527589-109] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:45:50.499+07:00  INFO 69743 --- [qtp1582527589-77] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:45:50.504+07:00  INFO 69743 --- [qtp1582527589-34] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:45:50.506+07:00  INFO 69743 --- [qtp1582527589-109] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:45:50.507+07:00  INFO 69743 --- [qtp1582527589-66] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:45:50.508+07:00  INFO 69743 --- [qtp1582527589-77] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:45:50.550+07:00  INFO 69743 --- [qtp1582527589-34] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:45:58.808+07:00  INFO 69743 --- [qtp1582527589-109] n.d.m.data.db.util.DBConnectionUtil      : Execute SQL: 
 DELETE FROM kpi_item WHERE id IN (:ids)
2025-08-04T15:46:06.324+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T15:46:19.488+07:00  INFO 69743 --- [qtp1582527589-71] n.d.module.session.ClientSessionManager  : Add a client session id = node013imfkzwljylcqfodzs3bxdbk1, token = caf174a6741170d6b0db872f1f34f339
2025-08-04T15:46:19.490+07:00  INFO 69743 --- [qtp1582527589-109] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:46:19.490+07:00  INFO 69743 --- [qtp1582527589-41] n.d.module.session.ClientSessionManager  : Add a client session id = node013imfkzwljylcqfodzs3bxdbk1, token = caf174a6741170d6b0db872f1f34f339
2025-08-04T15:46:19.490+07:00  INFO 69743 --- [qtp1582527589-77] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:46:19.498+07:00  INFO 69743 --- [qtp1582527589-41] n.d.m.c.a.CompanyAuthenticationService   : User liam.vnhph is logged in successfully system
2025-08-04T15:46:19.498+07:00  INFO 69743 --- [qtp1582527589-109] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:46:19.498+07:00  INFO 69743 --- [qtp1582527589-71] n.d.m.c.a.CompanyAuthenticationService   : User liam.vnhph is logged in successfully system
2025-08-04T15:46:19.498+07:00  INFO 69743 --- [qtp1582527589-77] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:46:19.668+07:00  INFO 69743 --- [qtp1582527589-117] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:46:19.678+07:00  INFO 69743 --- [qtp1582527589-117] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:46:19.685+07:00  INFO 69743 --- [qtp1582527589-77] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:46:19.704+07:00  INFO 69743 --- [qtp1582527589-77] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:46:51.290+07:00  INFO 69743 --- [qtp1582527589-99] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:46:51.292+07:00  INFO 69743 --- [qtp1582527589-109] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:46:51.298+07:00  INFO 69743 --- [qtp1582527589-109] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:46:51.298+07:00  INFO 69743 --- [qtp1582527589-99] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:46:51.688+07:00  INFO 69743 --- [qtp1582527589-99] n.d.module.session.ClientSessionManager  : Add a client session id = node013imfkzwljylcqfodzs3bxdbk1, token = caf174a6741170d6b0db872f1f34f339
2025-08-04T15:46:51.695+07:00  INFO 69743 --- [qtp1582527589-77] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:46:51.699+07:00  INFO 69743 --- [qtp1582527589-39] n.d.module.session.ClientSessionManager  : Add a client session id = node013imfkzwljylcqfodzs3bxdbk1, token = caf174a6741170d6b0db872f1f34f339
2025-08-04T15:46:51.699+07:00  INFO 69743 --- [qtp1582527589-73] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:46:51.701+07:00  INFO 69743 --- [qtp1582527589-99] n.d.m.c.a.CompanyAuthenticationService   : User liam.vnhph is logged in successfully system
2025-08-04T15:46:51.704+07:00  INFO 69743 --- [qtp1582527589-77] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:46:51.711+07:00  INFO 69743 --- [qtp1582527589-73] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:46:51.731+07:00  INFO 69743 --- [qtp1582527589-39] n.d.m.c.a.CompanyAuthenticationService   : User liam.vnhph is logged in successfully system
2025-08-04T15:47:02.459+07:00  INFO 69743 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 2
2025-08-04T15:47:02.521+07:00  INFO 69743 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T15:47:03.527+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T15:47:19.514+07:00  INFO 69743 --- [qtp1582527589-117] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:47:19.524+07:00  INFO 69743 --- [qtp1582527589-109] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:47:19.536+07:00  INFO 69743 --- [qtp1582527589-117] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:47:19.536+07:00  INFO 69743 --- [qtp1582527589-109] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:47:20.471+07:00  INFO 69743 --- [qtp1582527589-73] n.d.module.session.ClientSessionManager  : Add a client session id = node013imfkzwljylcqfodzs3bxdbk1, token = caf174a6741170d6b0db872f1f34f339
2025-08-04T15:47:20.471+07:00  INFO 69743 --- [qtp1582527589-99] n.d.module.session.ClientSessionManager  : Add a client session id = node013imfkzwljylcqfodzs3bxdbk1, token = caf174a6741170d6b0db872f1f34f339
2025-08-04T15:47:20.476+07:00  INFO 69743 --- [qtp1582527589-99] n.d.m.c.a.CompanyAuthenticationService   : User liam.vnhph is logged in successfully system
2025-08-04T15:47:20.476+07:00  INFO 69743 --- [qtp1582527589-73] n.d.m.c.a.CompanyAuthenticationService   : User liam.vnhph is logged in successfully system
2025-08-04T15:47:21.383+07:00  INFO 69743 --- [qtp1582527589-99] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:47:21.394+07:00  INFO 69743 --- [qtp1582527589-99] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:47:21.559+07:00  INFO 69743 --- [qtp1582527589-142] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:47:21.633+07:00  INFO 69743 --- [qtp1582527589-142] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:48:01.582+07:00  INFO 69743 --- [qtp1582527589-110] n.d.module.session.ClientSessionManager  : Add a client session id = node013imfkzwljylcqfodzs3bxdbk1, token = caf174a6741170d6b0db872f1f34f339
2025-08-04T15:48:01.584+07:00  INFO 69743 --- [qtp1582527589-73] n.d.module.session.ClientSessionManager  : Add a client session id = node013imfkzwljylcqfodzs3bxdbk1, token = caf174a6741170d6b0db872f1f34f339
2025-08-04T15:48:01.735+07:00  INFO 69743 --- [qtp1582527589-77] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:48:01.735+07:00  INFO 69743 --- [qtp1582527589-78] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:48:01.823+07:00  INFO 69743 --- [qtp1582527589-78] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:48:01.823+07:00  INFO 69743 --- [qtp1582527589-110] n.d.m.c.a.CompanyAuthenticationService   : User liam.vnhph is logged in successfully system
2025-08-04T15:48:01.823+07:00  INFO 69743 --- [qtp1582527589-77] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:48:02.066+07:00  INFO 69743 --- [qtp1582527589-73] n.d.m.c.a.CompanyAuthenticationService   : User liam.vnhph is logged in successfully system
2025-08-04T15:48:02.505+07:00  INFO 69743 --- [qtp1582527589-41] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:48:02.505+07:00  INFO 69743 --- [qtp1582527589-78] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:48:02.516+07:00  INFO 69743 --- [qtp1582527589-78] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:48:02.516+07:00  INFO 69743 --- [qtp1582527589-41] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:48:06.691+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T15:48:23.222+07:00  INFO 69743 --- [qtp1582527589-117] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:48:23.222+07:00  INFO 69743 --- [qtp1582527589-110] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:48:23.228+07:00  INFO 69743 --- [qtp1582527589-110] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:48:23.230+07:00  INFO 69743 --- [qtp1582527589-117] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:48:23.503+07:00  INFO 69743 --- [qtp1582527589-110] n.d.module.session.ClientSessionManager  : Add a client session id = node013imfkzwljylcqfodzs3bxdbk1, token = caf174a6741170d6b0db872f1f34f339
2025-08-04T15:48:23.504+07:00  INFO 69743 --- [qtp1582527589-109] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:48:23.511+07:00  INFO 69743 --- [qtp1582527589-99] n.d.module.session.ClientSessionManager  : Add a client session id = node013imfkzwljylcqfodzs3bxdbk1, token = caf174a6741170d6b0db872f1f34f339
2025-08-04T15:48:23.515+07:00  INFO 69743 --- [qtp1582527589-110] n.d.m.c.a.CompanyAuthenticationService   : User liam.vnhph is logged in successfully system
2025-08-04T15:48:23.526+07:00  INFO 69743 --- [qtp1582527589-109] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:48:23.537+07:00  INFO 69743 --- [qtp1582527589-99] n.d.m.c.a.CompanyAuthenticationService   : User liam.vnhph is logged in successfully system
2025-08-04T15:48:23.602+07:00  INFO 69743 --- [qtp1582527589-112] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:48:23.626+07:00  INFO 69743 --- [qtp1582527589-112] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:48:40.182+07:00  INFO 69743 --- [qtp1582527589-78] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:48:40.183+07:00  INFO 69743 --- [qtp1582527589-34] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:48:40.194+07:00  INFO 69743 --- [qtp1582527589-34] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:48:40.194+07:00  INFO 69743 --- [qtp1582527589-78] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:48:40.534+07:00  INFO 69743 --- [qtp1582527589-34] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:48:40.536+07:00  INFO 69743 --- [qtp1582527589-117] n.d.module.session.ClientSessionManager  : Add a client session id = node013imfkzwljylcqfodzs3bxdbk1, token = caf174a6741170d6b0db872f1f34f339
2025-08-04T15:48:40.541+07:00  INFO 69743 --- [qtp1582527589-77] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:48:40.546+07:00  INFO 69743 --- [qtp1582527589-109] n.d.module.session.ClientSessionManager  : Add a client session id = node013imfkzwljylcqfodzs3bxdbk1, token = caf174a6741170d6b0db872f1f34f339
2025-08-04T15:48:40.548+07:00  INFO 69743 --- [qtp1582527589-34] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:48:40.549+07:00  INFO 69743 --- [qtp1582527589-117] n.d.m.c.a.CompanyAuthenticationService   : User liam.vnhph is logged in successfully system
2025-08-04T15:48:40.558+07:00  INFO 69743 --- [qtp1582527589-77] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:48:40.559+07:00  INFO 69743 --- [qtp1582527589-109] n.d.m.c.a.CompanyAuthenticationService   : User liam.vnhph is logged in successfully system
2025-08-04T15:49:02.933+07:00  INFO 69743 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 10, expire count 14
2025-08-04T15:49:02.944+07:00  INFO 69743 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T15:49:02.946+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T15:49:02.993+07:00  INFO 69743 --- [qtp1582527589-34] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:49:02.993+07:00  INFO 69743 --- [qtp1582527589-112] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:49:03.002+07:00  INFO 69743 --- [qtp1582527589-112] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:49:03.002+07:00  INFO 69743 --- [qtp1582527589-34] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:49:22.371+07:00  INFO 69743 --- [qtp1582527589-117] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:49:22.373+07:00  INFO 69743 --- [qtp1582527589-77] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:49:22.381+07:00  INFO 69743 --- [qtp1582527589-117] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:49:22.382+07:00  INFO 69743 --- [qtp1582527589-77] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:49:35.936+07:00  INFO 69743 --- [qtp1582527589-78] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:49:35.937+07:00  INFO 69743 --- [qtp1582527589-77] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:49:35.944+07:00  INFO 69743 --- [qtp1582527589-77] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:49:35.944+07:00  INFO 69743 --- [qtp1582527589-78] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:50:02.615+07:00  INFO 69743 --- [qtp1582527589-78] n.d.m.data.db.util.DBConnectionUtil      : Execute SQL: 
 DELETE FROM kpi_item WHERE id IN (:ids)
2025-08-04T15:50:06.044+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T15:50:06.044+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T15:50:23.674+07:00  INFO 69743 --- [qtp1582527589-109] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:50:23.675+07:00  INFO 69743 --- [qtp1582527589-78] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:50:23.729+07:00  INFO 69743 --- [qtp1582527589-78] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:50:23.729+07:00  INFO 69743 --- [qtp1582527589-109] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:50:46.729+07:00  INFO 69743 --- [qtp1582527589-77] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:50:46.733+07:00  INFO 69743 --- [qtp1582527589-110] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:50:46.740+07:00  INFO 69743 --- [qtp1582527589-110] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:50:46.740+07:00  INFO 69743 --- [qtp1582527589-77] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:51:02.139+07:00  INFO 69743 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 12
2025-08-04T15:51:02.149+07:00  INFO 69743 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T15:51:02.150+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T15:52:02.240+07:00  INFO 69743 --- [qtp1582527589-73] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:52:02.242+07:00  INFO 69743 --- [qtp1582527589-78] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:52:02.248+07:00  INFO 69743 --- [qtp1582527589-73] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:52:02.252+07:00  INFO 69743 --- [qtp1582527589-78] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:52:05.233+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T15:53:01.350+07:00  INFO 69743 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 6, expire count 11
2025-08-04T15:53:01.392+07:00  INFO 69743 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T15:53:02.229+07:00  INFO 69743 --- [qtp1582527589-110] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:53:02.242+07:00  INFO 69743 --- [qtp1582527589-110] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:53:02.257+07:00  INFO 69743 --- [qtp1582527589-121] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:53:02.265+07:00  INFO 69743 --- [qtp1582527589-121] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:53:06.397+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T15:53:07.144+07:00  INFO 69743 --- [qtp1582527589-73] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:53:07.145+07:00  INFO 69743 --- [qtp1582527589-110] n.d.module.session.ClientSessionManager  : Add a client session id = node0199naent08mr3s0oud2m7nkyi2, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T15:53:07.164+07:00  INFO 69743 --- [qtp1582527589-110] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:53:07.164+07:00  INFO 69743 --- [qtp1582527589-73] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T15:53:33.099+07:00  INFO 69743 --- [qtp1582527589-78] n.d.m.data.db.util.DBConnectionUtil      : Execute SQL: 
 DELETE FROM kpi_template_item WHERE id IN (:ids)
2025-08-04T15:53:47.788+07:00  INFO 69743 --- [qtp1582527589-78] n.d.m.data.db.util.DBConnectionUtil      : Execute SQL: 
 DELETE FROM kpi_template_item WHERE id IN (:ids)
2025-08-04T15:54:04.511+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T15:55:00.613+07:00  INFO 69743 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 2
2025-08-04T15:55:00.622+07:00  INFO 69743 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T15:55:06.634+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T15:55:06.635+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T15:56:03.722+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T15:56:59.843+07:00  INFO 69743 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 1
2025-08-04T15:56:59.848+07:00  INFO 69743 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T15:57:06.866+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T15:58:02.934+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T15:58:59.028+07:00  INFO 69743 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 4
2025-08-04T15:58:59.031+07:00  INFO 69743 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T15:59:06.043+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T16:00:02.137+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T16:00:02.139+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T16:00:02.139+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-04T16:00:02.139+07:00  INFO 69743 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 04/08/2025@16:00:02+0700
2025-08-04T16:00:02.182+07:00  INFO 69743 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 04/08/2025@16:00:00+0700 to 04/08/2025@16:15:00+0700
2025-08-04T16:00:02.182+07:00  INFO 69743 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 04/08/2025@16:00:00+0700 to 04/08/2025@16:15:00+0700
2025-08-04T16:00:02.186+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-08-04T16:01:03.289+07:00  INFO 69743 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 9, expire count 2
2025-08-04T16:01:03.300+07:00  INFO 69743 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T16:01:05.310+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T16:02:06.407+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T16:03:02.521+07:00  INFO 69743 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 0
2025-08-04T16:03:02.524+07:00  INFO 69743 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T16:03:04.539+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T16:04:06.633+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T16:05:02.758+07:00  INFO 69743 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 5, expire count 4
2025-08-04T16:05:02.783+07:00  INFO 69743 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T16:05:03.789+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T16:05:03.791+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T16:06:06.896+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T16:07:03.007+07:00  INFO 69743 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-08-04T16:07:03.016+07:00  INFO 69743 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T16:07:03.019+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T16:08:06.127+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T16:09:02.219+07:00  INFO 69743 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T16:09:02.226+07:00  INFO 69743 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T16:09:02.226+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T16:10:05.328+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T16:10:05.331+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T16:11:01.457+07:00  INFO 69743 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 11
2025-08-04T16:11:01.477+07:00  INFO 69743 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T16:11:06.485+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T16:12:04.586+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T16:13:00.685+07:00  INFO 69743 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 4
2025-08-04T16:13:00.697+07:00  INFO 69743 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T16:13:06.705+07:00  INFO 69743 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T16:13:44.675+07:00  INFO 69743 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@33c179e9{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-08-04T16:13:44.677+07:00  INFO 69743 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-08-04T16:13:44.677+07:00  INFO 69743 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-08-04T16:13:44.677+07:00  INFO 69743 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-08-04T16:13:44.677+07:00  INFO 69743 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-08-04T16:13:44.678+07:00  INFO 69743 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-08-04T16:13:44.678+07:00  INFO 69743 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-08-04T16:13:44.678+07:00  INFO 69743 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-08-04T16:13:44.678+07:00  INFO 69743 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-08-04T16:13:44.678+07:00  INFO 69743 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-08-04T16:13:44.678+07:00  INFO 69743 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-08-04T16:13:44.678+07:00  INFO 69743 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-08-04T16:13:44.678+07:00  INFO 69743 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-08-04T16:13:44.678+07:00  INFO 69743 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-08-04T16:13:44.678+07:00  INFO 69743 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-08-04T16:13:44.678+07:00  INFO 69743 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-08-04T16:13:44.694+07:00  INFO 69743 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T16:13:44.762+07:00  INFO 69743 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-08-04T16:13:44.766+07:00  INFO 69743 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-08-04T16:13:44.793+07:00  INFO 69743 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-04T16:13:44.796+07:00  INFO 69743 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-04T16:13:44.802+07:00  INFO 69743 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-04T16:13:44.804+07:00  INFO 69743 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-08-04T16:13:44.808+07:00  INFO 69743 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-08-04T16:13:44.808+07:00  INFO 69743 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-08-04T16:13:44.809+07:00  INFO 69743 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-08-04T16:13:44.809+07:00  INFO 69743 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-08-04T16:13:44.966+07:00  INFO 69743 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-08-04T16:13:44.966+07:00  INFO 69743 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-08-04T16:13:44.967+07:00  INFO 69743 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-08-04T16:13:44.967+07:00  INFO 69743 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-08-04T16:13:44.967+07:00  INFO 69743 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-08-04T16:13:44.971+07:00  INFO 69743 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@570cef1e{STOPPING}[12.0.15,sto=0]
2025-08-04T16:13:44.978+07:00  INFO 69743 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-04T16:13:44.981+07:00  INFO 69743 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@5068a2b1{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.13385377560270647409/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@3938e876{STOPPED}}
