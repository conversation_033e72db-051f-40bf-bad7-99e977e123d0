2025-08-04T16:35:36.771+07:00  INFO 80364 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 80364 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-08-04T16:35:36.772+07:00  INFO 80364 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-08-04T16:35:37.496+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:35:37.559+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 60 ms. Found 22 JPA repository interfaces.
2025-08-04T16:35:37.569+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:35:37.570+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-08-04T16:35:37.571+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:35:37.577+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 9 JPA repository interfaces.
2025-08-04T16:35:37.578+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:35:37.614+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 35 ms. Found 3 JPA repository interfaces.
2025-08-04T16:35:37.614+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:35:37.618+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-08-04T16:35:37.630+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:35:37.635+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 1 JPA repository interface.
2025-08-04T16:35:37.644+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:35:37.648+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 5 JPA repository interfaces.
2025-08-04T16:35:37.651+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:35:37.654+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-08-04T16:35:37.654+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:35:37.654+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-08-04T16:35:37.658+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:35:37.665+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 10 JPA repository interfaces.
2025-08-04T16:35:37.670+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:35:37.672+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-08-04T16:35:37.673+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:35:37.676+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-08-04T16:35:37.678+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:35:37.685+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-08-04T16:35:37.686+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:35:37.689+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 4 JPA repository interfaces.
2025-08-04T16:35:37.689+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:35:37.689+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-08-04T16:35:37.690+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:35:37.691+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-08-04T16:35:37.691+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:35:37.696+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 7 JPA repository interfaces.
2025-08-04T16:35:37.696+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:35:37.698+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-08-04T16:35:37.698+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:35:37.699+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-08-04T16:35:37.699+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:35:37.709+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 19 JPA repository interfaces.
2025-08-04T16:35:37.718+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:35:37.724+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 8 JPA repository interfaces.
2025-08-04T16:35:37.724+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:35:37.727+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 5 JPA repository interfaces.
2025-08-04T16:35:37.728+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:35:37.732+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-08-04T16:35:37.732+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:35:37.737+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-08-04T16:35:37.737+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:35:37.741+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-08-04T16:35:37.741+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:35:37.744+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-08-04T16:35:37.744+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:35:37.749+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-08-04T16:35:37.749+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:35:37.757+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 14 JPA repository interfaces.
2025-08-04T16:35:37.758+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:35:37.770+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 12 ms. Found 20 JPA repository interfaces.
2025-08-04T16:35:37.770+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:35:37.771+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-08-04T16:35:37.776+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:35:37.776+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-08-04T16:35:37.777+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:35:37.783+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-08-04T16:35:37.785+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:35:37.822+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 36 ms. Found 69 JPA repository interfaces.
2025-08-04T16:35:37.822+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:35:37.823+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-08-04T16:35:37.828+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:35:37.830+07:00  INFO 80364 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-08-04T16:35:38.015+07:00  INFO 80364 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-08-04T16:35:38.019+07:00  INFO 80364 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-08-04T16:35:38.289+07:00  WARN 80364 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-08-04T16:35:38.495+07:00  INFO 80364 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-08-04T16:35:38.497+07:00  INFO 80364 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-08-04T16:35:38.509+07:00  INFO 80364 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-08-04T16:35:38.509+07:00  INFO 80364 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1621 ms
2025-08-04T16:35:38.575+07:00  WARN 80364 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-04T16:35:38.576+07:00  INFO 80364 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-08-04T16:35:38.669+07:00  INFO 80364 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@635f4be1
2025-08-04T16:35:38.669+07:00  INFO 80364 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-08-04T16:35:38.674+07:00  WARN 80364 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-04T16:35:38.674+07:00  INFO 80364 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-08-04T16:35:38.680+07:00  INFO 80364 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@2ea3f905
2025-08-04T16:35:38.680+07:00  INFO 80364 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-08-04T16:35:38.680+07:00  WARN 80364 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-04T16:35:38.680+07:00  INFO 80364 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-04T16:35:39.167+07:00  INFO 80364 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@64dd94ed
2025-08-04T16:35:39.167+07:00  INFO 80364 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-04T16:35:39.167+07:00  WARN 80364 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-04T16:35:39.167+07:00  INFO 80364 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-08-04T16:35:39.246+07:00  INFO 80364 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@67f25c
2025-08-04T16:35:39.246+07:00  INFO 80364 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-08-04T16:35:39.246+07:00  WARN 80364 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-04T16:35:39.246+07:00  INFO 80364 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-08-04T16:35:39.263+07:00  INFO 80364 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@43941806
2025-08-04T16:35:39.264+07:00  INFO 80364 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-08-04T16:35:39.264+07:00  INFO 80364 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '****************************************************', '***********************************************', '**********************************************'
2025-08-04T16:35:39.318+07:00  INFO 80364 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-08-04T16:35:39.321+07:00  INFO 80364 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@5068a2b1{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.3851768389252600097/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@3938e876{STARTED}}
2025-08-04T16:35:39.322+07:00  INFO 80364 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@5068a2b1{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.3851768389252600097/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@3938e876{STARTED}}
2025-08-04T16:35:39.324+07:00  INFO 80364 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@570cef1e{STARTING}[12.0.15,sto=0] @3128ms
2025-08-04T16:35:39.384+07:00  INFO 80364 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-04T16:35:39.413+07:00  INFO 80364 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-08-04T16:35:39.430+07:00  INFO 80364 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-04T16:35:39.559+07:00  INFO 80364 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-04T16:35:39.630+07:00  WARN 80364 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-04T16:35:40.264+07:00  INFO 80364 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-04T16:35:40.276+07:00  INFO 80364 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@2d83ed58] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-08-04T16:35:40.434+07:00  INFO 80364 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-04T16:35:40.713+07:00  INFO 80364 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.graphapi", "net.datatp.module.kpi", "net.datatp.module.monitor.activity", "net.datatp.module.wfms", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "net.datatp.module.okr", "cloud.datatp.module.fleet", "net.datatp.module.core.template", "cloud.datatp.gps", "cloud.datatp.module.pegasus.shipment", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "net.datatp.module.company.tmpl", "net.datatp.module.groovy", "cloud.datatp.tms", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "cloud.datatp.vendor", "net.datatp.module.resource", "net.datatp.module.company.hr", "net.datatp.module.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "cloud.datatp.module.odoo", "net.datatp.module.core.print", "net.datatp.module.chatbot", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.zalo", "net.datatp.module.accounting" ]
2025-08-04T16:35:40.715+07:00  INFO 80364 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-08-04T16:35:40.722+07:00  INFO 80364 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-04T16:35:40.723+07:00  INFO 80364 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-04T16:35:40.750+07:00  INFO 80364 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-04T16:35:40.759+07:00  WARN 80364 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-04T16:35:42.814+07:00  INFO 80364 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-04T16:35:42.815+07:00  INFO 80364 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@5647578b] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-08-04T16:35:43.060+07:00  WARN 80364 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-08-04T16:35:43.060+07:00  WARN 80364 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-08-04T16:35:43.068+07:00  WARN 80364 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-08-04T16:35:43.068+07:00  WARN 80364 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-08-04T16:35:43.083+07:00  WARN 80364 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-08-04T16:35:43.083+07:00  WARN 80364 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-08-04T16:35:43.563+07:00  INFO 80364 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-04T16:35:43.569+07:00  INFO 80364 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-04T16:35:43.571+07:00  INFO 80364 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-04T16:35:43.586+07:00  INFO 80364 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-04T16:35:43.595+07:00  WARN 80364 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-04T16:35:44.077+07:00  INFO 80364 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-04T16:35:44.077+07:00  INFO 80364 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@35a2beae] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-08-04T16:35:44.155+07:00  WARN 80364 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-08-04T16:35:44.155+07:00  WARN 80364 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-08-04T16:35:44.494+07:00  INFO 80364 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-04T16:35:44.526+07:00  INFO 80364 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-08-04T16:35:44.531+07:00  INFO 80364 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-08-04T16:35:44.531+07:00  INFO 80364 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-08-04T16:35:44.538+07:00  WARN 80364 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-08-04T16:35:44.666+07:00  INFO 80364 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-08-04T16:35:45.133+07:00  INFO 80364 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-08-04T16:35:45.136+07:00  INFO 80364 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-08-04T16:35:45.170+07:00  INFO 80364 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-08-04T16:35:45.213+07:00  INFO 80364 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-08-04T16:35:45.256+07:00  INFO 80364 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-08-04T16:35:45.285+07:00  INFO 80364 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-08-04T16:35:45.307+07:00  INFO 80364 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 189934561ms : this is harmless.
2025-08-04T16:35:45.315+07:00  INFO 80364 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-08-04T16:35:45.318+07:00  INFO 80364 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-08-04T16:35:45.331+07:00  INFO 80364 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 872166364ms : this is harmless.
2025-08-04T16:35:45.332+07:00  INFO 80364 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-08-04T16:35:45.344+07:00  INFO 80364 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-08-04T16:35:45.345+07:00  INFO 80364 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-08-04T16:35:47.448+07:00  INFO 80364 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-08-04T16:35:47.448+07:00  INFO 80364 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-08-04T16:35:47.448+07:00  WARN 80364 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-08-04T16:35:47.590+07:00  INFO 80364 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 04/08/2025@16:30:00+0700 to 04/08/2025@16:45:00+0700
2025-08-04T16:35:47.590+07:00  INFO 80364 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 04/08/2025@16:30:00+0700 to 04/08/2025@16:45:00+0700
2025-08-04T16:35:48.191+07:00  INFO 80364 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-08-04T16:35:48.192+07:00  INFO 80364 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-08-04T16:35:48.192+07:00  WARN 80364 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-08-04T16:35:48.414+07:00  INFO 80364 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-08-04T16:35:48.414+07:00  INFO 80364 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-08-04T16:35:48.414+07:00  INFO 80364 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-08-04T16:35:48.414+07:00  INFO 80364 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-08-04T16:35:48.414+07:00  INFO 80364 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-08-04T16:35:49.926+07:00  WARN 80364 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 2fff6820-597a-4ddf-b05a-0b610e3fa6fd

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-04T16:35:49.930+07:00  INFO 80364 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-04T16:35:50.239+07:00  INFO 80364 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-08-04T16:35:50.239+07:00  INFO 80364 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-08-04T16:35:50.239+07:00  INFO 80364 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-08-04T16:35:50.239+07:00  INFO 80364 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-08-04T16:35:50.239+07:00  INFO 80364 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-08-04T16:35:50.239+07:00  INFO 80364 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-08-04T16:35:50.239+07:00  INFO 80364 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-08-04T16:35:50.240+07:00  INFO 80364 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-08-04T16:35:50.240+07:00  INFO 80364 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-08-04T16:35:50.240+07:00  INFO 80364 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-08-04T16:35:50.240+07:00  INFO 80364 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-08-04T16:35:50.240+07:00  INFO 80364 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-08-04T16:35:50.244+07:00  INFO 80364 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-08-04T16:35:50.244+07:00  INFO 80364 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-08-04T16:35:50.244+07:00  INFO 80364 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-08-04T16:35:50.296+07:00  INFO 80364 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-04T16:35:50.296+07:00  INFO 80364 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-04T16:35:50.297+07:00  INFO 80364 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-08-04T16:35:50.305+07:00  INFO 80364 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@5e5b265b{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-08-04T16:35:50.306+07:00  INFO 80364 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-08-04T16:35:50.307+07:00  INFO 80364 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-08-04T16:35:50.346+07:00  INFO 80364 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-08-04T16:35:50.347+07:00  INFO 80364 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-08-04T16:35:50.352+07:00  INFO 80364 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 13.895 seconds (process running for 14.157)
2025-08-04T16:36:04.288+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T16:36:14.140+07:00  INFO 80364 --- [qtp1582527589-41] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0uen6tc8y3bmglkxqbyya7gk50
2025-08-04T16:36:14.527+07:00  INFO 80364 --- [qtp1582527589-41] n.d.module.session.ClientSessionManager  : Add a client session id = node0uen6tc8y3bmglkxqbyya7gk50, token = 9c5f4fed43a8af3bdfef3897fb9df2a1
2025-08-04T16:36:14.980+07:00  INFO 80364 --- [qtp1582527589-41] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:36:15.055+07:00  INFO 80364 --- [qtp1582527589-35] n.d.m.c.s.http.SystemRPCController       : Run the script from 127.0.0.1, by user nhat.le
2025-08-04T16:36:15.055+07:00  INFO 80364 --- [qtp1582527589-35] n.d.m.c.s.http.SystemRPCController       : Script Model: 
{
  "scriptDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/src/main/groovy",
  "scriptDataDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/data",
  "scripts" : [ ],
  "script" : "migration/server/hr/CheckAccount.groovy"
}
2025-08-04T16:36:19.082+07:00  INFO 80364 --- [qtp1582527589-41] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node06v5oxqkxcjxf89abcaux3qml1
2025-08-04T16:36:19.177+07:00  INFO 80364 --- [qtp1582527589-41] n.d.module.session.ClientSessionManager  : Add a client session id = node06v5oxqkxcjxf89abcaux3qml1, token = 12dd1f72c14bbcb3baac131babd24a8b
2025-08-04T16:36:19.186+07:00  INFO 80364 --- [qtp1582527589-41] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:36:19.210+07:00  INFO 80364 --- [qtp1582527589-40] n.d.m.c.s.http.SystemRPCController       : Run the script from 127.0.0.1, by user nhat.le
2025-08-04T16:36:19.211+07:00  INFO 80364 --- [qtp1582527589-40] n.d.m.c.s.http.SystemRPCController       : Script Model: 
{
  "scriptDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/src/main/groovy",
  "scriptDataDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/data",
  "scripts" : [ ],
  "script" : "migration/server/hr/CheckAccount.groovy"
}
2025-08-04T16:36:21.238+07:00  INFO 80364 --- [qtp1582527589-39] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node02ru65ydb53celvq1iopo1qa2
2025-08-04T16:36:21.348+07:00  INFO 80364 --- [qtp1582527589-39] n.d.module.session.ClientSessionManager  : Add a client session id = node02ru65ydb53celvq1iopo1qa2, token = 13d3d3be8c16643af4279523b9ec3684
2025-08-04T16:36:21.355+07:00  INFO 80364 --- [qtp1582527589-39] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:36:21.362+07:00  INFO 80364 --- [qtp1582527589-40] n.d.m.c.s.http.SystemRPCController       : Run the script from 127.0.0.1, by user nhat.le
2025-08-04T16:36:21.362+07:00  INFO 80364 --- [qtp1582527589-40] n.d.m.c.s.http.SystemRPCController       : Script Model: 
{
  "scriptDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/src/main/groovy",
  "scriptDataDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/data",
  "scripts" : [ ],
  "script" : "migration/server/hr/CheckAccount.groovy"
}
2025-08-04T16:36:25.983+07:00  INFO 80364 --- [qtp1582527589-39] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01lvthichtvf86uc8vevwmy5z33
2025-08-04T16:36:26.078+07:00  INFO 80364 --- [qtp1582527589-39] n.d.module.session.ClientSessionManager  : Add a client session id = node01lvthichtvf86uc8vevwmy5z33, token = 96196ff2b17bc770b8072ad2dd7b8345
2025-08-04T16:36:26.084+07:00  INFO 80364 --- [qtp1582527589-39] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:36:26.090+07:00  INFO 80364 --- [qtp1582527589-40] n.d.m.c.s.http.SystemRPCController       : Run the script from 127.0.0.1, by user nhat.le
2025-08-04T16:36:26.090+07:00  INFO 80364 --- [qtp1582527589-40] n.d.m.c.s.http.SystemRPCController       : Script Model: 
{
  "scriptDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/src/main/groovy",
  "scriptDataDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/data",
  "scripts" : [ ],
  "script" : "migration/server/hr/CheckAccount.groovy"
}
2025-08-04T16:36:30.383+07:00  INFO 80364 --- [qtp1582527589-39] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01d9f5er5n8aoyrqv1jk2gs2fx4
2025-08-04T16:36:30.474+07:00  INFO 80364 --- [qtp1582527589-39] n.d.module.session.ClientSessionManager  : Add a client session id = node01d9f5er5n8aoyrqv1jk2gs2fx4, token = a568420adecac32488e1a1a31899192a
2025-08-04T16:36:30.481+07:00  INFO 80364 --- [qtp1582527589-39] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:36:30.487+07:00  INFO 80364 --- [qtp1582527589-40] n.d.m.c.s.http.SystemRPCController       : Run the script from 127.0.0.1, by user nhat.le
2025-08-04T16:36:30.487+07:00  INFO 80364 --- [qtp1582527589-40] n.d.m.c.s.http.SystemRPCController       : Script Model: 
{
  "scriptDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/src/main/groovy",
  "scriptDataDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/data",
  "scripts" : [ ],
  "script" : "migration/server/hr/CheckAccount.groovy"
}
2025-08-04T16:36:44.420+07:00  INFO 80364 --- [qtp1582527589-36] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node04yugdimp1igflvidlxyav1ed5
2025-08-04T16:36:44.541+07:00  INFO 80364 --- [qtp1582527589-36] n.d.module.session.ClientSessionManager  : Add a client session id = node04yugdimp1igflvidlxyav1ed5, token = e74c69a2221325c5fe089c3653a1a26f
2025-08-04T16:36:44.547+07:00  INFO 80364 --- [qtp1582527589-36] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:36:44.574+07:00  INFO 80364 --- [qtp1582527589-41] n.d.m.c.s.http.SystemRPCController       : Run the script from 127.0.0.1, by user nhat.le
2025-08-04T16:36:44.575+07:00  INFO 80364 --- [qtp1582527589-41] n.d.m.c.s.http.SystemRPCController       : Script Model: 
{
  "scriptDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/src/main/groovy",
  "scriptDataDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/data",
  "scripts" : [ ],
  "script" : "migration/server/hr/InitKpiTemplate.groovy"
}
2025-08-04T16:36:47.115+07:00  INFO 80364 --- [qtp1582527589-39] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0nil6nv1m8exz1b968r4thbu7b6
2025-08-04T16:36:47.212+07:00  INFO 80364 --- [qtp1582527589-39] n.d.module.session.ClientSessionManager  : Add a client session id = node0nil6nv1m8exz1b968r4thbu7b6, token = 3140dd2050424c9bae08141fcb4f3672
2025-08-04T16:36:47.218+07:00  INFO 80364 --- [qtp1582527589-39] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:36:47.224+07:00  INFO 80364 --- [qtp1582527589-35] n.d.m.c.s.http.SystemRPCController       : Run the script from 127.0.0.1, by user nhat.le
2025-08-04T16:36:47.224+07:00  INFO 80364 --- [qtp1582527589-35] n.d.m.c.s.http.SystemRPCController       : Script Model: 
{
  "scriptDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/src/main/groovy",
  "scriptDataDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/data",
  "scripts" : [ ],
  "script" : "migration/server/hr/InitKpiTemplate.groovy"
}
2025-08-04T16:36:50.386+07:00  INFO 80364 --- [qtp1582527589-39] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0f7xz44ncmdhd15edohcn751c47
2025-08-04T16:36:50.478+07:00  INFO 80364 --- [qtp1582527589-39] n.d.module.session.ClientSessionManager  : Add a client session id = node0f7xz44ncmdhd15edohcn751c47, token = 03bd8f0aa1955aef0ac86cbeca2ab2d6
2025-08-04T16:36:50.483+07:00  INFO 80364 --- [qtp1582527589-39] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:36:50.490+07:00  INFO 80364 --- [qtp1582527589-35] n.d.m.c.s.http.SystemRPCController       : Run the script from 127.0.0.1, by user nhat.le
2025-08-04T16:36:50.491+07:00  INFO 80364 --- [qtp1582527589-35] n.d.m.c.s.http.SystemRPCController       : Script Model: 
{
  "scriptDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/src/main/groovy",
  "scriptDataDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/data",
  "scripts" : [ ],
  "script" : "migration/server/hr/InitKpiTemplate.groovy"
}
2025-08-04T16:36:53.383+07:00  INFO 80364 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T16:36:53.388+07:00  INFO 80364 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T16:36:53.678+07:00  INFO 80364 --- [qtp1582527589-39] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node09bp0a5bznolkl1w6z0ms16fs8
2025-08-04T16:36:53.782+07:00  INFO 80364 --- [qtp1582527589-39] n.d.module.session.ClientSessionManager  : Add a client session id = node09bp0a5bznolkl1w6z0ms16fs8, token = c1caffd9741dc0a635a82a4d56efcb96
2025-08-04T16:36:53.785+07:00  INFO 80364 --- [qtp1582527589-39] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:36:53.791+07:00  INFO 80364 --- [qtp1582527589-35] n.d.m.c.s.http.SystemRPCController       : Run the script from 127.0.0.1, by user nhat.le
2025-08-04T16:36:53.791+07:00  INFO 80364 --- [qtp1582527589-35] n.d.m.c.s.http.SystemRPCController       : Script Model: 
{
  "scriptDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/src/main/groovy",
  "scriptDataDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/data",
  "scripts" : [ ],
  "script" : "migration/server/hr/InitKpiTemplate.groovy"
}
2025-08-04T16:36:55.151+07:00  INFO 80364 --- [qtp1582527589-39] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01qv2i7d8khrdy1uja0ifzvo3po9
2025-08-04T16:36:55.247+07:00  INFO 80364 --- [qtp1582527589-39] n.d.module.session.ClientSessionManager  : Add a client session id = node01qv2i7d8khrdy1uja0ifzvo3po9, token = 59fe30bd2788f1d5d91c7aec72c382ad
2025-08-04T16:36:55.252+07:00  INFO 80364 --- [qtp1582527589-39] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:36:55.259+07:00  INFO 80364 --- [qtp1582527589-35] n.d.m.c.s.http.SystemRPCController       : Run the script from 127.0.0.1, by user nhat.le
2025-08-04T16:36:55.259+07:00  INFO 80364 --- [qtp1582527589-35] n.d.m.c.s.http.SystemRPCController       : Script Model: 
{
  "scriptDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/src/main/groovy",
  "scriptDataDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/data",
  "scripts" : [ ],
  "script" : "migration/server/hr/InitKpiTemplate.groovy"
}
2025-08-04T16:36:59.960+07:00  INFO 80364 --- [qtp1582527589-39] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01o9zu1fdzlroa1suwx5s1vdnls10
2025-08-04T16:37:00.043+07:00  INFO 80364 --- [qtp1582527589-39] n.d.module.session.ClientSessionManager  : Add a client session id = node01o9zu1fdzlroa1suwx5s1vdnls10, token = c044ac369dbcca9c65bcdab6979ab0f4
2025-08-04T16:37:00.045+07:00  INFO 80364 --- [qtp1582527589-39] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:37:00.051+07:00  INFO 80364 --- [qtp1582527589-35] n.d.m.c.s.http.SystemRPCController       : Run the script from 127.0.0.1, by user nhat.le
2025-08-04T16:37:00.051+07:00  INFO 80364 --- [qtp1582527589-35] n.d.m.c.s.http.SystemRPCController       : Script Model: 
{
  "scriptDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/src/main/groovy",
  "scriptDataDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/data",
  "scripts" : [ ],
  "script" : "migration/server/hr/InitKpi.groovy"
}
2025-08-04T16:37:01.749+07:00  INFO 80364 --- [qtp1582527589-39] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0xxo7p3wz57nwjbxoa51saiwl11
2025-08-04T16:37:01.839+07:00  INFO 80364 --- [qtp1582527589-39] n.d.module.session.ClientSessionManager  : Add a client session id = node0xxo7p3wz57nwjbxoa51saiwl11, token = c595d8875056590b0b79987a42326b9a
2025-08-04T16:37:01.842+07:00  INFO 80364 --- [qtp1582527589-39] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:37:01.848+07:00  INFO 80364 --- [qtp1582527589-35] n.d.m.c.s.http.SystemRPCController       : Run the script from 127.0.0.1, by user nhat.le
2025-08-04T16:37:01.848+07:00  INFO 80364 --- [qtp1582527589-35] n.d.m.c.s.http.SystemRPCController       : Script Model: 
{
  "scriptDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/src/main/groovy",
  "scriptDataDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/data",
  "scripts" : [ ],
  "script" : "migration/server/hr/InitKpi.groovy"
}
2025-08-04T16:37:03.643+07:00  INFO 80364 --- [qtp1582527589-39] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01xj78pftlf3ubvexkp5pr9lat12
2025-08-04T16:37:03.739+07:00  INFO 80364 --- [qtp1582527589-39] n.d.module.session.ClientSessionManager  : Add a client session id = node01xj78pftlf3ubvexkp5pr9lat12, token = cd5b788bd798228f0a5ad49404378370
2025-08-04T16:37:03.743+07:00  INFO 80364 --- [qtp1582527589-39] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:37:03.748+07:00  INFO 80364 --- [qtp1582527589-35] n.d.m.c.s.http.SystemRPCController       : Run the script from 127.0.0.1, by user nhat.le
2025-08-04T16:37:03.748+07:00  INFO 80364 --- [qtp1582527589-35] n.d.m.c.s.http.SystemRPCController       : Script Model: 
{
  "scriptDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/src/main/groovy",
  "scriptDataDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/data",
  "scripts" : [ ],
  "script" : "migration/server/hr/InitKpi.groovy"
}
2025-08-04T16:37:05.397+07:00  INFO 80364 --- [qtp1582527589-39] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0gl0zmpu7c4cx1l014xq34m5w513
2025-08-04T16:37:05.497+07:00  INFO 80364 --- [qtp1582527589-39] n.d.module.session.ClientSessionManager  : Add a client session id = node0gl0zmpu7c4cx1l014xq34m5w513, token = 7c70d208885b874e1fb328c0cfc24c29
2025-08-04T16:37:05.499+07:00  INFO 80364 --- [qtp1582527589-39] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:37:05.505+07:00  INFO 80364 --- [qtp1582527589-35] n.d.m.c.s.http.SystemRPCController       : Run the script from 127.0.0.1, by user nhat.le
2025-08-04T16:37:05.505+07:00  INFO 80364 --- [qtp1582527589-35] n.d.m.c.s.http.SystemRPCController       : Script Model: 
{
  "scriptDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/src/main/groovy",
  "scriptDataDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/data",
  "scripts" : [ ],
  "script" : "migration/server/hr/InitKpi.groovy"
}
2025-08-04T16:37:06.411+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T16:37:07.186+07:00  INFO 80364 --- [qtp1582527589-39] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node014jlhrgl28or0u7a9fgts5v7v14
2025-08-04T16:37:07.280+07:00  INFO 80364 --- [qtp1582527589-39] n.d.module.session.ClientSessionManager  : Add a client session id = node014jlhrgl28or0u7a9fgts5v7v14, token = 7154e9017690f79f5f195cc63be08031
2025-08-04T16:37:07.283+07:00  INFO 80364 --- [qtp1582527589-39] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:37:07.289+07:00  INFO 80364 --- [qtp1582527589-35] n.d.m.c.s.http.SystemRPCController       : Run the script from 127.0.0.1, by user nhat.le
2025-08-04T16:37:07.289+07:00  INFO 80364 --- [qtp1582527589-35] n.d.m.c.s.http.SystemRPCController       : Script Model: 
{
  "scriptDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/src/main/groovy",
  "scriptDataDir" : "/Users/<USER>/nez/code/datatp/datatp-build/app/cli/data",
  "scripts" : [ ],
  "script" : "migration/server/hr/InitKpi.groovy"
}
2025-08-04T16:37:16.828+07:00  INFO 80364 --- [qtp1582527589-34] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node01rkv011l5ky89g8dm16q39bz415
2025-08-04T16:37:16.964+07:00  INFO 80364 --- [qtp1582527589-34] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = ce0eac81ba105870ec16523f0b509722
2025-08-04T16:37:16.969+07:00  INFO 80364 --- [qtp1582527589-34] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:38:03.511+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T16:38:52.655+07:00  INFO 80364 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-08-04T16:38:52.667+07:00  INFO 80364 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T16:39:06.686+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T16:40:02.787+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T16:40:02.794+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T16:40:56.913+07:00  INFO 80364 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 9, expire count 0
2025-08-04T16:40:56.923+07:00  INFO 80364 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T16:41:05.952+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T16:41:06.831+07:00  INFO 80364 --- [qtp1582527589-34] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = ce0eac81ba105870ec16523f0b509722
2025-08-04T16:41:06.840+07:00  INFO 80364 --- [qtp1582527589-34] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:41:06.860+07:00  INFO 80364 --- [qtp1582527589-67] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = ce0eac81ba105870ec16523f0b509722
2025-08-04T16:41:06.871+07:00  INFO 80364 --- [qtp1582527589-67] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:41:59.377+07:00  INFO 80364 --- [qtp1582527589-41] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = ce0eac81ba105870ec16523f0b509722
2025-08-04T16:41:59.390+07:00  INFO 80364 --- [qtp1582527589-41] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:41:59.446+07:00  INFO 80364 --- [qtp1582527589-71] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = ce0eac81ba105870ec16523f0b509722
2025-08-04T16:41:59.473+07:00  INFO 80364 --- [qtp1582527589-71] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:42:02.057+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T16:42:17.263+07:00  INFO 80364 --- [qtp1582527589-41] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = ce0eac81ba105870ec16523f0b509722
2025-08-04T16:42:17.264+07:00  INFO 80364 --- [qtp1582527589-71] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = ce0eac81ba105870ec16523f0b509722
2025-08-04T16:42:17.275+07:00  INFO 80364 --- [qtp1582527589-71] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:42:17.281+07:00  INFO 80364 --- [qtp1582527589-41] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:42:24.228+07:00  INFO 80364 --- [qtp1582527589-36] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = ce0eac81ba105870ec16523f0b509722
2025-08-04T16:42:24.238+07:00  INFO 80364 --- [qtp1582527589-36] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:42:24.277+07:00  INFO 80364 --- [qtp1582527589-40] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = ce0eac81ba105870ec16523f0b509722
2025-08-04T16:42:24.290+07:00  INFO 80364 --- [qtp1582527589-40] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:42:56.893+07:00  INFO 80364 --- [qtp1582527589-39] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = ce0eac81ba105870ec16523f0b509722
2025-08-04T16:42:56.910+07:00  INFO 80364 --- [qtp1582527589-39] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:42:56.931+07:00  INFO 80364 --- [qtp1582527589-66] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = ce0eac81ba105870ec16523f0b509722
2025-08-04T16:42:56.935+07:00  INFO 80364 --- [qtp1582527589-66] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:42:57.181+07:00  INFO 80364 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-08-04T16:42:57.190+07:00  INFO 80364 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T16:43:05.212+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T16:43:32.444+07:00  INFO 80364 --- [qtp1582527589-71] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = ce0eac81ba105870ec16523f0b509722
2025-08-04T16:43:32.454+07:00  INFO 80364 --- [qtp1582527589-71] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:43:32.471+07:00  INFO 80364 --- [qtp1582527589-75] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = ce0eac81ba105870ec16523f0b509722
2025-08-04T16:43:32.540+07:00  INFO 80364 --- [qtp1582527589-75] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:44:06.316+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T16:44:13.779+07:00  INFO 80364 --- [qtp1582527589-34] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = ce0eac81ba105870ec16523f0b509722
2025-08-04T16:44:13.781+07:00  INFO 80364 --- [qtp1582527589-66] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = ce0eac81ba105870ec16523f0b509722
2025-08-04T16:44:13.797+07:00  INFO 80364 --- [qtp1582527589-34] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:44:13.797+07:00  INFO 80364 --- [qtp1582527589-66] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:44:26.705+07:00  INFO 80364 --- [qtp1582527589-34] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = ce0eac81ba105870ec16523f0b509722
2025-08-04T16:44:26.707+07:00  INFO 80364 --- [qtp1582527589-75] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = ce0eac81ba105870ec16523f0b509722
2025-08-04T16:44:26.715+07:00  INFO 80364 --- [qtp1582527589-34] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:44:26.716+07:00  INFO 80364 --- [qtp1582527589-75] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:44:56.413+07:00  INFO 80364 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T16:44:56.425+07:00  INFO 80364 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T16:45:04.443+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T16:45:04.444+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T16:45:04.445+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-04T16:45:04.448+07:00  INFO 80364 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 04/08/2025@16:45:04+0700
2025-08-04T16:45:04.459+07:00  INFO 80364 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 04/08/2025@16:45:00+0700 to 04/08/2025@17:00:00+0700
2025-08-04T16:45:04.459+07:00  INFO 80364 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 04/08/2025@16:45:00+0700 to 04/08/2025@17:00:00+0700
2025-08-04T16:45:06.266+07:00  INFO 80364 --- [qtp1582527589-66] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = ce0eac81ba105870ec16523f0b509722
2025-08-04T16:45:06.279+07:00  INFO 80364 --- [qtp1582527589-66] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:45:06.308+07:00  INFO 80364 --- [qtp1582527589-35] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = ce0eac81ba105870ec16523f0b509722
2025-08-04T16:45:06.322+07:00  INFO 80364 --- [qtp1582527589-35] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:45:21.242+07:00  INFO 80364 --- [qtp1582527589-39] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = ce0eac81ba105870ec16523f0b509722
2025-08-04T16:45:21.243+07:00  INFO 80364 --- [qtp1582527589-35] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = ce0eac81ba105870ec16523f0b509722
2025-08-04T16:45:21.251+07:00  INFO 80364 --- [qtp1582527589-35] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:45:21.251+07:00  INFO 80364 --- [qtp1582527589-39] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:46:06.597+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T16:46:39.368+07:00  INFO 80364 --- [Scheduler-2145867337-1] n.d.m.session.AppHttpSessionListener     : The session node04yugdimp1igflvidlxyav1ed5 is destroyed.
2025-08-04T16:46:39.370+07:00  INFO 80364 --- [Scheduler-2145867337-1] n.d.m.session.AppHttpSessionListener     : The session node01qv2i7d8khrdy1uja0ifzvo3po9 is destroyed.
2025-08-04T16:46:39.370+07:00  INFO 80364 --- [Scheduler-2145867337-1] n.d.m.session.AppHttpSessionListener     : The session node014jlhrgl28or0u7a9fgts5v7v14 is destroyed.
2025-08-04T16:46:39.370+07:00  INFO 80364 --- [Scheduler-2145867337-1] n.d.m.session.AppHttpSessionListener     : The session node01o9zu1fdzlroa1suwx5s1vdnls10 is destroyed.
2025-08-04T16:46:39.371+07:00  INFO 80364 --- [Scheduler-2145867337-1] n.d.m.session.AppHttpSessionListener     : The session node09bp0a5bznolkl1w6z0ms16fs8 is destroyed.
2025-08-04T16:46:39.371+07:00  INFO 80364 --- [Scheduler-2145867337-1] n.d.m.session.AppHttpSessionListener     : The session node01d9f5er5n8aoyrqv1jk2gs2fx4 is destroyed.
2025-08-04T16:46:39.371+07:00  INFO 80364 --- [Scheduler-2145867337-1] n.d.m.session.AppHttpSessionListener     : The session node02ru65ydb53celvq1iopo1qa2 is destroyed.
2025-08-04T16:46:39.371+07:00  INFO 80364 --- [Scheduler-2145867337-1] n.d.m.session.AppHttpSessionListener     : The session node0xxo7p3wz57nwjbxoa51saiwl11 is destroyed.
2025-08-04T16:46:39.372+07:00  INFO 80364 --- [Scheduler-2145867337-1] n.d.m.session.AppHttpSessionListener     : The session node0f7xz44ncmdhd15edohcn751c47 is destroyed.
2025-08-04T16:46:39.372+07:00  INFO 80364 --- [Scheduler-2145867337-1] n.d.m.session.AppHttpSessionListener     : The session node01lvthichtvf86uc8vevwmy5z33 is destroyed.
2025-08-04T16:46:39.372+07:00  INFO 80364 --- [Scheduler-2145867337-1] n.d.m.session.AppHttpSessionListener     : The session node0uen6tc8y3bmglkxqbyya7gk50 is destroyed.
2025-08-04T16:46:39.372+07:00  INFO 80364 --- [Scheduler-2145867337-1] n.d.m.session.AppHttpSessionListener     : The session node0gl0zmpu7c4cx1l014xq34m5w513 is destroyed.
2025-08-04T16:46:39.372+07:00  INFO 80364 --- [Scheduler-2145867337-1] n.d.m.session.AppHttpSessionListener     : The session node0nil6nv1m8exz1b968r4thbu7b6 is destroyed.
2025-08-04T16:46:39.373+07:00  INFO 80364 --- [Scheduler-2145867337-1] n.d.m.session.AppHttpSessionListener     : The session node06v5oxqkxcjxf89abcaux3qml1 is destroyed.
2025-08-04T16:46:39.373+07:00  INFO 80364 --- [Scheduler-2145867337-1] n.d.m.session.AppHttpSessionListener     : The session node01xj78pftlf3ubvexkp5pr9lat12 is destroyed.
2025-08-04T16:46:56.726+07:00  INFO 80364 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-08-04T16:46:56.739+07:00  INFO 80364 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T16:47:03.755+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T16:48:06.854+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T16:48:55.955+07:00  INFO 80364 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 8
2025-08-04T16:48:55.962+07:00  INFO 80364 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T16:49:02.981+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T16:50:06.080+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T16:50:06.082+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T16:50:55.199+07:00  INFO 80364 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T16:50:55.207+07:00  INFO 80364 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T16:51:02.230+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T16:52:05.360+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T16:52:54.450+07:00  INFO 80364 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T16:52:54.456+07:00  INFO 80364 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T16:53:06.474+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T16:54:04.567+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T16:54:53.683+07:00  INFO 80364 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T16:54:53.696+07:00  INFO 80364 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T16:55:06.715+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T16:55:06.716+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T16:56:03.817+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T16:56:52.907+07:00  INFO 80364 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 4
2025-08-04T16:56:52.915+07:00  INFO 80364 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T16:57:06.931+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T16:58:03.026+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T16:58:57.153+07:00  INFO 80364 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T16:58:57.163+07:00  INFO 80364 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T16:59:06.182+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T17:00:02.272+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T17:00:02.277+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T17:00:02.278+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-04T17:00:02.285+07:00  INFO 80364 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 04/08/2025@17:00:02+0700
2025-08-04T17:00:02.330+07:00  INFO 80364 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 04/08/2025@17:00:00+0700 to 04/08/2025@17:15:00+0700
2025-08-04T17:00:02.330+07:00  INFO 80364 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 04/08/2025@17:00:00+0700 to 04/08/2025@17:15:00+0700
2025-08-04T17:00:02.332+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every hour
2025-08-04T17:00:56.424+07:00  INFO 80364 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T17:00:56.427+07:00  INFO 80364 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T17:01:05.445+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T17:02:06.537+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T17:02:56.645+07:00  INFO 80364 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T17:02:56.658+07:00  INFO 80364 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T17:03:04.672+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T17:04:06.759+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T17:04:56.917+07:00  INFO 80364 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T17:04:56.951+07:00  INFO 80364 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T17:05:03.969+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T17:05:03.981+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T17:06:06.091+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T17:06:56.182+07:00  INFO 80364 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T17:06:56.188+07:00  INFO 80364 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T17:07:03.201+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T17:08:06.301+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T17:08:55.403+07:00  INFO 80364 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T17:08:55.411+07:00  INFO 80364 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T17:09:02.424+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T17:10:05.520+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T17:10:05.523+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T17:10:44.659+07:00  INFO 80364 --- [qtp1582527589-122] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = ce0eac81ba105870ec16523f0b509722
2025-08-04T17:10:44.660+07:00  INFO 80364 --- [qtp1582527589-81] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = ce0eac81ba105870ec16523f0b509722
2025-08-04T17:10:44.763+07:00  INFO 80364 --- [qtp1582527589-81] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T17:10:44.781+07:00  INFO 80364 --- [qtp1582527589-122] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T17:10:54.627+07:00  INFO 80364 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 1
2025-08-04T17:10:54.642+07:00  INFO 80364 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T17:10:55.622+07:00  INFO 80364 --- [qtp1582527589-67] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = ce0eac81ba105870ec16523f0b509722
2025-08-04T17:10:55.634+07:00  INFO 80364 --- [qtp1582527589-67] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T17:10:55.676+07:00  INFO 80364 --- [qtp1582527589-153] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = ce0eac81ba105870ec16523f0b509722
2025-08-04T17:10:55.679+07:00  INFO 80364 --- [qtp1582527589-153] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T17:11:06.663+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T17:11:35.623+07:00  INFO 80364 --- [qtp1582527589-67] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = ce0eac81ba105870ec16523f0b509722
2025-08-04T17:11:35.625+07:00  INFO 80364 --- [qtp1582527589-81] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = ce0eac81ba105870ec16523f0b509722
2025-08-04T17:11:35.647+07:00  INFO 80364 --- [qtp1582527589-67] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T17:11:35.648+07:00  INFO 80364 --- [qtp1582527589-81] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T17:12:04.786+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T17:12:53.892+07:00  INFO 80364 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T17:12:53.895+07:00  INFO 80364 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T17:13:06.917+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T17:14:04.021+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T17:14:53.111+07:00  INFO 80364 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 0
2025-08-04T17:14:53.114+07:00  INFO 80364 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T17:15:06.131+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T17:15:06.132+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T17:15:06.133+07:00  INFO 80364 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 04/08/2025@17:15:06+0700
2025-08-04T17:15:06.152+07:00  INFO 80364 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 04/08/2025@17:15:00+0700 to 04/08/2025@17:30:00+0700
2025-08-04T17:15:06.153+07:00  INFO 80364 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 04/08/2025@17:15:00+0700 to 04/08/2025@17:30:00+0700
2025-08-04T17:15:06.153+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-04T17:16:03.250+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T17:16:05.074+07:00  INFO 80364 --- [qtp1582527589-122] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = ce0eac81ba105870ec16523f0b509722
2025-08-04T17:16:05.091+07:00  INFO 80364 --- [qtp1582527589-122] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T17:16:05.116+07:00  INFO 80364 --- [qtp1582527589-155] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = ce0eac81ba105870ec16523f0b509722
2025-08-04T17:16:05.126+07:00  INFO 80364 --- [qtp1582527589-155] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T17:16:43.607+07:00  INFO 80364 --- [qtp1582527589-75] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = ce0eac81ba105870ec16523f0b509722
2025-08-04T17:16:43.610+07:00  INFO 80364 --- [qtp1582527589-122] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = ce0eac81ba105870ec16523f0b509722
2025-08-04T17:16:43.622+07:00  INFO 80364 --- [qtp1582527589-75] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T17:16:43.629+07:00  INFO 80364 --- [qtp1582527589-122] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T17:16:57.337+07:00  INFO 80364 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 10, expire count 0
2025-08-04T17:16:57.349+07:00  INFO 80364 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T17:17:06.372+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T17:17:43.848+07:00  INFO 80364 --- [qtp1582527589-149] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le logout successfully 
2025-08-04T17:17:45.617+07:00  INFO 80364 --- [qtp1582527589-150] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-08-04T17:17:46.143+07:00  INFO 80364 --- [qtp1582527589-153] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = 6e3ebf1304655685e244c85a6bd075cb
2025-08-04T17:17:46.146+07:00  INFO 80364 --- [qtp1582527589-153] n.d.m.c.a.CompanyAuthenticationService   : User max.vnhph is logged in successfully system
2025-08-04T17:18:02.474+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T17:18:21.225+07:00  INFO 80364 --- [qtp1582527589-41] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0j9c4c3f11aq5hdp7tm9hxd9616
2025-08-04T17:18:21.375+07:00  INFO 80364 --- [qtp1582527589-41] n.d.module.session.ClientSessionManager  : Add a client session id = node0j9c4c3f11aq5hdp7tm9hxd9616, token = fc06f2338d2de7ec25f8fa9070a21af5
2025-08-04T17:18:21.383+07:00  INFO 80364 --- [qtp1582527589-41] n.d.m.c.a.CompanyAuthenticationService   : User haunt is logged in successfully system
2025-08-04T17:18:47.600+07:00  INFO 80364 --- [qtp1582527589-41] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = 6e3ebf1304655685e244c85a6bd075cb
2025-08-04T17:18:47.603+07:00  INFO 80364 --- [qtp1582527589-155] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = 6e3ebf1304655685e244c85a6bd075cb
2025-08-04T17:18:47.611+07:00  INFO 80364 --- [qtp1582527589-41] n.d.m.c.a.CompanyAuthenticationService   : User max.vnhph is logged in successfully system
2025-08-04T17:18:47.616+07:00  INFO 80364 --- [qtp1582527589-155] n.d.m.c.a.CompanyAuthenticationService   : User max.vnhph is logged in successfully system
2025-08-04T17:18:56.601+07:00  INFO 80364 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 11, expire count 0
2025-08-04T17:18:56.607+07:00  INFO 80364 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T17:19:05.626+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T17:19:35.881+07:00  INFO 80364 --- [qtp1582527589-153] n.d.module.session.ClientSessionManager  : Add a client session id = node0j9c4c3f11aq5hdp7tm9hxd9616, token = fc06f2338d2de7ec25f8fa9070a21af5
2025-08-04T17:19:35.884+07:00  INFO 80364 --- [qtp1582527589-41] n.d.module.session.ClientSessionManager  : Add a client session id = node0j9c4c3f11aq5hdp7tm9hxd9616, token = fc06f2338d2de7ec25f8fa9070a21af5
2025-08-04T17:19:35.894+07:00  INFO 80364 --- [qtp1582527589-153] n.d.m.c.a.CompanyAuthenticationService   : User haunt is logged in successfully system
2025-08-04T17:19:35.894+07:00  INFO 80364 --- [qtp1582527589-41] n.d.m.c.a.CompanyAuthenticationService   : User haunt is logged in successfully system
2025-08-04T17:19:36.617+07:00  INFO 80364 --- [qtp1582527589-67] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = 6e3ebf1304655685e244c85a6bd075cb
2025-08-04T17:19:36.619+07:00  INFO 80364 --- [qtp1582527589-122] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = 6e3ebf1304655685e244c85a6bd075cb
2025-08-04T17:19:36.620+07:00  INFO 80364 --- [qtp1582527589-155] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = 6e3ebf1304655685e244c85a6bd075cb
2025-08-04T17:19:36.623+07:00  INFO 80364 --- [qtp1582527589-122] n.d.m.c.a.CompanyAuthenticationService   : User max.vnhph is logged in successfully system
2025-08-04T17:19:36.623+07:00  INFO 80364 --- [qtp1582527589-67] n.d.m.c.a.CompanyAuthenticationService   : User max.vnhph is logged in successfully system
2025-08-04T17:19:36.631+07:00  INFO 80364 --- [qtp1582527589-155] n.d.m.c.a.CompanyAuthenticationService   : User max.vnhph is logged in successfully system
2025-08-04T17:19:36.659+07:00  INFO 80364 --- [qtp1582527589-150] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = 6e3ebf1304655685e244c85a6bd075cb
2025-08-04T17:19:36.665+07:00  INFO 80364 --- [qtp1582527589-150] n.d.m.c.a.CompanyAuthenticationService   : User max.vnhph is logged in successfully system
2025-08-04T17:19:49.238+07:00  INFO 80364 --- [qtp1582527589-36] n.d.module.session.ClientSessionManager  : Add a client session id = node0j9c4c3f11aq5hdp7tm9hxd9616, token = fc06f2338d2de7ec25f8fa9070a21af5
2025-08-04T17:19:49.246+07:00  INFO 80364 --- [qtp1582527589-36] n.d.m.c.a.CompanyAuthenticationService   : User haunt is logged in successfully system
2025-08-04T17:19:49.272+07:00  INFO 80364 --- [qtp1582527589-158] n.d.module.session.ClientSessionManager  : Add a client session id = node0j9c4c3f11aq5hdp7tm9hxd9616, token = fc06f2338d2de7ec25f8fa9070a21af5
2025-08-04T17:19:49.283+07:00  INFO 80364 --- [qtp1582527589-158] n.d.m.c.a.CompanyAuthenticationService   : User haunt is logged in successfully system
2025-08-04T17:19:49.599+07:00  INFO 80364 --- [qtp1582527589-155] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = 6e3ebf1304655685e244c85a6bd075cb
2025-08-04T17:19:49.599+07:00  INFO 80364 --- [qtp1582527589-36] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = 6e3ebf1304655685e244c85a6bd075cb
2025-08-04T17:19:49.600+07:00  INFO 80364 --- [qtp1582527589-41] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = 6e3ebf1304655685e244c85a6bd075cb
2025-08-04T17:19:49.600+07:00  INFO 80364 --- [qtp1582527589-153] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = 6e3ebf1304655685e244c85a6bd075cb
2025-08-04T17:19:49.605+07:00  INFO 80364 --- [qtp1582527589-155] n.d.m.c.a.CompanyAuthenticationService   : User max.vnhph is logged in successfully system
2025-08-04T17:19:49.605+07:00  INFO 80364 --- [qtp1582527589-41] n.d.m.c.a.CompanyAuthenticationService   : User max.vnhph is logged in successfully system
2025-08-04T17:19:49.608+07:00  INFO 80364 --- [qtp1582527589-36] n.d.m.c.a.CompanyAuthenticationService   : User max.vnhph is logged in successfully system
2025-08-04T17:19:49.608+07:00  INFO 80364 --- [qtp1582527589-153] n.d.m.c.a.CompanyAuthenticationService   : User max.vnhph is logged in successfully system
2025-08-04T17:19:57.634+07:00  INFO 80364 --- [qtp1582527589-67] n.d.module.session.ClientSessionManager  : Add a client session id = node0j9c4c3f11aq5hdp7tm9hxd9616, token = fc06f2338d2de7ec25f8fa9070a21af5
2025-08-04T17:19:57.635+07:00  INFO 80364 --- [qtp1582527589-75] n.d.module.session.ClientSessionManager  : Add a client session id = node0j9c4c3f11aq5hdp7tm9hxd9616, token = fc06f2338d2de7ec25f8fa9070a21af5
2025-08-04T17:19:57.648+07:00  INFO 80364 --- [qtp1582527589-67] n.d.m.c.a.CompanyAuthenticationService   : User haunt is logged in successfully system
2025-08-04T17:19:57.651+07:00  INFO 80364 --- [qtp1582527589-75] n.d.m.c.a.CompanyAuthenticationService   : User haunt is logged in successfully system
2025-08-04T17:19:57.756+07:00  INFO 80364 --- [qtp1582527589-158] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = 6e3ebf1304655685e244c85a6bd075cb
2025-08-04T17:19:57.763+07:00  INFO 80364 --- [qtp1582527589-158] n.d.m.c.a.CompanyAuthenticationService   : User max.vnhph is logged in successfully system
2025-08-04T17:19:57.851+07:00  INFO 80364 --- [qtp1582527589-149] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = 6e3ebf1304655685e244c85a6bd075cb
2025-08-04T17:19:57.907+07:00  INFO 80364 --- [qtp1582527589-149] n.d.m.c.a.CompanyAuthenticationService   : User max.vnhph is logged in successfully system
2025-08-04T17:19:58.602+07:00  INFO 80364 --- [qtp1582527589-41] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = 6e3ebf1304655685e244c85a6bd075cb
2025-08-04T17:19:58.603+07:00  INFO 80364 --- [qtp1582527589-67] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = 6e3ebf1304655685e244c85a6bd075cb
2025-08-04T17:19:58.607+07:00  INFO 80364 --- [qtp1582527589-41] n.d.m.c.a.CompanyAuthenticationService   : User max.vnhph is logged in successfully system
2025-08-04T17:19:58.617+07:00  INFO 80364 --- [qtp1582527589-67] n.d.m.c.a.CompanyAuthenticationService   : User max.vnhph is logged in successfully system
2025-08-04T17:20:06.739+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T17:20:06.741+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T17:20:56.865+07:00  INFO 80364 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 26, expire count 0
2025-08-04T17:20:56.873+07:00  INFO 80364 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T17:21:04.888+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T17:22:06.984+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T17:22:34.010+07:00  INFO 80364 --- [qtp1582527589-149] n.d.module.session.ClientSessionManager  : Add a client session id = node0j9c4c3f11aq5hdp7tm9hxd9616, token = fc06f2338d2de7ec25f8fa9070a21af5
2025-08-04T17:22:34.012+07:00  INFO 80364 --- [qtp1582527589-153] n.d.module.session.ClientSessionManager  : Add a client session id = node0j9c4c3f11aq5hdp7tm9hxd9616, token = fc06f2338d2de7ec25f8fa9070a21af5
2025-08-04T17:22:34.024+07:00  INFO 80364 --- [qtp1582527589-149] n.d.m.c.a.CompanyAuthenticationService   : User haunt is logged in successfully system
2025-08-04T17:22:34.024+07:00  INFO 80364 --- [qtp1582527589-153] n.d.m.c.a.CompanyAuthenticationService   : User haunt is logged in successfully system
2025-08-04T17:22:34.609+07:00  INFO 80364 --- [qtp1582527589-41] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = 6e3ebf1304655685e244c85a6bd075cb
2025-08-04T17:22:34.610+07:00  INFO 80364 --- [qtp1582527589-67] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = 6e3ebf1304655685e244c85a6bd075cb
2025-08-04T17:22:34.613+07:00  INFO 80364 --- [qtp1582527589-155] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = 6e3ebf1304655685e244c85a6bd075cb
2025-08-04T17:22:34.622+07:00  INFO 80364 --- [qtp1582527589-41] n.d.m.c.a.CompanyAuthenticationService   : User max.vnhph is logged in successfully system
2025-08-04T17:22:34.624+07:00  INFO 80364 --- [qtp1582527589-67] n.d.m.c.a.CompanyAuthenticationService   : User max.vnhph is logged in successfully system
2025-08-04T17:22:34.636+07:00  INFO 80364 --- [qtp1582527589-155] n.d.m.c.a.CompanyAuthenticationService   : User max.vnhph is logged in successfully system
2025-08-04T17:22:34.658+07:00  INFO 80364 --- [qtp1582527589-194] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = 6e3ebf1304655685e244c85a6bd075cb
2025-08-04T17:22:34.662+07:00  INFO 80364 --- [qtp1582527589-194] n.d.m.c.a.CompanyAuthenticationService   : User max.vnhph is logged in successfully system
2025-08-04T17:22:57.084+07:00  INFO 80364 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 15, expire count 0
2025-08-04T17:22:57.116+07:00  INFO 80364 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T17:23:04.128+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T17:24:02.395+07:00  INFO 80364 --- [qtp1582527589-187] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = 6e3ebf1304655685e244c85a6bd075cb
2025-08-04T17:24:02.396+07:00  INFO 80364 --- [qtp1582527589-152] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = 6e3ebf1304655685e244c85a6bd075cb
2025-08-04T17:24:02.405+07:00  INFO 80364 --- [qtp1582527589-152] n.d.m.c.a.CompanyAuthenticationService   : User max.vnhph is logged in successfully system
2025-08-04T17:24:02.405+07:00  INFO 80364 --- [qtp1582527589-187] n.d.m.c.a.CompanyAuthenticationService   : User max.vnhph is logged in successfully system
2025-08-04T17:24:02.598+07:00  INFO 80364 --- [qtp1582527589-152] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = 6e3ebf1304655685e244c85a6bd075cb
2025-08-04T17:24:02.599+07:00  INFO 80364 --- [qtp1582527589-41] n.d.module.session.ClientSessionManager  : Add a client session id = node0j9c4c3f11aq5hdp7tm9hxd9616, token = fc06f2338d2de7ec25f8fa9070a21af5
2025-08-04T17:24:02.602+07:00  INFO 80364 --- [qtp1582527589-152] n.d.m.c.a.CompanyAuthenticationService   : User max.vnhph is logged in successfully system
2025-08-04T17:24:02.603+07:00  INFO 80364 --- [qtp1582527589-41] n.d.m.c.a.CompanyAuthenticationService   : User haunt is logged in successfully system
2025-08-04T17:24:02.635+07:00  INFO 80364 --- [qtp1582527589-192] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = 6e3ebf1304655685e244c85a6bd075cb
2025-08-04T17:24:02.643+07:00  INFO 80364 --- [qtp1582527589-195] n.d.module.session.ClientSessionManager  : Add a client session id = node0j9c4c3f11aq5hdp7tm9hxd9616, token = fc06f2338d2de7ec25f8fa9070a21af5
2025-08-04T17:24:02.657+07:00  INFO 80364 --- [qtp1582527589-192] n.d.m.c.a.CompanyAuthenticationService   : User max.vnhph is logged in successfully system
2025-08-04T17:24:02.658+07:00  INFO 80364 --- [qtp1582527589-195] n.d.m.c.a.CompanyAuthenticationService   : User haunt is logged in successfully system
2025-08-04T17:24:06.235+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T17:24:16.143+07:00  INFO 80364 --- [qtp1582527589-158] n.d.m.c.a.CompanyAuthenticationService   : User max.vnhph logout successfully 
2025-08-04T17:24:21.998+07:00  INFO 80364 --- [qtp1582527589-206] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = 89d34a29a8361fd8d7427c71f5e8c639
2025-08-04T17:24:22.001+07:00  INFO 80364 --- [qtp1582527589-206] n.d.m.c.a.CompanyAuthenticationService   : User edgar.vnhph is logged in successfully system
2025-08-04T17:24:56.314+07:00  INFO 80364 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T17:24:56.317+07:00  INFO 80364 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T17:25:03.327+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T17:25:03.327+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T17:25:05.604+07:00  INFO 80364 --- [qtp1582527589-187] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-08-04T17:25:59.796+07:00  INFO 80364 --- [qtp1582527589-155] n.d.module.session.ClientSessionManager  : Add a client session id = node0j9c4c3f11aq5hdp7tm9hxd9616, token = fc06f2338d2de7ec25f8fa9070a21af5
2025-08-04T17:25:59.814+07:00  INFO 80364 --- [qtp1582527589-155] n.d.m.c.a.CompanyAuthenticationService   : User haunt is logged in successfully system
2025-08-04T17:25:59.834+07:00  INFO 80364 --- [qtp1582527589-159] n.d.module.session.ClientSessionManager  : Add a client session id = node0j9c4c3f11aq5hdp7tm9hxd9616, token = fc06f2338d2de7ec25f8fa9070a21af5
2025-08-04T17:25:59.842+07:00  INFO 80364 --- [qtp1582527589-159] n.d.m.c.a.CompanyAuthenticationService   : User haunt is logged in successfully system
2025-08-04T17:26:00.610+07:00  INFO 80364 --- [qtp1582527589-187] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = 89d34a29a8361fd8d7427c71f5e8c639
2025-08-04T17:26:00.610+07:00  INFO 80364 --- [qtp1582527589-67] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = 89d34a29a8361fd8d7427c71f5e8c639
2025-08-04T17:26:00.611+07:00  INFO 80364 --- [qtp1582527589-195] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = 89d34a29a8361fd8d7427c71f5e8c639
2025-08-04T17:26:00.611+07:00  INFO 80364 --- [qtp1582527589-152] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = 89d34a29a8361fd8d7427c71f5e8c639
2025-08-04T17:26:00.627+07:00  INFO 80364 --- [qtp1582527589-187] n.d.m.c.a.CompanyAuthenticationService   : User edgar.vnhph is logged in successfully system
2025-08-04T17:26:00.629+07:00  INFO 80364 --- [qtp1582527589-67] n.d.m.c.a.CompanyAuthenticationService   : User edgar.vnhph is logged in successfully system
2025-08-04T17:26:00.650+07:00  INFO 80364 --- [qtp1582527589-152] n.d.m.c.a.CompanyAuthenticationService   : User edgar.vnhph is logged in successfully system
2025-08-04T17:26:00.650+07:00  INFO 80364 --- [qtp1582527589-195] n.d.m.c.a.CompanyAuthenticationService   : User edgar.vnhph is logged in successfully system
2025-08-04T17:26:06.426+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T17:26:25.562+07:00  INFO 80364 --- [qtp1582527589-36] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = 89d34a29a8361fd8d7427c71f5e8c639
2025-08-04T17:26:25.590+07:00  INFO 80364 --- [qtp1582527589-36] n.d.m.c.a.CompanyAuthenticationService   : User edgar.vnhph is logged in successfully system
2025-08-04T17:26:25.616+07:00  INFO 80364 --- [qtp1582527589-197] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = 89d34a29a8361fd8d7427c71f5e8c639
2025-08-04T17:26:25.626+07:00  INFO 80364 --- [qtp1582527589-197] n.d.m.c.a.CompanyAuthenticationService   : User edgar.vnhph is logged in successfully system
2025-08-04T17:26:55.556+07:00  INFO 80364 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 25, expire count 15
2025-08-04T17:26:55.568+07:00  INFO 80364 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T17:27:02.581+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T17:27:15.884+07:00  INFO 80364 --- [qtp1582527589-67] n.d.m.c.a.CompanyAuthenticationService   : User edgar.vnhph logout successfully 
2025-08-04T17:27:29.165+07:00  INFO 80364 --- [qtp1582527589-187] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint CompanyAuthenticationService/logout
2025-08-04T17:27:32.827+07:00  INFO 80364 --- [qtp1582527589-187] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = 8a972047a53f3d95fa970c534a21b387
2025-08-04T17:27:32.835+07:00  INFO 80364 --- [qtp1582527589-187] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T17:28:05.672+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T17:28:54.759+07:00  INFO 80364 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 13, expire count 12
2025-08-04T17:28:54.768+07:00  INFO 80364 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T17:28:56.196+07:00  INFO 80364 --- [qtp1582527589-41] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = 8a972047a53f3d95fa970c534a21b387
2025-08-04T17:28:56.196+07:00  INFO 80364 --- [qtp1582527589-206] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = 8a972047a53f3d95fa970c534a21b387
2025-08-04T17:28:56.210+07:00  INFO 80364 --- [qtp1582527589-41] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T17:28:56.216+07:00  INFO 80364 --- [qtp1582527589-206] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T17:29:06.787+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T17:29:07.706+07:00  INFO 80364 --- [qtp1582527589-206] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = 8a972047a53f3d95fa970c534a21b387
2025-08-04T17:29:07.707+07:00  INFO 80364 --- [qtp1582527589-122] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = 8a972047a53f3d95fa970c534a21b387
2025-08-04T17:29:07.736+07:00  INFO 80364 --- [qtp1582527589-206] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T17:29:07.736+07:00  INFO 80364 --- [qtp1582527589-122] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T17:29:15.300+07:00  INFO 80364 --- [qtp1582527589-158] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = 8a972047a53f3d95fa970c534a21b387
2025-08-04T17:29:15.304+07:00  INFO 80364 --- [qtp1582527589-194] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = 8a972047a53f3d95fa970c534a21b387
2025-08-04T17:29:15.322+07:00  INFO 80364 --- [qtp1582527589-158] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T17:29:15.322+07:00  INFO 80364 --- [qtp1582527589-194] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T17:29:36.969+07:00  INFO 80364 --- [qtp1582527589-158] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = 8a972047a53f3d95fa970c534a21b387
2025-08-04T17:29:36.970+07:00  INFO 80364 --- [qtp1582527589-187] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = 8a972047a53f3d95fa970c534a21b387
2025-08-04T17:29:36.977+07:00  INFO 80364 --- [qtp1582527589-158] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T17:29:36.977+07:00  INFO 80364 --- [qtp1582527589-187] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T17:29:46.562+07:00  INFO 80364 --- [qtp1582527589-187] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = 8a972047a53f3d95fa970c534a21b387
2025-08-04T17:29:46.564+07:00  INFO 80364 --- [qtp1582527589-41] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = 8a972047a53f3d95fa970c534a21b387
2025-08-04T17:29:46.574+07:00  INFO 80364 --- [qtp1582527589-41] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T17:29:46.574+07:00  INFO 80364 --- [qtp1582527589-187] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T17:29:55.427+07:00  INFO 80364 --- [qtp1582527589-187] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = 8a972047a53f3d95fa970c534a21b387
2025-08-04T17:29:55.431+07:00  INFO 80364 --- [qtp1582527589-155] n.d.module.session.ClientSessionManager  : Add a client session id = node01rkv011l5ky89g8dm16q39bz415, token = 8a972047a53f3d95fa970c534a21b387
2025-08-04T17:29:55.449+07:00  INFO 80364 --- [qtp1582527589-187] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T17:29:55.449+07:00  INFO 80364 --- [qtp1582527589-155] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T17:30:04.904+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T17:30:04.907+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T17:30:04.908+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-04T17:30:04.908+07:00  INFO 80364 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 04/08/2025@17:30:04+0700
2025-08-04T17:30:04.931+07:00  INFO 80364 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 04/08/2025@17:30:00+0700 to 04/08/2025@17:45:00+0700
2025-08-04T17:30:04.932+07:00  INFO 80364 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 04/08/2025@17:30:00+0700 to 04/08/2025@17:45:00+0700
2025-08-04T17:30:54.046+07:00  INFO 80364 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 13, expire count 2
2025-08-04T17:30:54.057+07:00  INFO 80364 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T17:31:06.074+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T17:32:04.180+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T17:32:53.293+07:00  INFO 80364 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 2
2025-08-04T17:32:53.337+07:00  INFO 80364 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T17:33:06.359+07:00  INFO 80364 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T17:33:54.129+07:00  INFO 80364 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@5e5b265b{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-08-04T17:33:54.132+07:00  INFO 80364 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-08-04T17:33:54.132+07:00  INFO 80364 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-08-04T17:33:54.132+07:00  INFO 80364 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-08-04T17:33:54.133+07:00  INFO 80364 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-08-04T17:33:54.134+07:00  INFO 80364 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-08-04T17:33:54.134+07:00  INFO 80364 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-08-04T17:33:54.134+07:00  INFO 80364 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-08-04T17:33:54.134+07:00  INFO 80364 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-08-04T17:33:54.134+07:00  INFO 80364 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-08-04T17:33:54.134+07:00  INFO 80364 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-08-04T17:33:54.134+07:00  INFO 80364 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-08-04T17:33:54.134+07:00  INFO 80364 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-08-04T17:33:54.134+07:00  INFO 80364 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-08-04T17:33:54.134+07:00  INFO 80364 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-08-04T17:33:54.134+07:00  INFO 80364 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-08-04T17:33:54.223+07:00  INFO 80364 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T17:33:54.296+07:00  INFO 80364 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-08-04T17:33:54.301+07:00  INFO 80364 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-08-04T17:33:54.332+07:00  INFO 80364 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-04T17:33:54.334+07:00  INFO 80364 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-04T17:33:54.335+07:00  INFO 80364 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-04T17:33:54.336+07:00  INFO 80364 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-08-04T17:33:54.337+07:00  INFO 80364 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-08-04T17:33:54.337+07:00  INFO 80364 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-08-04T17:33:54.337+07:00  INFO 80364 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-08-04T17:33:54.337+07:00  INFO 80364 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-08-04T17:33:54.476+07:00  INFO 80364 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-08-04T17:33:54.476+07:00  INFO 80364 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-08-04T17:33:54.477+07:00  INFO 80364 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-08-04T17:33:54.477+07:00  INFO 80364 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-08-04T17:33:54.477+07:00  INFO 80364 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-08-04T17:33:54.479+07:00  INFO 80364 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@570cef1e{STOPPING}[12.0.15,sto=0]
2025-08-04T17:33:54.485+07:00  INFO 80364 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-04T17:33:54.487+07:00  INFO 80364 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@5068a2b1{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.3851768389252600097/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@3938e876{STOPPED}}
