2025-08-04T16:14:27.761+07:00  INFO 76643 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 76643 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-08-04T16:14:27.761+07:00  INFO 76643 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-08-04T16:14:28.490+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:14:28.553+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 59 ms. Found 22 JPA repository interfaces.
2025-08-04T16:14:28.563+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:14:28.565+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-08-04T16:14:28.565+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:14:28.572+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 9 JPA repository interfaces.
2025-08-04T16:14:28.606+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:14:28.610+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 3 JPA repository interfaces.
2025-08-04T16:14:28.610+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:14:28.614+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-08-04T16:14:28.624+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:14:28.630+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 1 JPA repository interface.
2025-08-04T16:14:28.643+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:14:28.648+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 5 JPA repository interfaces.
2025-08-04T16:14:28.652+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:14:28.655+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-08-04T16:14:28.655+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:14:28.655+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-08-04T16:14:28.661+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:14:28.667+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 10 JPA repository interfaces.
2025-08-04T16:14:28.673+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:14:28.675+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-08-04T16:14:28.676+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:14:28.679+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-08-04T16:14:28.681+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:14:28.689+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-08-04T16:14:28.689+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:14:28.692+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-08-04T16:14:28.692+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:14:28.692+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-08-04T16:14:28.692+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:14:28.693+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-08-04T16:14:28.693+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:14:28.697+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-08-04T16:14:28.697+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:14:28.699+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 2 JPA repository interfaces.
2025-08-04T16:14:28.699+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:14:28.699+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-08-04T16:14:28.699+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:14:28.709+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 19 JPA repository interfaces.
2025-08-04T16:14:28.720+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:14:28.726+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 8 JPA repository interfaces.
2025-08-04T16:14:28.727+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:14:28.729+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-08-04T16:14:28.730+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:14:28.733+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-08-04T16:14:28.734+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:14:28.739+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-08-04T16:14:28.740+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:14:28.743+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-08-04T16:14:28.744+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:14:28.746+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 5 JPA repository interfaces.
2025-08-04T16:14:28.747+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:14:28.751+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-08-04T16:14:28.751+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:14:28.760+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 14 JPA repository interfaces.
2025-08-04T16:14:28.760+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:14:28.772+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 11 ms. Found 20 JPA repository interfaces.
2025-08-04T16:14:28.772+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:14:28.773+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-08-04T16:14:28.779+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:14:28.779+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-08-04T16:14:28.780+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:14:28.787+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-08-04T16:14:28.789+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:14:28.827+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 38 ms. Found 69 JPA repository interfaces.
2025-08-04T16:14:28.827+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:14:28.828+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-08-04T16:14:28.833+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-04T16:14:28.836+07:00  INFO 76643 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-08-04T16:14:29.075+07:00  INFO 76643 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-08-04T16:14:29.080+07:00  INFO 76643 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-08-04T16:14:29.351+07:00  WARN 76643 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-08-04T16:14:29.549+07:00  INFO 76643 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-08-04T16:14:29.551+07:00  INFO 76643 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-08-04T16:14:29.562+07:00  INFO 76643 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-08-04T16:14:29.562+07:00  INFO 76643 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1700 ms
2025-08-04T16:14:29.617+07:00  WARN 76643 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-04T16:14:29.617+07:00  INFO 76643 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-08-04T16:14:29.710+07:00  INFO 76643 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@635f4be1
2025-08-04T16:14:29.711+07:00  INFO 76643 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-08-04T16:14:29.716+07:00  WARN 76643 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-04T16:14:29.716+07:00  INFO 76643 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-08-04T16:14:29.722+07:00  INFO 76643 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@2ea3f905
2025-08-04T16:14:29.722+07:00  INFO 76643 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-08-04T16:14:29.722+07:00  WARN 76643 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-04T16:14:29.722+07:00  INFO 76643 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-04T16:14:30.275+07:00  INFO 76643 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@64dd94ed
2025-08-04T16:14:30.275+07:00  INFO 76643 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-04T16:14:30.275+07:00  WARN 76643 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-04T16:14:30.275+07:00  INFO 76643 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-08-04T16:14:30.346+07:00  INFO 76643 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@67f25c
2025-08-04T16:14:30.346+07:00  INFO 76643 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-08-04T16:14:30.346+07:00  WARN 76643 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-04T16:14:30.346+07:00  INFO 76643 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-08-04T16:14:30.363+07:00  INFO 76643 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@43941806
2025-08-04T16:14:30.363+07:00  INFO 76643 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-08-04T16:14:30.363+07:00  INFO 76643 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '****************************************************', '***********************************************', '**********************************************'
2025-08-04T16:14:30.431+07:00  INFO 76643 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-08-04T16:14:30.434+07:00  INFO 76643 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@5068a2b1{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.12892799069959139935/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@3938e876{STARTED}}
2025-08-04T16:14:30.435+07:00  INFO 76643 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@5068a2b1{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.12892799069959139935/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@3938e876{STARTED}}
2025-08-04T16:14:30.437+07:00  INFO 76643 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@7da4c956{STARTING}[12.0.15,sto=0] @3324ms
2025-08-04T16:14:30.574+07:00  INFO 76643 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-04T16:14:30.627+07:00  INFO 76643 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-08-04T16:14:30.666+07:00  INFO 76643 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-04T16:14:30.872+07:00  INFO 76643 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-04T16:14:30.909+07:00  WARN 76643 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-04T16:14:31.680+07:00  INFO 76643 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-04T16:14:31.689+07:00  INFO 76643 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@a471163] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-08-04T16:14:31.842+07:00  INFO 76643 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-04T16:14:32.041+07:00  INFO 76643 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.graphapi", "net.datatp.module.kpi", "net.datatp.module.monitor.activity", "net.datatp.module.wfms", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "cloud.datatp.module.fleet", "net.datatp.module.okr", "net.datatp.module.core.template", "cloud.datatp.module.pegasus.shipment", "cloud.datatp.gps", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "net.datatp.module.company.tmpl", "net.datatp.module.groovy", "cloud.datatp.tms", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "net.datatp.module.resource", "cloud.datatp.vendor", "net.datatp.module.hr", "net.datatp.module.company.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "net.datatp.module.core.print", "cloud.datatp.module.odoo", "net.datatp.module.chatbot", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.accounting", "net.datatp.module.zalo" ]
2025-08-04T16:14:32.043+07:00  INFO 76643 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-08-04T16:14:32.050+07:00  INFO 76643 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-04T16:14:32.051+07:00  INFO 76643 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-04T16:14:32.084+07:00  INFO 76643 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-04T16:14:32.090+07:00  WARN 76643 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-04T16:14:34.192+07:00  INFO 76643 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-04T16:14:34.193+07:00  INFO 76643 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@7e67eb70] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-08-04T16:14:34.594+07:00  WARN 76643 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-08-04T16:14:34.594+07:00  WARN 76643 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-08-04T16:14:34.604+07:00  WARN 76643 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-08-04T16:14:34.604+07:00  WARN 76643 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-08-04T16:14:34.619+07:00  WARN 76643 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-08-04T16:14:34.619+07:00  WARN 76643 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-08-04T16:14:35.075+07:00  INFO 76643 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-04T16:14:35.081+07:00  INFO 76643 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-04T16:14:35.083+07:00  INFO 76643 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-04T16:14:35.099+07:00  INFO 76643 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-04T16:14:35.108+07:00  WARN 76643 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-04T16:14:35.609+07:00  INFO 76643 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-04T16:14:35.610+07:00  INFO 76643 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@77de3e3d] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-08-04T16:14:35.687+07:00  WARN 76643 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-08-04T16:14:35.687+07:00  WARN 76643 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-08-04T16:14:36.006+07:00  INFO 76643 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-04T16:14:36.041+07:00  INFO 76643 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-08-04T16:14:36.046+07:00  INFO 76643 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-08-04T16:14:36.046+07:00  INFO 76643 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-08-04T16:14:36.053+07:00  WARN 76643 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-08-04T16:14:36.186+07:00  INFO 76643 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-08-04T16:14:36.669+07:00  INFO 76643 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-08-04T16:14:36.672+07:00  INFO 76643 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-08-04T16:14:36.708+07:00  INFO 76643 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-08-04T16:14:36.753+07:00  INFO 76643 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-08-04T16:14:36.862+07:00  INFO 76643 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-08-04T16:14:36.890+07:00  INFO 76643 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-08-04T16:14:36.919+07:00  INFO 76643 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 189058280ms : this is harmless.
2025-08-04T16:14:36.928+07:00  INFO 76643 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-08-04T16:14:36.931+07:00  INFO 76643 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-08-04T16:14:36.952+07:00  INFO 76643 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 871290082ms : this is harmless.
2025-08-04T16:14:36.954+07:00  INFO 76643 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-08-04T16:14:36.969+07:00  INFO 76643 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-08-04T16:14:36.970+07:00  INFO 76643 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-08-04T16:14:39.275+07:00  INFO 76643 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-08-04T16:14:39.275+07:00  INFO 76643 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-08-04T16:14:39.276+07:00  WARN 76643 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-08-04T16:14:39.432+07:00  INFO 76643 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 04/08/2025@16:00:00+0700 to 04/08/2025@16:15:00+0700
2025-08-04T16:14:39.433+07:00  INFO 76643 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 04/08/2025@16:00:00+0700 to 04/08/2025@16:15:00+0700
2025-08-04T16:14:39.873+07:00  INFO 76643 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-08-04T16:14:39.873+07:00  INFO 76643 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-08-04T16:14:39.873+07:00  WARN 76643 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-08-04T16:14:40.092+07:00  INFO 76643 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-08-04T16:14:40.093+07:00  INFO 76643 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-08-04T16:14:40.093+07:00  INFO 76643 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-08-04T16:14:40.093+07:00  INFO 76643 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-08-04T16:14:40.093+07:00  INFO 76643 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-08-04T16:14:41.822+07:00  WARN 76643 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 2c404ea1-1b88-4cb5-9577-2d61ab733a3b

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-04T16:14:41.825+07:00  INFO 76643 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-04T16:14:42.156+07:00  INFO 76643 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-08-04T16:14:42.156+07:00  INFO 76643 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-08-04T16:14:42.156+07:00  INFO 76643 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-08-04T16:14:42.156+07:00  INFO 76643 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-08-04T16:14:42.156+07:00  INFO 76643 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-08-04T16:14:42.156+07:00  INFO 76643 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-08-04T16:14:42.156+07:00  INFO 76643 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-08-04T16:14:42.156+07:00  INFO 76643 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-08-04T16:14:42.156+07:00  INFO 76643 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-08-04T16:14:42.156+07:00  INFO 76643 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-08-04T16:14:42.156+07:00  INFO 76643 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-08-04T16:14:42.156+07:00  INFO 76643 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-08-04T16:14:42.159+07:00  INFO 76643 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-08-04T16:14:42.159+07:00  INFO 76643 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-08-04T16:14:42.159+07:00  INFO 76643 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-08-04T16:14:42.263+07:00  INFO 76643 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-04T16:14:42.263+07:00  INFO 76643 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-04T16:14:42.265+07:00  INFO 76643 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 2 ms
2025-08-04T16:14:42.274+07:00  INFO 76643 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@17ce0484{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-08-04T16:14:42.275+07:00  INFO 76643 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-08-04T16:14:42.276+07:00  INFO 76643 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-08-04T16:14:42.306+07:00  INFO 76643 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-08-04T16:14:42.306+07:00  INFO 76643 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-08-04T16:14:42.312+07:00  INFO 76643 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 14.854 seconds (process running for 15.199)
2025-08-04T16:14:47.913+07:00  INFO 76643 --- [qtp85102332-35] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-08-04T16:14:49.368+07:00  INFO 76643 --- [qtp85102332-40] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0kinxu8q03ifaco0l2vx6o6q51
2025-08-04T16:14:49.367+07:00  INFO 76643 --- [qtp85102332-35] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0tqaicbak73na8e90au6fr43m0
2025-08-04T16:14:49.414+07:00  INFO 76643 --- [qtp85102332-35] n.d.module.session.ClientSessionManager  : Add a client session id = node0tqaicbak73na8e90au6fr43m0, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T16:14:49.432+07:00  INFO 76643 --- [qtp85102332-40] n.d.module.session.ClientSessionManager  : Add a client session id = node0kinxu8q03ifaco0l2vx6o6q51, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T16:14:49.851+07:00  INFO 76643 --- [qtp85102332-35] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:14:49.858+07:00  INFO 76643 --- [qtp85102332-40] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:15:03.207+07:00  INFO 76643 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T16:15:03.209+07:00  INFO 76643 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-04T16:15:03.210+07:00  INFO 76643 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T16:15:03.211+07:00  INFO 76643 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 04/08/2025@16:15:03+0700
2025-08-04T16:15:03.217+07:00  INFO 76643 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 04/08/2025@16:15:00+0700 to 04/08/2025@16:30:00+0700
2025-08-04T16:15:03.217+07:00  INFO 76643 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 04/08/2025@16:15:00+0700 to 04/08/2025@16:30:00+0700
2025-08-04T16:15:45.355+07:00  INFO 76643 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 6, expire count 0
2025-08-04T16:15:45.366+07:00  INFO 76643 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T16:16:06.400+07:00  INFO 76643 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T16:17:02.495+07:00  INFO 76643 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T16:17:44.589+07:00  INFO 76643 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T16:17:44.593+07:00  INFO 76643 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T16:17:49.135+07:00  INFO 76643 --- [qtp85102332-64] n.d.module.session.ClientSessionManager  : Add a client session id = node0kinxu8q03ifaco0l2vx6o6q51, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T16:17:49.139+07:00  INFO 76643 --- [qtp85102332-60] n.d.module.session.ClientSessionManager  : Add a client session id = node0kinxu8q03ifaco0l2vx6o6q51, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T16:17:49.158+07:00  INFO 76643 --- [qtp85102332-60] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:17:49.158+07:00  INFO 76643 --- [qtp85102332-64] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:18:05.708+07:00  INFO 76643 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T16:19:06.816+07:00  INFO 76643 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T16:19:18.487+07:00  INFO 76643 --- [qtp85102332-36] n.d.module.session.ClientSessionManager  : Add a client session id = node0kinxu8q03ifaco0l2vx6o6q51, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T16:19:18.489+07:00  INFO 76643 --- [qtp85102332-61] n.d.module.session.ClientSessionManager  : Add a client session id = node0kinxu8q03ifaco0l2vx6o6q51, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T16:19:18.498+07:00  INFO 76643 --- [qtp85102332-61] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:19:18.499+07:00  INFO 76643 --- [qtp85102332-36] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:19:38.232+07:00  INFO 76643 --- [qtp85102332-61] n.d.module.session.ClientSessionManager  : Add a client session id = node0kinxu8q03ifaco0l2vx6o6q51, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T16:19:38.278+07:00  INFO 76643 --- [qtp85102332-61] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:19:38.293+07:00  INFO 76643 --- [qtp85102332-67] n.d.module.session.ClientSessionManager  : Add a client session id = node0kinxu8q03ifaco0l2vx6o6q51, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T16:19:38.353+07:00  INFO 76643 --- [qtp85102332-67] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:19:45.732+07:00  INFO 76643 --- [qtp85102332-39] n.d.module.session.ClientSessionManager  : Add a client session id = node0kinxu8q03ifaco0l2vx6o6q51, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T16:19:45.747+07:00  INFO 76643 --- [qtp85102332-70] n.d.module.session.ClientSessionManager  : Add a client session id = node0kinxu8q03ifaco0l2vx6o6q51, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T16:19:45.764+07:00  INFO 76643 --- [qtp85102332-39] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:19:45.764+07:00  INFO 76643 --- [qtp85102332-70] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:19:48.917+07:00  INFO 76643 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-08-04T16:19:48.924+07:00  INFO 76643 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T16:20:00.839+07:00  INFO 76643 --- [qtp85102332-70] n.d.module.session.ClientSessionManager  : Add a client session id = node0kinxu8q03ifaco0l2vx6o6q51, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T16:20:00.856+07:00  INFO 76643 --- [qtp85102332-70] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:20:00.876+07:00  INFO 76643 --- [qtp85102332-34] n.d.module.session.ClientSessionManager  : Add a client session id = node0kinxu8q03ifaco0l2vx6o6q51, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T16:20:00.885+07:00  INFO 76643 --- [qtp85102332-34] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:20:05.065+07:00  INFO 76643 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T16:20:05.067+07:00  INFO 76643 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T16:20:32.091+07:00  INFO 76643 --- [qtp85102332-60] n.d.module.session.ClientSessionManager  : Add a client session id = node0kinxu8q03ifaco0l2vx6o6q51, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T16:20:32.108+07:00  INFO 76643 --- [qtp85102332-60] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:20:32.132+07:00  INFO 76643 --- [qtp85102332-62] n.d.module.session.ClientSessionManager  : Add a client session id = node0kinxu8q03ifaco0l2vx6o6q51, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T16:20:32.137+07:00  INFO 76643 --- [qtp85102332-62] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:20:46.805+07:00  INFO 76643 --- [qtp85102332-36] n.d.module.session.ClientSessionManager  : Add a client session id = node0kinxu8q03ifaco0l2vx6o6q51, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T16:20:46.806+07:00  INFO 76643 --- [qtp85102332-39] n.d.module.session.ClientSessionManager  : Add a client session id = node0kinxu8q03ifaco0l2vx6o6q51, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T16:20:46.827+07:00  INFO 76643 --- [qtp85102332-36] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:20:46.828+07:00  INFO 76643 --- [qtp85102332-39] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:21:06.172+07:00  INFO 76643 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T16:21:33.522+07:00  INFO 76643 --- [qtp85102332-60] n.d.module.session.ClientSessionManager  : Add a client session id = node0kinxu8q03ifaco0l2vx6o6q51, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T16:21:33.524+07:00  INFO 76643 --- [qtp85102332-70] n.d.module.session.ClientSessionManager  : Add a client session id = node0kinxu8q03ifaco0l2vx6o6q51, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T16:21:33.537+07:00  INFO 76643 --- [qtp85102332-60] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:21:33.541+07:00  INFO 76643 --- [qtp85102332-70] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:21:43.135+07:00  INFO 76643 --- [qtp85102332-62] n.d.module.session.ClientSessionManager  : Add a client session id = node0kinxu8q03ifaco0l2vx6o6q51, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T16:21:43.149+07:00  INFO 76643 --- [qtp85102332-62] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:21:43.163+07:00  INFO 76643 --- [qtp85102332-37] n.d.module.session.ClientSessionManager  : Add a client session id = node0kinxu8q03ifaco0l2vx6o6q51, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T16:21:43.176+07:00  INFO 76643 --- [qtp85102332-37] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:21:49.260+07:00  INFO 76643 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-08-04T16:21:49.271+07:00  INFO 76643 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T16:22:04.299+07:00  INFO 76643 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T16:23:05.599+07:00  INFO 76643 --- [qtp85102332-40] n.d.module.session.ClientSessionManager  : Add a client session id = node0kinxu8q03ifaco0l2vx6o6q51, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T16:23:05.615+07:00  INFO 76643 --- [qtp85102332-40] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:23:05.625+07:00  INFO 76643 --- [qtp85102332-77] n.d.module.session.ClientSessionManager  : Add a client session id = node0kinxu8q03ifaco0l2vx6o6q51, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T16:23:05.636+07:00  INFO 76643 --- [qtp85102332-77] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:23:06.406+07:00  INFO 76643 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T16:23:23.930+07:00  INFO 76643 --- [qtp85102332-77] n.d.module.session.ClientSessionManager  : Add a client session id = node0kinxu8q03ifaco0l2vx6o6q51, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T16:23:23.931+07:00  INFO 76643 --- [qtp85102332-37] n.d.module.session.ClientSessionManager  : Add a client session id = node0kinxu8q03ifaco0l2vx6o6q51, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T16:23:23.950+07:00  INFO 76643 --- [qtp85102332-37] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:23:23.950+07:00  INFO 76643 --- [qtp85102332-77] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:23:37.659+07:00  INFO 76643 --- [qtp85102332-78] n.d.module.session.ClientSessionManager  : Add a client session id = node0kinxu8q03ifaco0l2vx6o6q51, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T16:23:37.660+07:00  INFO 76643 --- [qtp85102332-69] n.d.module.session.ClientSessionManager  : Add a client session id = node0kinxu8q03ifaco0l2vx6o6q51, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T16:23:37.670+07:00  INFO 76643 --- [qtp85102332-69] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:23:37.670+07:00  INFO 76643 --- [qtp85102332-78] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:23:48.558+07:00  INFO 76643 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-08-04T16:23:48.564+07:00  INFO 76643 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T16:23:52.309+07:00  INFO 76643 --- [qtp85102332-77] n.d.module.session.ClientSessionManager  : Add a client session id = node0kinxu8q03ifaco0l2vx6o6q51, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T16:23:52.310+07:00  INFO 76643 --- [qtp85102332-69] n.d.module.session.ClientSessionManager  : Add a client session id = node0kinxu8q03ifaco0l2vx6o6q51, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T16:23:52.324+07:00  INFO 76643 --- [qtp85102332-77] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:23:52.324+07:00  INFO 76643 --- [qtp85102332-69] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:24:03.588+07:00  INFO 76643 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T16:24:03.672+07:00  INFO 76643 --- [qtp85102332-78] n.d.module.session.ClientSessionManager  : Add a client session id = node0kinxu8q03ifaco0l2vx6o6q51, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T16:24:03.672+07:00  INFO 76643 --- [qtp85102332-61] n.d.module.session.ClientSessionManager  : Add a client session id = node0kinxu8q03ifaco0l2vx6o6q51, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T16:24:03.703+07:00  INFO 76643 --- [qtp85102332-61] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:24:03.703+07:00  INFO 76643 --- [qtp85102332-78] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:24:09.596+07:00  INFO 76643 --- [qtp85102332-78] n.d.module.session.ClientSessionManager  : Add a client session id = node0kinxu8q03ifaco0l2vx6o6q51, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T16:24:09.599+07:00  INFO 76643 --- [qtp85102332-69] n.d.module.session.ClientSessionManager  : Add a client session id = node0kinxu8q03ifaco0l2vx6o6q51, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T16:24:09.607+07:00  INFO 76643 --- [qtp85102332-78] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:24:09.607+07:00  INFO 76643 --- [qtp85102332-69] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:24:30.440+07:00  INFO 76643 --- [Scheduler-1390121014-1] n.d.m.session.AppHttpSessionListener     : The session node0tqaicbak73na8e90au6fr43m0 is destroyed.
2025-08-04T16:24:47.629+07:00  INFO 76643 --- [qtp85102332-40] n.d.module.session.ClientSessionManager  : Add a client session id = node0kinxu8q03ifaco0l2vx6o6q51, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T16:24:47.629+07:00  INFO 76643 --- [qtp85102332-69] n.d.module.session.ClientSessionManager  : Add a client session id = node0kinxu8q03ifaco0l2vx6o6q51, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T16:24:47.645+07:00  INFO 76643 --- [qtp85102332-40] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:24:47.645+07:00  INFO 76643 --- [qtp85102332-69] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:24:49.957+07:00  INFO 76643 --- [qtp85102332-77] n.d.module.session.ClientSessionManager  : Add a client session id = node0kinxu8q03ifaco0l2vx6o6q51, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T16:24:49.957+07:00  INFO 76643 --- [qtp85102332-69] n.d.module.session.ClientSessionManager  : Add a client session id = node0kinxu8q03ifaco0l2vx6o6q51, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T16:24:49.960+07:00  INFO 76643 --- [qtp85102332-69] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:24:49.960+07:00  INFO 76643 --- [qtp85102332-77] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:25:06.695+07:00  INFO 76643 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-04T16:25:06.696+07:00  INFO 76643 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T16:25:27.026+07:00  INFO 76643 --- [qtp85102332-40] n.d.module.session.ClientSessionManager  : Add a client session id = node0kinxu8q03ifaco0l2vx6o6q51, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T16:25:27.038+07:00  INFO 76643 --- [qtp85102332-40] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:25:27.061+07:00  INFO 76643 --- [qtp85102332-72] n.d.module.session.ClientSessionManager  : Add a client session id = node0kinxu8q03ifaco0l2vx6o6q51, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T16:25:27.066+07:00  INFO 76643 --- [qtp85102332-72] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:25:27.539+07:00  INFO 76643 --- [qtp85102332-70] n.d.module.session.ClientSessionManager  : Add a client session id = node0kinxu8q03ifaco0l2vx6o6q51, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T16:25:27.553+07:00  INFO 76643 --- [qtp85102332-70] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:25:27.576+07:00  INFO 76643 --- [qtp85102332-65] n.d.module.session.ClientSessionManager  : Add a client session id = node0kinxu8q03ifaco0l2vx6o6q51, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T16:25:27.594+07:00  INFO 76643 --- [qtp85102332-65] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:25:39.565+07:00  INFO 76643 --- [qtp85102332-40] n.d.module.session.ClientSessionManager  : Add a client session id = node0kinxu8q03ifaco0l2vx6o6q51, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T16:25:39.566+07:00  INFO 76643 --- [qtp85102332-77] n.d.module.session.ClientSessionManager  : Add a client session id = node0kinxu8q03ifaco0l2vx6o6q51, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T16:25:39.576+07:00  INFO 76643 --- [qtp85102332-40] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:25:39.576+07:00  INFO 76643 --- [qtp85102332-77] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:25:40.539+07:00  INFO 76643 --- [qtp85102332-61] n.d.module.session.ClientSessionManager  : Add a client session id = node0kinxu8q03ifaco0l2vx6o6q51, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T16:25:40.551+07:00  INFO 76643 --- [qtp85102332-69] n.d.module.session.ClientSessionManager  : Add a client session id = node0kinxu8q03ifaco0l2vx6o6q51, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T16:25:40.555+07:00  INFO 76643 --- [qtp85102332-61] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:25:40.555+07:00  INFO 76643 --- [qtp85102332-69] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:25:48.517+07:00  INFO 76643 --- [qtp85102332-77] n.d.module.session.ClientSessionManager  : Add a client session id = node0kinxu8q03ifaco0l2vx6o6q51, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T16:25:48.518+07:00  INFO 76643 --- [qtp85102332-70] n.d.module.session.ClientSessionManager  : Add a client session id = node0kinxu8q03ifaco0l2vx6o6q51, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T16:25:48.524+07:00  INFO 76643 --- [qtp85102332-77] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:25:48.525+07:00  INFO 76643 --- [qtp85102332-70] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:25:48.772+07:00  INFO 76643 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 6, expire count 3
2025-08-04T16:25:48.777+07:00  INFO 76643 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T16:25:49.537+07:00  INFO 76643 --- [qtp85102332-70] n.d.module.session.ClientSessionManager  : Add a client session id = node0kinxu8q03ifaco0l2vx6o6q51, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T16:25:49.538+07:00  INFO 76643 --- [qtp85102332-40] n.d.module.session.ClientSessionManager  : Add a client session id = node0kinxu8q03ifaco0l2vx6o6q51, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T16:25:49.543+07:00  INFO 76643 --- [qtp85102332-70] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:25:49.543+07:00  INFO 76643 --- [qtp85102332-40] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:26:02.795+07:00  INFO 76643 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T16:26:13.379+07:00  INFO 76643 --- [qtp85102332-72] n.d.module.session.ClientSessionManager  : Add a client session id = node0kinxu8q03ifaco0l2vx6o6q51, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T16:26:13.380+07:00  INFO 76643 --- [qtp85102332-69] n.d.module.session.ClientSessionManager  : Add a client session id = node0kinxu8q03ifaco0l2vx6o6q51, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T16:26:13.386+07:00  INFO 76643 --- [qtp85102332-69] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:26:13.386+07:00  INFO 76643 --- [qtp85102332-72] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:26:14.536+07:00  INFO 76643 --- [qtp85102332-65] n.d.module.session.ClientSessionManager  : Add a client session id = node0kinxu8q03ifaco0l2vx6o6q51, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T16:26:14.536+07:00  INFO 76643 --- [qtp85102332-70] n.d.module.session.ClientSessionManager  : Add a client session id = node0kinxu8q03ifaco0l2vx6o6q51, token = 8a18dc73d7a1a501ee39bb465ba16771
2025-08-04T16:26:14.541+07:00  INFO 76643 --- [qtp85102332-70] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:26:14.541+07:00  INFO 76643 --- [qtp85102332-65] n.d.m.c.a.CompanyAuthenticationService   : User nhat.le is logged in successfully system
2025-08-04T16:27:05.906+07:00  INFO 76643 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T16:27:48.021+07:00  INFO 76643 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-04T16:27:48.036+07:00  INFO 76643 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T16:28:02.062+07:00  INFO 76643 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-04T16:28:20.961+07:00  INFO 76643 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@17ce0484{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-08-04T16:28:20.963+07:00  INFO 76643 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-08-04T16:28:20.963+07:00  INFO 76643 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-08-04T16:28:20.963+07:00  INFO 76643 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-08-04T16:28:20.963+07:00  INFO 76643 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-08-04T16:28:20.963+07:00  INFO 76643 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-08-04T16:28:20.963+07:00  INFO 76643 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-08-04T16:28:20.963+07:00  INFO 76643 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-08-04T16:28:20.963+07:00  INFO 76643 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-08-04T16:28:20.963+07:00  INFO 76643 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-08-04T16:28:20.963+07:00  INFO 76643 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-08-04T16:28:20.963+07:00  INFO 76643 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-08-04T16:28:20.963+07:00  INFO 76643 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-08-04T16:28:20.963+07:00  INFO 76643 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-08-04T16:28:20.963+07:00  INFO 76643 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-08-04T16:28:20.963+07:00  INFO 76643 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-08-04T16:28:20.976+07:00  INFO 76643 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-04T16:28:21.043+07:00  INFO 76643 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-08-04T16:28:21.047+07:00  INFO 76643 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-08-04T16:28:21.069+07:00  INFO 76643 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-04T16:28:21.071+07:00  INFO 76643 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-04T16:28:21.074+07:00  INFO 76643 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-04T16:28:21.075+07:00  INFO 76643 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-08-04T16:28:21.076+07:00  INFO 76643 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-08-04T16:28:21.076+07:00  INFO 76643 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-08-04T16:28:21.076+07:00  INFO 76643 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-08-04T16:28:21.076+07:00  INFO 76643 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-08-04T16:28:21.217+07:00  INFO 76643 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-08-04T16:28:21.217+07:00  INFO 76643 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-08-04T16:28:21.218+07:00  INFO 76643 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-08-04T16:28:21.218+07:00  INFO 76643 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-08-04T16:28:21.219+07:00  INFO 76643 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-08-04T16:28:21.221+07:00  INFO 76643 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@7da4c956{STOPPING}[12.0.15,sto=0]
2025-08-04T16:28:21.224+07:00  INFO 76643 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-04T16:28:21.229+07:00  INFO 76643 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@5068a2b1{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.12892799069959139935/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@3938e876{STOPPED}}
