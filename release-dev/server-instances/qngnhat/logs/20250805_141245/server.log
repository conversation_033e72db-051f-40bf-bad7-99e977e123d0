2025-08-05T14:12:45.905+07:00  INFO 28773 --- [main] net.datatp.server.ServerApp              : Starting ServerApp using Java 21.0.6 with PID 28773 (/Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/lib/datatp-erp-app-core-1.0.0.jar started by qngnhat in /Users/<USER>/nez/code/datatp/working/release-dev/server)
2025-08-05T14:12:45.906+07:00  INFO 28773 --- [main] net.datatp.server.ServerApp              : The following 10 profiles are active: "core", "datatp-crm", "document-ie", "logistics", "prod-update", "database", "db-schema-update", "data", "log-debug", "log-info-file"
2025-08-05T14:12:46.583+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:12:46.644+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 57 ms. Found 22 JPA repository interfaces.
2025-08-05T14:12:46.655+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:12:46.656+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-08-05T14:12:46.657+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:12:46.697+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 40 ms. Found 9 JPA repository interfaces.
2025-08-05T14:12:46.698+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:12:46.701+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 3 JPA repository interfaces.
2025-08-05T14:12:46.702+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:12:46.705+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-08-05T14:12:46.715+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:12:46.720+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 1 JPA repository interface.
2025-08-05T14:12:46.728+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:12:46.732+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 5 JPA repository interfaces.
2025-08-05T14:12:46.736+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:12:46.738+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-08-05T14:12:46.739+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:12:46.739+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-08-05T14:12:46.743+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:12:46.750+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 10 JPA repository interfaces.
2025-08-05T14:12:46.755+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:12:46.758+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 3 JPA repository interfaces.
2025-08-05T14:12:46.758+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:12:46.762+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-08-05T14:12:46.763+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:12:46.771+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 7 ms. Found 12 JPA repository interfaces.
2025-08-05T14:12:46.771+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:12:46.774+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-08-05T14:12:46.774+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:12:46.774+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-08-05T14:12:46.774+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:12:46.775+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-08-05T14:12:46.775+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:12:46.779+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 7 JPA repository interfaces.
2025-08-05T14:12:46.779+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:12:46.782+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 2 JPA repository interfaces.
2025-08-05T14:12:46.782+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:12:46.782+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-08-05T14:12:46.782+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:12:46.793+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 19 JPA repository interfaces.
2025-08-05T14:12:46.802+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:12:46.808+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 8 JPA repository interfaces.
2025-08-05T14:12:46.808+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:12:46.811+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 5 JPA repository interfaces.
2025-08-05T14:12:46.811+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:12:46.817+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 7 JPA repository interfaces.
2025-08-05T14:12:46.817+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:12:46.823+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 5 ms. Found 9 JPA repository interfaces.
2025-08-05T14:12:46.823+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:12:46.827+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 6 JPA repository interfaces.
2025-08-05T14:12:46.828+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:12:46.831+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 3 ms. Found 5 JPA repository interfaces.
2025-08-05T14:12:46.831+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:12:46.836+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 4 ms. Found 7 JPA repository interfaces.
2025-08-05T14:12:46.837+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:12:46.847+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 10 ms. Found 14 JPA repository interfaces.
2025-08-05T14:12:46.848+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:12:46.860+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 12 ms. Found 20 JPA repository interfaces.
2025-08-05T14:12:46.861+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:12:46.862+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 1 JPA repository interface.
2025-08-05T14:12:46.865+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:12:46.866+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 0 ms. Found 0 JPA repository interfaces.
2025-08-05T14:12:46.866+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:12:46.872+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 6 ms. Found 12 JPA repository interfaces.
2025-08-05T14:12:46.874+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:12:46.910+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 36 ms. Found 69 JPA repository interfaces.
2025-08-05T14:12:46.910+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:12:46.912+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 1 ms. Found 1 JPA repository interface.
2025-08-05T14:12:46.914+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-05T14:12:46.917+07:00  INFO 28773 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 2 ms. Found 4 JPA repository interfaces.
2025-08-05T14:12:47.086+07:00  INFO 28773 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'errorChannel' has been explicitly defined. Therefore, a default PublishSubscribeChannel will be created.
2025-08-05T14:12:47.094+07:00  INFO 28773 --- [main] faultConfiguringBeanFactoryPostProcessor : No bean named 'integrationHeaderChannelRegistry' has been explicitly defined. Therefore, a default DefaultHeaderChannelRegistry will be created.
2025-08-05T14:12:47.385+07:00  WARN 28773 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'net.datatp.module.data.batch.BatchConfiguration' of type [net.datatp.module.data.batch.BatchConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [jobRegistryBeanPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.
2025-08-05T14:12:47.592+07:00  INFO 28773 --- [main] o.s.b.w.e.j.JettyServletWebServerFactory : Server initialized with port: 7080
2025-08-05T14:12:47.594+07:00  INFO 28773 --- [main] org.eclipse.jetty.server.Server          : jetty-12.0.15; built: 2024-11-05T19:44:57.623Z; git: 8281ae9740d4b4225e8166cc476bad237c70213a; jvm 21.0.6+8-LTS-188
2025-08-05T14:12:47.606+07:00  INFO 28773 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring embedded WebApplicationContext
2025-08-05T14:12:47.606+07:00  INFO 28773 --- [main] w.s.c.ServletWebServerApplicationContext : Root WebApplicationContext: initialization completed in 1595 ms
2025-08-05T14:12:47.652+07:00  WARN 28773 --- [main] com.zaxxer.hikari.HikariConfig           : jdbc - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-05T14:12:47.652+07:00  INFO 28773 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Starting...
2025-08-05T14:12:47.837+07:00  INFO 28773 --- [main] com.zaxxer.hikari.pool.HikariPool        : jdbc - Added connection org.postgresql.jdbc.PgConnection@3ba55a6b
2025-08-05T14:12:47.838+07:00  INFO 28773 --- [main] com.zaxxer.hikari.HikariDataSource       : jdbc - Start completed.
2025-08-05T14:12:47.843+07:00  WARN 28773 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-05T14:12:47.843+07:00  INFO 28773 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-08-05T14:12:47.879+07:00  INFO 28773 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@66032b8d
2025-08-05T14:12:47.879+07:00  INFO 28773 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-08-05T14:12:47.879+07:00  WARN 28773 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-1 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-05T14:12:47.879+07:00  INFO 28773 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Starting...
2025-08-05T14:12:49.230+07:00  INFO 28773 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@4cde5d66
2025-08-05T14:12:49.231+07:00  INFO 28773 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Start completed.
2025-08-05T14:12:49.231+07:00  WARN 28773 --- [main] com.zaxxer.hikari.HikariConfig           : HikariPool-2 - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-05T14:12:49.231+07:00  INFO 28773 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Starting...
2025-08-05T14:12:49.239+07:00  INFO 28773 --- [main] com.zaxxer.hikari.pool.HikariPool        : HikariPool-2 - Added connection org.postgresql.jdbc.PgConnection@6e1e9a10
2025-08-05T14:12:49.240+07:00  INFO 28773 --- [main] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Start completed.
2025-08-05T14:12:49.240+07:00  WARN 28773 --- [main] com.zaxxer.hikari.HikariConfig           : rw - idleTimeout is close to or more than maxLifetime, disabling it.
2025-08-05T14:12:49.240+07:00  INFO 28773 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Starting...
2025-08-05T14:12:49.246+07:00  INFO 28773 --- [main] com.zaxxer.hikari.pool.HikariPool        : rw - Added connection org.postgresql.jdbc.PgConnection@5e24f013
2025-08-05T14:12:49.247+07:00  INFO 28773 --- [main] com.zaxxer.hikari.HikariDataSource       : rw - Start completed.
2025-08-05T14:12:49.247+07:00  INFO 28773 --- [main] o.s.b.a.h2.H2ConsoleAutoConfiguration    : H2 console available at '/h2-console'. Databases available at '*****************************************', '*****************************************', '****************************************************', '***********************************************', '**********************************************'
2025-08-05T14:12:49.319+07:00  INFO 28773 --- [main] o.e.j.session.DefaultSessionIdManager    : Session workerName=node0
2025-08-05T14:12:49.321+07:00  INFO 28773 --- [main] o.e.jetty.server.handler.ContextHandler  : Started osbwej.JettyEmbeddedWebAppContext@28ba90c1{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.983429557213245244/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@7545cb17{STARTED}}
2025-08-05T14:12:49.322+07:00  INFO 28773 --- [main] o.e.j.e.servlet.ServletContextHandler    : Started osbwej.JettyEmbeddedWebAppContext@28ba90c1{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.983429557213245244/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@7545cb17{STARTED}}
2025-08-05T14:12:49.323+07:00  INFO 28773 --- [main] org.eclipse.jetty.server.Server          : Started oejs.Server@228c854f{STARTING}[12.0.15,sto=0] @3826ms
2025-08-05T14:12:49.392+07:00  INFO 28773 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-05T14:12:49.421+07:00  INFO 28773 --- [main] org.hibernate.Version                    : HHH000412: Hibernate ORM core version 6.4.4.Final
2025-08-05T14:12:49.440+07:00  INFO 28773 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-05T14:12:49.573+07:00  INFO 28773 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-05T14:12:49.598+07:00  WARN 28773 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-05T14:12:50.210+07:00  INFO 28773 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-05T14:12:50.217+07:00  INFO 28773 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@583b844] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-08-05T14:12:50.350+07:00  INFO 28773 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-05T14:12:50.529+07:00  INFO 28773 --- [main] n.d.module.data.db.JpaConfiguration      : Entity Manager Factory scan packages:
[ "net.datatp.module.project", "cloud.datatp.fleet", "net.datatp.module.app", "net.datatp.module.partner", "net.datatp.module.kpi", "net.datatp.module.graphapi", "net.datatp.module.wfms", "net.datatp.module.monitor.activity", "cloud.datatp.jobtracking", "cloud.datatp.bfsone", "net.datatp.module.monitor.system", "net.datatp.module.dtable", "net.datatp.module.data.aggregation", "net.datatp.module.okr", "cloud.datatp.module.fleet", "net.datatp.module.core.template", "cloud.datatp.gps", "cloud.datatp.module.pegasus.shipment", "net.datatp.module.i18n", "net.datatp.module.bot", "net.datatp.module.workflow", "cloud.datatp.tms", "net.datatp.module.company.tmpl", "net.datatp.module.groovy", "cloud.datatp.fforwarder.settings", "net.datatp.module.data.db", "net.datatp.module.core.security", "net.datatp.module.data.entity", "net.datatp.module.data.input", "net.datatp.module.communication", "net.datatp.module.asset", "net.datatp.module.hr.kpi", "cloud.datatp.vendor", "net.datatp.module.resource", "net.datatp.module.hr", "net.datatp.module.company.hr", "net.datatp.module.websocket", "net.datatp.module.company", "net.datatp.module.company.web", "net.datatp.module.storage", "net.datatp.module.chat", "net.datatp.module.monitor.call", "net.datatp.module.data.operation", "net.datatp.module.service", "net.datatp.module.chatbot", "cloud.datatp.module.odoo", "net.datatp.module.core.print", "net.datatp.module.settings", "net.datatp.module.account", "net.datatp.module.zalo", "net.datatp.module.accounting" ]
2025-08-05T14:12:50.530+07:00  INFO 28773 --- [main] n.d.module.data.db.JpaConfiguration      : Jpa Hibernate Props: 
 {
  "hibernate.session_factory.interceptor" : { },
  "hibernate.format_sql" : "true",
  "hibernate.enable_lazy_load_no_trans" : "true",
  "hibernate.hbm2ddl.auto" : "update",
  "hibernate.dialect" : "org.hibernate.dialect.PostgreSQLDialect",
  "hibernate.show_sql" : "false",
  "hibernate.connection.provider_disables_autocommit" : "true",
  "hibernate.globally_quoted_identifiers" : "true"
}
2025-08-05T14:12:50.536+07:00  INFO 28773 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-05T14:12:50.537+07:00  INFO 28773 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-05T14:12:50.567+07:00  INFO 28773 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-05T14:12:50.573+07:00  WARN 28773 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-05T14:12:52.454+07:00  INFO 28773 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-05T14:12:52.454+07:00  INFO 28773 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@7002c1c6] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-08-05T14:12:52.637+07:00  WARN 28773 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-08-05T14:12:52.637+07:00  WARN 28773 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-08-05T14:12:52.647+07:00  WARN 28773 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 42622
2025-08-05T14:12:52.647+07:00  WARN 28773 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : identifier "okr_key_result_master_automation_config_attribute_automation_id_name" will be truncated to "okr_key_result_master_automation_config_attribute_automation_id"
2025-08-05T14:12:52.663+07:00  WARN 28773 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-08-05T14:12:52.663+07:00  WARN 28773 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "settings_location_un_locode" of relation "settings_location" does not exist, skipping
2025-08-05T14:12:53.130+07:00  INFO 28773 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-05T14:12:53.136+07:00  INFO 28773 --- [main] o.hibernate.jpa.internal.util.LogHelper  : HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-05T14:12:53.137+07:00  INFO 28773 --- [main] o.h.c.internal.RegionFactoryInitiator    : HHH000026: Second-level cache disabled
2025-08-05T14:12:53.155+07:00  INFO 28773 --- [main] o.s.o.j.p.SpringPersistenceUnitInfo      : No LoadTimeWeaver setup: ignoring JPA class transformer
2025-08-05T14:12:53.161+07:00  WARN 28773 --- [main] org.hibernate.orm.deprecation            : HHH90000025: PostgreSQLDialect does not need to be specified explicitly using 'hibernate.dialect' (remove the property setting and it will be selected by default)
2025-08-05T14:12:53.637+07:00  INFO 28773 --- [main] o.h.e.t.j.p.i.JtaPlatformInitiator       : HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-08-05T14:12:53.637+07:00  INFO 28773 --- [main] org.hibernate.orm.connections.access     : HHH10001501: Connection obtained from JdbcConnectionAccess [org.hibernate.engine.jdbc.env.internal.JdbcEnvironmentInitiator$ConnectionProviderJdbcConnectionAccess@12fd301f] for (non-JTA) DDL execution was not in auto-commit mode; the Connection 'local transaction' will be committed and the Connection will be set into auto-commit mode.
2025-08-05T14:12:53.705+07:00  WARN 28773 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : SQL Warning Code: 0, SQLState: 00000
2025-08-05T14:12:53.705+07:00  WARN 28773 --- [main] o.h.engine.jdbc.spi.SqlExceptionHelper   : constraint "lgc_price_sea_fcl_charge_code" of relation "lgc_price_sea_fcl_charge" does not exist, skipping
2025-08-05T14:12:54.085+07:00  INFO 28773 --- [main] j.LocalContainerEntityManagerFactoryBean : Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-05T14:12:54.117+07:00  INFO 28773 --- [main] n.d.module.data.db.JpaConfiguration      : Create PlatformTransactionManager name = transactionManager
2025-08-05T14:12:54.122+07:00  INFO 28773 --- [main] n.d.m.d.d.repository.DAOTemplatePrimary  : On Init DAOTemplatePrimary
2025-08-05T14:12:54.122+07:00  INFO 28773 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-08-05T14:12:54.128+07:00  WARN 28773 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-08-05T14:12:54.248+07:00  INFO 28773 --- [main] o.s.d.j.r.query.QueryEnhancerFactory     : Hibernate is in classpath; If applicable, HQL parser will be used.
2025-08-05T14:12:54.678+07:00  INFO 28773 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-08-05T14:12:54.681+07:00  INFO 28773 --- [main] o.e.impl.config.SizedResourcePoolImpl    : Byte based heap resources are deprecated and will be removed in a future version.
2025-08-05T14:12:54.712+07:00  INFO 28773 --- [main] o.e.s.filters.AnnotationSizeOfFilter     : Using regular expression provided through VM argument org.ehcache.sizeof.filters.AnnotationSizeOfFilter.pattern for IgnoreSizeOf annotation : ^.*cache\..*IgnoreSizeOf$
2025-08-05T14:12:54.749+07:00  INFO 28773 --- [main] org.ehcache.sizeof.impl.JvmInformation   : Detected JVM data model settings of: 64-Bit HotSpot JVM with Compressed OOPs
2025-08-05T14:12:54.795+07:00  INFO 28773 --- [main] org.ehcache.sizeof.impl.AgentLoader      : Failed to attach to VM and load the agent: class java.io.IOException: Can not attach to current VM
2025-08-05T14:12:54.822+07:00  INFO 28773 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-08-05T14:12:54.842+07:00  INFO 28773 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=generic}The index for data file ehcache-disk-store.data is more recent than the data file itself by 12509952ms : this is harmless.
2025-08-05T14:12:54.850+07:00  INFO 28773 --- [main] org.ehcache.core.EhcacheManager          : Cache 'generic' created in EhcacheManager.
2025-08-05T14:12:54.863+07:00  INFO 28773 --- [main] .e.s.o.t.o.p.UpfrontAllocatingPageSource : Allocating 100.0MB in chunks
2025-08-05T14:12:54.875+07:00  INFO 28773 --- [main] o.e.i.i.store.disk.OffHeapDiskStore      : {cache-alias=entity}The index for data file ehcache-disk-store.data is more recent than the data file itself by 949864665ms : this is harmless.
2025-08-05T14:12:54.876+07:00  INFO 28773 --- [main] org.ehcache.core.EhcacheManager          : Cache 'entity' created in EhcacheManager.
2025-08-05T14:12:54.887+07:00  INFO 28773 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=generic
2025-08-05T14:12:54.888+07:00  INFO 28773 --- [main] org.ehcache.jsr107.Eh107CacheManager     : Registering Ehcache MBean javax.cache:type=CacheStatistics,CacheManager=urn.X-ehcache.jsr107-default-config,Cache=entity
2025-08-05T14:12:57.023+07:00  INFO 28773 --- [main] c.d.f.core.db.CRMDAOTemplatePrimary      : On Init CRM DAOTemplatePrimary
2025-08-05T14:12:57.023+07:00  INFO 28773 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-08-05T14:12:57.024+07:00  WARN 28773 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-08-05T14:12:57.146+07:00  INFO 28773 --- [main] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 05/08/2025@14:00:00+0700 to 05/08/2025@14:15:00+0700
2025-08-05T14:12:57.146+07:00  INFO 28773 --- [main] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 05/08/2025@14:00:00+0700 to 05/08/2025@14:15:00+0700
2025-08-05T14:12:57.560+07:00  INFO 28773 --- [main] n.d.m.d.db.DocumentDAOTemplatePrimary    : On Init DAOTemplatePrimary
2025-08-05T14:12:57.560+07:00  INFO 28773 --- [main] n.d.m.data.db.repository.DAOTemplate     : On Init DAOTemplatePrimary
2025-08-05T14:12:57.560+07:00  WARN 28773 --- [main] o.s.aop.framework.CglibAopProxy          : Unable to proxy interface-implementing method [public final void org.springframework.dao.support.DaoSupport.afterPropertiesSet() throws java.lang.IllegalArgumentException,org.springframework.beans.factory.BeanInitializationException] because it is marked as final, consider using interface-based JDK proxies instead.
2025-08-05T14:12:57.745+07:00  INFO 28773 --- [main] n.d.m.core.template.TemplateService      : onInit()
2025-08-05T14:12:57.745+07:00  INFO 28773 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/core/templates
2025-08-05T14:12:57.745+07:00  INFO 28773 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/datatp-crm/templates
2025-08-05T14:12:57.745+07:00  INFO 28773 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/logistics/templates
2025-08-05T14:12:57.745+07:00  INFO 28773 --- [main] n.d.m.core.template.TemplateService      : Template Dir /Users/<USER>/nez/code/datatp/working/release-dev/server/addons/document-ie/templates
2025-08-05T14:12:59.305+07:00  WARN 28773 --- [main] .s.s.UserDetailsServiceAutoConfiguration : 

Using generated security password: 91786537-0a13-4d20-a804-b98e32202956

This generated password is for development use only. Your security configuration must be updated before running your application in production.

2025-08-05T14:12:59.308+07:00  INFO 28773 --- [main] r$InitializeUserDetailsManagerConfigurer : Global AuthenticationManager configured with UserDetailsService bean with name inMemoryUserDetailsManager
2025-08-05T14:12:59.605+07:00  INFO 28773 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-08-05T14:12:59.605+07:00  INFO 28773 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 1 subscriber(s).
2025-08-05T14:12:59.606+07:00  INFO 28773 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean '_org.springframework.integration.errorLogger'
2025-08-05T14:12:59.606+07:00  INFO 28773 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-08-05T14:12:59.606+07:00  INFO 28773 --- [main] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 1 subscriber(s).
2025-08-05T14:12:59.606+07:00  INFO 28773 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'monitorTaskEventHandler.serviceActivator'
2025-08-05T14:12:59.606+07:00  INFO 28773 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-08-05T14:12:59.606+07:00  INFO 28773 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 1 subscriber(s).
2025-08-05T14:12:59.606+07:00  INFO 28773 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'inputConfigChannelHandler.serviceActivator'
2025-08-05T14:12:59.606+07:00  INFO 28773 --- [main] o.s.i.endpoint.EventDrivenConsumer       : Adding {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-08-05T14:12:59.606+07:00  INFO 28773 --- [main] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 1 subscriber(s).
2025-08-05T14:12:59.606+07:00  INFO 28773 --- [main] o.s.i.endpoint.EventDrivenConsumer       : started bean 'botEventMessageHandler.serviceActivator'
2025-08-05T14:12:59.609+07:00  INFO 28773 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-08-05T14:12:59.609+07:00  INFO 28773 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-08-05T14:12:59.609+07:00  INFO 28773 --- [main] o.s.i.e.SourcePollingChannelAdapter      : started bean 'botEventMessageSource.inboundChannelAdapter'
2025-08-05T14:12:59.622+07:00  INFO 28773 --- [main] o.e.j.s.h.ContextHandler.application     : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-05T14:12:59.622+07:00  INFO 28773 --- [main] o.s.web.servlet.DispatcherServlet        : Initializing Servlet 'dispatcherServlet'
2025-08-05T14:12:59.623+07:00  INFO 28773 --- [main] o.s.web.servlet.DispatcherServlet        : Completed initialization in 1 ms
2025-08-05T14:12:59.631+07:00  INFO 28773 --- [main] o.e.jetty.server.AbstractConnector       : Started ServerConnector@7667afdf{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-08-05T14:12:59.632+07:00  INFO 28773 --- [main] o.s.b.web.embedded.jetty.JettyWebServer  : Jetty started on port 7080 (http/1.1, h2c) with context path '/'
2025-08-05T14:12:59.633+07:00  INFO 28773 --- [main] net.datatp.server.DataInitService        : Start Init Data
2025-08-05T14:12:59.658+07:00  INFO 28773 --- [main] net.datatp.server.DataInitService        : The default company has been successfully initialized
2025-08-05T14:12:59.658+07:00  INFO 28773 --- [main] net.datatp.server.DataInitService        : Init the default data in {0}
2025-08-05T14:12:59.664+07:00  INFO 28773 --- [main] net.datatp.server.ServerApp              : Started ServerApp in 14.016 seconds (process running for 14.167)
2025-08-05T14:13:01.501+07:00  INFO 28773 --- [qtp689240342-34] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-08-05T14:13:06.632+07:00  INFO 28773 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T14:13:54.256+07:00  INFO 28773 --- [qtp689240342-35] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-08-05T14:14:02.756+07:00  INFO 28773 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-05T14:14:02.770+07:00  INFO 28773 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T14:14:02.770+07:00  INFO 28773 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T14:15:05.881+07:00  INFO 28773 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T14:15:05.883+07:00  INFO 28773 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-05T14:15:05.884+07:00  INFO 28773 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-05T14:15:05.887+07:00  INFO 28773 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 05/08/2025@14:15:05+0700
2025-08-05T14:15:05.899+07:00  INFO 28773 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 05/08/2025@14:15:00+0700 to 05/08/2025@14:30:00+0700
2025-08-05T14:15:05.900+07:00  INFO 28773 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 05/08/2025@14:15:00+0700 to 05/08/2025@14:30:00+0700
2025-08-05T14:16:02.009+07:00  INFO 28773 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-05T14:16:02.019+07:00  INFO 28773 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T14:16:07.025+07:00  INFO 28773 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T14:16:47.804+07:00  INFO 28773 --- [qtp689240342-39] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0lrqjmyqzgnabwwdvt8cl67c1
2025-08-05T14:16:47.804+07:00  INFO 28773 --- [qtp689240342-34] n.d.m.session.AppHttpSessionListener     : A new session is created, session id = node0v9pld5ajdsxd1cegscqrvscuw0
2025-08-05T14:16:47.869+07:00  INFO 28773 --- [qtp689240342-34] n.d.module.session.ClientSessionManager  : Add a client session id = node0v9pld5ajdsxd1cegscqrvscuw0, token = c2199c7247b48ac51324a7c3dfccd5a9
2025-08-05T14:16:47.882+07:00  INFO 28773 --- [qtp689240342-39] n.d.module.session.ClientSessionManager  : Add a client session id = node0lrqjmyqzgnabwwdvt8cl67c1, token = c2199c7247b48ac51324a7c3dfccd5a9
2025-08-05T14:16:47.977+07:00  INFO 28773 --- [qtp689240342-34] n.d.m.c.a.CompanyAuthenticationService   : User elaine.vnsgn is logged in successfully system
2025-08-05T14:16:47.994+07:00  INFO 28773 --- [qtp689240342-39] n.d.m.c.a.CompanyAuthenticationService   : User elaine.vnsgn is logged in successfully system
2025-08-05T14:17:05.150+07:00  INFO 28773 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T14:18:06.294+07:00  INFO 28773 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 4, expire count 0
2025-08-05T14:18:06.304+07:00  INFO 28773 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T14:18:06.304+07:00  INFO 28773 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T14:19:04.396+07:00  INFO 28773 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T14:20:06.540+07:00  INFO 28773 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-05T14:20:06.556+07:00  INFO 28773 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T14:20:06.557+07:00  INFO 28773 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T14:20:06.560+07:00  INFO 28773 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-05T14:21:03.643+07:00  INFO 28773 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T14:22:05.756+07:00  INFO 28773 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T14:22:05.768+07:00  INFO 28773 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T14:22:06.778+07:00  INFO 28773 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T14:22:49.348+07:00  INFO 28773 --- [Scheduler-1643026034-1] n.d.m.session.AppHttpSessionListener     : The session node0v9pld5ajdsxd1cegscqrvscuw0 is destroyed.
2025-08-05T14:23:02.879+07:00  INFO 28773 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T14:23:49.060+07:00  INFO 28773 --- [qtp689240342-40] n.d.m.monitor.call.EndpointCallService   : Call is not authorized. Endpoint BackendMockService/get
2025-08-05T14:24:06.027+07:00  INFO 28773 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-08-05T14:24:06.050+07:00  INFO 28773 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T14:24:06.050+07:00  INFO 28773 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T14:24:49.700+07:00  INFO 28773 --- [qtp689240342-39] n.d.module.session.ClientSessionManager  : Add a client session id = node0lrqjmyqzgnabwwdvt8cl67c1, token = c2199c7247b48ac51324a7c3dfccd5a9
2025-08-05T14:24:49.722+07:00  INFO 28773 --- [qtp689240342-39] n.d.m.c.a.CompanyAuthenticationService   : User elaine.vnsgn is logged in successfully system
2025-08-05T14:24:49.815+07:00  INFO 28773 --- [qtp689240342-37] n.d.module.session.ClientSessionManager  : Add a client session id = node0lrqjmyqzgnabwwdvt8cl67c1, token = c2199c7247b48ac51324a7c3dfccd5a9
2025-08-05T14:24:49.832+07:00  INFO 28773 --- [qtp689240342-37] n.d.m.c.a.CompanyAuthenticationService   : User elaine.vnsgn is logged in successfully system
2025-08-05T14:25:02.159+07:00  INFO 28773 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T14:25:02.164+07:00  INFO 28773 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-05T14:26:05.294+07:00  INFO 28773 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 3, expire count 1
2025-08-05T14:26:05.324+07:00  INFO 28773 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T14:26:05.324+07:00  INFO 28773 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T14:27:06.420+07:00  INFO 28773 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T14:28:04.553+07:00  INFO 28773 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-05T14:28:04.568+07:00  INFO 28773 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T14:28:04.568+07:00  INFO 28773 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T14:29:06.674+07:00  INFO 28773 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T14:30:03.800+07:00  INFO 28773 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-05T14:30:03.827+07:00  INFO 28773 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T14:30:03.827+07:00  INFO 28773 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T14:30:03.828+07:00  INFO 28773 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-05T14:30:03.828+07:00  INFO 28773 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-05T14:30:03.829+07:00  INFO 28773 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 05/08/2025@14:30:03+0700
2025-08-05T14:30:03.841+07:00  INFO 28773 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 05/08/2025@14:30:00+0700 to 05/08/2025@14:45:00+0700
2025-08-05T14:30:03.841+07:00  INFO 28773 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 05/08/2025@14:30:00+0700 to 05/08/2025@14:45:00+0700
2025-08-05T14:31:06.936+07:00  INFO 28773 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T14:32:03.035+07:00  INFO 28773 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-08-05T14:32:03.051+07:00  INFO 28773 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T14:32:03.053+07:00  INFO 28773 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T14:33:06.155+07:00  INFO 28773 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T14:34:02.242+07:00  INFO 28773 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 1
2025-08-05T14:34:02.246+07:00  INFO 28773 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T14:34:02.247+07:00  INFO 28773 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T14:35:05.359+07:00  INFO 28773 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T14:35:05.361+07:00  INFO 28773 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-05T14:36:06.479+07:00  INFO 28773 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 2
2025-08-05T14:36:06.488+07:00  INFO 28773 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T14:36:06.489+07:00  INFO 28773 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T14:37:04.590+07:00  INFO 28773 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T14:38:05.697+07:00  INFO 28773 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T14:38:05.705+07:00  INFO 28773 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T14:38:06.711+07:00  INFO 28773 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T14:39:03.798+07:00  INFO 28773 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T14:40:05.921+07:00  INFO 28773 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-08-05T14:40:05.933+07:00  INFO 28773 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T14:40:06.944+07:00  INFO 28773 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T14:40:06.946+07:00  INFO 28773 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-05T14:41:03.051+07:00  INFO 28773 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T14:42:06.170+07:00  INFO 28773 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 1
2025-08-05T14:42:06.189+07:00  INFO 28773 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T14:42:06.192+07:00  INFO 28773 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T14:43:02.280+07:00  INFO 28773 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T14:44:05.387+07:00  INFO 28773 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T14:44:05.395+07:00  INFO 28773 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T14:44:05.396+07:00  INFO 28773 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T14:45:06.495+07:00  INFO 28773 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T14:45:06.498+07:00  INFO 28773 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-05T14:45:06.498+07:00  INFO 28773 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 15 minutes
2025-08-05T14:45:06.499+07:00  INFO 28773 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : 🔄 Refresh queue at 05/08/2025@14:45:06+0700
2025-08-05T14:45:06.536+07:00  INFO 28773 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loading 0 messages for session 05/08/2025@14:45:00+0700 to 05/08/2025@15:00:00+0700
2025-08-05T14:45:06.536+07:00  INFO 28773 --- [scheduling-1] c.d.f.core.message.MessageQueueManager   : Loaded 0 messages for session 05/08/2025@14:45:00+0700 to 05/08/2025@15:00:00+0700
2025-08-05T14:46:04.695+07:00  INFO 28773 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 2, expire count 0
2025-08-05T14:46:04.714+07:00  INFO 28773 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T14:46:04.714+07:00  INFO 28773 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T14:47:06.812+07:00  INFO 28773 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T14:48:03.923+07:00  INFO 28773 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T14:48:03.929+07:00  INFO 28773 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T14:48:03.930+07:00  INFO 28773 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T14:49:06.034+07:00  INFO 28773 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T14:50:03.150+07:00  INFO 28773 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 1, expire count 0
2025-08-05T14:50:03.161+07:00  INFO 28773 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T14:50:03.162+07:00  INFO 28773 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T14:50:03.163+07:00  INFO 28773 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 5 minutes
2025-08-05T14:51:06.262+07:00  INFO 28773 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T14:52:02.363+07:00  INFO 28773 --- [scheduling-1] n.d.m.m.activity.MonitorActivityLogic    : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T14:52:02.371+07:00  INFO 28773 --- [scheduling-1] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T14:52:02.373+07:00  INFO 28773 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T14:53:05.482+07:00  INFO 28773 --- [scheduling-1] net.datatp.module.bot.cron.CronService   : Run the cron job at every 1 minutes
2025-08-05T14:53:47.147+07:00  INFO 28773 --- [SpringApplicationShutdownHook] o.e.jetty.server.AbstractConnector       : Stopped ServerConnector@7667afdf{HTTP/1.1, (http/1.1, h2c)}{0.0.0.0:7080}
2025-08-05T14:53:47.149+07:00  INFO 28773 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'projectTaskAutomationSource.inboundChannelAdapter'
2025-08-05T14:53:47.149+07:00  INFO 28773 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'inputConfigChannelSource.inboundChannelAdapter'
2025-08-05T14:53:47.149+07:00  INFO 28773 --- [SpringApplicationShutdownHook] o.s.i.e.SourcePollingChannelAdapter      : stopped bean 'botEventMessageSource.inboundChannelAdapter'
2025-08-05T14:53:47.150+07:00  INFO 28773 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {logging-channel-adapter:_org.springframework.integration.errorLogger} as a subscriber to the 'errorChannel' channel
2025-08-05T14:53:47.151+07:00  INFO 28773 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.errorChannel' has 0 subscriber(s).
2025-08-05T14:53:47.151+07:00  INFO 28773 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean '_org.springframework.integration.errorLogger'
2025-08-05T14:53:47.151+07:00  INFO 28773 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:monitorTaskEventHandler.serviceActivator} as a subscriber to the 'project-task-automation' channel
2025-08-05T14:53:47.151+07:00  INFO 28773 --- [SpringApplicationShutdownHook] o.s.i.channel.PublishSubscribeChannel    : Channel 'application.project-task-automation' has 0 subscriber(s).
2025-08-05T14:53:47.151+07:00  INFO 28773 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'monitorTaskEventHandler.serviceActivator'
2025-08-05T14:53:47.151+07:00  INFO 28773 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:inputConfigChannelHandler.serviceActivator} as a subscriber to the 'data-input-channel' channel
2025-08-05T14:53:47.151+07:00  INFO 28773 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.data-input-channel' has 0 subscriber(s).
2025-08-05T14:53:47.151+07:00  INFO 28773 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'inputConfigChannelHandler.serviceActivator'
2025-08-05T14:53:47.151+07:00  INFO 28773 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : Removing {message-handler:botEventMessageHandler.serviceActivator} as a subscriber to the 'bot-event-channel' channel
2025-08-05T14:53:47.151+07:00  INFO 28773 --- [SpringApplicationShutdownHook] o.s.integration.channel.DirectChannel    : Channel 'application.bot-event-channel' has 0 subscriber(s).
2025-08-05T14:53:47.151+07:00  INFO 28773 --- [SpringApplicationShutdownHook] o.s.i.endpoint.EventDrivenConsumer       : stopped bean 'botEventMessageHandler.serviceActivator'
2025-08-05T14:53:47.164+07:00  INFO 28773 --- [SpringApplicationShutdownHook] n.d.m.monitor.activity.StatisticService  : Schedule a monitor activity cleanup, save count 0, expire count 0
2025-08-05T14:53:47.243+07:00  INFO 28773 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'generic' removed from EhcacheManager.
2025-08-05T14:53:47.248+07:00  INFO 28773 --- [SpringApplicationShutdownHook] org.ehcache.core.EhcacheManager          : Cache 'entity' removed from EhcacheManager.
2025-08-05T14:53:47.270+07:00  INFO 28773 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-05T14:53:47.279+07:00  INFO 28773 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-05T14:53:47.295+07:00  INFO 28773 --- [SpringApplicationShutdownHook] j.LocalContainerEntityManagerFactoryBean : Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-05T14:53:47.295+07:00  INFO 28773 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-08-05T14:53:47.296+07:00  INFO 28773 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-08-05T14:53:47.297+07:00  INFO 28773 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown initiated...
2025-08-05T14:53:47.297+07:00  INFO 28773 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-2 - Shutdown completed.
2025-08-05T14:53:47.297+07:00  INFO 28773 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown initiated...
2025-08-05T14:53:47.438+07:00  INFO 28773 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : HikariPool-1 - Shutdown completed.
2025-08-05T14:53:47.438+07:00  INFO 28773 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown initiated...
2025-08-05T14:53:47.439+07:00  INFO 28773 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : rw - Shutdown completed.
2025-08-05T14:53:47.439+07:00  INFO 28773 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown initiated...
2025-08-05T14:53:47.440+07:00  INFO 28773 --- [SpringApplicationShutdownHook] com.zaxxer.hikari.HikariDataSource       : jdbc - Shutdown completed.
2025-08-05T14:53:47.445+07:00  INFO 28773 --- [SpringApplicationShutdownHook] org.eclipse.jetty.server.Server          : Stopped oejs.Server@228c854f{STOPPING}[12.0.15,sto=0]
2025-08-05T14:53:47.451+07:00  INFO 28773 --- [SpringApplicationShutdownHook] o.e.j.s.h.ContextHandler.application     : Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-08-05T14:53:47.452+07:00  INFO 28773 --- [SpringApplicationShutdownHook] o.e.j.e.servlet.ServletContextHandler    : Stopped osbwej.JettyEmbeddedWebAppContext@28ba90c1{application,/,b=[file:/private/var/folders/8b/1ydrhwys3md1llgbqcs09lgm0000gn/T/jetty-docbase.7080.983429557213245244/, jar:file:///Users/<USER>/nez/code/datatp/working/release-dev/server/lib/spring/stomp-websocket-2.3.4.jar!/META-INF/resources/],a=AVAILABLE,h=oeje10s.SessionHandler@7545cb17{STOPPED}}
