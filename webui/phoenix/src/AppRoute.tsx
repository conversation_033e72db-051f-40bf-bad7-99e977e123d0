import React from "react";
import { app, bs } from '@datatp-ui/lib'

//import { ServiceWorker } from './ServiceWorker';

//ServiceWorker.register();

let react = React as any;
if (!react['id']) {
  react['id'] = '@datatp-ui/logistics'
  console.log('React is not loaded. Expect React is already loaded in module @datatp-ui/lib');
}

bs.ScreenUtil.detectScreenType(bs.ScreenUtil.getScreenWidth());

if (app.host.CONFIG.isEmbeddedMode()) {
} else {
  import("./HostRoute").then(router => {
    router.configureHostRouter();
  });
}
