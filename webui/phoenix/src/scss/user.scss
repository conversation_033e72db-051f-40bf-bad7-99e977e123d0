@import '../../node_modules/bootstrap/scss/_functions';
@import 'theme/functions';

@import '../../node_modules/bootstrap/scss/mixins';
@import 'theme/mixins';

@import 'user-variables';
@import 'theme/variables';
@import '../../node_modules/bootstrap/scss/variables';

// user.scss
// Place your own theme CSS or SCSS rules below this line, these rules will override any Bootstrap and theme variables.
@import "datatp/configuration.scss";
@import "datatp/print.scss";

/*
html {
  height: 100vh;
  width: 100%;
  overflow: hidden;
}
*/

body {
  width: 100%;
  height: 100%;
  position: fixed;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  overscroll-behavior-y: contain;
}

main {
  overflow: auto;

  .container-fluid {
    >.navbar-top {
      padding: 0.25em 0.5em;
      height: 2.5em;

      .btn-link {
        color: var(--phoenix-nav-link-color) !important;
      }

      .nav-link {
        color: var(--phoenix-nav-link-color) !important;
      }
    }

    >.content {
      padding-top: 0rem;
      /** remove nav top and footer*/
      min-height: calc(100vh - (2em + $footer-height));

      @include media-breakpoint-between(xs, sm) {
        /** nav top is not in the content*/
        min-height: calc(100vh - $footer-height);
      }
    }

    .footer {
      height: $footer-height;
      /*
      @include media-breakpoint-between(xs, sm) {
        height: 1rem;
      }
      */
    }
  }
}

form {
  padding: 0.25em 0.25em;
}

.form-select {
  background-position: right 1rem center;
}

.form-control {
  padding: 2px;
  height: calc(1.75em + 5px);
  border-radius: 0px;
  border: none;
  outline: none;
  border-bottom: 1px solid var(--phoenix-gray-300, lightgray);
}

.form-label {
  text-align: left !important;
  padding: 0.25em 0px;
}

.form-control:focus {
  box-shadow: none;
  border-bottom: 1px solid var(--phoenix-gray-900, black);
}

textarea.form-control {
  border: 1px dotted var(--phoenix-gray-300, lightgray);
}

.modal {
  /*
  display: flex !important;
  flex-direction: column;
  */

  .modal-dialog {
    display: flex !important;
    flex-direction: column;
    flex-grow: 1;
    background-image: none;

    max-width: 100%;
    max-height: 100%;
    margin: 30px 30px;

    >.modal-content {
      margin: 0px auto;
    }

    .modal-header {
      display: flex;
      flex-grow: 0;
      justify-content: space-between;
      padding: 0.2rem 0.5rem;
    }

    .modal-body {
      @include flex-vbox();
      padding: 5px;
    }
  }

  .modal-xl {
    @include flex-vbox();
    max-width: 100%;
    height: 100%;
    padding: 25px !important;
    margin: 0px !important;

    >.modal-content {
      @include flex-vbox();
    }
  }

  .modal-lg {
    @include flex-vbox();
    padding: 25px 0px !important;
    max-width: 75%;
    height: 100%;
    margin: auto;

    >.modal-content {
      @include flex-vbox();
    }
  }

  .modal-flex-lg {
    @include flex-vbox();
    padding: 25px 0px !important;
    max-width: 75%;
    max-height: 100%;
    margin: auto;

    >.modal-content {
      @include flex-vbox();
    }
  }

  .modal-md {
    @include flex-vbox();
    padding: 25px;
    max-width: 720px;
    margin: auto;

    >.modal-content {
      @include flex-vbox();
    }
  }

  .modal-sm {
    @include flex-vbox();
    padding: 25px;
    max-width: 520px;
    margin: auto;

    >.modal-content {
      @include flex-vbox();
    }
  }
}

.popover {
  max-width: 800px;
}

.navbar-top {
  z-index: 1000;
  border-bottom: none;
}

.ui-application {
  margin-top: 2px;

  .app-nav {
    background-color: var(--phoenix-white, lightgray);
    margin: 1px 0px;
    padding: 0.35em 0.25em !important;
  }
}

.v-grid {
  @include flex-vbox();

  .v-grid-view {
    .v-grid-control {
      background-color: var(--phoenix-navbar-vertical-bg-color, lightgray);

      >.title {
        background-color: var(--phoenix-secondary-bg-subtle, lightgray);
      }
    }

    .v-grid-toolbar {
      background-color: var(--phoenix-white, lightgray);

      >.border-bottom {
        border-bottom: 1px solid var(--phoenix-gray-500, whitesmoke) !important;
      }
    }

    .grid-container {
      >.grid {
        .cell-header {
          background-color: var(--phoenix-light, lightgray);
        }

        .cell {
          .btn-link {
            color: var(--phoenix-info, gray);
          }
        }

        .cell-odd-even {
          background-color: var(--phoenix-gray-50, white);
        }

        .cell-odd-odd {
          background-color: var(--phoenix-gray-50, white);
        }

        .cell-highlight {
          background-color: var(--phoenix-gray-200, rgb(104, 171, 181));

          input,
          select {
            background-color: var(--phoenix-gray-200, rgb(104, 171, 181));
          }
        }

        .cell-selected {
          background-color: var(--phoenix-info-bg-subtle);
          color: var(--phoenix-info-text-emphasis)
        }

      }
    }
  }

}

.w-toolbar {

  .btn,
  .btn-primary,
  .btn-secondary {
    padding: 0.325em 0.5em !important;
    margin: 0px 2px !important;
  }
}

#ruller-layer {
  position: fixed;
  top: 0px;
  width: 100%;
  height: 100%;
}

.flex-vbox {
  @include flex-vbox();
}

.flex-vbox-grow-0 {
  @include flex-vbox-grow-0();
}

.flex-hbox {
  @include flex-hbox();
}

.flex-hbox-grow-0 {
  @include flex-hbox-grow-0();
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

/* Track */
::-webkit-scrollbar-track {
  background: #f1f1f1;
}

/* Handle */
::-webkit-scrollbar-thumb {
  background: #b8b5b5;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
  background: #555;
}