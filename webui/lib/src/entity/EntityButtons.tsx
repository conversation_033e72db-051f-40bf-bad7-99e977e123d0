import React from 'react';
import * as FeatherIcon from 'react-feather';

import * as bs from '../wbootstrap/core';
import * as server from '../server';
import * as app from '../app';

import { IBeanObserver } from '.';
import { T } from './Dependency'

interface WButtonEntityWriteProps extends app.AppComponentProps {
  label?: string;
  onClick: () => void;
  color?: 'primary' | 'secondary' | 'link';
  icon?: FeatherIcon.Icon;
  hide?: boolean;
  disable?: boolean;
  outline?: boolean;
}
export class WButtonEntityWrite extends app.AppComponent<WButtonEntityWriteProps> {
  render() {
    let { onClick, label, icon, color, hide, outline, disable, className } = this.props;
    if (hide) return (null);
    if (!color) color = 'primary';
    if (!icon) icon = FeatherIcon.Save;
    let html = (
      <bs.Button disabled={disable} laf={color} outline={outline} onClick={onClick} className={`${className}`}>
        {bs.renderIcon(icon)} {label}
      </bs.Button>
    );
    return html;
  }
}

export interface CommitConfig {
  context?: server.BackendContext;
  service: string;
  commitMethod: string;
  createMethod?: string;
  entityLabel: string;
}
interface ButtonEntityCommitProps extends app.AppComponentProps {
  hide?: boolean;
  observer: IBeanObserver;
  btnLabel?: string;
  confirmMessage?: string;
  commit: CommitConfig;
  disable?: boolean;
  onPreCommit?: (observer: IBeanObserver) => void;
  onPostCommit?: (observer: IBeanObserver) => void;
}
export class ButtonEntityCommit extends app.AppComponent<ButtonEntityCommitProps> {
  onClickCommit = (_evt: React.MouseEvent) => {
    let { confirmMessage } = this.props;
    if (confirmMessage) {
      let onConfirm = () => this.doCommit();
      bs.dialogConfirmMessage("Confirm", confirmMessage, onConfirm);
    } else {
      this.doCommit();
    }
  }

  doCommit() {
    let { appContext, pageContext, observer, commit, onPreCommit, onPostCommit } = this.props;
    let errorCollector = observer.getErrorCollector();
    if (errorCollector.getCount() > 0) {
      let items = [];
      let errors = errorCollector.getErrors();
      for (let name in errors) {
        items.push(<div>{name}: {errors[name]}</div>);
      }
      let ui = (
        <div>
          <div>Have {errorCollector.count} errors</div>
          <div className='pl-2'>{items}</div>
        </div>
      );
      bs.notificationShow("danger", "Errors", ui);
      return;
    }
    let commitedEntityCallback = (entity: any) => {
      observer.replaceWithUpdate(entity);
      observer.commitAndGet();
      if (onPostCommit) onPostCommit(entity);
      if (pageContext.breadcumbs) pageContext.breadcumbs.forceUpdatePreviousPage();
    };

    if (onPreCommit) {
      try {
        onPreCommit(observer);
      } catch (error: any) {
        return;
      }
    }

    let entity = observer.commitAndGet();
    let method = commit.commitMethod;
    let params = { 'entity': entity };
    if (observer.isNewBean() && commit.createMethod) method = commit.createMethod;
    appContext
      .createHttpBackendCall(commit.service, method, params)
      .withSuccessData(commitedEntityCallback)
      .withEntityOpNotification('commit', commit.entityLabel)
      .withFail((response: server.BackendResponse) => {
        let message = response.error.message || '';
        bs.notificationShow("danger", commit.entityLabel, <div className='text-danger'>{message}</div>);
        if (onPostCommit) onPostCommit(entity);
      })
      .call()
  }

  renderActionLabel(): string {
    let { btnLabel, observer } = this.props;
    if (!btnLabel) return observer.isNewBean() ? T('Create') : T('Save');
    else return btnLabel;
  }

  render() {
    let { hide, disable, className } = this.props;
    if (hide) return (null);
    return (
      <bs.Button laf='primary' disabled={disable} onClick={this.onClickCommit} className={`${className}`}>
        <FeatherIcon.Save size={12} className='me-2' /> {this.renderActionLabel()}
      </bs.Button>
    );
  }
}

interface WButtonEntityCommitProps extends app.AppComponentProps {
  observer: IBeanObserver;
  btnLabel?: string;
  label?: string;
  confirmMessage?: string;
  commitURL: string;
  createURL?: string;
  hide?: boolean;
  disable?: boolean;
  closePopupOnCommit?: boolean;
  onPreCommit?: (observer: IBeanObserver) => void;
  onPostCommit?: (observer: IBeanObserver) => void;
}
export class WButtonEntityCommit extends app.AppComponent<WButtonEntityCommitProps> {
  getActionLabel(): string {
    let { btnLabel, observer } = this.props;
    if (!btnLabel) return observer.isNewBean() ? T('Create') : T('Save');
    else return btnLabel;
  }

  onCommit = (evt: React.MouseEvent) => {
    let { confirmMessage, closePopupOnCommit } = this.props;
    if (confirmMessage) {
      let onConfirm = () => this.doCommit();
      bs.dialogConfirmMessage("Confirm", confirmMessage, onConfirm);
    } else {
      this.doCommit();
    }
    if (closePopupOnCommit) {
      bs.BootstrapUtil.dialogHide(evt.currentTarget);
    }
  }

  doCommit() {
    let { appContext, pageContext, observer, commitURL, createURL, label, onPreCommit, onPostCommit } = this.props;
    let errorCollector = observer.getErrorCollector();
    if (errorCollector.getCount() > 0) {
      let items = [];
      let errors = errorCollector.getErrors();
      for (let name in errors) {
        items.push(<div>{name}: {errors[name]}</div>);
      }
      let ui = (
        <div>
          <div>Have {errorCollector.count} errors</div>
          <div className='pl-2'>{items}</div>
        </div>
      );
      bs.notificationShow("danger", "Errors", ui);
      return;
    }
    let msgObj = { actionLabel: this.getActionLabel(), label: label };
    let successCB = (response: server.BackendResponse) => {
      let entity = response.data;
      observer.replaceWithUpdate(entity);
      observer.commitAndGet();
      if (onPostCommit) onPostCommit(entity);
      if (pageContext.breadcumbs) pageContext.breadcumbs.forceUpdatePreviousPage();
      appContext.addOSNotification("success", T('{{actionLabel}} {{label}} Success', msgObj));
    }
    if (onPreCommit) {
      try {
        onPreCommit(observer);
      } catch (error: any) {
        return;
      }
    }

    let entity = observer.commitAndGet();
    let failCB = (response: server.BackendResponse) => {
      let entityLabel = T('Save {{label}} Fail', msgObj);
      appContext.addOSNotification('danger', entityLabel, response.error, response);
      let messageContent = this._getErrorMessage(response);
      bs.notificationShow("danger", entityLabel, <div className='alert alert-danger'>{messageContent}</div>);
      return;
    }
    let restURL = commitURL;
    if (observer.isNewBean() && createURL) restURL = createURL;
    appContext.DO_NOT_USE_serverPUT(restURL, entity, successCB, failCB);
  }

  _getErrorMessage(resp: server.BackendResponse) {
    let errorType = resp.error.errorType;
    let message;
    if (errorType === 'EntityModified') {
      message = "Your data has been modified by another. Reload the data and update your change!"
    } else {
      message = resp.error.message;
    }
    return message;
  }

  render() {
    let { hide, disable, className } = this.props;
    if (hide) return (null);
    return (
      <bs.Button laf='primary' disabled={disable} onClick={this.onCommit} className={`${className}`}>
        <FeatherIcon.Save size={12} /> {this.getActionLabel()}
      </bs.Button>
    );
  }
}

interface WButtonEntityResetProps extends app.AppComponentProps {
  observer: IBeanObserver;
  onPostRollback?: (entity: any) => void;
  color?: 'primary' | 'secondary' | 'link';
  hide?: boolean;
}
export class WButtonEntityReset extends app.AppComponent<WButtonEntityResetProps> {
  onRollBack() {
    let { observer, onPostRollback } = this.props;
    observer.rollback();
    if (onPostRollback) {
      onPostRollback(observer.getMutableBean());
    }
  }

  render() {
    let { hide, color } = this.props;
    if (hide) return (null);
    if (!color) color = 'secondary';
    let html = (
      <bs.Button laf={color} onClick={() => this.onRollBack()}>
        <FeatherIcon.RefreshCcw size={12} className="mx-2" />{T('Reset')}
      </bs.Button>
    );
    return html;
  }
}

interface WButtonEntityReadProps extends app.AppComponentProps {
  label?: string;
  onClick: () => void;
  color?: 'primary' | 'secondary' | 'link';
  icon?: FeatherIcon.Icon;
  hide?: boolean;
}
export class WButtonEntityRead extends app.AppComponent<WButtonEntityReadProps> {
  render() {
    let { onClick, label, color, hide, icon, className } = this.props;
    if (hide) return (null);
    if (!color) color = 'primary';
    let iconUI = bs.renderIcon(icon);
    let html = (
      <bs.Button laf={color} onClick={onClick} className={`${className}`}>
        {iconUI} {label}
      </bs.Button>
    );
    return html;
  }
}

export class WButtonNewMembership extends app.AppComponent<WButtonEntityWriteProps> {
  render() {
    return <WButtonEntityWrite {...this.props} icon={FeatherIcon.User} />
  }
}

export class WButtonDeleteMembership extends app.AppComponent<WButtonEntityWriteProps> {
  render() {
    return <WButtonEntityWrite {...this.props} icon={FeatherIcon.Minus} />
  }
}
export class WButtonEntityNew extends app.AppComponent<WButtonEntityWriteProps> {
  render() {
    return <WButtonEntityWrite {...this.props} icon={FeatherIcon.Plus} />
  }
}

export class WButtonEntityClone extends app.AppComponent<WButtonEntityWriteProps> {
  render() {
    let { label } = this.props;
    if (!label) label = T('Clone');
    return <WButtonEntityWrite {...this.props} label={label} icon={FeatherIcon.Copy} />
  }
}

export class WButtonEntityPrint extends app.AppComponent<WButtonEntityWriteProps> {
  render() {
    let { label } = this.props;
    if (!label) label = T('Print');
    return <WButtonEntityRead {...this.props} label={label} icon={FeatherIcon.Printer} />
  }
}

export class WButtonEntityExport extends app.AppComponent<WButtonEntityWriteProps> {
  render() {
    let { label } = this.props;
    if (!label) label = T('Export');
    return <WButtonEntityRead {...this.props} label={label} icon={FeatherIcon.Download} />
  }
}

export class WButtonEntityEmail extends app.AppComponent<WButtonEntityWriteProps> {
  render() {
    let { label } = this.props;
    if (!label) label = T('Email');
    return <WButtonEntityRead {...this.props} label={label} icon={FeatherIcon.Mail} />
  }
}

export class WButtonEntityGoTo extends app.AppComponent<WButtonEntityWriteProps> {
  render() {
    return <WButtonEntityRead {...this.props} icon={FeatherIcon.Link} />
  }
}
/*
interface WButtonEntityStorageProps extends WButtonEntityCommitProps {
  states: Array<'ACTIVE' | 'ARCHIVED' | 'DEPRECATED' | 'JUNK'>;
  hide?: boolean;
}
export class WButtonEntityStorage extends app.AppComponent<WButtonEntityStorageProps> {

  onChangeState(newState: any) {
    let { appContext, observer, commitURL, label, onPostCommit } = this.props;
    let entity = observer.getMutableBean();
    let successCB = (_result: server.BackendResponse) => {
      observer.replaceBeanProperty('storageState', newState);
      appContext.addOSNotification("success", T('Change Storage State {{label}} Success', { label: label }));
      if (onPostCommit) onPostCommit(entity);
    }
    let failCB = (_result: any) => {
      appContext.addOSNotification('danger', T('Change Storage State {{label}} Fail', { label: label }), null, { label: label });
    }
    if (commitURL) {
      let changeStorageReq = { entityIds: [entity.id], newStorageState: newState };
      appContext.DO_NOT_USE_serverPUT(commitURL, changeStorageReq, successCB, failCB);
    } else {
      throw new Error("You need to config commitURL")
    }
  }

  render() {
    let { observer, states, hide: disable } = this.props;
    let entity = observer.getMutableBean();
    let currState = entity.storageState;
    if (disable) {
      return <bs.Button laf='primary' disabled>{currState}</bs.Button>
    };

    return (
      <Popover placement="top" className='flex-grow-0' closeOnTrigger={'.popover'} minWidth={200} minHeight={100}>
        <PopoverToggle className="btn-primary">
          <FeatherIcon.Database size={12} /> {currState} <FeatherIcon.ChevronUp size={12} />
        </PopoverToggle>
        <bs.PopoverContent className="flex-vbox">
          {states.map((state: any) => {
            return (
              <bs.Button key={state} laf='secondary' outline className='flex-hbox-grow-0 mt-1'
                onClick={() => this.onChangeState(state)}> <FeatherIcon.Edit size={12} /> {state}
              </bs.Button>
            )
          })}
        </bs.PopoverContent>
      </Popover>
    );
  }
}
*/
