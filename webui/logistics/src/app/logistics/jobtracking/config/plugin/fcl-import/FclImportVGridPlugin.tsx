import { grid } from '@datatp-ui/lib';

import { T } from '../../../Dependency';
import { JobTrackingVGridPlugin } from '../JobTrackingVGridPlugin';
import { ShipmentImportCombineMblPlugin as FclImportCombineMblPlugin } from './FclImportCombineMblPlugin';
import { UIJobTrackingList } from '../../../data/UIJobTrackingList';
import { JobTrackingProject } from '../../JobTrackingListConfig';
import { shipmentImportStepAutomaticPlugin as FclImportStepAutomaticPlugin } from './FclImportStepAutomaticPlugin';
import { ShipmentImportSyncBFSOneJobNoPlugin as FclImportSyncBFSOneJobNoPlugin } from './FclImportSyncBFSOneJobPlugin';
import { fclImportOnInitVGrid } from './FclImportOnInitVGrid';
import { shipmentImportReport } from './FclImportMonthReportPlugin';

export const FclImportVGridPlugin: JobTrackingVGridPlugin = {
  name: 'shipment-import-vgrid-plugin', label: 'Actions',
  isApplyFor(ctx: grid.VGridContext) {
    let uiRoot = ctx.uiRoot as UIJobTrackingList;
    let { listConfig } = uiRoot.props;
    let project: JobTrackingProject = listConfig.project;
    if (project.code === 'job-tracking-shipment-import') return true;
    return false;
  },
  toolbar: {
    actions: [
      FclImportSyncBFSOneJobNoPlugin
    ],
    dropdownActions: [
      {
        name: 'shipment-import-dropdown-actions',
        label: T('Others'),
        actions: [
          FclImportCombineMblPlugin,
          shipmentImportReport
        ]
      }
    ]
  },
  onInitVGrid(ctx: grid.VGridContext) {
    fclImportOnInitVGrid(ctx);
    FclImportStepAutomaticPlugin(ctx);
  },
}

