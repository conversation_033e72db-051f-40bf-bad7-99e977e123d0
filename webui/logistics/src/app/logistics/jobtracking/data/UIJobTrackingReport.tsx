import React from 'react';
import * as FeatherIcon from 'react-feather';
import { bs, grid, util, app, input, entity, sql } from '@datatp-ui/lib';

import { T } from '../Dependency';
import { JobTrackingProject, StepStatus } from '../config/JobTrackingListConfig';
import { UIJobTrackingList } from './UIJobTrackingList';

interface UIReportListProps extends app.AppComponentProps {
  reportRecords: Array<any>;
}
class UIReportList extends app.AppComponent<UIReportListProps> {
  config: bs.GridConfig = {
    showHeader: true,
    showBorder: true,
    columns: [
      { field: 'ownerAccountFullName', label: 'Owner', width: 500, cssClass: 'fw-bold' },
      { field: 'doneCount', label: 'Total', cssClass: 'pe-2 justify-content-end' },
    ]
  }

  onExport = (records: Array<any>, grid: bs.GridConfig, fileName: string) => {
    const { appContext } = this.props;
    const currentDate = util.TimeUtil.toCompactDateFormat(new Date());
    let fields: entity.Field[] = grid.columns.map(column => ({
      name: column.field,
      label: column.label
    }));
    const exportModel: entity.DataListExportModel = {
      modelName: fileName,
      fileName: `${fileName}_${currentDate}.xlsx`,
      fields: fields,
      fieldGroups: [],
      records: records,
    };

    appContext.createHttpBackendCall('DataMappingService', 'xlsxPrivateExport', { model: exportModel })
      .withSuccessData((response: any) => {
        let storeInfo = response;
        if (storeInfo.type == 'ir.actions.act_url') {
          entity.StoreInfo.download(storeInfo.url);
        } else if (storeInfo.privateMode) {
          entity.StoreInfo.privateDownload(storeInfo);
        } else {
          entity.StoreInfo.download(storeInfo.url);
        }
      })
      .call();
  }

  render(): React.ReactNode {
    let { reportRecords } = this.props;
    return <>
      <div className='flex-vbox px-3'>
        <div className='flex-hbox flex-grow-0 justify-content-end pb-2'>
          <bs.Button laf="primary" outline size="sm" className="me-1 p-1"
            onClick={() => { this.onExport(reportRecords, this.config, 'Report') }}>
            <FeatherIcon.Download size={14} className="me-1" />
            Export
          </bs.Button>
        </div>
        <bs.Grid config={this.config} beans={reportRecords} />
      </div>
    </>
  }
}

interface UIReportFormProps extends entity.AppDbEntityProps {
  context: grid.VGridContext;
}
export class UIReportForm extends entity.AppDbEntity<UIReportFormProps> {

  onReport = (reportModel: any) => {
    let { context, pageContext } = this.props;
    let uiRoot = context.uiRoot as UIJobTrackingList;
    let { appContext, listConfig } = uiRoot.props;
    let project = listConfig.project;
    if (!reportModel.fromDate && !reportModel.toDate) {
      bs.notificationShow("danger", 'ERROR', <div className='alert alert-danger'>FromDate and ToDate must not be null!</div>);
      return;
    }

    let timeRange = new util.TimeRange();
    if (reportModel.fromDate) timeRange.fromSetDate(new Date(reportModel.fromDate));
    if (reportModel.toDate) timeRange.toSetDate(new Date(reportModel.toDate))
    let fromDate: string = reportModel.fromDate ? util.TimeUtil.toCompactDateFormat(reportModel.fromDate) : 'Before';
    let toDate: string = reportModel.toDate ? util.TimeUtil.toCompactDateFormat(reportModel.toDate) : 'Now';

    let searchParams = {
      "filters": [
        ...sql.createSearchFilter(),
      ],
      "optionFilters": [
        sql.createStorageStateFilter([entity.StorageState.ACTIVE, entity.StorageState.ARCHIVED])
      ],
      "rangeFilters": [
        ...sql.createDateTimeFilter("endTime", "End Time", timeRange),
      ],
      "params": { status: 'Done', jobTrackingProjectId: project.id, withPermission: !listConfig.permissionHelper.hasModeratorPermission() },
      "maxReturn": 7000
    };
    pageContext.back();
    appContext
      .createHttpBackendCall('JobTrackingService', 'jobTrackingReport', { params: searchParams })
      .withSuccessData((data: any) => {
        let createAppPage = (appContext: app.AppContext, pageContext: app.PageContext) => {
          return (
            <UIReportList appContext={appContext} pageContext={pageContext}
              reportRecords={data} />
          );
        }
        pageContext.createPopupPage('job-tracking-report', T(`Report: ${fromDate} - ${toDate}`), createAppPage, { size: 'lg', backdrop: 'static' });
      })
      .call();
  }

  onInputChange = (bean: any, field: string, oldVal: any, newVal: any) => {
    let date: Date = util.TimeUtil.parseCompactDateTimeFormat(newVal)
    if (field === 'fromDate') {
      date.setHours(0);
      date.setMinutes(0);
      date.setSeconds(0);
    } else if (field === 'toDate') {
      date.setHours(23);
      date.setMinutes(59);
      date.setSeconds(59);
    }
    bean[field] = date
    if (bean['fromDate'] && bean['toDate'] && bean['fromDate'].getTime() > bean['toDate'].getTime()) {
      bs.notificationShow("danger", 'ERROR', <div className='alert alert-danger'>ToDate must be greater than or equal to FromDate!</div>);
      if (field === 'fromDate') {
        bean['fromDate'] = bean['toDate']
      } else if (field === 'toDate') {
        bean['toDate'] = bean['fromDate']
      }
      this.forceUpdate();
    }
  }

  render() {
    let { appContext, pageContext, observer, readOnly } = this.props;
    let reportModel = observer.getMutableBean();
    return <div className='flex-vbox p-1'>
      <bs.Row>
        <bs.Col span={6}>
          <input.BBDateTimeField
            disable={readOnly}
            label={T('From Date')}
            bean={reportModel} field={'fromDate'} dateFormat={'DD/MM/YYYY'} timeFormat={false}
            onInputChange={this.onInputChange} />
        </bs.Col>
        <bs.Col span={6}>
          <input.BBDateTimeField
            disable={readOnly}
            label={T('To Date')}
            bean={reportModel} field={'toDate'} dateFormat={'DD/MM/YYYY'} timeFormat={false}
            onInputChange={this.onInputChange} />
        </bs.Col>
      </bs.Row>

      <bs.Toolbar hide={readOnly}>
        <entity.WButtonEntityWrite
          appContext={appContext} pageContext={pageContext} color='secondary'
          label={T('Report')} icon={FeatherIcon.Book} onClick={() => this.onReport(reportModel)} />
      </bs.Toolbar>
    </div>
  }
}