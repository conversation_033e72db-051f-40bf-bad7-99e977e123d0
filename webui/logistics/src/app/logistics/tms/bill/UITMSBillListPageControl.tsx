import React, { Component } from "react";
import * as FeatherIcon from 'react-feather'
import { server, bs, grid, app, util, entity, input } from "@datatp-ui/lib";

import { T } from "../backend";
import { XLSXCustomButton } from "../XLSXButton";
import { UITMSBillSummary } from "./TMSBillSummary";
import { UITMSBillList, UITMSBillListPlugin } from "./UITMSBillList";
import { TMSBillTransportationModeTools } from "../utils";
import { UITMSBillUtils } from "./UITMSBillUtils";
import { TMSBillBFSOneFeeTemplate, TMSBillBFSOneVehicleInfoTemplate } from "./UITMSBillBfsOneTemplate";
import { BBRefVehicleFleet } from "../vehicle/BBRefVehicleFleet";

export function updateTMSBillData(context: grid.VGridContext, updateBills: Array<any>, callback?: (records: any) => void) {
  let uiRoot = context.uiRoot as entity.DbEntityList;
  let { appContext } = uiRoot.props;
  let ids: Array<any> = [];
  updateBills.forEach(sel => ids.push(sel.id));
  let successCB = (data: Array<any>) => {
    for (let bill of updateBills) {
      for (let result of data) {
        if (bill['id'] == result['id']) {
          for (const propertyName in result) {
            bill[propertyName] = result[propertyName];
          }
        }
      }
      let state = grid.getRecordState(bill);
      bill['_state'] = new grid.RecordState(state.row);
    }
    if (callback) callback(updateBills);
  }
  if (ids.length == 0) {
    if (callback) callback(updateBills);
    return;
  }
  let searchParams = {
    params: {
      ids: ids,
      dataScope: app.AppDataScope.COMPANY
    }
  }
  appContext
    .createHttpBackendCall('TMSRestCallService', 'searchTMSBills', { params: searchParams })
    .withSuccessData(successCB)
    .call();
}

export function onSyncTMSBillForVendorBills(appContext: app.AppContext, records: Array<any>) {
  let ids: Array<any> = [];
  records.forEach(rec => ids.push(rec['id']));
  let params = {
    tmsBillIds: ids
  }
  appContext
    .createHttpBackendCall('TMSRestCallService', 'syncVendorBillWithTMSBills', params)
    .call();
}

export function onSaveTMSBillModified(context: grid.VGridContext, dRecords: Array<grid.DisplayRecord>, callBack?: (response: any) => void) {
  for (let dRec of dRecords) {
    dRec.getRecordState().markModified();
  }
  onSaveRows(context, callBack);
}

export function onSaveRows(context: grid.VGridContext, callBack?: (response: any) => void) {
  let uiRoot = context.uiRoot as UITMSBillList;
  let { appContext, plugin } = uiRoot.props;
  let modifiedRecords = context.model.getMarkModifiedRecords();
  let deleteRecords = context.model.getMarkDeletedRecords();
  let records = [...modifiedRecords, ...deleteRecords];
  if (records.length === 0) {
    let selectedBills = plugin.getListModel().getSelectedRecords();
    selectedBills.forEach(sel => {
      let state = grid.getRecordState(sel);
      sel['_state'] = new grid.RecordState(state.row);
    })
    appContext.addOSNotification("success", T("No Bills were Edited"));
    if (callBack) callBack(null);
  } else {
    let failCB = (response: server.BackendResponse) => {
      server.rest.handleResponseError('', response);
      if (callBack) callBack(response)
    }
    let successCB = (data: any) => {
      let results: Array<any> = data;
      for (let modifiedRecord of modifiedRecords) {
        let result = results.find((result) => modifiedRecord['uikey'] == result['uikey']);
        if (result) {
          modifiedRecord['id'] = result['id'];
        }
      }
      for (let sel of deleteRecords) {
        plugin.getListModel().removeRecord(sel);
      }
      if (callBack) callBack(data);
      updateTMSBillData(context, modifiedRecords, (records) => {
        appContext.addOSNotification("success", T("Save Bills Success"));
        context.getVGrid().forceUpdateView();
      });
      // onSyncTMSBillForVendorBills(appContext, modifiedRecords);

    }
    context.getVGrid().forceUpdateView();
    appContext
      .createHttpBackendCall('TMSRestCallService', 'saveTMSBillModels', { records })
      .withSuccessData(successCB)
      .withFail(failCB)
      .call();
  }
}

export class UITMSBillListPageControl extends Component<grid.VGridContextProps> {
  saving = false;
  onCollectBills = () => {
    let { context } = this.props;
    let uiRoot = context.uiRoot as entity.DbEntityList;
    const { appContext, plugin } = uiRoot.props;
    let ids = plugin.getListModel().getSelectedRecordIds();
    let successCB = (data: any) => {
      uiRoot.reloadData();
      appContext.addOSNotification("success", T('Collect Bill Success'));
    }
    appContext
      .createHttpBackendCall('TMSRestCallService', 'collectBills', { ids: ids })
      .withSuccessData(successCB)
      .call();
  }

  onSave = () => {
    let { context } = this.props;
    this.saving = true;
    this.forceUpdate();
    onSaveRows(context, () => {
      this.saving = false;
      this.forceUpdate();
    });
  }

  onShowSummary = () => {
    let { context } = this.props;
    let uiRoot = context.uiRoot as app.AppComponent;
    let { pageContext } = uiRoot.props;
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <UITMSBillSummary appContext={appCtx} pageContext={pageCtx} context={context} />
      );
    }
    pageContext.createPopupPage('summary', T('TMS Bill Summary'), createAppPage, { size: 'xl' })
  }

  requestRoundUsed = () => {
    let { context } = this.props;
    let uiRoot = context.uiRoot as entity.DbEntityList;
    let { plugin, appContext } = uiRoot.props;
    let ids = plugin.getListModel().getSelectedRecordIds();
    if (!ids || ids.length === 0) {
      bs.notificationShow("danger", T("Bill is not selected"), T("You should select at least 1 Record!!!"));
      return;
    }
    let successCB = (_data: any) => {
      updateTMSBillData(context, plugin.getListModel().getSelectedRecords(), (_records) => {
        appContext.addOSNotification("success", T("Request Round Used Success"));
        context.getVGrid().forceUpdateView();
        this.forceUpdate();
      });
    }
    appContext
      .createHttpBackendCall('TMSRoundUsedService', 'requestRoundUsedBill', { ids: ids })
      .withSuccessData(successCB)
      .call();
  }

  requestOPS = () => {
    let { context } = this.props;
    let uiRoot = context.uiRoot as entity.DbEntityList;
    let { plugin, appContext } = uiRoot.props;
    let ids = plugin.getListModel().getSelectedRecordIds();
    if (!ids || ids.length === 0) {
      bs.notificationShow("danger", T("Bill is not selected"), T("You should select at least 1 Record!!!"));
      return;
    }
    let successCB = (_data: any) => {
      updateTMSBillData(context, plugin.getListModel().getSelectedRecords(), (_records) => {
        appContext.addOSNotification("success", T("Request OPS Success"));
        context.getVGrid().forceUpdateView();
        this.forceUpdate();
      });
    }
    appContext
      .createHttpBackendCall('TMSOperationsService', 'requestOPS', { ids: ids })
      .withSuccessData(successCB)
      .call();
  }

  requestVendorBill = () => {
    let { context } = this.props;
    let uiRoot = context.uiRoot as entity.DbEntityList;
    let { plugin, appContext } = uiRoot.props;
    let ids = plugin.getListModel().getSelectedRecordIds();
    if (!ids || ids.length === 0) {
      bs.notificationShow("danger", T("Bill is not selected"), T("You should select at least 1 Record!!!"));
      return;
    }
    let successCB = (_data: any) => {
      appContext.addOSNotification("success", T("Request Success"));
    }
    appContext
      .createHttpBackendCall('TMSVendorBillService', 'requestVendorBill', { billIds: ids })
      .withSuccessData(successCB)
      .call();
  }

  onReportVendor = () => {
    const { context } = this.props;
    const uiRoot = context.uiRoot as entity.DbEntityList;
    const { pageContext, plugin } = uiRoot.props;
    let selectedRecords = plugin.getListModel().getSelectedRecords();
    if (selectedRecords.length === 0) {
      const createContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
        let bean = {
          'from': util.TimeUtil.javaCompactDateTimeFormat(new Date()),
          'to': util.TimeUtil.javaCompactDateTimeFormat(new Date())
        }
        return (
          <TMSReportVendorBill appContext={appCtx} pageContext={pageCtx}
            observer={new entity.BeanObserver(bean)} />
        )
      }
      pageContext.createPopupPage('create-report-vendor-bill', T('Create Report Vendor Bill'), createContent);
    } else {
      const createContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
        return (
          <TMSBillAccountingList appContext={appCtx} pageContext={pageCtx} plugin={new entity.DbEntityListPlugin(selectedRecords)} />
        )
      }
      pageContext.createPopupPage('tms-report-bill', T('TMS Bill'), createContent, { size: 'xl' })
    }
  }

  requestContainerDeposit = () => {
    let { context } = this.props;
    let uiRoot = context.uiRoot as entity.DbEntityList;
    let { plugin, appContext } = uiRoot.props;
    let ids = plugin.getListModel().getSelectedRecordIds();
    let records = plugin.getListModel().getSelectedRecords();
    if (!ids || ids.length === 0) {
      bs.notificationShow("danger", T("Bill is not selected"), T("You should select at least 1 Record!!!"));
      return;
    }
    let labels = [];
    for (let record of records) {
      if (!TMSBillTransportationModeTools.isImport(record.mode)) {
        labels.push(record['label']);
      }
    }
    if (labels.length > 0) {
      let error: string = "";
      if (labels.length === 1) {
        error = `Bill ${labels.join(", ")} is not for imported goods.`
      } else {
        error = `Bill ${labels.join(", ")} are not for imported goods.`
      }
      bs.notificationShow("danger", T('Error'), T(error));
      return;
    }
    let successCB = (_data: any) => {
      appContext.addOSNotification("success", T("Request Success"));
    }
    appContext
      .createHttpBackendCall('TMSRoundUsedService', 'requestTMSBillsToContainerDeposits', { tmsBillIds: ids })
      .withSuccessData(successCB)
      .call();
  }
  billDeliveredFails: Array<any> = [];
  render() {
    let { context } = this.props;
    let uiRoot = context.uiRoot as UITMSBillList;
    let { appContext, pageContext, plugin, readOnly } = uiRoot.props;
    let writeCap = pageContext.hasUserWriteCapability();
    let readCap = pageContext.hasUserReadCapability();
    return (
      <bs.Toolbar className='border justify-content-center' hide={!readCap || readOnly}>
        <bs.ButtonGroup label="Print & Email" className="flex-grow-0" smallScreenLaf="popover">
          {/* <bs.Movable style={{ top: 40, right: 380, width: 40 }} className="flex-grow-0" movableId="waiting">
            <NotificationMessage
              context={context} fieldName={'_actions'} />
          </bs.Movable> */}
          <entity.WButtonEntityWrite icon={FeatherIcon.Send}
            appContext={appContext} pageContext={pageContext}
            disable={!writeCap} hide={!writeCap}
            label={T('Send Vendor')} onClick={uiRoot.processingGoodsRequestv2} />
          <entity.WButtonEntityWrite icon={FeatherIcon.Mail}
            appContext={appContext} pageContext={pageContext}
            disable={!writeCap} hide={!writeCap}
            label={T('Send Customer(FCL)')} onClick={uiRoot.onShowUIMessageCustomerFCL} />
          <entity.WButtonEntityWrite icon={FeatherIcon.Printer}
            appContext={appContext} pageContext={pageContext}
            disable={!pageContext.hasUserReadCapability()} hide={!pageContext.hasUserReadCapability()}
            label={T('Print POD')} onClick={() => {
              let ids = plugin.getListModel().getSelectedRecordIds();
              let newRecords = plugin.getListModel().getRecords().find(sel => grid.getRecordState(sel).isMarkNew());
              if (newRecords) {
                bs.notificationShow("danger", "You need to save changes");
                return;
              }
              UITMSBillUtils.onPrintReceiptOfDelivery(pageContext, ids);
            }} />
        </bs.ButtonGroup>
        <bs.ButtonGroup label="Tools" className="flex-grow-0" smallScreenLaf="popover" >
          <entity.WButtonEntityWrite icon={FeatherIcon.GitMerge}
            disable={!writeCap} hide={!writeCap}
            appContext={appContext} pageContext={pageContext}
            label={T('Combine Bill')} onClick={this.onCollectBills} />
          <bs.Popover className="flex-hbox-grow-0" title={'Export Data'} closeOnTrigger=".btn" >
            <bs.PopoverToggle laf='primary' >
              <FeatherIcon.Layers size={12} /> {'Export'}
            </bs.PopoverToggle>
            <bs.PopoverContent >
              <div className='flex-vbox'>
                <XLSXCustomButton className="p-2 my-1" tableName="tms-bill" context={context}
                  options={{ fileName: `Tms Bill ${util.TimeUtil.toCompactDateFormat(new Date())}.xlsx`, modelName: 'tms-bill' }}
                  appContext={appContext} pageContext={pageContext} fieldSelect={['label', 'code']} />
                <bs.Button laf="primary" className="p-2 my-1" onClick={this.onReportVendor}>
                  <FeatherIcon.FileText size={12} /> {T('Report Bill')}
                </bs.Button>
                <bs.Button laf="primary" className="p-2 my-1" onClick={() => UITMSBillUtils.showUploadVendorBillXLSXFile(uiRoot, () => { uiRoot.reloadData() })}>
                  <FeatherIcon.Upload size={12} /> {T('Upload Vendor Bill')}
                </bs.Button>
                <bs.Button laf="primary" className="p-2 my-1" onClick={() => {
                  appContext.createHttpBackendCall('TMSHouseBillService', 'convertTMSBillToTMSHouseBill', { ids: plugin.getListModel().getSelectedRecordIds() })
                    .withEntityOpNotification('commit', 'Convert To Hbl Bill Success!!!!')
                    .call();
                }}>
                  <FeatherIcon.Upload size={12} /> {T('To Hbl Bill')}
                </bs.Button>
              </div>
            </bs.PopoverContent>
          </bs.Popover>

        </bs.ButtonGroup>
        {/* {moderatorCap ? <></> :
          <bs.Popover className="flex-hbox-grow-0" title={'Request App'} closeOnTrigger=".btn" >
            <bs.PopoverToggle laf='primary' >
              <FeatherIcon.ArrowRightCircle size={12} /> {'Request App'}
            </bs.PopoverToggle>
            <bs.PopoverContent >
              <div className='flex-vbox'>
                <bs.Button laf='secondary' outline className={buttonStyle} onClick={this.requestVendorBill} >
                  <FeatherIcon.ArrowRightCircle size={12} /> {T('Vendor')}
                </bs.Button>
                <bs.Button laf='secondary' outline className={buttonStyle} onClick={this.requestOPS}>
                  <FeatherIcon.ArrowRightCircle size={12} /> {T('OPS')}
                </bs.Button>
                <bs.Button laf='secondary' outline className={buttonStyle} onClick={this.requestRoundUsed}>
                  <FeatherIcon.ArrowRightCircle size={12} /> {T('Round Used')}
                </bs.Button>
                <bs.Button laf='secondary' outline className={buttonStyle} onClick={this.requestContainerDeposit}>
                  <FeatherIcon.ArrowRightCircle size={12} /> {T('Container Deposit')}
                </bs.Button>
              </div>
            </bs.PopoverContent>
          </bs.Popover>
        } */}
        {writeCap ?
          <bs.Button laf='primary' disabled={this.saving || !writeCap} onClick={this.onSave}>
            {this.saving ?
              <FeatherIcon.Loader size={12} style={{ animation: '0.75s linear infinite spinner-border' }} />
              :
              <FeatherIcon.Save size={12} />
            }
            <span style={{ marginLeft: 4 }}>{T('Save Rows')}</span>
          </bs.Button>
          :
          null
        }
      </bs.Toolbar>
    )
  }
}

class TMSReportVendorBill extends entity.DbEntityEditor {
  onLoadBill = () => {
    let { pageContext, observer } = this.props;
    let params = observer.getMutableBean();
    let vendorId = params['vendorId'];
    const createContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      let dateRange = new util.TimeRange(util.TimeUtil.parseCompactDateTimeFormat(params['from']), util.TimeUtil.parseCompactDateTimeFormat(params['to']));
      let plugin = new UITMSBillListPlugin(dateRange).withTMSPartnerId('vendorId', vendorId);
      return (
        <TMSBillAccountingList appContext={appCtx} pageContext={pageCtx} plugin={plugin} />
      )
    }
    pageContext.createPopupPage('tms-report-bill', T('TMS Bill'), createContent, { size: 'xl' })
  }

  render(): React.ReactNode {
    const { appContext, pageContext, observer } = this.props;
    const params = observer.getMutableBean();
    return (
      <div className="flex-vbox">
        <div className="flex-grow-1">
          <input.BBCheckboxField bean={params} label="Not Paid" field="notPaid" value={false} />
          <bs.Row>
            <bs.Col span={6}>
              <input.BBDateTimeField bean={params} label="From" field="from" timeFormat={false} />
            </bs.Col>
            <bs.Col span={6}>
              <input.BBDateTimeField bean={params} label="To" field="to" timeFormat={false} />
            </bs.Col>
          </bs.Row>
          <BBRefVehicleFleet minWidth={400}
            appContext={appContext} pageContext={pageContext}
            bean={params} beanIdField={'vendorId'} beanLabelField={'vendorFullName'} label={T('Vendor')} placeholder={'Vendor'} />
        </div>
        <bs.Toolbar>
          <bs.Button laf="info" onClick={this.onLoadBill}>
            <FeatherIcon.Search size={12} /> {T('Search')}
          </bs.Button>
        </bs.Toolbar>
      </div>
    )
  }
}

class TMSBillAccountingList extends entity.DbEntityList {

  verifyAndCreateBFSOneTemplate = () => {
    let { pageContext } = this.props;
    let createAppPage = (_appCtx: app.AppContext, _pageCtx: app.PageContext) => {
      return (
        <div className="flex-vbox">
          <h4>{'Are you sure to close vehicle info TMS Bills?'}</h4>
          <bs.Toolbar>
            <bs.Button laf="success" onClick={() => {
              this.onCreateBFSOneTemplate(true);
              _pageCtx.back();
            }}>
              <FeatherIcon.Check size={12} /> {T('Verify House Bill')}
            </bs.Button>
            <bs.Button laf="danger" onClick={() => {
              this.onCreateBFSOneTemplate(false)
              _pageCtx.back();
            }}>
              <FeatherIcon.X size={12} />{T('Not Verify House Bill')}
            </bs.Button>
          </bs.Toolbar>
        </div>
      )
    };
    pageContext.createPopupPage('confirm', 'Confirm Close Vehicle Info TMS Bills!', createAppPage, { backdrop: 'static' })
  }

  onCreateBFSOneTemplate = (verifyHouseBill: boolean) => {
    const { appContext, pageContext, plugin } = this.props;
    let records = plugin.getListModel().getRecords();
    const successCB = (bills: any[]) => {
      for (let rec of records) {
        let findBillDb = bills.find((b) => b['id'] == rec['id']);
        if (findBillDb) {
          rec['tmsBillFee'] = findBillDb['tmsBillFee'] ? findBillDb['tmsBillFee'] : {};
          rec['verifyHblNo'] = findBillDb['tmsBillForwarderTransport']['verifyHblNo'];
        }
      }
      const createContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
        let config: bs.TabPaneConfig = {
          tabs: [
            {
              name: 'fee-template', label: T('Fee Template'), active: true,
              renderContent: (_ctx: bs.UIContext) =>
                < TMSBillBFSOneFeeTemplate appContext={appCtx} pageContext={pageCtx}
                  plugin={TMSBillBFSOneFeeTemplate.createPlugin(records)} />
            },
            {
              name: 'vehicle-info-template', label: T('Vehicle Info Template'),
              renderContent: (_ctx: bs.UIContext) =>
                <TMSBillBFSOneVehicleInfoTemplate appContext={appContext} pageContext={pageContext} plugin={TMSBillBFSOneVehicleInfoTemplate.createPlugin(records)} />
            },
          ]
        }

        return (
          <bs.DefaultTabPane className="flex-vbox" config={config} />
        )

      }
      pageContext.createPopupPage('bfs-one-template', T('BFSOne Template'), createContent, { size: 'xl' });
      try {
        bs.dialogHideById(dialogId);
      } catch (e) { }
    }

    let ids: any[] = records.map(rec => rec['id']);
    if (verifyHouseBill) {
      appContext
        .createHttpBackendCall('TMSBillService', 'verifyTMSBillHblNos', { ids: ids })
        .withSuccessData(successCB)
        .call();
    } else {
      appContext
        .createHttpBackendCall('TMSBillService', 'findTMSBillByIds', { ids: ids })
        .withSuccessData(successCB)
        .call();
    }
    let html = (
      <h4>
        {'Loading... Please wait.'}
      </h4>
    )
    let dialogId = bs.dialogShow(T(`Loading`), html, { backdrop: 'static' })

  }

  createVGridConfig() {
    let { appContext, pageContext, plugin } = this.props;
    let config: grid.VGridConfig = {
      title: T("TMS Report Bill"),
      record: {
        fields: [
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          { name: 'code', label: T('Code') },
          { name: 'label', label: T('File No.') },
          {
            name: 'hwbNo', label: T('HBL No'),
          },
          {
            name: 'dateTime', label: T('Ngày'), width: 100, cssClass: 'flex-grow-1 text-end', dataType: 'date',
            sortable: true, filterableType: 'date', filterable: true, format: util.text.formater.compactDate,
          },
          {
            name: "responsibleFullName", label: T('Người chịu trách nhiệm'), width: 200, cssClass: 'pe-1', filterableType: 'options', filterable: true,
          },
          {
            name: 'office', label: T('Văn phòng'), width: 120, hint: 'Office', filterableType: 'Options', filterable: true,
          },
          {
            name: 'customerFullName', label: T('Khách hàng'), width: 170, cssClass: 'px-1', sortable: true,
            dataTooltip: true, filterableType: 'Options', filterable: true,
          },
          {
            name: 'mode', label: T('Loại'), width: 100, filterableType: 'options', filterable: true, sortable: true,
          },
          { name: 'bookingCode', label: T('Booking/Bill'), width: 150 },
          {
            name: 'truckType', label: T('Loại xe'), cssClass: 'justify-content-center ',
            filterableType: 'options', filterable: true,
          },
          { name: 'containerNo', label: T('Container No') },
          {
            name: 'licensePlate', label: T('Biển số xe'), width: 120,
            filterableType: 'options', filterable: true,
            fieldDataGetter: (record) => {
              let vendorBills: Array<any> = record['vendorBills'];
              if (vendorBills) {
                return vendorBills.map(vendorBill => vendorBill.licensePlate).join(', ');
              }
              return null;
            },
          },
          {
            name: 'address', label: T('Địa chỉ'), width: 250, dataTooltip: true,
            fieldDataGetter(record) {
              let mode = record['mode'];
              if (TMSBillTransportationModeTools.isExport(mode)) {
                return record['senderAddress'];
              }
              if (TMSBillTransportationModeTools.isImport(mode)) {
                return record['receiverAddress'];
              }
              return `${record['senderAddress']} - ${record['receiverAddress']}`;
            },
          },
          {
            name: 'round', label: T('Tuyến đường'), width: 250, dataTooltip: true,
            fieldDataGetter(record) {
              let mode = record['mode'];
              let whLabel;
              let senderLocationSubdistrict = record['senderLocationSubdistrict'];
              let senderLocationState = record['senderLocationState'];
              let senderLocationId = record['senderLocationId'];
              let receiverLocationSubdistrict = record['receiverLocationSubdistrict'];
              let receiverLocationState = record['receiverLocationState'];
              let receiverLocationId = record['receiverLocationId'];
              let senderLabel = senderLocationId ? senderLocationSubdistrict + ', ' + senderLocationState : record['senderAddress'];
              let receiverLabel = receiverLocationId ? receiverLocationSubdistrict + ', ' + receiverLocationState : record['receiverAddress'];
              if (TMSBillTransportationModeTools.isExport(mode)) {
                whLabel = record['receiverAddress'];
                receiverLabel = `${whLabel}, ${receiverLabel}`;
              }
              if (TMSBillTransportationModeTools.isImport(mode)) {
                whLabel = record['senderAddress'];
                senderLabel = `${whLabel}, ${senderLabel}`;
              }

              let stopLocations: any[] = record['stopLocations'];
              let locationLabels: any[] = [];
              if (stopLocations && stopLocations.length > 0) {
                for (let stopLocation of stopLocations) {
                  const locationLabel = stopLocation['locationLabel'];
                  if (locationLabel) {
                    let split: any[] = locationLabel.split(',');
                    if (split.length > 3) {
                      let first = split[0];
                      let end = split[split.length - 2];
                      locationLabels.push(`${first.trim()}, ${end.trim()}`);
                    }
                  }
                }
              }

              let label = `${senderLabel} - ${receiverLabel}`;
              if (locationLabels.length > 0) {
                label = `${senderLabel} - ${locationLabels.join(' - ')} - ${receiverLabel}`;
              }
              label = label.replaceAll('Xã', '').replaceAll('Phường', '').replaceAll('Thị trấn', '').trim();
              label = label.replaceAll('Tỉnh', '').replaceAll('Thành phố', '').trim();
              label = label.replaceAll("  ", " ");
              return label;
            },
          },
          {
            name: 'feedback', label: T(`Phản hồi`), width: 200
          },
          {
            name: 'fixedPayment', label: T(`Cước`), dataType: 'double'
          },
          {
            name: 'extraPayment', label: T(`Cước phát sinh`), dataType: 'double'
          },
          {
            name: 'totalPayment', label: T('Tổng'), dataType: 'double'
          },
          {
            name: 'vendorFixed', label: T('Cước (Thầu phụ)'), dataType: 'double'
          },
          {
            name: 'vendorExtra', label: T('Cước phát sinh (Thầu phụ)'), width: 200, dataType: 'double'
          },
          {
            name: 'vendorCost', label: T('Tổng (Thầu phụ)'), dataType: 'double'
          },
        ],

      },

      toolbar: {
        actions: [
        ],
        filters: entity.DbEntityListConfigTool.TOOLBAR_FILTERS(!!plugin.searchParams),
      },
      footer: {
        page: {
          render: (ctx: grid.VGridContext) => {
            return (
              <bs.Toolbar className='border' >
                <bs.Button laf="info" onClick={this.verifyAndCreateBFSOneTemplate}>
                  <FeatherIcon.Check size={12} /> {T('Create BFS Template')}
                </bs.Button>
                <XLSXCustomButton className="p-2 my-1" tableName="tms-bill" context={ctx}
                  options={{ fileName: `Vendor Bill ${util.TimeUtil.toCompactDateFormat(new Date())}.xlsx`, modelName: 'tms-bill' }}
                  appContext={appContext} pageContext={pageContext} fieldSelect={'all'} selectMode="all" />
              </bs.Toolbar>
            )
          }
        },
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          },
        }
      }
    }
    return config;
  }
}