import React from "react";
import { bs, grid, util, entity, app } from "@datatp-ui/lib";
import * as FeatherIcon from "react-feather";

import { T } from "../backend";
import { XLSXCustomButton } from "../XLSXButton";

const MAP_FIELDS: Record<string, string> = {
  'id': 'id',
  'hwbNo': 'hwbNo',
  'vendorBfsOneCode': 'vendorBfsOneCode',
  'vendorFullName': 'vendorName',
  'totalPayment': 'unitPrice',
  'paymentNote': 'notes',
  'verifyHblNo': 'verifyHblNo',
  'containerNo': 'contNo',
};

const MAP_UNIT: Record<string, string> = {
  '20ISOTANK': '20´ ISO TANK',
  '20DC': '20´DC',
  '20FF': '20´FF',
  '20FL': '20´FL',
  '20FR': '20´FR',
  '20GP': '20´GP',
  '20OT': '20´OT',
  '20RF': '20´RF',
  '20RH': '20´RH',
  '20TK': '20´TK',
  '30DC': '30´DC',
  '40DC': '40´DC',
  '40FF': '40´FF',
  '40FL': '40´FL',
  '40FR': '40´FR',
  '40GP': '40´GP',
  '40HC': '40´HC',
  '40HF': '40´HF',
  '40HQ': '40´HQ',
  '40HR': '40´HR',
  '40OT': '40´OT',
  '40RF': '40´RF',
  '40RH': '40´RH',
  '40RQ': '40´RQ',
  '40TK': '40´TK',
  '45RF': '45 RF',
  '45DC': '45´DC',
  '45HQ': '45´HQ',
  '45HC': '40´HQ',
  '48RF': '48 RF',
  '50DC': '50´DC',

  '0.5T': '0.5T',
  '1T': '1T',
  '1.25T': '1.25T',
  '1.5T': '1.5T',
  '1.8T': '1.8T',
  '10T': '10T',
  '11T': '11T',
  '12T': '12T',
  '14T': '14T',
  '15T': '15T',
  '16T': '16T',
  '17T': '17T',
  '2T': '2T',
  '2.5T': '2.5T',
  '3T': '3T',
  '3.5T': '3.5T',
  '5T': '5T',
  '7T': '7T',
  '8T': '8T',
  '9T': '9T'
};

export function getBfsOneUnit(unit: string) {
  if (MAP_UNIT[unit]) {
    return MAP_UNIT[unit];
  } else {
    return unit;
  }
}

export class TMSBillBFSOneFeeTemplate extends entity.DbEntityList {
  showInfo: boolean = false;
  static createPlugin = (records: Array<any>) => {
    return new entity.DbEntityListPlugin(
      TMSBillBFSOneFeeTemplate.mapToBfsOneData(records)
    );
  }

  static mapToBfsOneData = (records: Array<any>) => {
    let bfsData: any[] = [];
    for (let rec of records) {
      let bfsRec: any = {};
      for (let [key, value] of Object.entries(MAP_FIELDS)) {
        bfsRec[value] = rec[key];
      }
      bfsRec['unit'] = getBfsOneUnit(rec['truckType']);
      bfsRec['truckOnly'] = false;
      bfsRec['appendToCharge'] = true;
      bfsRec['chargeGroup'] = 'CREDIT';
      bfsRec['feeName'] = 'DOMESTIC TRUCKING FEE';
      bfsRec['feeCode'] = 'B_TRUCK';
      bfsRec['quantity'] = 1;
      bfsRec['currency'] = 'VND';
      bfsRec['exchangeRate'] = 1;
      bfsRec['vat'] = 8;
      bfsRec['unitPrice'] = 0;
      bfsRec['cost'] = 0;

      let senderInvAddress: string = rec['senderInvAddress'] ? rec['senderInvAddress'] : '';
      senderInvAddress = senderInvAddress
        .replace('Xã', '').replace('Phường', '').replace('Thị trấn', '')
        .replace('Tỉnh', '').replace('Thành phố', '').trim();
      let receiverInvAddress: string = rec['receiverInvAddress'] ? rec['receiverInvAddress'] : '';
      receiverInvAddress = receiverInvAddress
        .replace('Xã', '').replace('Phường', '').replace('Thị trấn', '')
        .replace('Tỉnh', '').replace('Thành phố', '').trim();

      bfsRec['truckingFrom'] = senderInvAddress;
      bfsRec['truckingTo'] = receiverInvAddress;
      if (rec['containerNo']) {
        bfsRec['contType'] = rec['truckType'];
      } else {
        bfsRec['truckType'] = rec['truckType'];
      }

      const trackings: Array<any> = rec['trackings'];
      const vendorBills: any[] = rec['vendorBills'];
      const tmsBillFee = rec['tmsBillFee'] ? rec['tmsBillFee'] : {};

      const costItems: Array<any> = tmsBillFee['costItems'] ? tmsBillFee['costItems'] : [];
      let vehicleMap: Record<string, any[]> = {};
      let defaultId = -1;
      if (vendorBills) {
        let vehicleInfo: any = {};
        let truckingNos: string[] = [];
        for (let vendorBill of vendorBills) {
          const licensePlate = vendorBill['licensePlate']
          if (licensePlate) truckingNos.push(licensePlate);
          vehicleInfo['truckingNo'] = vendorBill['licensePlate'];
          vehicleInfo['driverName'] = vendorBill['driverFullName'];
          vehicleInfo['driverTel'] = vendorBill['driverMobile'];
        }
        vehicleInfo['truckingNo'] = truckingNos.join(', ');
        vehicleMap[defaultId] = vehicleInfo;
      }

      if (trackings) {
        if (trackings.length == 1) {
          let tracking = trackings[0];
          let vehicleInfo: any = {
            'vendorBfsOneCode': tracking['fleetBfsOneCode'],
            'truckingNo': tracking['vehicleLabel'],
            'driverName': tracking['driverFullName'],
            'driverTel': tracking['mobile'],
            'vendorName': tracking['fleetLabel'],
          };
          vehicleMap[defaultId] = vehicleInfo;
          vehicleMap[tracking.id] = vehicleInfo;
        } else {
          for (let tracking of trackings) {
            let vehicleInfo: any = {
              'vendorBfsOneCode': tracking['fleetBfsOneCode'],
              'truckingNo': tracking['vehicleLabel'],
              'driverName': tracking['driverFullName'],
              'driverTel': tracking['mobile'],
              'vendorName': tracking['fleetLabel'],
            };
            vehicleMap[tracking.id] = vehicleInfo;
          }
        }
      }

      if (costItems.length > 0) {
        let feeMap: Record<string, any[]> = {};
        for (let costItem of costItems) {
          let refEntityId = costItem['refEntityId'] ? costItem['refEntityId'] : defaultId;
          if (feeMap[refEntityId]) {
            feeMap[refEntityId].push(costItem);
          } else {
            feeMap[refEntityId] = [costItem];
          }
        }

        for (const entry of Object.entries(feeMap)) {
          const [key, items] = entry;
          let vehicleInfo = vehicleMap[key] ? vehicleMap[key] : {};
          let cloneBfsRec = { ...bfsRec, ...vehicleInfo };
          if (cloneBfsRec['vendorBfsOneCode'] == 'CS000198' || cloneBfsRec['vendorBfsOneCode'] == 'CS040943') cloneBfsRec['vat'] = 0;
          let itemIds: Array<any> = [];
          for (let item of items) {
            cloneBfsRec['unit'] = item['unit'];
            cloneBfsRec['unitPrice'] += item['cost'];
            cloneBfsRec['cost'] += item['cost'] * item['quantity'];
            itemIds.push(item['id']);
          }
          cloneBfsRec['total'] = cloneBfsRec['cost'] + cloneBfsRec['cost'] * (cloneBfsRec['vat'] / 100);
          cloneBfsRec['unit'] = getBfsOneUnit(cloneBfsRec['unit']);
          cloneBfsRec['itemIds'] = itemIds;
          bfsData.push(cloneBfsRec);
        }
      } else {
        let vehicleInfo = vehicleMap[defaultId] ? vehicleMap[defaultId] : {};
        bfsRec = { ...bfsRec, ...vehicleInfo };
        bfsData.push({ ...bfsRec });
      }
    }

    this.calculateFeeSeq(bfsData);
    grid.initRecordStates(bfsData);
    for (let bfsRec of bfsData) {
      let verifyUnit = Object.values(MAP_UNIT).includes(bfsRec['unit']);
      if (bfsRec['total'] > 0 && bfsRec['verifyHblNo'] && bfsRec['vendorBfsOneCode'] && verifyUnit) {
        grid.getRecordState(bfsRec).selected = true;
      }

      let verifyPaymentNotes: Array<string> = [];
      if (bfsRec['total'] == 0 || !bfsRec['total']) verifyPaymentNotes.push("Cost = 0");
      if (!verifyUnit) verifyPaymentNotes.push("Unit chưa xác minh");
      if (!bfsRec['verifyHblNo']) verifyPaymentNotes.push("House Bill chưa xác minh");
      if (!bfsRec['vendorBfsOneCode']) verifyPaymentNotes.push("BFSOneCode thầu phụ trống");
      if (verifyPaymentNotes.length > 0) bfsRec.verifyPaymentNote = verifyPaymentNotes.join("\n");

      let verifyVehicleInfoNotes: Array<string> = [];
      if (!bfsRec['truckingFrom']) verifyVehicleInfoNotes.push('Điểm đi trống');
      if (!bfsRec['truckingTo']) verifyVehicleInfoNotes.push('Điểm đến trống');
      if (!bfsRec['truckingNo']) verifyVehicleInfoNotes.push('Biển số xe trống');
      if (verifyVehicleInfoNotes.length > 0) bfsRec.verifyVehicleInfoNote = verifyVehicleInfoNotes.join("\n");
    }
    return bfsData;
  }

  static calculateFeeSeq = (bfsData: any[]) => {
    let hblMap: Record<string, any[]> = {};
    for (let i = 0; i < bfsData.length; i++) {
      let rec = bfsData[i];
      rec['index'] = i + 1;
      let hwbNo = rec['hwbNo'];
      if (hwbNo) {
        if (!hblMap[hwbNo]) {
          hblMap[hwbNo] = [];
        }
        hblMap[hwbNo].push(rec);
      }
    }
    for (let hwbNo in hblMap) {
      let recs: Array<any> = hblMap[hwbNo];
      if (recs.length > 0) {
        for (let i = 0; i < recs.length; i++) {
          if (i == 0) continue;
          let rec = recs[i];
          rec['seq'] = i;
        }
      }
    }
  }

  verifyAndClosePayment = () => {
    let { pageContext } = this.props
    let createAppPage = (_appCtx: app.AppContext, _pageCtx: app.PageContext) => {
      return (
        <div className="flex-vbox">
          <h4>{'Are you sure to close cost TMS Bills?'}</h4>
          <bs.Toolbar>
            <bs.Button laf="success" onClick={() => {
              this.verifyAndClosePaymentCommit(true);
              _pageCtx.back();
            }}>
              <FeatherIcon.Check size={12} /> {T('Confirm')}
            </bs.Button>
            <bs.Button laf="danger" onClick={() => {
              this.verifyAndClosePaymentCommit(false)
              _pageCtx.back();
            }}>
              <FeatherIcon.X size={12} />{T('Cancel')}
            </bs.Button>
          </bs.Toolbar>
        </div>
      )
    };
    pageContext.createPopupPage('confirm', 'Confirm Close Cost TMS Bills!', createAppPage, { backdrop: 'static' })
  }

  verifyAndClosePaymentCommit = (closePayment: boolean) => {
    let { appContext, plugin } = this.props;
    let params: Array<any> = [];
    for (let rec of plugin.getListModel().getRecords()) {
      let state = grid.getRecordState(rec);
      let param = {
        'tmsBillId': rec['id'],
        'closePayment': false,
        'verifyPaymentNote': rec['verifyPaymentNote'],
        'verifyVehicleInfoNote': rec['verifyVehicleInfoNote'],
        'costItemIds': rec['itemIds']
      }
      if (state.isSelected() && closePayment) {
        param.closePayment = true;
      }
      params.push(param);
    }
    appContext.createHttpBackendCall('TMSBillService', 'verifyAndClosePaymentTMSBillFees', { params: params })
      .withEntityOpNotification('commit', 'Paid Tms Bill Success!!!!')
      .call();
  }

  originRecs: Array<any> = [];
  constructor(props: entity.DbEntityListProps) {
    super(props);
    this.originRecs = [...props.plugin.getListModel().getRecords()];
  }

  mergeHblCost = () => {
    let { plugin } = this.props;
    if (!this.showInfo) {
      let results: any[] = [];
      let mapRec: Record<string, any[]> = {};
      for (let rec of this.originRecs) {
        let hwbNo = rec['hwbNo'];
        let unit = rec['unit'];
        let price = rec['unitPrice'];
        let vendorBfsOneCode = rec['vendorBfsOneCode'];
        if (hwbNo) {
          let key = `${vendorBfsOneCode}-${hwbNo}-${unit}-${price}`;
          if (!mapRec[key]) {
            mapRec[key] = [];
          }
          mapRec[key].push(rec);
        } else {
          results.push(rec);
        }
      }

      for (let key in mapRec) {
        let recs = mapRec[key];
        if (recs.length > 0) {
          let mergedRec: any = { ...recs[0] };
          let truckingNos: string[] = [];
          let quantity = 0;
          recs.forEach((rec: any) => {
            quantity += rec['quantity'];
            if (rec['truckingNo']) truckingNos.push(rec['truckingNo'])
          })
          mergedRec['quantity'] = quantity;
          let totalUnitPrice = mergedRec['unitPrice'] * mergedRec['quantity'];
          mergedRec['total'] = totalUnitPrice + (mergedRec['vat'] / 100) * totalUnitPrice;
          mergedRec['truckingNo'] = truckingNos.join(', ');
          results.push(mergedRec);
        }
      }
      plugin.getListModel().replaceWith(results);
    } else {
      plugin.getListModel().replaceWith(this.originRecs);
    }
    TMSBillBFSOneFeeTemplate.calculateFeeSeq(plugin.getListModel().getRecords());
    this.showInfo = !this.showInfo;
    this.forceUpdate();
  }

  createVGridConfig() {
    let { appContext, pageContext } = this.props;
    let config: grid.VGridConfig = {
      title: T("BFS One Template"),
      record: {
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(true),
          { name: 'index', label: 'NO', width: 50, container: 'fixed-left' },
          { name: 'hwbNo', label: 'HWBNO' },
          { name: 'vendorBfsOneCode', label: 'PartnerID' },
          { name: 'vendorName', label: 'PartnerName' },
          {
            name: 'feeName', label: 'FeeName', fieldDataGetter(record) {
              let seq = record['seq'];
              if (seq) return `${record['feeName']}(${seq})`;
              return record['feeName'];
            },
          },
          { name: 'feeCode', label: 'FeeCode' },
          { name: 'unit', label: 'Unit' },
          { name: 'quantity', label: 'Quantity', dataType: 'double' },
          { name: 'unitPrice', label: 'UnitPrice', dataType: 'currency' },
          { name: 'currency', label: 'Currency' },
          { name: 'exchangeRate', label: 'ExchangeRate', dataType: 'double' },
          { name: 'vat', label: 'VAT', dataType: 'double' },
          { name: 'total', label: 'Total', dataType: 'currency' },
          { name: 'notes', label: 'Notes' },
          { name: 'chargeGroup', label: 'ChargeGroup' },
          { name: 'truckingFrom', label: 'TruckingFrom' },
          { name: 'truckingTo', label: 'TruckingTo' },
          { name: 'truckingNo', label: 'TruckNo' },
          { name: 'truckType', label: 'TruckType' },
          { name: 'driverName', label: 'DriverName' },
          { name: 'driverTel', label: 'DriverTel' },
          { name: 'contNo', label: 'ContNo' },
          { name: 'contType', label: 'ContType' },
          { name: 'deliveryDate', label: 'DeliveryDate' },
          { name: 'truckOnly', label: 'TruckOnly' },
          { name: 'appendToCharge', label: 'AppendToCharge' },
          {
            name: '_error_', label: T('Error'),
            fieldDataGetter(record) {
              return record['verifyPaymentNote'];
            },
          },
        ],
      },

      toolbar: {
        actions: [
        ],
        filters: entity.DbEntityListConfigTool.TOOLBAR_FILTERS(false),
      },
      footer: {
        page: {
          render: (ctx: grid.VGridContext) => {
            let ids = ctx.model.getRecords().map((record) => record.id);
            return (
              <bs.Toolbar className='border' >
                <div className='flex-grow-0 flex-hbox align-items-center mt-1 mx-1' style={{ height: 15 }}>
                  <div className="form-check form-switch py-0 m-0">
                    <input className="form-check-input p-0" type="checkbox" style={{ cursor: 'pointer' }}
                      role="switch" id="rawDataToggle" checked={this.showInfo}
                      onChange={this.mergeHblCost}
                    />
                  </div>
                  <label className="form-check-label fs--1 d-flex align-items-center" htmlFor="rawDataToggle" >
                    <span>{T('Merge Row')}</span>
                  </label>
                </div>
                <bs.Button laf="info" onClick={this.verifyAndClosePayment}>
                  <FeatherIcon.CheckCircle size={12} className="me-1" /> {T('Close Payment')}
                </bs.Button>
                <XLSXCustomButton className="p-2 my-1" tableName="bfs-one-template" context={ctx}
                  options={{ fileName: `BFS One Template ${util.TimeUtil.toCompactDateFormat(new Date())}.xlsx`, modelName: 'bfs-one-template' }}
                  appContext={appContext} pageContext={pageContext} fieldSelect={'all'} selectMode="selected" onExported={
                    () => {
                      appContext.createHttpBackendCall('TMSRestCallService', 'updateFeeExporter', { ids: ids, feeExporter: app.host.DATATP_SESSION.getAccountAcl().getLoginId() }).call();
                      this.verifyAndClosePayment();
                    }

                  } />
              </bs.Toolbar>
            )
          }
        },
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          },
        }
      }
    }
    return config;
  }
}

export class TMSBillBFSOneVehicleInfoTemplate extends entity.DbEntityList {
  showInfo: boolean = false;
  static createPlugin = (records: Array<any>) => {
    return new entity.DbEntityListPlugin(
      TMSBillBFSOneVehicleInfoTemplate.mapToBfsOneData(records)
    );
  }

  static mapToBfsOneData = (records: Array<any>) => {
    let bfsData: any[] = [];
    for (let rec of records) {
      let bfsRec: any = {};
      bfsRec['id'] = rec['id'];
      bfsRec['hwbNo'] = rec['hwbNo'];
      bfsRec['truckOnly'] = true;
      bfsRec['verifyHblNo'] = rec['verifyHblNo']

      if (rec['containerNo']) {
        bfsRec['contType'] = rec['truckType'];
      } else {
        bfsRec['truckType'] = rec['truckType'];
      }
      let senderInvAddress: string = rec['senderInvAddress'] ? rec['senderInvAddress'] : '';
      senderInvAddress = senderInvAddress
        .replace('Xã', '').replace('Phường', '').replace('Thị trấn', '')
        .replace('Tỉnh', '').replace('Thành phố', '').trim();
      let receiverInvAddress: string = rec['receiverInvAddress'] ? rec['receiverInvAddress'] : '';
      receiverInvAddress = receiverInvAddress
        .replace('Xã', '').replace('Phường', '').replace('Thị trấn', '')
        .replace('Tỉnh', '').replace('Thành phố', '').trim();

      bfsRec['truckingFrom'] = senderInvAddress;
      bfsRec['truckingTo'] = receiverInvAddress;

      const trackings: Array<any> = rec['trackings'];
      if (trackings) {
        for (let tracking of trackings) {
          let vehicleInfo: any = {};
          if (tracking['vehicleLabel']) vehicleInfo.truckingNo = tracking['vehicleLabel'];
          if (tracking['driverFullName']) vehicleInfo.driverName = tracking['driverFullName'];
          if (tracking['mobile']) vehicleInfo.mobile = tracking['driverTel'];

          if (rec['containerNo']) {
            bfsRec['contType'] = tracking['vehicleType'];
          } else {
            bfsRec['truckType'] = rec['vehicleType'];
          }
          let newRec = { ...bfsRec, ...vehicleInfo };
          bfsData.push(newRec);
        }
        continue;
      }

      const vendorBills: Array<any> = rec['vendorBills'];
      if (vendorBills) {
        for (let vendorBill of vendorBills) {
          let vehicleInfo: any = {};
          if (vendorBill['licensePlate']) vehicleInfo.truckingNo = vendorBill['licensePlate'];
          if (vendorBill['driverFullName']) vehicleInfo.driverName = vendorBill['driverFullName'];
          if (vendorBill['driverMobile']) vehicleInfo.mobile = vendorBill['driverMobile'];
          let rec = { ...bfsRec, ...vehicleInfo };
          bfsData.push(rec);
        }
        continue;
      }
      bfsData.push(bfsRec);
    }

    grid.initRecordStates(bfsData);
    for (let i = 0; i < bfsData.length; i++) {
      let rec = bfsData[i];
      rec['index'] = i + 1;
      let verifyVehicleInfoNotes: Array<string> = [];
      if (!rec['truckingFrom']) verifyVehicleInfoNotes.push('Địa chỉ lấy hàng trống');
      if (!rec['truckingTo']) verifyVehicleInfoNotes.push('Địa chỉ giao hàng trống');
      if (!rec['truckingNo']) verifyVehicleInfoNotes.push('Thông tin xe trống');
      if (!rec['verifyHblNo']) verifyVehicleInfoNotes.push('HblNo chưa xác minh');
      if (verifyVehicleInfoNotes.length > 0) {
        rec.verifyVehicleInfoNote = verifyVehicleInfoNotes.join("\n");
      } else {
        grid.getRecordState(rec).selected = true;
      }
    }
    return bfsData;
  }

  originRecs: Array<any> = [];
  constructor(props: entity.DbEntityListProps) {
    super(props);
    this.originRecs = [...props.plugin.getListModel().getRecords()];
  }

  merge = () => {
    let { plugin } = this.props;
    if (!this.showInfo) {
      let results: any[] = [];
      let mapRec: Record<string, any[]> = {};
      for (let rec of this.originRecs) {
        let hwbNo = rec['hwbNo'];
        let truckingFrom = rec['truckingFrom'];
        let truckingTo = rec['truckingTo'];
        if (hwbNo) {
          let key = `${hwbNo}-${truckingFrom}-${truckingTo}`;
          if (!mapRec[key]) {
            mapRec[key] = [];
          }
          mapRec[key].push(rec);
        } else {
          results.push(rec);
        }
      }

      for (let key in mapRec) {
        let recs = mapRec[key];
        if (recs.length > 0) {
          let mergedRec: any = { ...recs[0] };
          let truckingNos: Array<string> = [];
          let driverNames: Array<string> = [];
          let driverTels: Array<string> = [];
          recs.forEach((rec: any) => {
            if (rec['truckingNo']) truckingNos.push(rec['truckingNo'])
            if (rec['driverName']) driverNames.push(rec['driverName'])
            if (rec['driverTel']) driverTels.push(rec['driverTel'])
          })
          mergedRec['truckingNo'] = truckingNos.join(', ');
          mergedRec['driverName'] = driverNames.join(', ');
          mergedRec['driverTel'] = driverTels.join(', ');

          results.push(mergedRec);
        }
      }

      plugin.getListModel().replaceWith(results);
    } else {
      plugin.getListModel().replaceWith(this.originRecs);
    }
    this.showInfo = !this.showInfo;
    this.forceUpdate();
  }

  verifyAndCloseVehicleInfo = () => {
    let { pageContext } = this.props;
    let createAppPage = (_appCtx: app.AppContext, _pageCtx: app.PageContext) => {
      return (
        <div className="flex-vbox">
          <h4>{'Are you sure to close vehicle info TMS Bills?'}</h4>
          <bs.Toolbar>
            <bs.Button laf="success" onClick={() => {
              this.verifyAndCloseVehicleInfoCommit(true);
              _pageCtx.back();
            }}>
              <FeatherIcon.Check size={12} /> {T('Confirm')}
            </bs.Button>
            <bs.Button laf="danger" onClick={() => {
              this.verifyAndCloseVehicleInfoCommit(false)
              _pageCtx.back();
            }}>
              <FeatherIcon.X size={12} />{T('Cancel')}
            </bs.Button>
          </bs.Toolbar>
        </div>
      )
    };
    pageContext.createPopupPage('confirm', 'Confirm Close Vehicle Info TMS Bills!', createAppPage, { backdrop: 'static' })
  }


  verifyAndCloseVehicleInfoCommit = (closeVehicleInfo: boolean) => {
    let { appContext, plugin } = this.props;
    let params: Array<any> = [];
    for (let rec of plugin.getListModel().getRecords()) {
      let state = grid.getRecordState(rec);
      let param = {
        'tmsBillId': rec['id'],
        'closeVehicleInfo': false,
        'verifyVehicleInfoNote': rec['verifyVehicleInfoNote'],
      }
      if (state.isSelected() && closeVehicleInfo) {
        param.closeVehicleInfo = true;
      }
      params.push(param);
    }
    appContext.createHttpBackendCall('TMSBillService', 'verifyAndCloseTMSBillVehicleInfo', { params: params })
      .withEntityOpNotification('commit', 'Verify Tms Bill Success!!!!')
      .call();
  }


  createVGridConfig() {
    let { appContext, pageContext } = this.props;
    let config: grid.VGridConfig = {
      title: T("BFS One Template"),
      record: {
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(true),
          { name: 'index', label: 'NO', width: 50, container: 'fixed-left' },
          { name: 'hwbNo', label: 'HWBNO' },
          { name: 'vendorBfsOneCode', label: 'PartnerID' },
          { name: 'vendorName', label: 'PartnerName' },
          { name: 'feeName', label: 'FeeName' },
          { name: 'feeCode', label: 'FeeCode' },
          { name: 'unit', label: 'Unit' },
          { name: 'quantity', label: 'Quantity' },
          { name: 'unitPrice', label: 'UnitPrice' },
          { name: 'currency', label: 'Currency' },
          { name: 'exchangeRate', label: 'ExchangeRate' },
          { name: 'vat', label: 'VAT' },
          { name: 'total', label: 'Total' },
          { name: 'notes', label: 'Notes' },
          { name: 'chargeGroup', label: 'ChargeGroup' },
          { name: 'truckingFrom', label: 'TruckingFrom' },
          { name: 'truckingTo', label: 'TruckingTo' },
          { name: 'truckingNo', label: 'TruckNo' },
          { name: 'truckType', label: 'TruckType' },
          { name: 'driverName', label: 'DriverName' },
          { name: 'driverTel', label: 'DriverTel' },
          { name: 'contNo', label: 'ContNo' },
          { name: 'contType', label: 'ContType' },
          { name: 'deliveryDate', label: 'DeliveryDate' },
          { name: 'truckOnly', label: 'TruckOnly' },
          { name: 'appendToCharge', label: 'AppendToCharge' },
          {
            name: '_error_', label: T('Error'),
            fieldDataGetter(record) {
              return record['verifyVehicleInfoNote'];
            },
          },
        ],
      },

      toolbar: {
        actions: [
        ],
        filters: entity.DbEntityListConfigTool.TOOLBAR_FILTERS(false),
      },
      footer: {
        page: {
          render: (ctx: grid.VGridContext) => {
            let ids = ctx.model.getRecords().map((record) => record.id);
            return (
              <bs.Toolbar className='border' >
                <div className='flex-grow-0 flex-hbox align-items-center mt-1 mx-1' style={{ height: 15 }}>
                  <div className="form-check form-switch py-0 m-0">
                    <input className="form-check-input p-0" type="checkbox" style={{ cursor: 'pointer' }}
                      role="switch" id="rawDataToggle" checked={this.showInfo}
                      onChange={this.merge}
                    />
                  </div>
                  <label className="form-check-label fs--1 d-flex align-items-center" htmlFor="rawDataToggle" >
                    <span>{T('Merge Row')}</span>
                  </label>
                </div>
                <bs.Button laf="info" onClick={this.verifyAndCloseVehicleInfo}>
                  <FeatherIcon.CheckCircle size={12} className="me-1" /> {T('Close Vehicle Info')}
                </bs.Button>
                <XLSXCustomButton className="p-2 my-1" tableName="bfs-one-template" context={ctx}
                  options={{ fileName: `BFS One Template ${util.TimeUtil.toCompactDateFormat(new Date())}.xlsx`, modelName: 'bfs-one-template' }}
                  appContext={appContext} pageContext={pageContext} fieldSelect={'all'} selectMode="selected"
                  onExported={() => {
                    appContext.createHttpBackendCall('TMSRestCallService', 'updateVehicleInfoExporter', { ids: ids, vehicleInfoExporter: app.host.DATATP_SESSION.getAccountAcl().getLoginId() }).call();
                    this.verifyAndCloseVehicleInfo();
                  }} />
              </bs.Toolbar>
            )
          }
        },
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          },
        }
      }
    }
    return config;
  }
}