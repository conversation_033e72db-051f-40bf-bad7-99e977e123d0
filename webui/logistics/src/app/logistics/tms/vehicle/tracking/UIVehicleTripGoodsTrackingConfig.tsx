import React, { Component } from 'react';
import { input, bs, entity, app, grid, util } from '@datatp-ui/lib';
import * as FeatherIcon from "react-feather";

import { T } from '../../backend';

export function showVehicleTripGoodsTrackingConfig(ctx: grid.VGridContext, size?: 'xs' | 'sm' | 'md' | 'lg' | 'flex-lg' | 'xl') {
  let uiRoot = ctx.uiRoot as entity.DbEntityList;
  let { appContext, pageContext } = uiRoot.props;
  let successCB = (data: any) => {
    let configOb = new entity.BeanObserver(data);
    if (!data) configOb = new entity.BeanObserver({});
    let createContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <div className='flex-vbox'>
          <VehicleTripGoodsTrackingConfig appContext={appCtx} pageContext={pageCtx} observer={configOb} />
        </div>
      )
    }
    pageContext.createPopupPage('config', T('Config'), createContent, { size: size });
  }
  appContext
    .createHttpBackendCall('VehicleRestCallService', 'getVehicleTripGoodsTrackingConfig')
    .withSuccessData(successCB)
    .call();
}

export class VehicleTripGoodsTrackingConfig extends entity.AppDbEntityEditor {
  render(): React.ReactNode {
    let { appContext, pageContext, observer } = this.props;
    let config = observer.getMutableBean();
    let writeCap = pageContext.hasUserWriteCapability();
    return (
      <div className='flex-vbox'>
        <div className='flex-grow-1'>
          <input.BBCheckboxField label={T('Input Data Creator')} bean={config} field='inputDataCreator' disable={!writeCap} value={false} />
          <input.BBCheckboxField label={T('Container View')} bean={config} field='containerView' disable={!writeCap} value={false} />
          <input.BBCheckboxField label={T('Truck View')} bean={config} field='truckView' disable={!writeCap} value={false} />
          <input.BBCheckboxField
            label={T('Auto Send Message When Complete Vehicle Info (Zalo, Outlook, Gmail,...)')}
            bean={config} field='autoForwardMessage' disable={!writeCap} value={false} />
          <input.BBCheckboxField
            label={T('Auto Forward Message To Yourself')} bean={config} field='autoForwardMessageYourself' disable={!writeCap} value={false} />
          <input.BBStringArrayField bean={config} label={T('Calculate Plugins')} field={'calculatePlugins'} disable={!writeCap} />
        </div>
        <bs.Toolbar>
          <entity.ButtonEntityCommit
            appContext={appContext} pageContext={pageContext} hide={!writeCap} observer={observer}
            commit={{
              entityLabel: T('Config'), context: 'company',
              service: 'VehicleRestCallService', commitMethod: 'saveVehicleTripGoodsTrackingConfig'
            }}
            onPostCommit={this.onPostCommitCallback} />
        </bs.Toolbar>
      </div>
    )
  }
}

export const RECORD_TOGGLE_FIELD_HANDLER: bs.EventHandler = {
  name: 'record:field:toggle', label: 'Field Toggle', icon: FeatherIcon.Settings,
  handle: (ctx: bs.WidgetContext, _uiSrc: Component, event: bs.Event) => {
    let context = ctx as grid.VGridContext;
    let field = event.param('field');
    context.getVGridConfigModel().getRecordConfigModel().toggleVisibleFieldConfig(field);
    context.getVGrid().forceUpdateView(true);
  }
}

interface UIColumnListProps extends entity.DbEntityListProps {
  ctxRoot: grid.VGridContext;
}
export class UIColumnList extends entity.DbEntityList<UIColumnListProps> {
  createVGridConfig(): grid.VGridConfig {
    let { ctxRoot } = this.props;
    let config: grid.VGridConfig = {
      title: 'Cols',
      record: {
        editor: {
          supportViewMode: ['table'],
          enable: true
        },
        control: {
          width: 25,
          items: [
            {
              name: 'dndrow', hint: 'DND Row', icon: FeatherIcon.Move,
              customRender: (ctx: grid.VGridContext, record: grid.DisplayRecord) => {
                return (<grid.WGridDNDItem key={'dndrow'} context={ctx} row={record.row} />);
              }
            }
          ],
          dnd: {
            dragOverHighlightClass: 'wheat',
            getDroppableTarget: (_ctx: any, target: HTMLElement) => {
              return util.HTMLUtil.getAncestorWithClassName(target, 'cell', 5);
            },
            onDrop: (vgridCtx: grid.VGridContext, _ctx: any, dragEle: HTMLElement, dropEle: HTMLElement) => {
              let dragCell = util.HTMLUtil.getAncestorWithClassName(dragEle, 'cell', 5);
              let dragRecord = grid.VGridUtil.getCellDisplayRecord(vgridCtx, dragCell);
              let dropCell = util.HTMLUtil.getAncestorWithClassName(dropEle, 'cell', 5);
              let dropAtRecord = grid.VGridUtil.getCellDisplayRecord(vgridCtx, dropCell);
              vgridCtx.model.moveDisplayRecord(dragRecord.row, dropAtRecord.row);
              let displayRecords: Array<grid.DisplayRecord> = vgridCtx.model.getDisplayRecordList().getDisplayRecords();
              let records: Array<any> = [];
              for (let i = 0; i < displayRecords.length; i++) {
                let displayRecord = displayRecords[i];
                displayRecord.record.idx = displayRecord.row + 1;
                records.push({ ...displayRecord.record, sortOrder: displayRecord.row });
              }
              this.updateAfterDrop(vgridCtx, records);
            }
          }
        },

        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector()),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          { name: 'label', label: 'Label' },
          { name: 'hint', label: 'Hint' },
          {
            name: 'visible', label: 'Visible', cssClass: 'flex-vbox align-items-center', editor: {
              type: 'boolean',
              renderCustom(ctx, _onInputChange) {
                let col = ctx.displayRecord.record;
                let uiRoot = ctxRoot.uiRoot as entity.DbEntityList;
                let state = col.state ? col.state : {};
                return (<input.BBCheckboxField bean={state} field='visible' value={false}
                  onInputChange={(bean: any, _field: string, _oldVal: any, _newVal: any) => {
                    bean['state'] = bean;
                    let config = ctxRoot.getVGridConfigModel().getRecordConfigModel().getFieldConfig(col.name);
                    config.state = { 'visible': _newVal };
                  }} />)
              },
            }
          },
          {
            name: 'container', label: 'Container', width: 200,
            editor: {
              type: 'boolean',
              renderCustom(ctx, _onInputChange) {
                let bean = ctx.displayRecord.record;
                return (<input.BBRadioInputField bean={bean} field='container'
                  options={['fixed-left', 'default', 'fixed-right']}
                  optionLabels={['Trái', 'Giữa', 'Phải',]} />)
              },
            }
          },
        ]
      },
      toolbar: {
        actions: [],
        filters: entity.DbEntityListConfigTool.TOOLBAR_FILTERS(false)
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          },
        }
      }
    }
    return config;
  }

  render() {
    if (this.isLoading()) return this.renderLoading();
    let context = this.getVGridContext();
    let html = (<grid.VGrid context={context} />);
    return html;
  }

  onModifyBean = (newRoutes: Array<any>, action: entity.ModifyBeanActions) => {
    const { onModifyBean } = this.props;
    this.forceUpdate();
    this.viewId = util.IDTracker.next();
    if (onModifyBean) onModifyBean(newRoutes, action);
  }

  updateAfterDrop(_vgridCtx: grid.VGridContext, records: Array<any>) {
    const { plugin } = this.props;
    plugin.getListModel().update(records)
    this.onModifyBean(records, entity.ModifyBeanActions.MODIFY);
  }
}