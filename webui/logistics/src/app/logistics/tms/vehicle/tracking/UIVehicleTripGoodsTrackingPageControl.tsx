import React, { Component } from "react";
import * as FeatherIcon from 'react-feather';

import { bs, grid, server, app, input, util, sql, entity } from '@datatp-ui/lib';

import { T } from "../../backend";
import { UIVehicleTrip } from "../../../vehicletrip/UIVehicleTrip";
import { UIVehicleTripGoodsTrackingList, UIVehicleTripGoodsTrackingListPlugin } from "./UIVehicleTripGoodsTrackingList";
import { XLSXCustomButton } from "../../XLSXButton";
import { VehicleTripGoodsTrackingStatus, VehicleTripStatus } from '../../models';
import { UIVehicleExpenseReport } from "../UIVehicleExpenseReport";
import { UIDriverSalaryReportList, UIDriverSalaryReportListPlugin } from "../report/UIDriverSalaryReportList";
import { WButtonListenDataChange } from "../../utils";
import { EntityFocus } from "./UIVehicleTripGoodsTrackingListBase";
import { UITMSBillUtils } from "../../bill/UITMSBillUtils";
import { UITMSBillMessage } from "../../bill/UITMSBillMessage";
import { UIColumnList } from "./UIVehicleTripGoodsTrackingConfig";

export function updateRowByIdOrTripIds(context: grid.VGridContext, modifiedRecords: Array<any>, callBack?: (updateRecs: Array<any>) => void) {
  let uiRoot = context.uiRoot as UIVehicleTripGoodsTrackingList;
  let { appContext, plugin, onModifyBean } = uiRoot.props;
  let ids = [];
  let tripIds = [];
  for (let rec of modifiedRecords) {
    let vehicleTripId = rec['vehicleTripId'];
    ids.push(rec['id']);
    if (vehicleTripId) tripIds.push(vehicleTripId);
  }
  let params = {
    params: {
      'trackingIds': ids,
      'vehicleTripIds': tripIds,
      'searchWithIdOrTripId': true
    }
  }

  let onSyncVendorBill = (appContext: app.AppContext, records: Array<any>) => {
    let ids: Array<any> = [];
    records.forEach(rec => ids.push(rec['tmsBillId']));
    let params = {
      tmsBillIds: ids
    }
    appContext.createHttpBackendCall('TMSRestCallService', 'syncVendorBillWithVehicleTripGoodsTrackings', params)
      .withFailNotification('danger', T('Sync Vendor Bills Fail!'))
      .call();
  }

  appContext.createHttpBackendCall('VehicleRestCallService', 'searchVehicleTripGoodsTrackings', { params: params })
    .withSuccessData((data: any) => {
      let updateRecs = data;
      for (let updateRec of updateRecs) {
        let findRec = plugin.getRecords().find(sel => sel['id'] === updateRec['id']);
        for (let propertyName in updateRec) {
          findRec[propertyName] = updateRec[propertyName];
        }
        delete findRec['vehicleInfoModified']
        delete findRec['trackingInfoModified']
        delete findRec['updateVehicleTrip'];
        delete findRec['updateTMSBill'];
        delete findRec['updateFileTrucking'];
        delete findRec['updateTracking'];
        delete findRec['updateFees'];
        delete findRec['updateCost'];
      }
      onSyncVendorBill(appContext, updateRecs);
      if (onModifyBean) onModifyBean(updateRecs, entity.ModifyBeanActions.MODIFY);
      if (callBack) callBack(updateRecs);
    })
    .call();
}
export class UIVehicleTripGoodsTrackingPageControl extends Component<grid.VGridContextProps> {
  saving = false;
  addVehicleTrip = () => {
    let { context } = this.props;
    let uiRoot = context.uiRoot as entity.DbEntityList;
    let { appContext, pageContext, plugin, readOnly } = uiRoot.props;
    let trackings = plugin.getListModel().getSelectedRecords();
    if (trackings.length === 0) {
      bs.notificationShow("danger", T("Tracking is not selected"), T("You should select at least 1 Record!!!"));
      return;
    }

    let dataCallback = (data: any) => {
      let createUI = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
        let onPostCommit = (_entity: any) => {
          let { context } = this.props;
          let uiList = context.uiRoot as entity.DbEntityList;
          pageCtx.back();
          uiList.getVGridContext().getVGrid().forceUpdateView();
        }
        return (
          <UIVehicleTrip
            key={`vehicle-trip`}
            appContext={appCtx} pageContext={pageCtx} readOnly={readOnly} trackings={trackings}
            observer={new entity.ComplexBeanObserver(data)} allowSaveAndUpdateTracking onPostCommit={onPostCommit} />)
      }
      pageContext.createPopupPage('vehicle-trip', T('Vehicle Trip'), createUI, { backdrop: 'static', size: 'xl' });
    }

    appContext
      .createHttpBackendCall('VehicleService', 'newVehicleTrip', { trackingId: trackings[0].id })
      .withSuccessData(dataCallback)
      .withFailNotification("danger", T(`New Vehicle Trip Fail`))
      .call();
  }

  onSelect = () => {
    let { context } = this.props;
    let uiRoot = context.uiRoot as UIVehicleTripGoodsTrackingList;
    let { plugin } = uiRoot.props;
    let trackings = plugin.getListModel().getSelectedRecords();
    if (trackings.length === 0) {
      bs.notificationShow("danger", T("Tracking is not selected"), T("You should select at least 1 Record!!!"));
      return;
    }
    let trackingIds = plugin.getListModel().getSelectedRecordIds();
    uiRoot.selectVehicleTrip(trackingIds);
  }

  onSaveChangeRows = () => {
    let { context } = this.props;
    let uiRoot = context.uiRoot as UIVehicleTripGoodsTrackingList;
    let { appContext, plugin } = uiRoot.props;
    let groupByBillId: Record<number, Array<any>> = {};
    let modifiedRecords = context.model.getMarkModifiedRecords();
    let deleteRecords = context.model.getMarkDeletedRecords();
    let records = [...modifiedRecords, ...deleteRecords];
    if (records.length === 0) {
      let selectedRecords = plugin.getListModel().getSelectedRecords();
      selectedRecords.forEach(sel => {
        let state = grid.getRecordState(sel);
        sel['_state'] = new grid.RecordState(state.row);
      })
      appContext.addOSNotification("success", T("No tracking were Edited"));
      context.getVGrid().forceUpdateView();
    } else {
      appContext.createHttpBackendCall('VehicleRestCallService', 'saveVehicleTripGoodsTrackingModels', { records: records })
        .withSuccessData((data: any) => {
          let results: Array<any> = data;
          for (let sel of deleteRecords) {
            let countBillId = sel['countBillId'];
            if (countBillId > 1) {
              let filterBills = groupByBillId[sel.tmsBillId];
              if (!filterBills) {
                filterBills = plugin.getRecords().filter(rec => rec.tmsBillId === sel.tmsBillId && !grid.getRecordState(rec).isMarkDeleted());
                groupByBillId[sel.tmsBillId] = filterBills;
              }
              filterBills.forEach(rec => rec['countBillId'] = rec['countBillId'] - 1);
            }
            plugin.getListModel().removeRecord(sel);
          }

          let newRecordIds: Array<number> = [];
          for (let result of results) {
            let findRec = modifiedRecords.find(modifiedRecord => !modifiedRecord['id'] && modifiedRecord['uikey'] == result['uikey']);
            if (findRec) {
              findRec['id'] = result['id'];
              newRecordIds.push(result['id']);
            }
          }
          for (let modifiedRecord of modifiedRecords) {
            let state = grid.getRecordState(modifiedRecord);
            modifiedRecord['_state'] = new grid.RecordState(state.row);
          }

          if (newRecordIds.length > 0 && uiRoot.entityFocus.type == 'focus') {
            let focusParams = {
              entityType: uiRoot.getTableName(),
              entityIds: newRecordIds,
            }
            EntityFocus.createFocus(appContext, focusParams);
          }

          if (modifiedRecords.length > 0) {
            updateRowByIdOrTripIds(context, modifiedRecords,
              (_updateRecs: Array<any>) => {
                appContext.addOSNotification("success", T("Save Trackings Success"));
                context.getVGrid().forceUpdateView();
              });
          } else {
            appContext.addOSNotification("success", T("Save Trackings Success"));
            context.getVGrid().forceUpdateView();
          }
          this.saving = false;
        })
        .withFail((response: server.BackendResponse) => {
          server.rest.handleResponseError('', response);
          this.saving = false;
          this.forceUpdate();
        })
        .call();
      this.saving = true;
      this.forceUpdate();
    }
  }

  onSendInfoChangeNotificationToPic = (appContext: app.AppContext, records: Array<any>) => {
    let holder: Array<any> = records.filter(sel => sel['vehicleInfoModified']);
    if (holder.length == 0) return;
    appContext.createHttpBackendCall('VehicleRestCallService', 'sendInfoChangeNotificationToPic', { records: holder })
      .withSuccessData((_data: any) => {
        () => { }
      })
      .call();
  }

  //TODO: review code
  combineVehicleTripGoodsTrackings = () => {
    let { context } = this.props;
    let uiRoot = context.uiRoot as UIVehicleTripGoodsTrackingList;
    let { appContext, pageContext, plugin } = uiRoot.props;

    let ids: Array<number> = plugin.getListModel().getSelectedRecordIds();
    let selectedRecords: Array<number> = plugin.getListModel().getSelectedRecords();
    if (ids.length === 0) {
      appContext.addOSNotification("warning", T("Select at least 1 record"));
      return;
    }
    let setIds = new Set<number>();
    let trackings = plugin.getListModel().getSelectedRecords();
    trackings.forEach(sel => {
      let vehicleTripId = sel['vehicleTripId'];
      if (vehicleTripId) setIds.add(vehicleTripId);
    });
    if (setIds.size > 1) {
      bs.dialogShow('Error', <div>{'File exists in more than one trip!!!'}</div>, { size: 'sm' })
      return;
    }

    if (setIds.size == 1) {
      let tracking = trackings.find(sel => setIds.has(sel['vehicleTripId']));
      let successCB = (_response: server.BackendResponse) => {
        uiRoot.clearGroupIds();
        appContext.addOSNotification("success", T("Combine Goods Trackings Success"));
        this.updateRowByIds(ids);
      }
      let params = {
        mainGoodsTracking: tracking,
        combineTrackings: selectedRecords
      }
      appContext
        .createHttpBackendCall('VehicleRestCallService', 'combineVehicleTripGoodsTrackings', { params: params })
        .withSuccess(successCB)
        .call();
    } else {
      let createAppPage = (_appCtx: app.AppContext, pageCtx: app.PageContext) => {
        return (<UICombineVehicleTripGoodsTracking context={context} onConfirm={() => {
          uiRoot.clearGroupIds();
          this.updateRowByIds(ids);
          pageCtx.back();
        }} />)
      }
      pageContext.createPopupPage('combine-goods-tracking', T("Combine"), createAppPage, { size: 'md' })
    }
  }

  updateRowByIds = (ids: Array<number>, callBack?: (trackingsDb: Array<any>) => void) => {
    let { context, } = this.props;
    let uiRoot = context.uiRoot as UIVehicleTripGoodsTrackingList;
    let { appContext, plugin } = uiRoot.props;
    let dGoodsTrackings: Array<grid.DisplayRecord> = plugin.getListModel().getSelectedDisplayRecords();

    let params = {
      params: {
        ids: ids
      }
    }
    appContext.createHttpBackendCall('VehicleRestCallService', 'searchVehicleTripGoodsTrackings', { params: params })
      .withSuccessData((data: any) => {
        let trackingsDb: Array<any> = data;
        for (let trackingDb of trackingsDb) {
          let trackingId = trackingDb['id'];
          for (let dRecord of dGoodsTrackings) {
            if (trackingId != dRecord.getValue('id')) continue;
            for (let propertyName in trackingDb) {
              dRecord.record[propertyName] = trackingDb[propertyName];
            }
          }
        }
        let firstDRecord = dGoodsTrackings[0];
        let firstDRecordRow = firstDRecord.row;
        for (let i = 1; i < dGoodsTrackings.length; i++) {
          let moveDRecord = dGoodsTrackings[i];
          let moveRecord = moveDRecord.cloneRecord();
          context.model.insertDisplayRecordAt(firstDRecordRow, moveRecord);
          context.model.getDisplayRecordList().updateDisplayRecords();
          plugin.getListModel().removeRecord(moveDRecord.record);
          moveRecord['_state'] = moveDRecord.getRecordState();
          firstDRecordRow++;
        }
        if (callBack) callBack(trackingsDb);
        uiRoot.getVGridContext().getVGrid().forceUpdateView();
      })
      .call();

  }

  onUpdateStatus = (status: VehicleTripStatus) => {
    let { context } = this.props;
    let uiRoot = context.uiRoot as UIVehicleTripGoodsTrackingList;
    let { plugin, appContext } = uiRoot.props;
    let pluginTracking = plugin as UIVehicleTripGoodsTrackingListPlugin;
    let trackings = pluginTracking.getListModel().getSelectedRecords();
    let ids: Set<number> = new Set();
    for (let tracking of trackings) {
      if (tracking.vehicleTripId) {
        ids.add(tracking.vehicleTripId);
      }
    }
    if (ids.size === 0) {
      bs.notificationShow('danger', 'VehicleTripGoodsTrackings is not selected', 'You should select at least 1 Record!!!');
      return;
    }
    appContext.createHttpBackendCall('VehicleService', 'updateStatusVehicleTrips', { status: status, ids: Array.from(ids) })
      .withSuccessData((_data: any) => {
        appContext.addOSNotification("success", T('Update Status Success'));
        uiRoot.reloadData();
      })
      .call();
  }

  onShowVehicleExpenseReport = () => {
    let { context } = this.props;
    let uiRoot = context.uiRoot as UIVehicleTripGoodsTrackingList;
    let { pageContext } = uiRoot.props;
    let createContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <UIVehicleExpenseReport appContext={appCtx} pageContext={pageCtx} />
      )
    }
    pageContext.addPageContent('vehicle-profit-report', 'Vehicle Profit Report', createContent);
  }

  onShowDriverSalaryReport = () => {
    let { context } = this.props;
    let uiRoot = context.uiRoot as UIVehicleTripGoodsTrackingList;
    let { pageContext } = uiRoot.props;
    let plugin = new UIDriverSalaryReportListPlugin();
    let createContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <UIDriverSalaryReportList appContext={appCtx} pageContext={pageCtx} plugin={plugin} />
      )
    }
    pageContext.addPageContent('driver-salary-report', 'Driver Salary Report', createContent);
  }

  onShowRecords = (uiSource: WButtonListenDataChange) => {
    let { context } = this.props;
    let uiRoot = context.uiRoot as UIVehicleTripGoodsTrackingList;
    let { pageContext } = uiRoot.props;
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (<UILoadGoodsTracking ctx={context} uiListenData={uiSource} appContext={appCtx} pageContext={pageCtx} />
      )
    }
    pageContext.createPopupPage('waiting', 'Waiting', createAppPage, { size: 'xl' })
  }

  renderNumber = (filteredRecords: Array<any>) => {
    let total = filteredRecords.length;
    if (total == 0) return;
    let needConfirmRecs = filteredRecords.filter(sel => sel['status'] === VehicleTripGoodsTrackingStatus.NEED_CONFIRM);
    return (
      <div className="px-1"
        style={{
          height: 20, width: 40, borderRadius: 10,
          backgroundColor: 'red',
          color: 'white',
          marginTop: -15,
          marginRight: -10,
          fontSize: 15
        }}>
        {needConfirmRecs.length}/{total}
      </div>
    )
  }

  onPostListenDataChangeLoad = (records: Array<any>) => {
    let { context } = this.props;
    let uiRoot = context.uiRoot as UIVehicleTripGoodsTrackingList;
    let { plugin } = uiRoot.props;
    let ids: Array<any> = plugin.getListModel().getRecords().map(sel => sel['id']);
    records = records.filter(sel => ids.includes(sel['id']) || sel['status'] === VehicleTripGoodsTrackingStatus.NEED_CONFIRM);
    return records;
  }

  processingGoodsRequestv2 = () => {
    let { context } = this.props;
    let uiRoot = context.uiRoot as UIVehicleTripGoodsTrackingList;
    let { appContext, pageContext, plugin } = uiRoot.props;
    let ids = plugin.getListModel().getSelectedRecords().map(sel => sel['tmsBillId']);
    if (!ids || ids.length === 0) {
      bs.notificationShow("danger", T("Bill is not selected"), T("You should select at least 1 Record!!!"));
      return;
    }
    let newRecords = plugin.getListModel().getRecords().find(sel => grid.getRecordState(sel).isMarkNew());
    if (newRecords) {
      bs.notificationShow("danger", "You need to save changes");
      return;
    }

    const _updateMessage = (message: any, allowCreateVendorBill: boolean) => {
      let selected = plugin.getListModel().getSelectedRecords();
      let ids = plugin.getListModel().getSelectedRecords().map(sel => sel['tmsBillId']);
      let successCb = (_data: any) => {
        if (allowCreateVendorBill) {
          // _requestVendorBill(ids);
        } else {
          selected.forEach(sel => {
            sel['messageId'] = message.id;
            sel['messageStatus'] = message.status;
            context.getVGrid().forceUpdateView();
          })
        }
      }
      const params = {
        messageId: message.id,
        billIds: ids
      }
      appContext
        .createHttpBackendCall('TMSBillService', 'updateMessageId', params)
        .withSuccessData(successCb)
        .call();
    }

    let callback = (data: any) => {
      let message = data.message;
      let vendor = data.vendor;
      let ownerAccount = data.owner;
      let createPopup = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
        return (<UITMSBillMessage appContext={appCtx} pageContext={pageCtx} context={context}
          plugin={plugin} observer={new entity.ComplexBeanObserver(message)} vendor={vendor} ownerAccount={ownerAccount}
          onSendMessageSuccessCallBack={_updateMessage} />)
      }
      pageContext.createPopupPage('processing-goods-request', T('Processing Goods Request'), createPopup, { size: 'xl' });
    }
    appContext.createHttpBackendCall('TMSPrintService', 'createCustomerServiceEmailv2', { ids: ids }).withSuccessData(callback).call();
  }

  render() {
    let { context } = this.props;
    let uiRoot = context.uiRoot as UIVehicleTripGoodsTrackingList;
    let { appContext, pageContext, plugin, readOnly, inputDataApp } = uiRoot.props;
    if (readOnly) return null;
    let writeCap = pageContext.hasUserWriteCapability();
    const buttonStyle = "mb-1 w-150 text-start";
    let listenSearchParams: sql.SqlSearchParams = {
      ...plugin.getSearchParams(),
      maxReturn: 100,
    }
    return (
      <bs.Toolbar className='border justify-content-center' hide={!writeCap || readOnly}>
        <bs.Movable style={{ marginRight: inputDataApp ? 600 : 400, width: 135 }} className="flex-grow-0" movableId="waiting">
          <FeatherIcon.X size={15} className="text-danger"
            style={{
              marginTop: -25,
              cursor: 'move',
            }} />
          <WButtonListenDataChange
            renderNumber={this.renderNumber}
            ctx={context} label="Waiting"
            searchParams={listenSearchParams}
            severCallComponent="VehicleRestCallService"
            severCallMethod="searchVehicleTripGoodsTrackingsRecentlyChanged"
            recordFilter={new entity.ExcludeRecordFilter(plugin.getRecords(), 'idx')}
            onPostLoad={this.onPostListenDataChangeLoad}
            onClick={this.onShowRecords}
          />
        </bs.Movable>
        {inputDataApp && <>
          <entity.WButtonEntityWrite icon={FeatherIcon.Send}
            appContext={appContext} pageContext={pageContext}
            disable={!writeCap} hide={!writeCap}
            label={T('Send Vendor')} onClick={this.processingGoodsRequestv2} />
          <entity.WButtonEntityWrite icon={FeatherIcon.Printer}
            appContext={appContext} pageContext={pageContext}
            disable={!pageContext.hasUserReadCapability()} hide={!pageContext.hasUserReadCapability()}
            label={T('Print POD')} onClick={() => {
              let ids = plugin.getListModel().getSelectedRecords().map(sel => sel['tmsBillId']);
              let newRecords = plugin.getListModel().getRecords().find(sel => grid.getRecordState(sel).isMarkNew());
              if (newRecords) {
                bs.notificationShow("danger", "You need to save changes");
                return;
              }
              UITMSBillUtils.onPrintReceiptOfDelivery(pageContext, ids);
            }} /></>
        }
        <entity.WButtonEntityWrite
          appContext={appContext} pageContext={pageContext} icon={FeatherIcon.GitMerge}
          label={T('Combine Trip')} onClick={this.combineVehicleTripGoodsTrackings} disable={!writeCap} />
        <bs.Popover className="flex-hbox-grow-0" title={'Tools'} closeOnTrigger=".btn" >
          <bs.PopoverToggle laf='primary' >
            <FeatherIcon.Layers size={12} /> {'Tools'}
          </bs.PopoverToggle>
          <bs.PopoverContent >
            <div className='flex-vbox my-1'>
              {writeCap ?
                <>
                  <bs.Popover title={'Status'} className="justify-content-end" closeOnTrigger=".btn" style={{ marginBottom: 2 }}>
                    <bs.PopoverToggle laf='primary'>
                      <FeatherIcon.List size={12} /> {'Status'}
                    </bs.PopoverToggle>
                    <bs.PopoverContent>
                      <div className='flex-vbox'>
                        <bs.Button laf="info" onClick={() => this.onUpdateStatus(VehicleTripStatus.SUBMITTED_PLAN)} disable={!writeCap}>
                          <FeatherIcon.Send size={12} /> {T('Submit Plan')} <FeatherIcon.Users size={15} />
                        </bs.Button>
                        <bs.Button laf='secondary' className={buttonStyle} outline onClick={() => this.onUpdateStatus(VehicleTripStatus.PLAN)}>
                          <FeatherIcon.List size={12} /> {T('Plan')}
                        </bs.Button>
                        <bs.Button laf='secondary' className={buttonStyle} outline onClick={() => this.onUpdateStatus(VehicleTripStatus.TRANSPORTING)}>
                          <FeatherIcon.List size={12} /> {T('Transporting')}
                        </bs.Button>
                        <bs.Button laf='secondary' className={buttonStyle} outline onClick={() => this.onUpdateStatus(VehicleTripStatus.DONE)}>
                          <FeatherIcon.List size={12} /> {T('Done')}
                        </bs.Button>
                      </div>
                    </bs.PopoverContent>
                  </bs.Popover>
                </>
                :
                <></>
              }
              <XLSXCustomButton
                tableName="goods-tracking"
                context={context} appContext={appContext} pageContext={pageContext}
                options={{ fileName: `Tracking ${util.TimeUtil.toCompactDateFormat(new Date())}.xlsx`, modelName: 'goods-tracking' }} />

              <bs.Button laf="info" onClick={() => {
                const createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
                  let colPlugin = new entity.DbEntityListPlugin(context.getVGridConfigModel().getRecordConfigModel().allFields);
                  return (
                    <div className='flex-vbox'>
                      <UIColumnList ctxRoot={context} appContext={appCtx} pageContext={pageCtx} plugin={colPlugin} />
                      <bs.Toolbar>
                        <bs.Button laf='info' outline onClick={() => {
                          context.getVGrid().forceUpdateView();
                        }}>
                          {'Apply'}
                        </bs.Button>
                      </bs.Toolbar>
                    </div>
                  );
                }
                pageContext.createPopupPage('cols-config', T('Columns Config'), createAppPage, { size: 'lg' });
              }}>
                {T('Cols Config')}
              </bs.Button>
            </div>
          </bs.PopoverContent>
        </bs.Popover>
        <bs.Button laf='primary' disabled={this.saving || !writeCap} onClick={this.onSaveChangeRows}>
          <FeatherIcon.Save size={12} />
          <span style={{ marginLeft: 4 }}>{T('Save Rows')}</span>
        </bs.Button>
      </bs.Toolbar>
    );
  }
}

interface UILoadGoodsTrackingProps extends app.AppComponentProps {
  uiListenData: WButtonListenDataChange;
  ctx: grid.VGridContext;
}
export class UILoadGoodsTracking extends app.AppComponent<UILoadGoodsTrackingProps> {
  goodsTrackingPlugin = new entity.DbEntityListPlugin();
  modifiedFields = [
    'createdBy', 'createdTime', 'modifiedBy', 'modifiedTime',
    'version', 'status',
    'coordinatorAccountId', 'coordinatorFullName'
  ];

  constructor(props: UILoadGoodsTrackingProps) {
    super(props);
    let { uiListenData } = this.props;
    let ids: Array<number> = [];
    for (let sel of uiListenData.filteredRecords) {
      ids.push(sel['id']);
    }
    if (ids.length > 0) this.goodsTrackingPlugin = new UIVehicleTripGoodsTrackingListPlugin().withIds(ids)
  }

  updateRec = (modifiedRecords: Array<any>, records: Array<any>): Array<any> => {
    for (let rec of modifiedRecords) {
      let find = records.find(sel => rec['id'] === sel['id']);
      if (find) {
        for (let field of this.modifiedFields) {
          find[field] = rec[field];
        }
      }
    }
    return records;
  }

  onConfirm = (goodsTrackingPlugin: entity.DbEntityListPlugin) => {
    let { appContext, ctx } = this.props;
    let uiList = ctx.uiRoot as entity.DbEntityList;
    let { plugin } = uiList.props;
    let ids = goodsTrackingPlugin.getListModel().getSelectedRecordIds();
    appContext
      .createHttpBackendCall('VehicleRestCallService', 'confirmVehicleTripGoodsTrackings', { 'ids': ids })
      .withSuccessData((data) => {
        let selectedRecs = goodsTrackingPlugin.getListModel().getSelectedRecords();
        let modifiedRecords = this.updateRec(data, selectedRecs);
        for (let rec of modifiedRecords) {
          if (rec['_state']) rec['_state']['focus'] = false;
          let findRec = plugin.getRecords().find(sel => rec['id'] === sel['id']);
          if (findRec) {
            for (let propertyName in rec) {
              findRec[propertyName] = rec[propertyName];
            }
          } else {
            plugin.getRecords().unshift(rec);
            grid.initRecordStates([rec]);
          }
          goodsTrackingPlugin.getListModel().removeRecord(rec);
        }
        plugin.getListModel().filter();
        uiList.forceUpdate();
        this.forceUpdate();
      })
      .call();
  }

  onListModifyBean = (bean: any | Array<any>, _action?: entity.ModifyBeanActions) => {
    let { ctx } = this.props;
    let uiList = ctx.uiRoot as entity.DbEntityList;
    let { plugin } = uiList.props;
    let findRec = plugin.getRecords().find(sel => bean['id'] === sel['id']);
    if (bean['_state']) bean['_state']['selected'] = true;
    if (findRec) {
      for (let propertyName in bean) {
        findRec[propertyName] = bean[propertyName];
      }
    } else {
      plugin.getRecords().unshift(bean);
      grid.initRecordStates([bean]);
    }
    plugin.getListModel().filter();
    uiList.forceUpdate();
    this.goodsTrackingPlugin.getListModel().removeRecord(bean);
    this.forceUpdate();
  }

  render(): React.ReactNode {
    let { ctx, appContext, pageContext, uiListenData } = this.props;
    let uiList = ctx.uiRoot as entity.DbEntityList;
    let { plugin } = uiList.props;
    return (
      <div className="flex-vbox">
        <UIVehicleTripGoodsTrackingList
          appContext={appContext} pageContext={pageContext} viewMode="table"
          plugin={this.goodsTrackingPlugin} readOnly onModifyBean={this.onListModifyBean} />
        <bs.Toolbar>
          <bs.Button laf="info" outline onClick={() => this.onConfirm(this.goodsTrackingPlugin)}>
            {T('Confirm')}
          </bs.Button>
          <bs.Button laf="info" onClick={() => {
            let recordModified = this.goodsTrackingPlugin.getRecords();
            if (recordModified.length == 0) {
              pageContext.back();
              return;
            }
            for (let rec of recordModified) {
              let findRec = plugin.getRecords().find(sel => rec['id'] === sel['id']);
              if (findRec) {
                for (let propertyName in rec) {
                  findRec[propertyName] = rec[propertyName];
                }
              } else {
                plugin.getRecords().unshift(rec);
                grid.initRecordStates([rec]);
              }
            }
            plugin.getListModel().filter();
            uiList.forceUpdate();
            pageContext.back();

            uiListenData.filteredRecords = [];
            uiListenData.notifiedRecords = [];
            this.forceUpdate();
          }}>
            {T('Sync Trackings')}
          </bs.Button>
        </bs.Toolbar>
      </div>
    )
  }
}

interface UICombineVehicleTripGoodsTrackingProps {
  context: grid.VGridContext;
  onConfirm(): void;
}
class UICombineVehicleTripGoodsTracking extends Component<UICombineVehicleTripGoodsTrackingProps> {
  bean: any = { selectOpt: null };

  onRenderRadio = () => {
    let uiList = this.props.context.uiRoot as entity.DbEntityList;
    let { plugin, readOnly } = uiList.props;
    let trackings = plugin.getListModel().getSelectedRecords();
    let options: Array<any> = [];
    let optionLabels: Array<any> = [];
    trackings.forEach((tracking, _index) => {
      let pickup = tracking['pickupLocation'] ? tracking['pickupLocation'] : '...';
      let delivery = tracking['deliveryLocation'] ? tracking['deliveryLocation'] : '...';
      optionLabels.push(`${tracking['label']} [${pickup}-${delivery}]`);
      options.push(tracking['id']);
    });

    if (!this.bean['selectOpt']) {
      this.bean['selectOpt'] = options[0];
    }

    return (
      <input.BBRadioInputField bean={this.bean} field={'selectOpt'} options={options} optionLabels={optionLabels}
        label={'Choose a main round'} disable={readOnly} />
    )
  }

  onCommit = () => {
    let { context, onConfirm } = this.props;
    let uiList = context.uiRoot as entity.DbEntityList;
    let { plugin } = uiList.props;
    let selectedRecords = plugin.getListModel().getSelectedRecords();
    let { appContext } = uiList.props;

    let mainGoodsTracking = selectedRecords.find(sel => sel['id'] === this.bean['selectOpt']);
    let params = {
      mainGoodsTracking: mainGoodsTracking,
      combineTrackings: selectedRecords
    }
    let successCB = (_response: server.BackendResponse) => {
      appContext.addOSNotification("success", T("Combine Goods Trackings Success"));
      onConfirm();
    }
    appContext
      .createHttpBackendCall('VehicleRestCallService', 'combineVehicleTripGoodsTrackings', { params: params })
      .withSuccess(successCB)
      .call();
  }

  render(): React.ReactNode {
    let uiList = this.props.context.uiRoot as entity.DbEntityList;
    let { appContext, pageContext, readOnly } = uiList.props;
    return (
      <div className="flex-vbox">
        {this.onRenderRadio()}
        <bs.Toolbar className='border' hide={readOnly}>
          <entity.WButtonEntityWrite
            appContext={appContext} pageContext={pageContext}
            label={T('Confirm')} icon={FeatherIcon.CheckCircle} onClick={this.onCommit} readOnly={readOnly} />
        </bs.Toolbar>
      </div>
    )
  }
}