import React from 'react';
import * as FeatherIcon from 'react-feather'

import { module } from '@datatp-ui/erp';
import { app, server, grid, bs, input, entity, sql } from '@datatp-ui/lib';
import { T } from '../backend';

import { UITMSPartnerBaseList, UITMSPartnerBaseListPageControl } from './UITMSPartnerBaseList';

import BBRefLocation = module.settings.BBRefLocation;

export class UITMSPartnerListPlugin extends entity.DbEntityListPlugin {
  constructor() {
    super([]);
    this.backend = {
      context: 'company',
      service: 'TMSPartnerService',
      searchMethod: 'searchTMSPartners',
      changeStorageStateMethod: 'changeTMSPartnerStorageState',
      deleteMethod: 'deleteTMSPartner'
    }
    this.searchParams = {
      "params": {},
      "filters": [
        ...sql.createSearchFilter()
      ],
      "optionFilters": [
        sql.createStorageStateFilter([entity.StorageState.ACTIVE, entity.StorageState.ARCHIVED])
      ],
      maxReturn: 3000,
    }
  }
  loadData(uiList: entity.DbEntityList<any>) {
    this.createBackendSearch(uiList, { 'params': this.searchParams }).call();
  }
}

export class UITMSPartnerListPageControl extends UITMSPartnerBaseListPageControl {
  onAdd(): void {
    throw new Error('Method not implemented.');
  }

  mergeTMSPartners() {
    let { context } = this.props;
    let uiRoot = context.uiRoot as entity.DbEntityList;
    const { pageContext } = uiRoot.props;
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return <UIMergeTMSPartners context={context} appContext={appCtx} pageContext={pageCtx} />
    }
    pageContext.createPopupPage('merger-tms-partner', T("Merge"), createAppPage, { size: 'md' })
  }


  addButtonToolBars(): Array<any> {
    let { context } = this.props;
    let uiRoot = context.uiRoot as entity.DbEntityList;
    const { appContext, pageContext } = uiRoot.props;
    return [
      <entity.WButtonEntityWrite
        appContext={appContext} pageContext={pageContext} icon={FeatherIcon.GitMerge}
        label={T('Merge')} onClick={() => this.mergeTMSPartners()} />
    ]
  }

  createHttpBackEndCallSave(records: Array<any>): server.HttpBackendCall {
    let { context } = this.props;
    let uiRoot = context.uiRoot as UITMSPartnerList;
    let { appContext } = uiRoot.props;
    return appContext.createHttpBackendCall('TMSPartnerService', 'saveTMSPartners', { partners: records });
  }
}

export class UITMSPartnerList extends UITMSPartnerBaseList {

  renderListPageControl(context: grid.VGridContext): React.ReactElement {
    return <UITMSPartnerListPageControl context={context} />
  }

  addFieldConfigs(): Array<grid.FieldConfig> {
    let { appContext, typePartner, onPostCommit } = this.props;
    return [
      {
        name: 'follow', label: T('Follow'), width: 70,
        container: 'fixed-right',
        state: { visible: typePartner !== undefined },
        customRender(ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, focus: boolean) {
          let partner = dRecord.record;

          let onCreate = () => {
            let callbackConfirm = () => {
              let httpBackendCall = appContext.createHttpBackendCall('TMSCustomerService', 'saveTMSCustomer', { customer: { tmsPartnerId: partner.id } });;
              if (typePartner === 'Carrier') {
                httpBackendCall = appContext.createHttpBackendCall('TMSCarrierService', 'saveTMSCarrier', { carrier: { tmsPartnerId: partner.id } });;
              } else if (typePartner === 'Agent') {
                httpBackendCall = appContext.createHttpBackendCall('TMSAgentService', 'saveTMSAgent', { agent: { tmsPartnerId: partner.id } });;
              }
              httpBackendCall.withSuccessData((data: any) => { if (onPostCommit) { onPostCommit(data) } }).withSuccessNotification('success', T('Create success')).call();
            }
            let message = (<div className="text-danger">Do you want to create {typePartner}?</div>);
            bs.dialogConfirmMessage('Confirm Message', message, callbackConfirm);
          }

          return (
            <div className='flex-hbox justify-content-center' >
              <bs.Button laf='link' onClick={onCreate}>
                + Create
              </bs.Button>
            </div>
          )
        }
      }
    ]
  }
}

export interface UIMergeTMSPartnersProps extends app.AppComponentProps {
  context: grid.VGridContext;
}
export class UIMergeTMSPartners extends app.AppComponent<UIMergeTMSPartnersProps> {
  bean: any = { selectOpt: null };

  onRenderRadio = () => {
    let uiList = this.props.context.uiRoot as entity.DbEntityList;
    let { plugin, readOnly } = uiList.props;
    let records = plugin.getListModel().getSelectedRecords();
    let options: Array<any> = [];
    let optionLabels: Array<any> = [];
    records.forEach((record, _index) => {
      optionLabels.push(
        < div className='flex-vbox'>
          <div className='fw-bold'>
            {record['label']} ({record['shortName']})
          </div>
          <div style={{ opacity: 0.65 }}>
            - {record['invoiceCompanyTaxCode']}
          </div>
          <div style={{ opacity: 0.65, whiteSpace: 'break-spaces' }} >
            - {record['invoiceCompanyAddress']}
          </div>
        </div>
      );
      options.push(record);
    });

    if (!this.bean['selectOpt']) {
      this.bean['selectOpt'] = options[0];
    }

    return (
      <input.BBRadioInputField bean={this.bean} field={'selectOpt'} options={options} optionLabels={optionLabels}
        label={'Choose'} disable={readOnly} />
    )
  }

  createMergeHttpBackendCall(): server.HttpBackendCall {
    let { appContext, context } = this.props;
    let uiList = context.uiRoot as entity.DbEntityList;;
    const { plugin } = uiList.props;
    let ids = plugin.getListModel().getSelectedRecordIds();
    let id = this.bean['selectOpt']['id'];
    let deleteIds = ids.filter((id: any) => id !== id);
    let params = {
      targetId: id,
      ids: deleteIds,
    }
    return appContext.createHttpBackendCall("TMSPartnerService", "mergePartners", params);
  }

  onConfirm = () => {
    let { context, appContext, pageContext } = this.props;
    let uiList = context.uiRoot as entity.DbEntityList;;
    const { plugin } = uiList.props;
    let successCB = (_response: server.BackendResponse) => {
      appContext.addOSNotification("success", T("Merge Success"));
      pageContext.back()
      uiList.reloadData();
    }

    this.createMergeHttpBackendCall()
      .withSuccess(successCB)
      .call();
  }

  render(): React.ReactNode {
    let uiList = this.props.context.uiRoot as entity.DbEntityList;
    let { appContext, pageContext, readOnly } = uiList.props;
    return (
      <div className="flex-vbox">
        {this.onRenderRadio()}
        <bs.Toolbar className='border' hide={readOnly}>
          <entity.WButtonEntityWrite
            appContext={appContext} pageContext={pageContext}
            label={T('Confirm')} icon={FeatherIcon.CheckCircle} onClick={this.onConfirm} readOnly={readOnly} />
        </bs.Toolbar>
      </div>
    )
  }
}

export class UITMSPartnerAddressListPlugin extends entity.DbEntityListPlugin {
  partnerId: number;
  constructor(partnerId?: any) {
    super([]);
    this.partnerId = partnerId;
    this.backend = {
      context: 'company',
      service: 'TMSPartnerService',
      searchMethod: 'searchPartnerAddresses',
      deleteMethod: 'deletePartnerAddresses'
    }
    this.searchParams = {
      "params": { 'tmsPartnerId': partnerId, 'locationStorageState': 'ACTIVE' },
      "filters": [
        ...sql.createSearchFilter()
      ],
      "optionFilters": [
        sql.createStorageStateFilter([entity.StorageState.ACTIVE, entity.StorageState.ARCHIVED])
      ],
      maxReturn: 3000,
    }
  }

  withAccountId(accountId: number) {
    this.addSearchParam('accountId', accountId);
    return this;
  }

  getPartnerId = () => {
    return this.partnerId;
  }
  loadData(uiList: entity.DbEntityList<any>) {
    this.createBackendSearch(uiList, { 'params': this.searchParams }).call();
  }
}

export class UITMSPartnerAddressList extends entity.DbEntityList {
  onInputChange(ctx: grid.FieldContext, oldVal: any, newVal: any) {
    let dRecord = ctx.displayRecord;
    let field = ctx.fieldConfig;
    let gridContext = ctx.gridContext;
    let event: grid.VGridCellEvent = {
      row: dRecord.row, field: field, event: 'Modified', data: dRecord
    }

    if (field.name == 'streetName' || field.name == 'locationLabel') {
      let streetName = dRecord.getValue('streetName');
      let addresses: Array<any> = [];
      if (streetName) addresses.push(streetName);
      let locationLabel: string = dRecord.getValue('locationLabel');
      if (locationLabel) {
        let split: any[] = locationLabel.split(',');
        if (split.length > 2) {
          let first = split[0];
          let end = split[split.length - 2];
          let label = `${first.trim()}, ${end.trim()}`;
          dRecord.record['invAddress'] = label;
          addresses.push(label);
        }
      }
      dRecord.record['address'] = addresses.join(', ');
    }
    gridContext.broadcastCellEvent(event);
  };
  createVGridConfig() {
    let { appContext, pageContext, readOnly, plugin } = this.props;
    let _ = this;
    let listPlugin = plugin as UITMSPartnerAddressListPlugin;
    let addDbSearchFilter = plugin.searchParams ? true : false;
    let writeCap = pageContext.hasUserWriteCapability() && !readOnly;
    let config: grid.VGridConfig = {

      record: {
        editor: {
          supportViewMode: ['table', 'aggregation'],
          enable: true
        },
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector()),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          {
            name: 'streetName', label: T('Số Nhà/Đường'), width: 250, state: { showRecordState: true },
            listener: {
              onDataCellEvent(cell, event) {
                if (cell.getRow() === event.row && event.field.name === 'streetName') {
                  cell.forceUpdate();
                }
              },
            },
            editor: {
              type: 'string',
              onInputChange: _.onInputChange
            }
          },
          {
            name: 'locationLabel', label: T('Xã/Phường, Tỉnh/Thành Phố'), width: 400,
            editor: {
              type: 'string',
              onInputChange: _.onInputChange,
              renderCustom(ctx, _onInputChange) {
                let { fieldConfig, displayRecord, focus, tabIndex } = ctx;
                let address = displayRecord.record;
                return (
                  <BBRefLocation required minWidth={800} tabIndex={tabIndex} autofocus={focus}
                    appContext={appContext} pageContext={pageContext} bean={address} locationTypes={['Subdistrict']}
                    beanIdField={'locationId'} beanLabelField={'locationLabel'} beanRefLabelField={'address'} placeholder='Location'
                    disable={!writeCap} refLocationBy='id'
                    onPostUpdate={(_inputUI: React.Component, _bean: any, selectOpt: any, _userInput: string) => {
                      _onInputChange(address, fieldConfig.name, null, _userInput);
                      _.forceUpdate();
                    }} />
                )
              },
            }
          },
          {
            name: 'type', label: T('Loại'),
            editor: {
              type: 'string',
              onInputChange: _.onInputChange,
              renderCustom(ctx, _onInputChange) {
                let { displayRecord } = ctx;
                let address = displayRecord.record;
                return (
                  <input.BBSelectField bean={address} field='type' options={['Export', 'Import', 'None']}
                    optionLabels={['Điểm Lấy Hàng', 'Điểm Trả Hàng', 'None']} />
                )
              },
            }
          },
          {
            name: 'invAddress', label: T('Hiển Thị Trên Hóa Đơn'), width: 300,
            listener: {
              onDataCellEvent(cell, event) {
                if (cell.getRow() === event.row && (event.field.name === 'streetName' || event.field.name == 'locationLabel')) {
                  cell.forceUpdate();
                }
              },
            },
            editor: {
              type: 'string',
              onInputChange: _.onInputChange
            }
          },
          {
            name: 'address', label: T('Địa chỉ'), width: 700,
            listener: {
              onDataCellEvent(cell, event) {
                if (cell.getRow() === event.row && (event.field.name === 'streetName' || event.field.name == 'locationLabel')) {
                  cell.forceUpdate();
                }
              },
            },
            editor: {
              type: 'string',
              onInputChange: _.onInputChange
            }
          },
          {
            name: 'locStorageState', label: T(''), width: 100, container: 'fixed-right',
            customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, focus: boolean) => {
              if (dRecord.record['locStorageState'] === 'ACTIVE') {
                return <bs.Badge laf="success">{'Hoạt Động'}</bs.Badge>
              }
              return <bs.Badge laf="danger">{'Hết Hạn'}</bs.Badge>
            }
          },
        ],
        control: {
          width: 25,
          items: [
            {
              name: 'copy', hint: 'Copy', icon: FeatherIcon.Copy,
              onClick: (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
                let record = dRecord.record;
                let newRecord = { ...record, id: null };
                ctx.model.insertDisplayRecordAt(dRecord.row, newRecord);
                grid.getRecordState(newRecord).markModified(true);
                ctx.getVGrid().forceUpdateView();
              },
            }
          ]
        }
      },
      toolbar: {
        actions: [
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION(!listPlugin.partnerId || !writeCap, {
            name: 'export',
            label: 'Điểm Lấy Hàng',
            icon: FeatherIcon.Plus,
            onClick(_ctx) {
              _.onAddRow('Export');
            },
          }),
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION(!listPlugin.partnerId || !writeCap, {
            name: 'import',
            label: 'Điểm Trả Hàng',
            icon: FeatherIcon.Plus,
            onClick(_ctx) {
              _.onAddRow('Import');
            },
          }),
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION(!listPlugin.partnerId || !writeCap, {
            name: 'none',
            label: 'None',
            icon: FeatherIcon.Plus,
            onClick(_ctx) {
              _.onAddRow('None');
            },
          }),
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_DELETE(!writeCap, T("Remove")),
        ],
        filters: entity.DbEntityListConfigTool.TOOLBAR_FILTERS(addDbSearchFilter),
      },
      footer: {
        page: {
          render: (ctx: grid.VGridContext) => {
            return (
              <bs.Toolbar>
                <bs.Button laf='info' onClick={() => {
                  let uiRoot = ctx.uiRoot as entity.DbEntityList;
                  let { plugin } = uiRoot.props;
                  let modified = plugin.getListModel().getModifiedRecords();
                  appContext.createHttpBackendCall('TMSPartnerService', 'bulkSaveTMSPartnerAddress', { records: modified })
                    .withSuccessData(() => {
                      this.reloadData();
                    })
                    .withEntityOpNotification('commit', 'Address')
                    .call();
                }}>
                  <FeatherIcon.Save size={12} /> {T('Bulk Save')}
                </bs.Button>
              </bs.Toolbar>
            )
          }
        },
      },
      view: {
        currentViewName: 'aggregation',
        availables: {
          table: {
            viewMode: 'table'
          },
          aggregation: {
            viewMode: 'aggregation',
            treeWidth: 150,
            createAggregationModel(_ctx: grid.VGridContext) {
              let model = new grid.AggregationDisplayModel(T('All'), false);
              if (listPlugin.partnerId) {
                model.addAggregation(
                  new grid.ValueAggregation(T("Type"), "type", true)
                    .withSortBucket('asc'));
              } else {
                model.addAggregation(
                  new grid.ValueAggregation(T("Short Name"), "shortName", true)
                    .withSortBucket('asc'));
              }

              return model;
            },
          }
        }
      }
    }
    return config;
  }

  onDefaultSelect(_dRecord: grid.DisplayRecord): void {
    const { appContext, pageContext } = this.props;
    let address = _dRecord.record;
    const successCb = (data: any) => {
      const createContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
        return (
          <TMSPartnerAddress appContext={appCtx} pageContext={pageCtx} observer={new entity.BeanObserver(data)} />
        )
      }
      pageContext.createPopupPage('new-address', T('New Address'), createContent);
    }
    appContext.createHttpBackendCall('TMSPartnerService', 'loadPartnerAddress', { 'id': address.id })
      .withSuccessData(successCb)
      .call();
  }

  onAddRow = (type: 'Export' | 'Import' | 'None') => {
    const { plugin } = this.props;
    let listPlugin = plugin as UITMSPartnerAddressListPlugin;
    let newAddress = {
      'type': type,
      'tmsPartnerId': listPlugin.partnerId,
    };
    plugin.getListModel().addRecord(newAddress);
    this.getVGridContext().getVGrid().forceUpdateView();
  }
}

class TMSPartnerAddress extends entity.AppDbEntityEditor {

  renderLocation = (location: any) => {
    return (
      <div className='flex-vbox'>
        <input.BBStringField bean={location} field='subdistrictLabel' label='Subdistrict' disable={true} />
        <input.BBStringField bean={location} field='districtLabel' label='District' disable={true} />
        <input.BBStringField bean={location} field='stateLabel' label='State' disable={true} />
        <input.BBStringField bean={location} field='countryLabel' label='Country' disable={true} />
      </div>
    )
  }

  createCommit(): entity.CommitConfig {
    return {
      entityLabel: T(`TMS Partner Address`), context: 'company',
      service: 'TMSPartnerService', commitMethod: 'savePartnerAddress'
    }
  }

  render(): React.ReactNode {
    const { appContext, pageContext, observer } = this.props;
    let address = observer.getMutableBean();
    let location = observer.getBeanProperty('location', {});
    let writeCap = pageContext.hasUserWriteCapability();
    return (
      <div className='flex-vbox'>
        <div className='flex-grow-0'>
          <input.BBTextField bean={address} label='Address' field='address' style={{ height: '7em' }} disable={!writeCap} />
          <BBRefLocation required inputObserver={observer} label='Location'
            appContext={appContext} pageContext={pageContext} bean={address} locationTypes={['Subdistrict', 'District']}
            beanIdField={'locationId'} beanLabelField={'locationLabel'} beanRefLabelField={'address'} placeholder='Location'
            disable={!writeCap} refLocationBy='id'
            onPostUpdate={(_inputUI: React.Component, _bean: any, selectOpt: any, _userInput: string) => {
              observer.replaceBeanProperty('location', selectOpt);
              this.forceUpdate();
            }} />
          <hr />
          {this.renderLocation(location)}
        </div>
        <bs.Toolbar>
          <entity.ButtonEntityCommit
            appContext={appContext} pageContext={pageContext} observer={observer}

            commit={this.createCommit()} />
        </bs.Toolbar>
      </div>
    )
  }
}

