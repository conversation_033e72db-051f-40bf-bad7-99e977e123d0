import React from 'react';
import {
  <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, Composed<PERSON><PERSON>, Bar<PERSON><PERSON>
} from 'recharts';

import * as FeatherIcon from 'react-feather';
import { app, chart, grid, util, entity, sql, bs, input } from "@datatp-ui/lib";
import { TMSBillTransportationModeTools } from '../utils';
import { WBtnRouteConfig } from 'app/logistics/vendor/UITMSVendorRouteTargetList';
import { T } from '../backend';

const COLOR = [
  '#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#0000ff'
]

class ReportTMSBillListPlugin extends entity.DbEntityListPlugin {
  constructor(dateRange: util.TimeRange) {
    super([]);
    this.backend = {
      context: 'company',
      service: 'TMSRestCallService',
      searchMethod: 'searchTMSBills'
    }
    this.searchParams = {
      "params": {},
      "optionFilters": [
        sql.createStorageStateFilter([entity.StorageState.ACTIVE, entity.StorageState.ARCHIVED])
      ],
      "rangeFilters": [
        ...sql.createDateTimeFilter("deliveryPlan", "Date", dateRange),
      ],
      maxReturn: 10000,
    }
  }
}

interface ReportRouteBarChartProps {
  width?: number;
  height?: number;
  data: any[];
  label: any;
  barNameOption: any;
  layout?: 'vertical' | 'horizontal'
}
export class ReportRouteBarChart extends React.Component<ReportRouteBarChartProps> {
  chartData: any[] = [];
  barName: Set<String> = new Set<String>();

  buildVerticalChart() {
    let { width, height, data, barNameOption } = this.props;
    if (!width) width = 900;
    if (!height) height = 35 * data.length;
    let bars: any[] = [];
    let barNames: any[] = [];
    if (barNameOption) {
      for (let propertyName in barNameOption) {
        if (barNameOption[propertyName]) barNames.push(propertyName);
      }
      barNames.forEach((key, index) => {
        if (index == barNames.length - 1) {
          bars.push(
            <Bar
              dataKey={`${key}`} stackId={'actual'} barSize={20} fill={COLOR[index]} label={{ position: 'right' }} yAxisId="one" />
          )
        } else {
          bars.push(
            <Bar
              dataKey={`${key}`} stackId={'actual'} barSize={20} fill={COLOR[index]} yAxisId="one" />
          )
        }
      })
    }
    return (
      <div className='d-flex justify-content-start'>
        <ComposedChart
          layout="vertical"
          width={width}
          height={height}
          data={data}
          margin={{
            right: 20,
            bottom: 20,
          }}
        >
          <CartesianGrid stroke="#f5f5f5" />
          <XAxis type="number" />
          <YAxis dataKey="name" type="category" width={120} fontSize={11} yAxisId="one" />
          <YAxis hide dataKey="name" type="category" width={120} fontSize={11} yAxisId="two" />
          <Tooltip />
          <Legend />
          <Bar dataKey={'target'} stackId={'target'} barSize={50} fill={'#C0C0C0'} yAxisId="two" />
          {bars}
        </ComposedChart>
      </div>
    )
  }

  buildHorizontalChart() {
    let { width, height, data, barNameOption, label } = this.props;
    let rWidth = data ? data.length * 35 : 0;
    if (rWidth < window.innerWidth / 3) rWidth = window.innerWidth / 3.5;
    if (!width) width = rWidth;
    if (!height) height = window.innerHeight / 4;
    let bars: any[] = [];
    let barNames: any[] = [];
    if (barNameOption) {
      for (let propertyName in barNameOption) {
        if (barNameOption[propertyName]) barNames.push(propertyName);
      }
      barNames.forEach((key, index) => {
        if (index == barNames.length - 1) {
          bars.push(<Bar dataKey={`${key}`} stackId={'actual'} barSize={20} fill={COLOR[index]} label={{ position: 'top' }} xAxisId="one" />)
        } else {
          bars.push(<Bar dataKey={`${key}`} stackId={'actual'} barSize={20} fill={COLOR[index]} xAxisId="one" />)
        }
      })
    }
    return (
      <div className='flex-vbox justify-content-start text-center'>
        <h4>{label}</h4>
        <BarChart
          layout="horizontal"
          width={width}
          height={height}
          data={data}
          margin={{
            right: 20,
            bottom: 20,
          }}
        >
          <CartesianGrid stroke="#f5f5f5" />
          <XAxis dataKey="name" type="category" height={60} fontSize={11} xAxisId="one" angle={-90}
            tick={({ x, y, stroke, payload }) => {
              return (
                <g transform={`translate(${x},${y})`}>
                  <text textAnchor="end" fontSize={11} fill="#666" transform="rotate(-45)">
                    {payload.value}
                  </text>
                </g>
              );
            }}
          />
          <XAxis hide dataKey="name" type="category" width={120} fontSize={11} xAxisId="two" />
          <YAxis type="number" />
          <Tooltip />
          <Legend verticalAlign='top' height={60} />
          <Bar dataKey={'Target'} stackId={'target'} barSize={30} fill={'#C0C0C0'} xAxisId="two" />
          {bars}
        </BarChart>
      </div>
    )
  }

  render() {
    let { layout } = this.props;
    if (layout == 'vertical') return this.buildVerticalChart();
    return this.buildHorizontalChart();
  }
}

export class UITMSBillReportRoute extends app.AppComponent {
  chartData: Record<string, any[]> = {
    'fcl': [],
    'lcl': [],
    'other': [],
  };
  rawData: any[];
  label: string = 'Loading...';
  barNameOption: any = {};
  routeTargetMap: Record<string, any> = {};
  dateRange = new util.TimeRange(new Date());
  componentDidMount(): void {
    this.dateRange = new util.TimeRange(new Date());
    this.dateRange.fromStartOf('week');
    this.reloadData(this.dateRange);
  }

  reloadData = (dateRange: util.TimeRange) => {
    let { appContext } = this.props;
    let plugin = new ReportTMSBillListPlugin(dateRange);
    this.label = `${util.text.formater.compactDate(dateRange.fromFormat())} - ${util.text.formater.compactDate(dateRange.toFormat())}`;
    appContext.createHttpBackendCall('TMSReportService', 'routeDataAnalysis', { params: plugin.searchParams })
      .withSuccessData((data: any) => {
        let routeTargets: any[] = data.routeTargets;
        if (routeTargets) {
          routeTargets.forEach(r => {
            this.routeTargetMap[r.vendorName] = r;
          })
        }
        this.rawData = data.bills;
        this.initData(this.rawData);
      })
      .call();
  }

  initData = (rawData: any[], filterByRoute?: boolean) => {
    let data = [...rawData];
    if (filterByRoute) {
      let selectRoutes: any[] = [];
      for (let propertyName in this.barNameOption) {
        if (this.barNameOption[propertyName]) selectRoutes.push(propertyName)
      }
      data = data.filter(sel => {
        let route = sel['route'] ? sel['route'] : 'unknown';
        if (selectRoutes.includes(route)) return true;
      });
    }
    let barNames: Set<String> = new Set<String>();
    barNames.add('unknown');
    data.forEach(sel => {
      if (!sel['vendorFullName']) return;
      let mode = sel['mode'];
      if (sel['route']) {
        barNames.add(sel['route']);
      }
      if (TMSBillTransportationModeTools.isFCL(mode)) {
        this.chartData['fcl'].push(sel);
      } else if (TMSBillTransportationModeTools.isLCL(mode)) {
        this.chartData['lcl'].push(sel);
      } else {
        this.chartData['other'].push(sel);
      }
    });
    let barNameArray: any[] = [...barNames];
    barNameArray.sort((a: string, b: string) => a.localeCompare(b));
    barNameArray.forEach(name => {
      if (this.barNameOption[name] == undefined) this.barNameOption[name] = true;
    });
    this.forceUpdate();
  }

  buildReportRouteBarChart = () => {
    let charts: any[] = [];
    for (let key in this.chartData) {
      let records = this.chartData[key];
      let barChartData = new TMSBillReportComputeData(records).computeBarChartData(true);
      this.mergerTarget(barChartData, this.routeTargetMap, key, this.dateRange.diff('day'));
      charts.push(
        <ReportRouteBarChart key={`${util.IDTracker.next()}-bar-chart`}
          label={key.toUpperCase()}
          barNameOption={this.barNameOption}
          data={barChartData} />
      )
    }
    return charts;
  }

  buildReportRouteBarChartByDay = () => {
    let chartMap: Record<string, any[]> = {};
    for (let type in this.chartData) {
      let records = this.chartData[type];
      let allReportModel = new chart.ReportModel("all", "All", records);
      let dayModelMap = allReportModel.groupByDate('deliveryPlan', 'month', 'YYYY-MM-DD', 'desc');
      for (let day in dayModelMap) {
        let dayReportMode = dayModelMap[day];
        let barChartData = new TMSBillReportComputeData(dayReportMode.records).computeBarChartData(true);
        this.mergerTarget(barChartData, this.routeTargetMap, type);
        const html = (
          <ReportRouteBarChart key={`${util.IDTracker.next()}-bar-chart`}
            label={type.toUpperCase()}
            barNameOption={this.barNameOption}
            data={barChartData} />
        );
        if (chartMap[day]) {
          chartMap[day].push(html);
        } else {
          chartMap[day] = [html];
        }
      }
    }
    let charts: any[] = [];
    for (let day in chartMap) {
      charts.push(
        <div className='text-start'>
          <h4>{day}</h4>
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: 5 }}>
            {chartMap[day]}
          </div>
        </div>
      )
    }
    return charts;
  }

  mergerTarget = (barChartData: any[], routeTargetMap: Record<string, any>, type: any, multiplyBy: number = 1) => {
    if (!routeTargetMap) return;
    barChartData.forEach(rec => {
      let selectRoutes: any[] = [];
      for (let propertyName in this.barNameOption) {
        if (this.barNameOption[propertyName]) selectRoutes.push(propertyName.toLowerCase());
      }
      let target = routeTargetMap[rec.name];
      if (!target) return;
      let totalTripTarget = 0;
      if (target) {
        let items: any[] = target.items;
        items.forEach(item => {
          let route: string = item.route;
          route = route.toLowerCase();
          if (selectRoutes.includes(route)) {
            if (type && item.type == type) {
              totalTripTarget += item.totalTripTarget;
            } else if (!type) {
              totalTripTarget += item.totalTripTarget;
            }
          }
        })
      }
      rec['Target'] = totalTripTarget * multiplyBy;
    })
  }

  buildBarOption = () => {
    let options: any[] = [];
    let routes: any[] = [];
    for (let propertyName in this.barNameOption) {
      routes.push(propertyName);
    }
    routes.forEach((route, index) => {
      options.push(
        <input.BBCheckboxField style={{ fontSize: '1.2em', height: '1.2em', color: `${COLOR[index]}` }} key={route}
          bean={this.barNameOption} field={route} label={route} value={false}
          onInputChange={() => this.initData(this.rawData, false)}
        />
      )
    })
    return (
      <div className='flex-hbox gap-2 my-1'>
        {options}
      </div>
    );
  }

  formatMonth(dateString: string) {
    const date = new Date(`${dateString}-01`);
    const monthNames = [
      "January", "February", "March", "April", "May", "June",
      "July", "August", "September", "October", "November", "December"
    ];
    const monthIndex = date.getMonth();
    return monthNames[monthIndex];
  }

  private computeData(records: Array<any>, type: 'fcl' | 'lcl' | 'other') {
    let allReportModel = new chart.ReportModel("all", "All", records);
    const dayModelMap = allReportModel.groupByDate('deliveryPlan', 'day', 'YYYY-MM-DD', 'asc');
    for (const dayLabel in dayModelMap) {
      let dayModel: chart.ReportModel = dayModelMap[dayLabel];

      dayModel.setReportRecord('day', dayLabel);
      dayModel.setReportRecord('label', dayLabel);
      this.computeReportRecord(dayModel);

      const vendorModelMap = dayModel.groupBy("vendorFullName", "vendor");
      for (const vendorLabel in vendorModelMap) {
        let vendorModel: chart.ReportModel = vendorModelMap[vendorLabel];
        if (vendorModel.records.length == 0) continue;
        this.computeReportRecord(vendorModel);
        let routeTarget: any = this.routeTargetMap[vendorLabel];
        let routeMap: Record<string, number> = {};
        if (routeTarget) {
          let items: any[] = routeTarget['items'];
          if (items) items.forEach(item => {
            if (item.type === type) routeMap[item.route] = item.totalTripTarget
          });
        }

        const routeModelMap = vendorModel.groupBy("route", "route");
        let totalTargetTrip = 0;
        for (const routeLabel in routeModelMap) {
          let routeModel: chart.ReportModel = routeModelMap[routeLabel];
          if (routeMap[routeLabel]) {
            totalTargetTrip += routeMap[routeLabel];
            routeModel.setReportRecord('totalTargetTrip', routeMap[routeLabel])
            let difference = routeMap[routeLabel] - routeModel.records.length;
            routeModel.setReportRecord('difference', difference);
            let complete = routeModel.records.length / routeMap[routeLabel] * 100;
            routeModel.setReportRecord('complete', complete);
          }
          this.computeReportRecord(routeModel);
        }
        if (totalTargetTrip > 0) {
          vendorModel.setReportRecord('totalTargetTrip', totalTargetTrip);
          let difference = totalTargetTrip - vendorModel.records.length;
          vendorModel.setReportRecord('difference', difference);
          let complete = vendorModel.records.length / totalTargetTrip * 100;
          vendorModel.setReportRecord('complete', complete);
        }
      }
    }
    return allReportModel.collectReportRecords(true);
  }

  private computeReportRecord(model: chart.ReportModel) {
    const { records } = model;
    model.setReportRecord('totalTrip', records.length);
  }

  render() {
    let { appContext, pageContext } = this.props;
    if (!this.rawData) return (
      <div className="flex-vbox">
        <h4>{'Loading...'}</h4>
      </div>
    )

    let recordFCLs = this.computeData(this.rawData.filter((record: any) => TMSBillTransportationModeTools.isFCL(record['mode'])), 'fcl');
    let recordLCLs = this.computeData(this.rawData.filter((record: any) => TMSBillTransportationModeTools.isLCL(record['mode'])), 'lcl');
    let recordOthers = this.computeData(this.rawData.filter((record: any) => TMSBillTransportationModeTools.isDomestic(record['mode'])), 'other');

    return (
      <div className='flex-vbox'>
        <div className='flex-grow-0 mx-4'>
          <div className='flex-hbox mx-2' style={{ width: 400 }}>
            <input.BBDateTimeField bean={{ from: this.dateRange.fromFormat() }} field='from' timeFormat={false}
              onInputChange={(bean: any, field: string, oldVal: any, newVal: any) => {
                this.dateRange.fromSetDate(util.TimeUtil.parseCompactDateTimeFormat(newVal));
              }} />
            <input.BBDateTimeField className='mx-2' bean={{ to: this.dateRange.toFormat() }} field='to' timeFormat={false}
              onInputChange={(bean: any, field: string, oldVal: any, newVal: any) => {
                this.dateRange.toSetDate(util.TimeUtil.parseCompactDateTimeFormat(newVal));
              }} />
            <bs.Button className='m-1 p-1' laf='link' onClick={() => this.reloadData(this.dateRange)}>
              <FeatherIcon.Search size={14} />
            </bs.Button>
          </div>
        </div>
        <bs.TabPane>
          <bs.Tab name='chart' label='Chart' active key={util.IDTracker.next()}>
            <div className='flex-vbox'>
              <div className='flex-hbox flex-grow-0'>
                <WBtnRouteConfig appContext={appContext} pageContext={pageContext} />
                {this.buildBarOption()}
              </div>
              <bs.ScrollableCards className='flex-vbox m-2'>
                <div className='flex-vbox text-center'>
                  <h3 className='text-primary my-1'>{T(`Report ${this.label}`)}</h3>
                  <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 1fr', gap: 5 }}>
                    {this.buildReportRouteBarChart()}
                  </div>
                </div>
                <div className='flex-vbox text-center'>
                  <h3 className='text-primary my-1'>{T('Report By Day')}</h3>
                  {this.buildReportRouteBarChartByDay()}
                </div>
              </bs.ScrollableCards>
            </div>
          </bs.Tab>
          <bs.Tab name='table' label='Table' key={util.IDTracker.next()}>
            <bs.ScrollableCards className='flex-vbox'>
              <bs.Card header='FCL'>
                <UITMSBillReportRoutetList appContext={appContext} pageContext={pageContext} plugin={new entity.DbEntityListPlugin(recordFCLs)} style={{ minHeight: 400 }} />
              </bs.Card>
              <bs.Card header='LCL'>
                <UITMSBillReportRoutetList appContext={appContext} pageContext={pageContext} plugin={new entity.DbEntityListPlugin(recordLCLs)} style={{ minHeight: 400 }} />
              </bs.Card>
              <bs.Card header='Other'>
                <UITMSBillReportRoutetList appContext={appContext} pageContext={pageContext} plugin={new entity.DbEntityListPlugin(recordOthers)} style={{ minHeight: 400 }} />
              </bs.Card>
            </bs.ScrollableCards>
          </bs.Tab>
        </bs.TabPane>
      </div>
    )
  }
}




export class UITMSBillReportRoutetList extends entity.DbEntityList {

  createVGridConfig() {
    let treePlugin = new grid.TreeDisplayModelPlugin();
    let CONFIG: grid.VGridConfig = {
      title: 'Data',
      record: {
        fields: [
          grid.createIndex("", 40, false),
          {
            label: T("Category"), name: "label", width: 250, sortable: true, filterable: true, filterableType: 'options',
            container: 'fixed-left',
          },
          { label: T("Sản lượng cam kết"), name: "totalTargetTrip", width: 200, dataType: 'double' },
          { label: T("Tổng sản lượng thực tế"), name: "totalTrip", width: 200, dataType: 'double' },
          { label: T("Chênh lệch"), name: "difference", width: 200, dataType: 'double' },
          { label: T("%Đạt"), name: "complete", width: 200, dataType: 'percent' },
        ]
      },

      toolbar: {
        actions: [
          {
            name: "export-xlsx", label: 'Export Xlsx',
            createComponent: function (ctx: grid.VGridContext) {
              let uiRoot = ctx.uiRoot as any;
              const { appContext, pageContext } = uiRoot.props;
              return (<entity.XlsxExportButton appContext={appContext} pageContext={pageContext} context={ctx}
                options={{ fileName: `Route Report ${util.TimeUtil.toCompactDateFormat(new Date())}.xlsx`, modelName: 'report' }}
              />)
            }
          },
        ],
        filterActions: [
          ...entity.DbEntityListConfigTool.TOOLBAR_AUTO_REFRESH('refresh', T('Refresh')),
        ],
      },

      view: {
        currentViewName: 'tree',
        availables: {
          tree: {
            viewMode: 'tree',
            label: 'Report',
            treeField: 'label',
            plugin: treePlugin
          },
        }
      }
    }
    let fields = CONFIG.record.fields;
    for (let sel of fields) {
      if (sel.name.startsWith("_")) continue;
      sel.computeCssClasses = (_ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
        let record = dRecord.record;
        if (record.type === 'day') return 'text-warning fw-bold';
        if (record.type === 'vendor') return 'text-primary fw-bold';
        if (record.type === 'route') return 'text-secondary fw-bold';
        return ''
      }
    }
    return CONFIG;
  }
}


class TMSBillReportComputeData {
  records: Array<any>;
  allReportModel: chart.ReportModel;
  constructor(records: Array<any>) {
    this.records = records;
    let allReportModel = new chart.ReportModel("all", "All", this.records);
    const vendorModelMap = allReportModel.groupBy('vendorFullName', 'vendor');
    for (const vendorLabel in vendorModelMap) {
      let vendorModel: chart.ReportModel = vendorModelMap[vendorLabel];
      vendorModel.groupBy('route', 'route');
    }
    this.allReportModel = allReportModel;
  }

  computeTreeData() {
    return this.allReportModel.collectReportRecords(true);
  }

  computeBarChartData(flat?: boolean) {
    let results: any[] = [];
    let childrenMap: Record<string, chart.ReportModel> = this.allReportModel.childrenMap;
    for (let name in childrenMap) {
      let vendorModel = childrenMap[name];
      let routes: Array<any> = [];
      let vendorChildrenMap: Record<string, chart.ReportModel> = vendorModel.childrenMap;
      for (let route in vendorChildrenMap) {
        let routeModel = vendorChildrenMap[route];
        let _routeChartData = {
          name: route,
          actual: routeModel.records.length,
          ids: routeModel.records.map(sel => sel.id)
        }
        routes.push(_routeChartData);
      }

      let routeListModel: grid.ListModel = new grid.ListModel(routes);
      routeListModel.sort('name');
      let chartData = {
        name: name,
        actual: vendorModel.records.length,
        routes: routeListModel.getRecords(),
        ids: vendorModel.records.map(sel => sel.id)
      }
      results.push(chartData);
    }
    let listModel: grid.ListModel = new grid.ListModel(results);
    listModel.sort('actual');
    if (flat) {
      let records: any[] = [];
      listModel.getRecords().forEach(rec => {
        let routes: any[] = rec['routes'];
        routes.forEach(route => {
          rec[route.name] = route.actual;
        });
        records.push(rec);
      });
      return records;
    } else {
      return listModel.getRecords();
    }
  }
}

