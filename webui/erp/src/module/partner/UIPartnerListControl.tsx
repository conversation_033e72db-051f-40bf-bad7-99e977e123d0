import React from 'react';
import * as FeatherIcon from 'react-feather'
import { app, server, bs, tree, entity } from '@datatp-ui/lib';

import { T } from './Dependency'
import { PartnerRestURL } from './RestURL'
import { UIPartnerListPlugin, UIPartnerList } from './UIPartnerList';
import { UIPartnerGroupEditor } from './UIPartnerGroup';
import { UILoadablePartnerAccountInfo } from './UIPartner';
import { UINewPartnerEditor } from './UINewPartner';

import { UIMergePartnerEditor } from './UIMergePartner';

class PartnerGroupTreeModel extends tree.TreeModel {
  appContext: app.AppContext;

  constructor(appContext: app.AppContext, showRoot: boolean = true) {
    super(showRoot);
    this.appContext = appContext;
    let root = new tree.TreeNode(null, '', '', T('All Partner Groups'), null, false);
    this.setRoot(root);
  }

  loadChildren(node: tree.TreeNode, postLoadCallback?: (node: tree.TreeNode) => void): any {
    let groupId = node.userData ? node.userData.id : null;

    this.appContext
      .createHttpBackendCall('PartnerService', 'findPartnerGroupByParentId', { groupId: groupId })
      .withSuccessData((data: any) => {
        let partnerGroups = data as any[];
        if (partnerGroups && partnerGroups.length > 0) {
          for (const child of partnerGroups) {
            if (node.getChildByName(child.name) == null) node.addChild(child.name, child.label, child, true);
          }
        }
        node.setLoadedChildren();
        if (postLoadCallback) postLoadCallback(node);
      })
      .call();
  }
}

interface UIPartnerExplorerProps extends entity.VGridComponentProps {
  plugin: UIPartnerListPlugin;
}
export class UIPartnerExplorer extends entity.VGridExplorer<UIPartnerExplorerProps> {

  createConfig() {
    const { context } = this.props;
    let uiRoot = context.uiRoot as app.AppComponent;
    let writeCap = uiRoot.props.pageContext.hasUserWriteCapability();
    let explorerConfig: entity.ExplorerConfig = {
      actions: [entity.ExplorerActions.ADD, entity.ExplorerActions.EDIT, entity.ExplorerActions.DEL]
    }
    if (!writeCap) {
      explorerConfig.actions = [];
    }
    return this.createTreeConfig(explorerConfig);
  }

  createTreeModel() { return new PartnerGroupTreeModel(this.props.appContext); }

  onSelectNode = (node: tree.TreeNode) => {
    let { context } = this.props;
    let plugin = context.uiRoot as UIPartnerList;
    plugin.filterByGroup(node.userData);
    this.forceUpdate();
  }

  onEdit(node: tree.TreeNode) {
    let { appContext, pageContext } = this.props;
    let partnerGroup = node.userData;
    let partnerGroupObserver = new entity.BeanObserver(partnerGroup);

    if (partnerGroup == null) {
      appContext.addOSNotification('danger', T("Cannot Edit the root"));
      return;
    }

    let onPostCommit = (group: any, _uiEditor: app.AppComponent) => {
      node.label = group.label;
      node.userData = group;
      _uiEditor.props.pageContext.back();
      this.forceUpdate();
    }

    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <UIPartnerGroupEditor
          appContext={appCtx} pageContext={pageCtx} observer={partnerGroupObserver} onPostCommit={onPostCommit} />
      )
    }

    pageContext.createPopupPage("edit-partner-group", T("Edit Partner Group"), createAppPage, { backdrop: "static" });
  }

  onAdd(node: tree.TreeNode) {
    let { pageContext, context } = this.props;
    let group = { parentId: node.userData?.id }
    let observer = new entity.BeanObserver(group);

    let createPageContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      let onPostCommit = (group: any, _uiEditor?: app.AppComponent) => {
        if (_uiEditor) {
          _uiEditor?.props.pageContext.back({ reload: true });
        }
        if (node.loadedChildren) {
          node.addChild(group.name, group.label, group, false);
        } else {
          let onPostLoad = (node: tree.TreeNode) => {
            node.collapse = false;
            this.forceUpdate();
          }
          this.model.onExpand(node, onPostLoad);
        }
        let uiPartnerList = context.uiRoot as UIPartnerList;
        uiPartnerList.reloadData();
      }
      let html = (
        <UIPartnerGroupEditor
          appContext={appCtx} pageContext={pageCtx} observer={observer} onPostCommit={onPostCommit} />
      );
      return html;
    }
    pageContext.createPopupPage('new-partner', T("Add New Partner Group"), createPageContent, { backdrop: "static" });
  }

  onDel(node: tree.TreeNode) {
    let { appContext, context } = this.props;
    let group = node.userData;
    let callback = (_response: server.BackendResponse) => {
      appContext.addOSNotification('success', T('Delete Partner Group Success!'));
      this.model.removeNode(node);
      this.model.setSelectedNode(this.model.root);
      let uiPartnerList = context.uiRoot as UIPartnerList;
      const partnerListPlugin = uiPartnerList.props.plugin as UIPartnerListPlugin;
      partnerListPlugin.withGroup(null)
      uiPartnerList.reloadData();
    }
    let failCB = (_response: server.BackendResponse) => {
      appContext.addOSNotification('danger', T(`Cannot delete partner group ${group.label}, this has the children!`))
    }
    appContext.DO_NOT_USE_serverDELETE(PartnerRestURL.group.delete(group.id), {}, callback, failCB)
  }
}

export class UIPartnerListPageControl extends entity.VGridComponent {
  onNewPartner() {
    let { context, appContext, pageContext } = this.props;
    let onPostCreate = (partner: any, uiEditor?: app.AppComponent) => {
      uiEditor?.props.pageContext.back();
      let createPageContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
        return (
          <UILoadablePartnerAccountInfo appContext={appCtx} pageContext={pageCtx} loginId={partner.loginId} />
        );
      }
      pageContext.addPageContent('partner-detail', T(`Partner {{loginId}}`, { loginId: partner.loginId }), createPageContent);

    }
    let selectedGroup = context.getAttribute('currentGroup');
    let groupIds = selectedGroup ? [selectedGroup.id] : null;
    let groupLabel = selectedGroup ? selectedGroup.label : 'Root';
    let observer = new entity.ComplexBeanObserver({
      account: { accountType: 'ORGANIZATION' },
      partnerGroupIds: groupIds,
    });
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <UINewPartnerEditor
          appContext={appCtx} pageContext={pageCtx} observer={observer} onPostCommit={onPostCreate} />
      );
    }
    pageContext.createPopupPage("add-new-partner", T(`Add New Partner To ${groupLabel}`), createAppPage, { size: "lg", backdrop: "static" })
  }

  onMergePartner = () => {
    let { context } = this.props;
    let uiRoot = context.uiRoot as UIPartnerList
    const { plugin, pageContext } = uiRoot.props;
    if (plugin.getListModel().getSelectedRecords().length < 2) {
      bs.notificationShow("danger", T("Must select at least 2 Partners to merge!"));
      return;
    }
    const onPostMerge = (_mergedPartner: any, _uiEditor?: app.AppComponent) => {
      _uiEditor?.props.pageContext.back();
      uiRoot.reloadData();
    }
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <UIMergePartnerEditor
          appContext={appCtx} pageContext={pageCtx} observer={new entity.ComplexBeanObserver({})}
          partners={plugin.getListModel().getSelectedRecords()} onPostMerge={onPostMerge} />
      );
    }
    pageContext.createPopupPage("merge-partner", T(`Merge Partner`), createAppPage, { size: "xl", backdrop: "static" });
  }

  onAddMembership() {
    let { context, pageContext } = this.props;
    let uiPartnerList = context.uiRoot as UIPartnerList;
    let excludeRecords = uiPartnerList.props.plugin.getRecords();
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <UIPartnerList
          style={{ minHeight: 400 }}
          plugin={new UIPartnerListPlugin().withExcludeRecords(excludeRecords)} type='selector'
          appContext={appCtx} pageContext={pageCtx} readOnly={true}
          onSelect={(_appCtx, pageCtx, partner) => this.onMultiSelect(pageCtx, [partner])}
          onMultiSelect={(_appCtx, pageCtx, partners) => this.onMultiSelect(pageCtx, partners)} />
      )
    }
    pageContext.createPopupPage("add-partner-relation", T('Add Partner Relation'), createAppPage, { size: "lg", backdrop: "static" });
  }

  onDeleteMembership() {
    const { context, appContext } = this.props;
    let uiPartnerList = context.uiRoot as UIPartnerList;
    let plugin = uiPartnerList.props.plugin;
    let selectedGroup = context.getAttribute('currentGroup');

    let partnerIds = plugin.getListModel().getSelectedRecordIds();
    if (partnerIds.length === 0) {
      bs.notificationShow("warning", T("No Partners were selected"));
      return;
    }
    appContext
      .createHttpBackendCall('PartnerService', 'deletePartnerGroupRelations', { groupId: selectedGroup.id, partnerIds: partnerIds })
      .withSuccessData((_data: any) => {
        plugin.getListModel().removeSelectedDisplayRecords();
        context.getVGrid().forceUpdateView();
        appContext.addOSNotification("success", T(`Delete Relations Success`));
      })
      .call();
  }

  onMultiSelect = (pageCtx: app.PageContext, partners: Array<any>) => {
    const { context, appContext } = this.props;
    let currentGroup = context.getAttribute('currentGroup');
    let partnerIds: Array<any> = [];
    for (let i = 0; i < partners.length; i++) {
      const partner = partners[i];
      partnerIds.push(partner.id);
      pageCtx.back();
    }
    const successCB = (_response: server.BackendResponse) => {
      appContext.addOSNotification("success", T(`Add Relations Success`));
      let uiPartnerList = context.uiRoot as UIPartnerList;
      uiPartnerList.reloadData();
      context.getVGrid().forceUpdateView();
    }
    appContext.DO_NOT_USE_serverPUT(PartnerRestURL.group.relation(currentGroup.id), partnerIds, successCB);
  }

  render() {
    let { context, appContext, pageContext } = this.props;
    const writeCap = pageContext.hasUserWriteCapability();
    let selectedGroup = context.getAttribute('currentGroup');
    const isSelectGroup = !selectedGroup ? true : false;

    return (
      <bs.Toolbar className='border' hide={!writeCap}>
        <entity.WButtonNewMembership
          appContext={appContext} pageContext={pageContext} hide={!writeCap || isSelectGroup}
          label={T('Add To Group')}
          onClick={() => this.onAddMembership()} />
        <entity.WButtonDeleteMembership
          appContext={appContext} pageContext={pageContext} hide={!writeCap || isSelectGroup}
          label={T('Remove From Group')}
          onClick={() => this.onDeleteMembership()} />
        <entity.WButtonEntityWrite icon={FeatherIcon.GitMerge}
          appContext={appContext} pageContext={pageContext}
          label={T('Merge Partner')} onClick={this.onMergePartner} />
        <entity.WButtonEntityNew
          appContext={appContext} pageContext={pageContext}
          label={T('New Partner')} onClick={() => this.onNewPartner()} />
      </bs.Toolbar>
    )
  }
}