import React from "react";
import { RefreshCw } from "react-feather";
import * as FeatherIcon from 'react-feather';

import { app, server, bs, input, entity } from "@datatp-ui/lib";

import {
  IUIAccountInfoPlugin,
  UILoadableAccountInfo,
  UILoadableAccountInfoProps
} from "../account/UIAccountInfo";
import { AccountType } from "../account";

import { T } from './Dependency'
import { PartnerRestURL } from './RestURL'
import { BBPartnerTagSelector } from './WPartnerTag'
import { PartnerTypePlugin, PartnerTypePluginManager } from "./type/api";
import { UIPartnerList, UIPartnerListPlugin } from "./UIPartnerList";
import { UIClonePartner } from "./UIClonePartner";
import { UISwitchPartnerAccountType } from "./UISwitchPartnerAccountType";
import { UIPartnerTypeEditor } from "./type/UIPartnerTypes";
import { B<PERSON>efPartner } from "./BBRefPartner";

export class UIPartnerEditor extends entity.AppDbComplexEntityEditor {
  onClickPartnerType(pluginType: any) {
    const { pageContext, observer } = this.props;
    let partner = observer.getMutableBean();
    let plugin: PartnerTypePlugin = PartnerTypePluginManager.getPluginByName(pluginType.pluginName);
    let createPageContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        plugin.createUIPartnerTypeEditor(appCtx, pageCtx, partner, pluginType)
      );
    }
    pageContext.addPageContent("partner-type", "Partner Type " + pluginType.type, createPageContent);
  }

  onPartnerType(action: 'Add' | 'Remove', type: any) {
    const { appContext, observer } = this.props;
    let partner = observer.getMutableBean();
    let successCB = (result: any) => {
      let partner = result.data;
      observer.replaceWith(partner);
      appContext.addOSNotification("success", T("Update Partner Type Successfully!"));
      this.forceUpdate();
    };
    let failCB = (result: any) => {
      appContext.addOSNotification("danger", T("Update Server Type Fail!"), null, result);
    };
    let restClient = appContext.getServerContext().getRestClient();
    let actionReq = { action: action, partner: partner, type: type };
    restClient.put("/partner/partner/partner-type", actionReq, successCB, failCB);
  }

  onShowMembers = () => {
    let { observer, pageContext } = this.props;
    let partner = observer.getMutableBean();
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (<UIPartnerList
        appContext={appCtx} pageContext={pageCtx} viewName={"aggregation"} hideGroups title={T("Members Group")}
        plugin={new UIPartnerListPlugin().addParam("parentId", partner.id).addParam("orgPartnerId", partner.id)} />);
    }
    return pageContext.createPopupPage('members', "Members", createAppPage, { size: 'xl' })
  }

  onClone = () => {
    let { observer, appContext, pageContext } = this.props;
    let partner = observer.getMutableBean();
    const callback = (response: server.BackendResponse) => {
      let entity = response.data;
      let cloneObserver = new entity.ComplexBeanObserver(entity);
      let createPageContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
        return (
          <UIClonePartner appContext={appCtx} pageContext={pageCtx} observer={cloneObserver} />
        );
      }
      pageContext.addPageContent(`partner-clone-${partner.loginId}`, T(`Clone Partner`), createPageContent);
    }
    appContext.DO_NOT_USE_serverPOST(PartnerRestURL.partner.clone, partner, callback);
  }

  onSwitchAccountType = () => {
    let { appContext, pageContext, observer } = this.props;
    let partner = observer.getMutableBean();
    let success = (data: any) => {
      let partnerProfile = data;
      let newAccountType = partner.partnerAccountType == AccountType.USER ? AccountType.ORGANIZATION : AccountType.USER;
      let model: any = {
        partner: partner,
        newAccountType: newAccountType,
      }
      if (newAccountType == AccountType.USER) {
        model['userProfile'] = {
          accountType: newAccountType,
          loginId: partnerProfile.loginId,
          email: partnerProfile.email,
          mobile: partnerProfile.mobile,
          fullName: partnerProfile.fullName,
          avatarUrl: partnerProfile.avatarUrl
        }
      } else {
        model['orgProfile'] = {
          accountType: newAccountType,
          loginId: partnerProfile.loginId,
          email: partnerProfile.email,
          mobile: partnerProfile.mobile,
          fullName: partnerProfile.fullName,
          avatarUrl: partnerProfile.avatarUrl
        }
      }
      let reqObserver = new entity.ComplexBeanObserver(model);
      let onPostCommit = (entity: any) => {
        observer.replaceWith(entity);
        if (pageContext.breadcumbs) pageContext.breadcumbs.forceUpdatePreviousPage();
        this.nextViewId();
        this.forceUpdate();
      }
      let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
        return (
          <UISwitchPartnerAccountType
            appContext={appCtx} pageContext={pageCtx}
            partnerObserver={observer} observer={reqObserver} onPostCommit={onPostCommit} />
        );
      }
      pageContext.createPopupPage(
        "switch-partner-account-type", T('Switch Partner Account Type'), createAppPage, { size: "lg", backdrop: "static" }
      );
    };
    appContext
      .createHttpBackendCall('AccountService', 'getProfile', { loginId: partner.loginId })
      .withSuccessData(success)
      .call();
  }

  render() {
    let { observer, appContext, pageContext, readOnly } = this.props;
    let partner = observer.getMutableBean();
    let partnerTags = observer.getComplexArrayProperty('partnerTags', [])
    let writeCap = pageContext.hasUserWriteCapability();
    let html = (
      <div className="flex-vbox">
        <bs.TabPane>
          <bs.Tab name='info' label={'Info'} active={true}>
            <bs.GreedyScrollable className='flex-vbox p-1' style={{ minHeight: '650px' }}>
              <input.BBStringField
                bean={partner} field={'name'} label={T('Name')} disable={!writeCap} />
              <input.BBStringField
                bean={partner} field={'label'} label={T('Label')} disable={!writeCap} />
              <input.BBStringField
                bean={partner} field={'localizedLabel'} label={T('Label (Localized)')} disable={!writeCap} />
              <input.BBSelectField
                bean={partner} field={'partnerAccountType'} label={T('Partner Account Type')}
                options={[AccountType.ORGANIZATION, AccountType.USER]} disable={true} />
              {partner.partnerAccountType == AccountType.USER
                ? <div>
                  <bs.FormLabel> {T('Organization Partner')} </bs.FormLabel>
                  <BBRefPartner
                    partnerAccountType={partner.partnerAccountType}
                    excludeParentId={partner.id} placeholder="Enter Organization Partner"
                    appContext={appContext} pageContext={pageContext} bean={partner}
                    beanIdField={'orgPartnerId'} beanLabelField={"orgPartnerLabel"} disable={!writeCap} />
                </div>
                : <div>
                  <bs.FormLabel> {T('Parent')} </bs.FormLabel>
                  <BBRefPartner
                    partnerAccountType={partner.partnerAccountType}
                    excludeParentId={partner.id} placeholder="Enter Parent"
                    appContext={appContext} pageContext={pageContext} bean={partner}
                    beanIdField={'parentId'} beanLabelField={'parentLabel'} disable={!writeCap} />
                </div>}
              <input.BBTextField
                style={{ height: '10em' }} bean={partner} field={'description'} label={T('Description')} disable={!writeCap} />
              <div>
                <bs.FormLabel> {T('Partner Tags')} </bs.FormLabel>
                <BBPartnerTagSelector
                  appContext={appContext} pageContext={pageContext}
                  labelBeans={partnerTags} labelField={'label'} disable={!writeCap} />
              </div>
            </bs.GreedyScrollable>
          </bs.Tab>
          <bs.Tab name='advanced' label={T('Advanced')}>
            <div className="flex-vbox p-1">
              <input.BBStringField
                bean={partner} field={'companyLegacyLoginId'} label={T('Legacy Company Login Id')} disable={!writeCap} />
              <input.BBDoubleField
                bean={partner} field={'odoo14PartnerId'} label={T('Odoo 14 Partner Id')} disable={true} />
              <input.BBStringField
                bean={partner} field={'moveLoginId'} label={T('Move Login Id')} disable={true} />
              <div>
                <bs.FormLabel> {T('Shareable Scope')} </bs.FormLabel>
                <entity.BBShareableScope
                  bean={partner} field={'shareable'} disable={!writeCap} />
              </div>
            </div>
          </bs.Tab>
        </bs.TabPane>
        <bs.Toolbar className='border' hide={!writeCap || readOnly}>
          <bs.Popover placement='top' className='flex-grow-0' closeOnTrigger={'button'}>
            <bs.PopoverToggle className='btn-primary'>
              <FeatherIcon.Plus size={12} />
              {T("Others")}
              <FeatherIcon.ChevronUp size={12} />
            </bs.PopoverToggle>
            <bs.PopoverContent className="flex-vbox">
              <bs.Button laf="primary" className="m-1" hidden={!writeCap} onClick={this.onSwitchAccountType}>
                <RefreshCw size={12} /> {T("Switch Account Type")}
              </bs.Button>
              <entity.WButtonEntityWrite className="m-1" icon={FeatherIcon.Users}
                appContext={appContext} pageContext={pageContext} label={T("Members")} hide={!writeCap}
                onClick={this.onShowMembers} />
            </bs.PopoverContent>
          </bs.Popover>
          <entity.WButtonEntityCommit
            appContext={appContext} pageContext={pageContext} hide={!writeCap} observer={observer}
            label={`Partner ${partner.loginId}`} commitURL={PartnerRestURL.partner.save}
            onPostCommit={(entity) => this.onPostCommit(entity)} />
          <entity.WButtonEntityReset
            appContext={appContext} pageContext={pageContext} hide={!writeCap} observer={observer}
            onPostRollback={(entity) => this.onPostRollback(entity)} />
        </bs.Toolbar>
      </div >
    );
    return html;
  }
}

export class UILoadablePartnerEditor extends app.AppComponent<UILoadableAccountInfoProps, {}> {
  observer: entity.ComplexBeanObserver = new entity.ComplexBeanObserver({});

  constructor(props: UILoadableAccountInfoProps) {
    super(props);
    let { appContext, loginId } = this.props;
    this.markLoading(true);

    appContext
      .createHttpBackendCall('PartnerService', 'getPartnerByLoginId', { loginId: loginId })
      .withSuccessData((data: any) => {
        let partner = data as any;
        if (!partner) {
          let message = "Partner is not found";
          bs.dialogShow('Message', <div className='ms-1 text-warning py-3'>{message}</div>, { backdrop: 'static', size: 'sm' });
          return;
        }
        this.observer = new entity.ComplexBeanObserver(partner);
        this.markLoading(false)
        this.forceUpdate();
      })
      .withFail((response: server.BackendResponse) => {
        appContext.addOSNotification("danger", T("Load Partner Fail!"), response.error.message);
        return;
      })
      .call();

  }

  render() {
    if (this.isLoading()) return this.renderLoading();
    let { appContext, pageContext } = this.props;
    const writeCap = pageContext.hasUserWriteCapability();
    let partner = this.observer.getMutableBean();
    return (
      <bs.VSplit updateOnResize>
        <bs.VSplitPane width={500} title={T("Info")}>
          <UIPartnerEditor appContext={appContext} pageContext={pageContext} observer={this.observer} readOnly={!writeCap} />
        </bs.VSplitPane>
        <bs.VSplitPane>
          <bs.TabPane>
            <bs.Tab name="logistics-info" label={T('Logistics Info')} active={true}>
              <UIPartnerTypeEditor
                appContext={appContext} pageContext={pageContext} partner={partner} readOnly={!writeCap} />
            </bs.Tab>
          </bs.TabPane>
        </bs.VSplitPane>
      </bs.VSplit>
    );
  }
}

export class UIPartnerAccountInfoPlugin implements IUIAccountInfoPlugin {
  createAdditionalTabs(appContext: app.AppContext, pageContext: app.PageContext, loginId: string, readOnly?: boolean) {
    let additionalTabs: Array<any> = [];
    additionalTabs.push(
      <bs.Tab key='partner' name="partner-info" label={"Partner Info"} active>
        <UILoadablePartnerEditor appContext={appContext} pageContext={pageContext} loginId={loginId} readOnly={readOnly} />
      </bs.Tab>
    );
    return additionalTabs;
  }
}

export class UILoadablePartnerAccountInfo extends app.AppComponent<UILoadableAccountInfoProps> {
  render() {
    let { appContext, pageContext, loginId } = this.props;
    let plugin = new UIPartnerAccountInfoPlugin();
    const writeCap = pageContext.hasUserWriteCapability();
    return (
      <UILoadableAccountInfo
        appContext={appContext} pageContext={pageContext} plugin={plugin} loginId={loginId} readOnly={!writeCap} />
    )
  }
}
