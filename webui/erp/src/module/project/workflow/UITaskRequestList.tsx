import React from 'react';
import * as FeatherIcon from 'react-feather';
import { T } from '../Dependency';
import { grid, bs, entity, app, util, sql, input } from '@datatp-ui/lib';
import { UITaskRequestEditor } from './UITaskRequest';
import { WQuickTimeRangeSelector, WTaskGroupBySelector } from './UITaskRequestUtility';
import { WAvatars } from 'module/account';

const SESSION = app.host.DATATP_SESSION;

function responsiveGridConfig(config: grid.VGridConfig): grid.VGridConfig {
  let fields: grid.FieldConfig[] = config.record.fields || [];

  const totalWidth = window.innerWidth || 1920;
  const totalControlWidth = config.record.control ? config.record.control.width : 0;
  let totalWidthRemaining = totalWidth - totalControlWidth - fields.reduce((sum, field) => {
    const isVisible =
      !field.state ||
      !field.state.hasOwnProperty('visible') ||
      field.state.visible === true;
    return isVisible ? sum + (field.width || 0) : sum;
  }, 0);

  if (totalWidthRemaining > 0) {
    for (let i = 0; i < fields.length; i++) {
      if (fields[i].container === 'fixed-right') {
        fields[i].container = 'default';
      }
    }
  }
  config.record.fields = fields;
  return config;
}


export const TaskRequestLevel = {
  Low: {
    label: 'Low',
    value: 'Low',
    color: 'success',
    icon: FeatherIcon.ArrowDownCircle
  },
  Medium: {
    label: 'Medium',
    value: 'Medium',
    color: 'warning',
    icon: FeatherIcon.MinusCircle
  },
  High: {
    label: 'High',
    value: 'High',
    color: 'danger',
    icon: FeatherIcon.AlertCircle
  },
} as const;

export class TaskRequestLevelUtils {
  static getInfo(type: string = 'Low') {
    const typeInfo = TaskRequestLevel[type as keyof typeof TaskRequestLevel];
    return typeInfo || TaskRequestLevel.Low;
  }

  static getTaskRequestList() {
    return Object.values(TaskRequestLevel);
  }
}

export const TaskStatus = {
  NEW: {
    label: 'New',
    value: 'NEW',
    color: 'info',
    icon: FeatherIcon.PlusCircle
  },
  APPROVED: {
    label: 'Approved',
    value: 'APPROVED',
    color: 'success',
    icon: FeatherIcon.CheckCircle
  },
  REJECTED: {
    label: 'Rejected',
    value: 'REJECTED',
    color: 'danger',
    icon: FeatherIcon.XCircle
  },
  CANCELLED: {
    label: 'Cancelled',
    value: 'CANCELLED',
    color: 'secondary',
    icon: FeatherIcon.Slash
  },
} as const;

export class TaskRequestStatusUtils {
  static getInfo(type: string = 'NEW') {
    const typeInfo = TaskStatus[type as keyof typeof TaskStatus];
    return typeInfo || TaskStatus.NEW;
  }

  static getTaskStatusList() {
    return Object.values(TaskStatus);
  }
}

export const TaskType = {
  FILE_ACCESS_PERMISSION: {
    label: 'File Access',
    value: 'FILE_ACCESS_PERMISSION',
    color: 'info',
    icon: FeatherIcon.Key
  },
  INVOICE_CORRECTION: {
    label: 'Invoice Correction',
    value: 'INVOICE_CORRECTION',
    color: 'danger',
    icon: FeatherIcon.Edit
  },
  SETTLE_EDIT: {
    label: 'Settle Edit',
    value: 'SETTLE_EDIT',
    color: 'warning',
    icon: FeatherIcon.Edit
  },
  EQUIPMENT_PURCHASE: {
    label: 'Equipment Purchase',
    value: 'EQUIPMENT_PURCHASE',
    color: 'primary',
    icon: FeatherIcon.ShoppingCart
  },
  OVERTIME: {
    label: 'Overtime',
    value: 'OVERTIME',
    color: 'warning',
    icon: FeatherIcon.Clock
  },
  LEAVE: {
    label: 'Leave',
    value: 'LEAVE',
    color: 'success',
    icon: FeatherIcon.Coffee
  },

} as const;

export class TaskRequestTypeUtils {
  static getTypeInfo(type: string = 'FILE_ACCESS_PERMISSION') {
    const typeInfo = TaskType[type as keyof typeof TaskType];
    return typeInfo || TaskType.FILE_ACCESS_PERMISSION;
  }

  static getTaskTypeList() {
    return Object.values(TaskType);
  }
}

interface FilterParam {
  dateFilter: { fromValue: string, toValue: string, label: string };
  groupedBy: { label: string, value: string };
}

export class UITaskRequestListPlugin extends entity.DbEntityListPlugin {

  constructor(space: 'User' | 'Company' | 'System') {
    super([]);

    let dateFilter = new util.TimeRange();
    dateFilter.fromSetDate(new Date()).fromStartOf('month');
    dateFilter.toSetDate(new Date()).toEndOf('month');


    this.backend = {
      service: 'TaskRequestService',
      searchMethod: 'searchTaskRequests'
    }

    this.searchParams = {
      params: {
        "space": space,
      },
      filters: [
        ...sql.createSearchFilter()
      ],
      rangeFilters: [
        ...sql.createCreatedTimeFilter(),
        sql.createDateTimeFilterNew("requestDate", "Request Date", dateFilter)
      ],
    }
  }

  withRequestDate(fromValue: string, toValue: string) {
    if (this.searchParams) {
      let rangeFilters = this.searchParams.rangeFilters || [];
      for (let i = 0; i < rangeFilters.length; i++) {
        let filter = rangeFilters[i];
        if (filter.name === 'requestDate') {
          filter.fromValue = fromValue;
          filter.toValue = toValue;
          break;
        }
      }
      this.searchParams.rangeFilters = rangeFilters;
    }
    return this;
  }

  loadData(uiList: entity.DbEntityList<any>) {
    let params = { 'params': this.searchParams }
    this.createBackendSearch(uiList, params).call();
  }
}


export class UITaskRequestList extends entity.DbEntityList {
  reportFilter: FilterParam;

  constructor(props: entity.DbEntityListProps) {
    super(props);

    const { plugin } = this.props;

    let searchParams: sql.SqlSearchParams = plugin.getSearchParams();

    if (searchParams) {
      let rangeFilters = searchParams.rangeFilters || [];
      for (let i = 0; i < rangeFilters.length; i++) {
        let filter = rangeFilters[i];
        if (filter.name === 'requestDate') {
          let dateFilter = new util.TimeRange();
          if (filter.fromValue) {
            dateFilter.fromSetDate(util.TimeUtil.parseCompactDateTimeFormat(filter.fromValue));
          }
          if (filter.toValue) {
            dateFilter.toSetDate(util.TimeUtil.parseCompactDateTimeFormat(filter.toValue));
          }
          this.reportFilter = {
            dateFilter: {
              fromValue: dateFilter.fromFormat(),
              toValue: dateFilter.toFormat(),
              label: 'Custom'
            },
            groupedBy: { label: 'Request By', value: 'REQUEST_BY' },
          }
          break;
        }
      }
    }

    if (!this.reportFilter) {
      this.reportFilter = {
        dateFilter: {
          fromValue: '',
          toValue: '',
          label: 'Custom'
        },
        groupedBy: { label: 'Request By', value: 'REQUEST_BY' },
      }
    }
  }

  createVGridConfig() {
    const { pageContext } = this.props;
    const moderatorCap = pageContext.hasUserModeratorCapability();

    let config: grid.VGridConfig = {
      record: {
        dataCellHeight: 40,
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          {
            name: 'requestType', label: T('Type'), width: 150, filterable: true, container: 'fixed-left',
            customRender: (_ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let taskStatusInfo = TaskRequestTypeUtils.getTypeInfo(record['requestType']);
              let TaskIcon = taskStatusInfo.icon;
              let label = taskStatusInfo.label;
              let color = taskStatusInfo.color;
              return (
                <div className={`flex-hbox flex-center px-2 py-2 rounded-2 bg-${color}-subtle text-${color} w-100 `}>
                  <TaskIcon size={14} className="me-1" />
                  <span>{label}</span>
                </div>
              );
            },
          },
          {
            name: 'requestByLabel', label: T(`Requestor`), width: 170, filterable: true, container: 'fixed-left',
            customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let uiList = ctx.uiRoot as UITaskRequestList;
              const { appContext, pageContext } = uiList.props;
              let employeeName: string = record['requestByLabel'] || 'N/A';

              let parts = employeeName.trim().toLowerCase().split(' ');
              parts = parts.map(word => word.charAt(0).toUpperCase() + word.slice(1));
              employeeName = parts.join(' ');

              return (
                <div className='flex-hbox justify-content-center align-items-center' >
                  <WAvatars className='px-2'
                    appContext={appContext} pageContext={pageContext} avatarIds={[record['requestByAccountId']]}
                    avatarIdType='AccountId' width={20} height={20} borderRadius={10} />
                  <div className="flex-hbox">{employeeName}</div>
                </div>
              )
            },
          },
          { name: 'departmentLabel', label: T('Department'), width: 150 },
          {
            name: 'requestDate', label: T('Request Date'), width: 130, filterable: true, filterableType: 'date',
            format: util.text.formater.compactDate,
          },
          { name: 'referenceNote', label: T('Ref'), width: 180 },
          {
            name: 'label', label: T('Label'), width: 300,
          },
          {
            name: 'description', label: T('Description'), width: 250,
            customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let val = record[_field.name] || 'N/A';
              const htmlVal = val.split('\n').map((line: any, i: any) =>
                <div key={i}>{line}</div>
              );

              return (
                <bs.CssTooltip position='bottom-left' width={400} offset={{ x: -200, y: -10 }}>
                  <bs.CssTooltipToggle className='flex-hbox justify-content-start h-100 w-100'>
                    {util.text.formater.uiTruncate(val, 250, true)}
                  </bs.CssTooltipToggle>
                  <bs.CssTooltipContent className='flex-vbox'>
                    <div className="tooltip-body">
                      {htmlVal}
                    </div>
                  </bs.CssTooltipContent>
                </bs.CssTooltip>
              )
            }
          },
          {
            name: 'approvedByLabel', label: T(`Approved By`), width: 170, filterable: true,
            customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let uiList = ctx.uiRoot as UITaskRequestList;
              const { appContext, pageContext } = uiList.props;
              let employeeName: string = record['approvedByLabel'] || 'N/A';

              let parts = employeeName.trim().toLowerCase().split(' ');
              parts = parts.map(word => word.charAt(0).toUpperCase() + word.slice(1));
              employeeName = parts.join(' ');

              return (
                <div className='flex-hbox justify-content-center align-items-center' >
                  <WAvatars className='px-2'
                    appContext={appContext} pageContext={pageContext} avatarIds={[record['approvedByAccountId']]}
                    avatarIdType='AccountId' width={20} height={20} borderRadius={10} />
                  <div className="flex-hbox">{employeeName}</div>
                </div>
              )
            },
          },
          {
            name: 'approvedDate', label: T('Approved Date'), width: 130, filterable: true, filterableType: 'date',
            format: util.text.formater.compactDate,
          },
          {
            name: 'approvedNote', label: T('Approved Note'), width: 270, style: { height: 40 },

            editor: {
              type: 'string', enable: true,
              onInputChange: (ctx: grid.FieldContext, oldVal: any, newVal: any) => {
                let record: any = ctx.displayRecord.record;
                if (newVal !== oldVal) {
                  record['approvedNote'] = newVal;
                  const { fieldConfig } = ctx;
                  let event: grid.VGridCellEvent = {
                    row: record.row,
                    field: fieldConfig,
                    event: 'Modified'
                  }
                  this.vgridContext.broadcastCellEvent(event);
                  this.handleOnUpdateNote(record)
                }
              },
              renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
                let record = ctx.displayRecord.record;
                const ACCESS_ACCOUNT_ID = SESSION.getAccountId();
                const approvedByAccountId = record['approvedByAccountId'];
                const isEditable = moderatorCap || (approvedByAccountId === ACCESS_ACCOUNT_ID);
                return isEditable ? (
                  <input.BBStringField bean={record} field="approvedNote" onInputChange={onInputChange} style={{ height: '40px' }} />
                ) : (
                  <div className='flex-hbox align-items-center px-2' style={{ height: '40px' }}>
                    {record['approvedNote'] || ''}
                  </div>
                );
              },
            },
          },
          {
            name: 'taskRequestLevel', label: T('Level'), width: 100, filterable: true,
            customRender: (_ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let uiList = _ctx.uiRoot as UITaskRequestList;
              let levelInfo = TaskRequestLevelUtils.getInfo(record['taskRequestLevel']);
              let Icon = levelInfo.icon;
              let label = levelInfo.label;
              let color = levelInfo.color;
              const ACCESS_ACCOUNT_ID = SESSION.getAccountId()
              const approvedByAccountId = record['approvedByAccountId']
              if (moderatorCap || approvedByAccountId === ACCESS_ACCOUNT_ID) {
                let levelList = TaskRequestLevelUtils.getTaskRequestList();
                const levelRemaining: any[] = levelList.filter(type => type.value !== record['taskRequestLevel']);
                if (levelRemaining.length === 0) {
                  return (
                    <div className={`flex-hbox flex-center px-2 py-2 rounded-2 bg-${color}-subtle text-${color} w-100 `}>
                      <Icon size={14} className="me-1" />
                      <span>{label}</span>
                    </div>
                  )
                }
                return (
                  <bs.Popover className="d-flex flex-center w-100 h-100"
                    title={T('Level')} closeOnTrigger=".btn" >

                    <bs.PopoverToggle
                      className={`flex-hbox flex-center px-2 py-2 rounded-2 bg-${color}-subtle text-${color} w-100 `}>
                      <Icon size={14} className="me-1" />
                      <span>{label}</span>
                    </bs.PopoverToggle>

                    <bs.PopoverContent>
                      <div className='flex-vbox gap-2' style={{ width: '200px' }}>
                        {levelRemaining.map(opt => {
                          let OptIcon = opt.icon;
                          return (
                            <div key={opt.value}
                              className={`d-flex flex-center px-2 py-1 rounded-2 bg-${opt.color}-subtle text-${opt.color} w-100 cursor-pointer`}
                              onClick={() => {
                                record['taskRequestLevel'] = opt.value;
                                uiList.onSaveChanges(record);
                              }}>
                              <OptIcon size={14} className="me-1" />
                              <span>{opt.label}</span>
                            </div>
                          );
                        })}
                      </div>
                    </bs.PopoverContent>
                  </bs.Popover>
                );
              } else {
                return (
                  <div className={`flex-hbox flex-center px-2 py-2 rounded-2 bg-${color}-subtle text-${color} w-100 `}>
                    <Icon size={14} className="me-1" />
                    <span>{label}</span>
                  </div>
                )
              }
            },
          },
          {
            name: 'status', label: T('Status'), width: 150, filterable: true, container: 'fixed-right',
            customRender: (_ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let uiList = _ctx.uiRoot as UITaskRequestList;
              let taskStatusInfo = TaskRequestStatusUtils.getInfo(record['status']);
              let TaskIcon = taskStatusInfo.icon;
              let label = taskStatusInfo.label;
              let color = taskStatusInfo.color;
              const ACCESS_ACCOUNT_ID = SESSION.getAccountId()
              const requestorAccountId = record['requestByAccountId']
              const approvedByAccountId = record['approvedByAccountId'];

              if (moderatorCap || (requestorAccountId === ACCESS_ACCOUNT_ID && taskStatusInfo.value === 'NEW') || approvedByAccountId === ACCESS_ACCOUNT_ID) {
                let statusList = TaskRequestStatusUtils.getTaskStatusList();

                if (requestorAccountId === ACCESS_ACCOUNT_ID) {
                  statusList = [TaskStatus.NEW, TaskStatus.CANCELLED];
                }

                if (approvedByAccountId === ACCESS_ACCOUNT_ID) {
                  statusList = [TaskStatus.APPROVED, TaskStatus.REJECTED, TaskStatus.CANCELLED];
                }
                const statusRemaining = statusList.filter(type => type.value !== record['status']);

                if (statusRemaining.length === 0) {
                  return (
                    <div className={`flex-hbox flex-center px-2 py-2 rounded-2 bg-${color}-subtle text-${color} w-100 `}>
                      <TaskIcon size={14} className="me-1" />
                      <span>{label}</span>
                    </div>
                  )
                }
                return (
                  <bs.Popover className="d-flex flex-center w-100 h-100"
                    title={T('Request Status')} closeOnTrigger=".btn" >

                    <bs.PopoverToggle
                      className={`flex-hbox flex-center px-2 py-2 rounded-2 bg-${color}-subtle text-${color} w-100 `}>
                      <TaskIcon size={14} className="me-1" />
                      <span>{label}</span>
                    </bs.PopoverToggle>

                    <bs.PopoverContent>
                      <div className='flex-vbox gap-2' style={{ width: '200px' }}>
                        {statusRemaining.map(opt => {
                          let OptIcon = opt.icon;
                          return (
                            <div key={opt.value}
                              className={`d-flex flex-center px-2 py-1 rounded-2 bg-${opt.color}-subtle text-${opt.color} w-100 cursor-pointer`}
                              onClick={() => { uiList.onChangeStatus(opt.value, record) }}>
                              <OptIcon size={14} className="me-1" />
                              <span>{opt.label}</span>
                            </div>
                          );
                        })}
                      </div>
                    </bs.PopoverContent>
                  </bs.Popover>
                );
              } else {
                return (
                  <div className={`flex-hbox flex-center px-2 py-2 rounded-2 bg-${color}-subtle text-${color} w-100 `}>
                    <TaskIcon size={14} className="me-1" />
                    <span>{label}</span>
                  </div>
                )
              }
            },
          },
        ]
      },
      toolbar: {
        hide: true,
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          },
        },
      }
    }
    return responsiveGridConfig(config);
  }

  handleOnUpdateNote(request: any) {
    const { pageContext } = this.props;

    let status = request['status'];
    if (status !== 'NEW') {
      this.onSaveChanges(request)
      return;
    }

    let createAppPage = (_appCtx: app.AppContext, pageCtx: app.PageContext) => {

      return (
        <div className='flex-vbox' style={{ minHeight: '100px', maxHeight: '200px' }}>
          <input.BBTextField bean={request} field={'feedback'} style={{ height: '8em' }} />
          <div className='flex-hbox justify-content-between align-items-center flex-grow-0 gap-2 border-top p-1'>
            {TaskRequestLevelUtils.getTaskRequestList().map(opt => {
              let OptIcon = opt.icon;
              return (
                <bs.Button laf='warning' className={`border-0 py-2 px-1 w-100 bg-${opt.color}-subtle text-${opt.color}`}
                  onClick={() => {
                    pageCtx.back();
                    let modified: any = { ...request, status: opt.value }
                    this.onSaveChanges(modified)
                  }}>
                  <OptIcon size={14} className="me-1" />
                  <span>{opt.label}</span>
                </bs.Button>
              );
            })}
          </div>
        </div>
      );
    }
    pageContext.createPopupPage('update-note', T('Update Approved Note'), createAppPage, { size: 'md' });
  }

  onNewTaskRequest = () => {
    let { pageContext } = this.props;
    let taskRequest: any = {
      status: 'NEW',
      requestDate: util.TimeUtil.javaCompactDateTimeFormat(new Date()),
      requestByAccountId: SESSION.getAccountId(),
      requestByLabel: SESSION.getAccountAcl().getFullName(),
    }
    let observer = new entity.ComplexBeanObserver(taskRequest);

    const pageContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <div className='flex-vbox' >
          <UITaskRequestEditor appContext={appCtx} pageContext={pageCtx} observer={observer}
            onPostCommit={(_bean) => {
              pageCtx.back();
              this.onAddOrModifyDBRecordCallback(_bean);
              this.getVGridContext().getVGrid().forceUpdateView();
            }} />
        </div>
      )
    }
    pageContext.createPopupPage('new-task-request', `New Task Request`, pageContent, { size: 'flex-lg', backdrop: 'static' });
  }

  onDeleteAction(): void {
    const { appContext, pageContext } = this.props;
    let approvalAccountId: number = SESSION.getAccountId();
    let records: any[] = this.vgridContext.model.getSelectedRecords();

    if (records.length === 0) {
      bs.dialogShow('Message',
        <div className="text-warning text-center p-2">
          No Task Request Selected!
        </div>
      );
      return;
    }
    const moderatorCap = pageContext.hasUserModeratorCapability();
    if (!moderatorCap) {
      const unauthorized = records.filter(record => record.approvedByAccountId !== approvalAccountId);
      if (unauthorized.length > 0) {
        bs.dialogShow('Warning',
          <div className="text-warning text-center p-2">
            You can only delete task requests that you have approved!
          </div>
        );
        return;
      }
    }

    const selectedIds = this.getVGridContext().model.getSelectedRecordIds();
    appContext.createHttpBackendCall('TaskRequestService', 'deleteTaskRequests', { ids: selectedIds })
      .withSuccessNotification("success", T("Success"), T("Delete task success!!!"))
      .withSuccessData((_data: any) => {
        this.reloadData();
        this.getVGridContext().model.removeSelectedDisplayRecords();
        this.getVGridContext().getVGrid().forceUpdateView();
      })
      .withEntityOpNotification('delete', 'task')
      .call();
  }

  onSaveChanges(modified: any) {
    if (!modified['id']) {
      bs.dialogShow('Warning',
        <div className="text-warning fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />
          {T('Only update task request action!!!')}
        </div>,
        { backdrop: 'static', size: 'md' }
      );
      return
    }
    let { appContext } = this.props;
    this.vgridContext.model.addOrUpdateByRecordId(modified);
    this.vgridContext.getVGrid().forceUpdateView(true);
    appContext.createHttpBackendCall("TaskRequestService", "saveTaskRequestRecords", { modified: [modified] })
      .withSuccessNotification("success", T("Success"), T("Update task success"))
      .withSuccessData((_data: any) => {

      })
      .call();
  }

  onChangeStatus = (status: string, record: any) => {
    if (status === 'APPROVED' || status === 'REJECTED') {
      record['approvedByAccountId'] = SESSION.getAccountId();
      record['approvedByLabel'] = SESSION.getAccountAcl().getFullName();
      record['approvedDate'] = util.TimeUtil.javaCompactDateTimeFormat(new Date());
    }
    record['status'] = status;
    this.onSaveChanges(record);
  }

  onModify = (_bean: any, _field: string, _oldVal: any, _newVal: any): void => {
    const { plugin } = this.props;
    let searchParam = plugin.getSearchParams();
    if (_field === 'maxReturn') {
      searchParam.maxReturn = _newVal || 5000;
    } else {
      searchParam.params = _bean;
    }
    plugin.searchParams = searchParam;
    this.reloadData();
    this.nextViewId();
    this.vgridContext.getVGrid().forceUpdateView();
  };

  doExport = () => {
    let { appContext, plugin } = this.props;
    let pluginImpl = plugin as UITaskRequestListPlugin;
    let targetRecords = pluginImpl.getListModel().getFilterRecords();

    let fieldGroupField: entity.GroupField = {
      label: "_blank_",
      fields: [
        { label: "STT.", name: "stt", dataType: 'number' },
        { label: "Type", name: "requestType", dataType: 'string' },
        { label: "Request By", name: "requestByLabel", dataType: 'string' },
        { label: "Department", name: "departmentLabel", dataType: 'string' },
        { label: "Request Date", name: "requestDate", dataType: 'string' },
        { label: "Ref", name: "referenceNote", dataType: 'string' },
        { label: "Customer Address", name: "label", dataType: 'string' },
        { label: "Description", name: "description", dataType: 'string' },
        { label: "Approved By", name: "approvedByLabel", dataType: 'string' },
        { label: "Approved Date", name: "approvedDate", dataType: 'date' },
        { label: "Status", name: "status", dataType: 'string' },
      ],
    }

    let records: Array<any> = [];
    for (let i = 0; i < targetRecords.length; i++) {
      let rec: any = targetRecords[i];
      let childRecData = {
        stt: records.length + 1,
        requestType: TaskRequestTypeUtils.getTypeInfo(rec['requestType']).label,
        requestByLabel: rec['requestByLabel'],
        departmentLabel: rec['departmentLabel'],
        requestDate: util.text.formater.compactDate(rec['requestDate']),
        referenceNote: rec['referenceNote'],
        label: rec['label'],
        description: rec['description'],
        approvedByLabel: rec['approvedByLabel'],
        approvedDate: rec['approvedDate'] ? util.text.formater.compactDate(rec['approvedDate']) : 'N/A',
        status: TaskRequestStatusUtils.getInfo(rec['status']).label,
      }
      records.push(childRecData);
    }

    let exportModel: entity.DataListExportModel = {
      fieldGroups: [],
      fields: [...fieldGroupField.fields],
      records: records,
      modelName: 'Task Requests',
      fileName: `Requests_${util.TimeUtil.toCompactDateFormat(new Date())}.xlsx`,
    }

    appContext.createHttpBackendCall('DataMappingService', 'xlsxPrivateExport', { model: exportModel })
      .withSuccessData((data: any) => {
        let storeInfo = data;
        if (storeInfo.type == 'ir.actions.act_url') {
          entity.StoreInfo.download(storeInfo.url);
        } else if (storeInfo.privateMode) {
          entity.StoreInfo.privateDownload(storeInfo);
        } else {
          entity.StoreInfo.download(storeInfo.url);
        }
      })
      .call()
  }

  renderHeader() {
    const { appContext, pageContext, plugin } = this.props;
    const { dateFilter } = this.reportFilter;
    let pluginImp: UITaskRequestListPlugin = plugin as UITaskRequestListPlugin;

    return (
      <div className="flex-hbox flex-grow-0 align-items-center justify-content-between px-2 py-1">

        <div className="flex-hbox justify-content-start align-items-center flex-grow-0 gap-2" >
          <h5 style={{ color: '#6c757d' }}>
            <FeatherIcon.Trello className="me-2" size={18} />
            Task Request
          </h5>

          <div className='flex-hbox align-items-center flex-grow-0 border-start' >
          </div>
        </div>

        <div className="flex-hbox justify-content-end align-items-center flex-grow-1 gap-1" >

          <bs.Button laf="secondary" outline size="sm" className="p-1" style={{ color: '#6c757d' }}
            onClick={this.onNewTaskRequest}>
            <FeatherIcon.Plus size={14} className="me-2" />
            New Request
          </bs.Button>

          <WQuickTimeRangeSelector appContext={appContext} pageContext={pageContext}
            initBean={dateFilter}
            onModify={(bean: any, _field: string, _oldVal: any, _newVal: any) => {
              this.reportFilter.dateFilter = bean;
              console.log(this.reportFilter.dateFilter);
              pluginImp.withRequestDate(bean.fromValue, bean.toValue);
              let searchParam: any = pluginImp.getSearchParams();
              plugin.searchParams = searchParam;
              this.reloadData();
              this.nextViewId();
              this.forceUpdate();
            }} />

          <WTaskGroupBySelector appContext={appContext} pageContext={pageContext}
            onModify={(bean: any, _field: string, _oldVal: any, _newVal: any) => {
              this.reportFilter.groupedBy = bean;
              this.forceUpdate();
              // this.loadData();
            }} />

          <bs.Button laf="secondary" outline size="sm" className="p-1" style={{ color: '#6c757d' }}
            onClick={this.doExport}>
            <FeatherIcon.Download size={14} className="me-2" />
            Export
          </bs.Button>

          <bs.Button laf='warning' size="sm" className="p-1" outline
            onClick={() => this.onDeleteAction()} hidden={bs.ScreenUtil.isMobileScreen()}>
            <FeatherIcon.Trash2 size={14} /> Delete
          </bs.Button>

        </div>

      </div>

    )
  }

  render() {
    return (
      <div className="flex-vbox mx-1 p-1 bg-white rounded-md w-100 h-100 my-1" >
        {this.renderHeader()}
        <div className="flex-vbox border-top bg-body-highlight" key={this.viewId}>
          {this.isLoading() ? this.renderLoading() : this.renderUIGrid()}
        </div>
      </div>
    )
  }

}