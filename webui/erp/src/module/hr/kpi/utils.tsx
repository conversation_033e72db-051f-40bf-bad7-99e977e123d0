import React from "react";
import { app, util } from "@datatp-ui/lib";
import { T } from "./backend";
import { UIEmployeeList, UIEmployeeListPlugin } from "../../company/hr";
import { KpiCalculateAlgorithm, KpiGrade, KpiObjectiveType, KpiStatus } from "../models";

export const onAddEmployeesToKpiTemplate = (uiRoot: app.AppComponent, template: any, callback: () => void) => {
  let { appContext, pageContext } = uiRoot.props;
  let onMultiSelect = (pageCtx: app.PageContext, beans: Array<any>) => {
    let ids: Array<any> = [];
    for (let bean of beans) {
      ids.push(bean.id);
    }
    pageCtx.back();

    appContext.createHttpBackendCall('KpiService', 'createKpiForEmployees', { kpiTmplId: template.id, employeeIds: ids })
      .withSuccessData((data: any) => {
        callback();
      })
      .withBackendNotification(T, 300)
      .call();
  }
  let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
    return (
      <UIEmployeeList
        type={'selector'}
        plugin={new UIEmployeeListPlugin()}
        appContext={appCtx} pageContext={pageCtx} readOnly={true}
        onSelect={(_appCtx, pageCtx, employee) => onMultiSelect(pageCtx, [employee])}
        onMultiSelect={(_appCtx, pageCtx, employees) => onMultiSelect(pageCtx, employees)} />
    )
  }
  pageContext.createPopupPage("select-employees", T('Select Employees'), createAppPage, { size: "lg", backdrop: "static" })
}

export const calculateKpiItemComplete = (item: any): number => {
  let { targetValue, actualValue, managerAdjustValue, employeeAdjustValue, calculateAlgorithm } = item;
  if (calculateAlgorithm == KpiCalculateAlgorithm.RATIO_BASED) {
    let actual = actualValue;
    if (managerAdjustValue) {
      actual = managerAdjustValue;
    } else if (employeeAdjustValue) {
      actual = employeeAdjustValue;
    }
    let complete = targetValue != 0 ? (actual / targetValue) * 100 : 0;
    return complete;
  } else if (calculateAlgorithm == KpiCalculateAlgorithm.FAILURE_BASED) {
    let actual = actualValue;
    if (managerAdjustValue) {
      actual = managerAdjustValue;
    } else if (employeeAdjustValue) {
      actual = employeeAdjustValue;
    }
    if (actual <= targetValue) return 100;
    else return 0;
  }
  return 0;
}

export const validateKpiItemList = (items: any[], objectiveType: KpiObjectiveType): boolean => {
  let weight: number = 0;
  let countItem = 0;
  for (let item of items) {
    let type = item['objectiveType'];
    if (type == objectiveType) {
      weight += item.contributionWeight ? item.contributionWeight * 100 : 0;
      countItem++;
    }
  }
  if (countItem == 0) return true;
  if (weight != 100) return false;
  return true;
}


export class BehaviorPointValidator implements util.validator.Validator {
  validate(val: any): void {
    if (![1, 2, 3, 4, 5].includes(val)) {
      throw new Error('Nhập số tự nhiên từ 1 đến 5');
    }
  }
}

export const getKpiStatusLabel = (status: KpiStatus): string => {
  if (status === KpiStatus.Plan) {
    return 'Lên  kế hoạch';;
  } else if (status === KpiStatus.PlanReviewing) {
    return 'Review kế hoạch';
  } else if (status === KpiStatus.Processing) {
    return 'Nhập kết quả';
  } else if (status === KpiStatus.ResultReviewing) {
    return 'Review kết quả';
  } else if (status === KpiStatus.LeaderApproved) {
    return 'Leader đã duyệt';
  } else if (status === KpiStatus.ManagerApproved) {
    return 'TBP đã duyệt';
  } else if (status === KpiStatus.DirectorApproved) {
    return 'Giám đốc đã duyệt';
  }
  return '';
}

export const getKpiGradeLabel = (grade: KpiGrade): string => {
  if (grade === KpiGrade.Failed) {
    return 'Không đạt';
  } else if (grade === KpiGrade.NeedImprovement) {
    return 'Cải thiện';
  } else if (grade === KpiGrade.Passed) {
    return 'Đạt';
  } else if (grade === KpiGrade.ExceedExpectations) {
    return 'Vượt mong đợi';
  } else if (grade === KpiGrade.Outstanding) {
    return 'Xuất sắc';
  }
  return '-';
}

export const renderKpiNoted = (space: 'user' | 'company' | 'system', view: 'kpi' | 'template'): React.ReactElement => {
  return (
    <div className='flex-vbox flex-grow-0 ps-2 pb-2' style={{ fontSize: '14px' }}>
      {view == 'kpi' ?
        <>
          <div className='fw-bold text-primary mb-2'>* Mục tiêu đánh giá:</div>
          <div className='ps-2'>
            <div className='d-flex align-items-center mb-2'>
              <div style={{ width: 130 }} className='fw-bold'>Mục tiêu bắt buộc</div>
              <div className="rounded bg-secondary" style={{ width: 24, height: 12 }}></div>
            </div>
            <div className='d-flex align-items-center mb-2'>
              <div style={{ width: 130 }} className='fw-bold'>Mục tiêu thêm</div>
              <div className="rounded bg-info" style={{ width: 24, height: 12 }}></div>
            </div>
          </div>
        </>
        : null
      }

      <div className='fw-bold text-primary mb-2'>* Phương pháp tính:</div>
      <div className='ps-2'>
        <div className='d-flex align-items-start mb-2'>
          <div style={{ width: '220px' }} className='fw-bold'>KPIs xuôi (càng lớn càng tốt)</div>
          <div className='flex-vbox'>
            <div>(%) hoàn thành = Thực hiện/Kế hoạch.</div>
            <div>Kết quả KPI = (%) hoàn thành x Trọng số.</div>
          </div>
        </div>

        <div className='d-flex align-items-start mb-2'>
          <div style={{ width: '220px' }} className='fw-bold'>KPIs ngược (càng bé càng tốt)</div>
          <div className='flex-vbox'>
            <div>(%) hoàn thành</div>
            <div>{` - Thực hiện <= Kế hoạch: 100%`}</div>
            <div>{` - Thực hiện > Kế hoạch:    0%`}</div>
            <div>Kết quả KPI = (%) hoàn thành x 100% x Trọng số.</div>
          </div>
        </div>
      </div>

      <div className='fw-bold text-primary mb-2'>* Quy ước số điểm đánh giá thái độ hành vi: điểm số từ 1 đến 5</div>
      <div className='ps-2'>
        <div className='d-flex align-items-center mb-2'>
          <div style={{ width: '50px' }} className='fw-bold'>5</div>
          <div>Không bao giờ vi phạm</div>
        </div>

        <div className='d-flex align-items-center mb-2'>
          <div style={{ width: '50px' }} className='fw-bold'>4</div>
          <div>Hiếm khi vi phạm {'<='} 2 lần</div>
        </div>

        <div className='d-flex align-items-center mb-2'>
          <div style={{ width: '50px' }} className='fw-bold'>3</div>
          <div>Vi phạm vài lần nhưng không gây ảnh hưởng đến công việc {'<='} 4 lần</div>
        </div>

        <div className='d-flex align-items-center mb-2'>
          <div style={{ width: '50px' }} className='fw-bold'>2</div>
          <div>Vi phạm nhiều lần và đã từng bị nhắc nhở bởi TBP/Phòng ban khác {'>'}4 lần</div>
        </div>

        <div className='d-flex align-items-center'>
          <div style={{ width: '50px' }} className='fw-bold'>1</div>
          <div>Vi phạm nhiều lần và gây thiệt hại nặng nề cho công ty (tài sản, tiền, ảnh hưởng thương hiệu,...)</div>
        </div>
      </div>

      {space == 'company' ?
        <>
          <div className='fw-bold text-primary mb-2 mt-2'>* Hướng dẫn xếp loại đánh giá</div>
          <div className='ps-2 fw-bold'>
            <div className='d-flex align-items-center text-danger'>
              <div style={{ height: 30, width: 75 }} className='border fw-bold text-center p-1'></div>
              <div style={{ height: 30, width: 125 }} className='border fw-bold text-center p-1'>Không đạt</div>
              <div style={{ height: 30, width: 75 }} className='border fw-bold text-center p-1'>{`<= 70%`}</div>
            </div>

            <div className='d-flex align-items-center text-warning'>
              <div style={{ height: 30, width: 75 }} className='border fw-bold text-center p-1'>{`70% <`}</div>
              <div style={{ height: 30, width: 125 }} className='border fw-bold text-center p-1'>Cải thiện</div>
              <div style={{ height: 30, width: 75 }} className='border fw-bold text-center p-1'>{`< 95%`}</div>
            </div>

            <div className='d-flex align-items-center text-primary'>
              <div style={{ height: 30, width: 75 }} className='border fw-bold text-center p-1'>{`95% <=`}</div>
              <div style={{ height: 30, width: 125 }} className='border fw-bold text-center p-1'>Đạt</div>
              <div style={{ height: 30, width: 75 }} className='border fw-bold text-center p-1'>{`<= 115%`}</div>
            </div>

            <div className='d-flex align-items-center text-success'>
              <div style={{ height: 30, width: 75 }} className='border fw-bold text-center p-1'>{`115% <`}</div>
              <div style={{ height: 30, width: 125 }} className='border fw-bold text-center p-1'>Vượt Mong đợi</div>
              <div style={{ height: 30, width: 75 }} className='border fw-bold text-center p-1'>{`< 135%`}</div>

            </div>
            <div className='d-flex align-items-center text-info'>
              <div style={{ height: 30, width: 75 }} className='border fw-bold text-center p-1'>{`135% <=`}</div>
              <div style={{ height: 30, width: 125 }} className='border fw-bold text-center p-1'>Xuất sắc</div>
              <div style={{ height: 30, width: 75 }} className='border fw-bold text-center p-1'></div>
            </div>
          </div>
        </>
        : null
      }

    </div>
  );
}