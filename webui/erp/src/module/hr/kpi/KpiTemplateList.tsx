import React from 'react';
import * as FeatherIcon from 'react-feather';
import { app, grid, sql, bs, entity, util, input } from '@datatp-ui/lib';

import { BBRefOKRMilestone } from '../../okr/milestone/BBOKRMilestone';
import { T } from './backend';
import { KpiTemplateModel, UIKpiTemplate } from './KpiTemplate'
import { BBRefEmployee } from '../../../module/company/hr';
import { KpiList, KpiListPlugin } from './KpiList';
import { onAddEmployeesToKpiTemplate } from './utils';
const SESSION = app.host.DATATP_HOST.session;

export class KpiTemplateListPlugin extends entity.DbEntityListPlugin {
  constructor(space: 'user' | 'company' | 'system') {
    super([]);
    this.backend = {
      context: 'company',
      service: 'KpiService',
      searchMethod: 'searchKpiTemplates',
      changeStorageStateMethod: 'updateKpiTemplateStorageState'
    }

    this.searchParams = {
      params: {
        space: space
      },
      filters: [
        ...sql.createSearchFilter()
      ],
      optionFilters: [
        sql.createStorageStateFilter([entity.StorageState.ACTIVE, entity.StorageState.ARCHIVED])
      ],
      rangeFilters: [
        ...sql.createCreatedTimeFilter(),
        ...sql.createModifiedTimeFilter()
      ],
      orderBy: {
        fields: ["modifiedTime"],
        fieldLabels: ["Modified Time"],
        selectFields: [],
        sort: "DESC"
      },
      maxReturn: 1000,
    }
  }

  loadData(uiList: entity.DbEntityList<any>) {
    let params = { params: this.searchParams }
    this.createBackendSearch(uiList, params).call();
  }
}

interface KpiTemplateListProps extends entity.DbEntityListProps {
  space: 'user' | 'company';
}
export class KpiTemplateList extends entity.DbEntityList<KpiTemplateListProps> {
  createVGridConfig() {
    let { appContext, pageContext, type } = this.props;
    let moderatorCap = pageContext.hasUserModeratorCapability();
    let config: grid.VGridConfig = {
      title: T("Kpi Templates"),
      record: {
        control: {
          width: 30,
          items: [
            {
              name: 'copy', hint: 'Copy Template', icon: FeatherIcon.Copy,
              customRender(ctx: grid.VGridContext, dRecord: grid.DisplayRecord) {
                if (!pageContext.hasUserModeratorCapability()) return <></>;
                const onClick = () => {
                  let uiList = ctx.uiRoot as KpiTemplateList;
                  uiList.onCopyTemplate(dRecord.record);
                }
                return (
                  <bs.Button laf='link' disabled={false} onClick={onClick}>
                    <FeatherIcon.Copy size={12} className='me-1' />
                  </bs.Button>);
              },
            },
          ]
        },
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector()),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          { ...entity.DbEntityListConfigTool.FIELD_ON_SELECT('label', T('Tên Job Code'), 300), state: { showRecordState: true }, },
          { name: 'editMode', label: 'Edit Mode', width: 90, state: { visible: false } },
          { name: 'jobCode', label: 'Mã Job Code', width: 200, filterable: true, filterableType: 'Options', },
          {
            name: 'numberApplyFor', label: 'CBNV áp dụng', hint: 'Số lượng Cán bộ nhân viên áp dụng', width: 110,
            customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let applyFor = record['numberApplyFor'] || 0;
              return (
                <div className="flex-vbox px-5">
                  <bs.Button laf='link' onClick={() => this.onShowApplyFor(record)}>
                    <div className='flex-hbox p-0 justify-content-between'>
                      <div className='text-secondary fw-light'>
                        {applyFor}
                      </div>
                      <FeatherIcon.List size={12} />
                    </div>
                  </bs.Button>
                </div>
              );
            }
          },
          { name: 'milestoneLabel', label: 'Milestone', width: 100, filterable: true, filterableType: 'Options', },
          { name: 'fromDate', label: 'Từ ngày', format: util.text.formater.compactDate, },
          { name: 'toDate', label: 'Đến ngày', format: util.text.formater.compactDate, },
          {
            name: 'leaderAccountId', label: 'Leader', width: 180, filterable: true, filterableType: 'Options',
            fieldDataGetter: (record: any) => {
              let val = '-';
              if (record['leaderFullName']) val = record['leaderFullName'];
              return val.toLocaleUpperCase();
            }
          },
          {
            name: 'managerAccountId', label: 'Manager', width: 180, filterable: true, filterableType: 'Options',
            fieldDataGetter: (record: any) => {
              let val = '-';
              if (record['managerFullName']) val = record['managerFullName'];
              return val.toLocaleUpperCase();
            }
          },
          { name: 'jobTitle', label: 'Chức vụ', width: 180, filterable: true, filterableType: 'Options', },
          { name: 'department', label: 'Phòng ban', width: 180, filterable: true, filterableType: 'Options', },
          { name: 'workplace', label: 'Nơi làm việc', filterable: true, filterableType: 'Options', },
          { name: 'companyBranch', label: 'Chi nhánh', width: 180, filterable: true, filterableType: 'Options', state: { visible: false } },
          { name: 'description', label: 'Mô tả', width: 400, dataTooltip: true },
          ...entity.DbEntityListConfigTool.FIELDS(!pageContext.hasUserAdminCapability(), [
            ...entity.DbEntityListConfigTool.FIELD_ENTITY,
          ])
        ],
        editor: {
          supportViewMode: ['table'],
          enable: false
        }
      },
      toolbar: {
        actions: [
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_STORAGE_STATES(
            [entity.StorageState.ACTIVE, entity.StorageState.ARCHIVED],
            !pageContext.hasUserModeratorCapability() || type == 'selector'
          ),
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_DELETE(!pageContext.hasUserAdminCapability() || type == 'selector', T('Delete'))
        ],
        filters: entity.DbEntityListConfigTool.TOOLBAR_FILTERS(true)
      },
      view: {
        currentViewName: 'aggregation',
        availables: {
          table: {
            viewMode: 'table'
          },
          aggregation: {
            viewMode: 'aggregation', treeWidth: 250,
            createAggregationModel(_ctx: grid.VGridContext) {
              let model = new grid.AggregationDisplayModel(T('All'), false);
              model.addAggregation(new grid.ValueAggregation(T("Milestone"), "milestone", true)
                .withFieldGetter(record => record['milestoneLabel']));
              model.addAggregation(new grid.ValueAggregation(T("Department"), "department", true));
              model.addAggregation(new grid.ValueAggregation(T("Job Code"), "jobCode", false));
              model.addAggregation(new grid.ValueAggregation(T("Manager"), "managerAccountId", false)
                .withFieldGetter(record => record['managerFullName']));
              return model;
            }
          }
        }
      },
      footer: {
        page: {
          render: (ctx: grid.VGridContext) => {
            return <bs.Toolbar className='border' hide={!moderatorCap || pageContext.getUserDataScope() == app.AppDataScope.OWNER}>
              <entity.WButtonEntityWrite
                appContext={appContext} pageContext={pageContext}
                icon={FeatherIcon.Plus} label={T('New KPI Template')} onClick={this.onNewAction} />
            </bs.Toolbar >
          }
        },
      },
    };
    return config;
  }

  onShowApplyFor = (template: any) => {
    let { pageContext, space } = this.props;
    let onAddEmployees: any = (callback: () => void) => {
      onAddEmployeesToKpiTemplate(this, template, () => {
        callback();
        this.reloadData();
      });
    }

    if (template['managerAccountId'] !== SESSION.getAccountId()) onAddEmployees = null;

    let createPageContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <KpiList appContext={appCtx} pageContext={pageCtx} plugin={new KpiListPlugin(template.id)}
          space={space} onAddEmployees={onAddEmployees} />
      )
    }
    pageContext.addPageContent("apply-for-kpis", T('Danh sách CBNV áp dụng'), createPageContent);
  }

  onCopyTemplate = (template: any) => {
    let { appContext, pageContext } = this.props;
    let newTemplate = {
      editMode: entity.EditMode.DRAFT,
      jobTitle: template.jobTitle,
      department: template.department,
      workplace: template.workplace,
      companyBranch: template.companyBranch,
      milestone: template.milestone,
      milestoneLabel: template.milestoneLabel,
      fromDate: template.fromDate,
      toDate: template.toDate,
      label: template.label,
      description: template.description,
    };
    let createPageContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {

      let onCopy = (copied: any) => {
        if (!copied['jobCode'] || !copied['jobTitle'] || !copied['managerAccountId']) {
          bs.notificationShow("warning", T(`Vui lòng điền đẩy đủ thông tin!`));
          return;
        }

        appContext.createHttpBackendCall('KpiService', 'copyKpiTemplate', { rootId: template.id, template: copied })
          .withSuccessData((_data: any) => {
            appContext.addOSNotification("success", T(`Copy Success`));
            pageCtx.back();
            this.reloadData();
          })
          .call();
      }

      return (
        <NewKpiTemplate appContext={appCtx} pageContext={pageCtx} observer={new entity.BeanObserver(newTemplate)}
          onCommit={onCopy} btnLabel='Submit' />
      )
    }
    pageContext.createPopupPage('copy-kpi-template', 'Copy KPI Template', createPageContent, { size: 'md', backdrop: 'static' });

  }

  onNewAction = () => {
    let { pageContext } = this.props;
    let newTemplate = { editMode: entity.EditMode.DRAFT };
    let createPageContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      let onCreate = (template: any) => {
        appCtx.createHttpBackendCall('KpiService', 'saveKpiTemplate', { tmpl: template })
          .withSuccessData((_data: any) => {
            appCtx.addOSNotification("success", T(`Create Success`));
            pageCtx.back();
            this.reloadData();
          })
          .call();
      }

      return (
        <NewKpiTemplate appContext={appCtx} pageContext={pageCtx} observer={new entity.BeanObserver(newTemplate)}
          onCommit={onCreate} btnLabel='Create' />
      )
    }
    pageContext.createPopupPage('new-kpi-template', 'New KPI Template', createPageContent, { size: 'md', backdrop: 'static' });
  }

  onDeleteAction(): void {
    let { appContext } = this.props;
    let selectedIds = this.vgridContext.model.getSelectedRecordIds();
    if (selectedIds.length === 0) {
      bs.notificationShow("warning", T("No records were selected"));
      return;
    }
    for (let rec of this.vgridContext.model.getSelectedRecords()) {
      if (rec['numberApplyFor'] != 0) {
        bs.dialogShow('Không thể xoá KPI Template này',
          <div className="text-danger fw-bold text-center py-3 border-bottom">
            <FeatherIcon.AlertCircle className="mx-2" />
            {T(`Có ${rec['numberApplyFor']} CBNV đã áp dụng KPI với Job Code ${rec['jobCode']}. Bạn không thể xóa KPI Template này.`)}
          </div>,
          { backdrop: 'static', size: 'sm' }
        );
        return;
      }
    }
    const onConfirmDelete = () => {
      appContext.createHttpBackendCall('KpiService', 'deleteKpiTemplates', { ids: selectedIds })
        .withSuccessData((_data: any) => {
          this.reloadData();
        })
        .withEntityOpNotification('delete', T('KPI Template'), selectedIds.length)
        .call();
    }

    let messageEle = (<div className="text-warning">Would you like to delete {selectedIds.length} records?</div>)
    bs.dialogConfirmMessage(T("Confirm Delete"), messageEle, onConfirmDelete);
  }

  onDefaultSelect(dRecord: grid.DisplayRecord) {
    let rec = dRecord.record;
    let { appContext, pageContext, space } = this.props;

    appContext.createHttpBackendCall('KpiService', 'getKpiTemplateModel', { kpiId: rec.id })
      .withSuccessData((data: any) => {
        let kpiTemplateModel: KpiTemplateModel = new KpiTemplateModel(data);
        let createPageContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return (
            <UIKpiTemplate appContext={appCtx} pageContext={pageCtx} space={space}
              kpiTemplateModel={kpiTemplateModel} onPostCommit={() => { this.reloadData() }} />
          )
        }
        pageContext.addPageContent('kpi-template', rec.label, createPageContent);
      })
      .call();
  }
}

interface NewKpiTemplateProps extends app.AppComponentProps {
  observer: entity.BeanObserver;
}
class NewKpiTemplateForm extends app.AppComponent<NewKpiTemplateProps> {

  onInputChangeJobCode = (bean: any, field: string, oldVal: any, newVal: any) => {
    if (!newVal || oldVal === newVal) return;
    const { appContext, observer } = this.props;
    let template = observer.getMutableBean();
    appContext.createHttpBackendCall("KpiService", 'getKpiTemplateByJobCode', { milestone: template.milestone, jobCode: newVal })
      .withSuccessData((data) => {
        if (data) {
          let detailErrorEle = (<div> <strong>{template.milestoneLabel}</strong> Job code <strong>{newVal} </strong> đã tồn tại!</div>);
          bs.notificationShow('warning', 'Input Job Code Error', detailErrorEle);
          bean[field] = oldVal;
          this.forceUpdate();
        }
      })
      .call();
  }

  render() {
    let { appContext, pageContext, observer } = this.props;
    let kpiTemplate = observer.getMutableBean();
    pageContext.setPageOwner(true);
    pageContext.setPageEditMode(entity.EditMode.DRAFT);
    let writeCap = pageContext.canEditPage();
    return (
      <div className='flex-vbox'>
        <input.BBStringField bean={kpiTemplate} label='Tên Job Code' field={'label'} disable={!writeCap} required />
        <input.BBStringField bean={kpiTemplate} label='Job Code' field={'jobCode'} disable={!writeCap} required
          onInputChange={this.onInputChangeJobCode} />
        <BBRefOKRMilestone
          appContext={appContext} pageContext={pageContext} placement="bottom-start" required
          label={T('Milestone')} placeholder="Enter Milestone" bean={kpiTemplate} beanIdField={'milestone'}
          beanLabelField={'milestoneLabel'} refMilestoneField={'milestone'} disable={!writeCap}
          onPostUpdate={(_inputUI, _bean, selOpt, _userInput) => {
            kpiTemplate.milestone = selOpt.milestone;
            kpiTemplate.milestoneLabel = selOpt.label;
            if (!kpiTemplate.fromDate) kpiTemplate.fromDate = selOpt.startDate;
            if (!kpiTemplate.toDate) kpiTemplate.toDate = selOpt.endDate;
            this.forceUpdate();
          }} />
        <bs.Row>
          <bs.Col span={6}>
            <input.BBDateTimeField
              bean={kpiTemplate} label='Từ ngày' field={'fromDate'}
              dateFormat={"DD/MM/YYYY"} timeFormat={false} disable={!writeCap} />
          </bs.Col>
          <bs.Col span={6}>
            <input.BBDateTimeField
              bean={kpiTemplate} label='Đến ngày' field={'toDate'}
              dateFormat={"DD/MM/YYYY"} timeFormat={false} disable={!writeCap} />
          </bs.Col>
        </bs.Row>
        <BBRefEmployee
          appContext={appContext} pageContext={pageContext} bean={kpiTemplate} required
          label='Quản lý' beanIdField='managerAccountId' beanLabelField='managerFullName' selectedId='accountId'
          placeholder='Input Manager' disable={!writeCap} />
        <input.BBStringField bean={kpiTemplate} label='Chức vụ' field={'jobTitle'} disable={!writeCap} />
        <input.BBStringField bean={kpiTemplate} label='Phòng ban' field={'department'} disable={!writeCap} />
        <input.BBStringField bean={kpiTemplate} label='Nơi làm việc' field={'workplace'} disable={!writeCap} />
        <input.BBStringField bean={kpiTemplate} label='Chi nhánh' field={'companyBranch'} disable={!writeCap} />
        <input.BBTextField bean={kpiTemplate} label='Mô tả' field={'description'} disable={!writeCap}
          style={{ height: '7em' }} />
      </div>
    );
  }
}

interface NewKpiTemplateProps extends app.AppComponentProps {
  observer: entity.BeanObserver;
  btnLabel: string;
  onCommit: (newTemplate: any) => void;
}
class NewKpiTemplate extends app.AppComponent<NewKpiTemplateProps> {
  render() {
    let { pageContext, observer, btnLabel, onCommit } = this.props;
    pageContext.setPageOwner(true);
    pageContext.setPageEditMode(entity.EditMode.DRAFT);
    let onClick = () => {
      let kpiTemplate = observer.getMutableBean();
      onCommit(kpiTemplate);
    }
    return (
      <div className='flex-vbox'>
        <NewKpiTemplateForm {...this.props} />
        <bs.Toolbar className='border'>
          <bs.Button laf='primary' onClick={onClick}>
            <FeatherIcon.Check size={12} /> {T(btnLabel)}
          </bs.Button>
        </bs.Toolbar>
      </div>
    );
  }
}