import React from 'react';
import { grid, sql, entity, input, util } from '@datatp-ui/lib';

import { T } from './backend';
import { KpiCalculateAlgorithm, KpiGrade, KpiStatus } from '../models';
import { calculateKpiItemComplete, getKpiGradeLabel, getKpiStatusLabel } from './utils';

export class KpiRawDataListPlugin extends entity.DbEntityListPlugin {
  constructor(space: 'user' | 'company' | 'system',) {
    super([]);
    this.backend = {
      service: 'KpiService',
      searchMethod: 'searchKpiRawData',
    }

    this.searchParams = {
      params: {
        "space": space,
      },
      filters: [
        ...sql.createSearchFilter()
      ],
      optionFilters: [
        sql.createStorageStateFilter([entity.StorageState.ACTIVE, entity.StorageState.ARCHIVED])
      ],
      rangeFilters: [
        ...sql.createCreatedTimeFilter(),
        ...sql.createModifiedTimeFilter()
      ],
      orderBy: {
        fields: ["modifiedTime"],
        fieldLabels: ["Modified Time"],
        selectFields: [],
        sort: "DESC"
      },
      maxReturn: 1000,
    }
  }

  withCompany(companyCode: string) {
    if (companyCode) this.addSearchParam("companyCode", companyCode);
    return this;
  }

  loadData(uiList: entity.DbEntityList<any>) {
    let params = { params: this.searchParams }
    this.createBackendSearch(uiList, params).call();
  }
}

interface KpiRawDataListProps extends entity.DbEntityListProps {
}
export class KpiRawDataList extends entity.DbEntityList<KpiRawDataListProps> {
  createVGridConfig() {
    let { pageContext } = this.props;
    let computeCssClasses = (_ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
      let record = dRecord.record;
      let status: KpiStatus = record['status'];
      let cssClass = status == KpiStatus.DirectorApproved ? 'text-success' : 'text-secondary';
      return cssClass;
    }

    let config: grid.VGridConfig = {
      title: T("Kpi List"),
      record: {
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector()),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          {
            ...entity.DbEntityListConfigTool.FIELD_ON_SELECT('employeeFullName', T('Họ và tên'), 200),
            state: { showRecordState: true },
            fieldDataGetter: (record: any) => {
              let val = '-';
              if (record['employeeFullName']) val = record['employeeFullName'];
              return val.toLocaleUpperCase();
            }
          },
          {
            name: 'employeeCode', label: 'Mã nhân viên', width: 155,
            computeCssClasses: computeCssClasses,
          },
          {
            name: 'leaderAccountId', label: 'Leader', width: 180, filterable: true, filterableType: 'Options',
            computeCssClasses: computeCssClasses,
            fieldDataGetter: (record: any) => {
              let val = '-';
              if (record['leaderFullName']) val = record['leaderFullName'];
              return val.toLocaleUpperCase();
            }
          },
          {
            name: 'managerAccountId', label: 'Manager', width: 180, filterable: true, filterableType: 'Options',
            computeCssClasses: computeCssClasses,
            fieldDataGetter: (record: any) => {
              let val = '-';
              if (record['managerFullName']) val = record['managerFullName'];
              return val.toLocaleUpperCase();
            }
          },
          { name: 'milestoneLabel', label: 'Milestone', width: 100, computeCssClasses: computeCssClasses, },
          {
            name: 'jobCode', label: 'Job Code', width: 180, filterable: true, filterableType: 'Options',
            computeCssClasses: computeCssClasses,
          },
          {
            name: 'jobTitle', label: 'Chức vụ', width: 180, filterable: true, filterableType: 'Options',
            computeCssClasses: computeCssClasses,
          },
          {
            name: 'department', label: 'Phòng ban', width: 180, filterable: true, filterableType: 'Options',
            computeCssClasses: computeCssClasses,
          },
          {
            name: 'workplace', label: 'Nơi làm việc', filterable: true, filterableType: 'Options',
            computeCssClasses: computeCssClasses,
          },
          {
            name: 'companyBranch', label: 'Chi nhánh', filterable: true, filterableType: 'Options',
            computeCssClasses: computeCssClasses,
          },
          {
            name: 'fromDate', label: 'Từ ngày', format: util.text.formater.compactDate,
            computeCssClasses: computeCssClasses,
          },
          {
            name: 'toDate', label: 'Đến ngày', format: util.text.formater.compactDate,
            computeCssClasses: computeCssClasses,
          },
          {
            name: 'status', label: 'Status', width: 145, filterable: true, filterableType: 'Options',
            computeCssClasses: computeCssClasses,
            fieldDataGetter(record) {
              let status: KpiStatus = record['status'];
              return getKpiStatusLabel(status);
            },
          },
          {
            name: 'kpiResult', label: 'Kết quả', width: 100, format: util.text.formater.percent,
            computeCssClasses: computeCssClasses,
          },
          {
            name: 'grade', label: 'Xếp loại', width: 120,
            filterable: true, filterableType: 'Options',
            computeCssClasses: (_ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
              let record = dRecord.record;
              let status: KpiStatus = record['status'];
              let cssClass = status == KpiStatus.DirectorApproved ? 'fw-bold text-success' : 'fw-bold text-secondary';
              return cssClass;
            },
            fieldDataGetter: (record: any) => {
              let status: KpiStatus = record['status'];
              if (![KpiStatus.DirectorApproved, KpiStatus.ManagerApproved].includes(status)) return '-';
              let grade: KpiGrade = record['grade'];
              return getKpiGradeLabel(grade);
            },
          },
          {
            name: 'finalGrade', label: 'Xếp loại Final', width: 140,
            filterable: true, filterableType: 'Options',
            computeCssClasses: (_ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
              let record = dRecord.record;
              if (record['finalGrade'] && record['finalGrade'] != record['grade']) {
                return 'fw-bold text-primary';
              }
              let status: KpiStatus = record['status'];
              let cssClass = status == KpiStatus.DirectorApproved ? 'fw-bold text-success' : 'fw-bold text-secondary';
              return cssClass;
            },
            fieldDataGetter: (record: any) => {
              let status: KpiStatus = record['status'];
              if (![KpiStatus.DirectorApproved, KpiStatus.ManagerApproved].includes(status)) return '-';
              let grade: KpiGrade = record['finalGrade'];
              return getKpiGradeLabel(grade);
            },
          },

          {
            name: 'itemLabel', label: T('Mục tiêu đánh giá'), width: 450,
            computeCssClasses: computeCssClasses,
          },
          {
            name: 'objectiveType', label: 'Thể loại', width: 90, filterable: true, filterableType: 'Options',
            computeCssClasses: computeCssClasses
          },
          {
            name: 'contributionWeight', label: 'Trọng số', width: 120,
            computeCssClasses: computeCssClasses,
            customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let cssClass = field.computeCssClasses?.(ctx, dRecord) || '';
              return (
                <input.BBPercentField bean={dRecord.record} field={field.name} disable className={cssClass} />
              );
            },
          },
          {
            name: 'unit', label: 'ĐVT', width: 120,
            computeCssClasses: computeCssClasses,
          },
          {
            name: 'targetValue', label: 'Kế hoạch', width: 90,
            computeCssClasses: (_ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
              let record = dRecord.record;
              let status: KpiStatus = record['status'];
              let cssClass = status == KpiStatus.DirectorApproved ? 'text-success cell-align-right' : 'text-secondary cell-align-right';
              return cssClass;
            },
            fieldDataGetter: (record: any) => {
              if (['PERCENT', 'Percent', 'percent', '%'].includes(record['unit'])) {
                return util.text.formater.percent(record['targetValue']);
              }
              return record['targetValue'];
            }
          },
          {
            name: 'actualValue', label: 'Thực hiện', width: 90,
            computeCssClasses: (_ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
              let record = dRecord.record;
              let status: KpiStatus = record['status'];
              let cssClass = status == KpiStatus.DirectorApproved ? 'text-success cell-align-right' : 'text-secondary cell-align-right';
              return cssClass;
            },
            fieldDataGetter: (record: any) => {
              if (['PERCENT', 'Percent', 'percent', '%'].includes(record['unit'])) {
                return util.text.formater.percent(record['actualValue']);
              }
              return record['actualValue'];
            }
          },
          {
            name: 'employeeAdjustValue', label: 'Nhân viên chỉnh sửa', width: 160,
            computeCssClasses: (_ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
              let record = dRecord.record;
              let status: KpiStatus = record['status'];
              let cssClass = status == KpiStatus.DirectorApproved ? 'text-success cell-align-right' : 'text-secondary cell-align-right';
              return cssClass;
            },
            fieldDataGetter: (record: any) => {
              if (['PERCENT', 'Percent', 'percent', '%'].includes(record['unit'])) {
                return record['employeeAdjustValue'] ? util.text.formater.percent(record['employeeAdjustValue']) : null;
              }
              return record['employeeAdjustValue'];
            }
          },
          {
            name: 'employeeAdjustExplanation', label: 'Lý do', width: 200,
            computeCssClasses: computeCssClasses,
          },
          {
            name: 'managerAdjustValue', label: 'Q.L Trực tiếp chỉnh sửa', width: 200,
            computeCssClasses: (_ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
              let record = dRecord.record;
              let status: KpiStatus = record['status'];
              let cssClass = status == KpiStatus.DirectorApproved ? 'text-success cell-align-right' : 'text-secondary cell-align-right';
              return cssClass;
            },
            fieldDataGetter: (record: any) => {
              if (['PERCENT', 'Percent', 'percent', '%'].includes(record['unit'])) {
                return record['managerAdjustValue'] ? util.text.formater.percent(record['managerAdjustValue']) : null;
              }
              return record['managerAdjustValue'];
            }
          },
          {
            name: 'managerAdjustExplanation', label: 'Lý do', width: 200,
            computeCssClasses: computeCssClasses,
          },

          {
            name: 'complete', label: '(%) Hoàn thành', width: 120,
            computeCssClasses: (_ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
              let record = dRecord.record;
              let status: KpiStatus = record['status'];
              let cssClass = status == KpiStatus.DirectorApproved ? 'text-success cell-align-right' : 'text-secondary cell-align-right';
              return cssClass;
            },
            fieldDataGetter: (record: any) => {
              let complete = calculateKpiItemComplete(record) / 100;
              record['complete'] = complete;
              return complete;

            },
            customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let cssClass = field.computeCssClasses?.(ctx, dRecord) || '';
              let complete = calculateKpiItemComplete(record) / 100;
              record['complete'] = complete;
              return (
                <input.BBPercentField bean={record} field={field.name} disable className={`flex-hbox justify-content-end ${cssClass}`} />
              );
            },
          },
          {
            name: 'itemResult', label: 'Kết quả KPI', width: 90,
            computeCssClasses: (_ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
              let record = dRecord.record;
              let status: KpiStatus = record['status'];
              let cssClass = status == KpiStatus.DirectorApproved ? 'text-success cell-align-right' : 'text-secondary cell-align-right';
              return cssClass;
            },
            fieldDataGetter: (record: any) => {
              let complete = calculateKpiItemComplete(record) / 100;
              let weight = record['contributionWeight'] || 0;
              let itemResult = complete * weight;
              record['itemResult'] = itemResult;
              return itemResult;
            },
            customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let cssClass = field.computeCssClasses?.(ctx, dRecord) || '';
              let complete = calculateKpiItemComplete(record) / 100;
              let weight = record['contributionWeight'] || 0;
              let itemResult = complete * weight;
              record['itemResult'] = itemResult;
              return (
                <div className={`flex-hbox justify-content-end ${cssClass}`}>
                  <input.BBPercentField bean={record} field={field.name} disable className={cssClass} />
                </div>
              );
            },
          },
          {
            name: 'calculateAlgorithm', label: T('Cách tính kết quả'), width: 190,
            computeCssClasses: computeCssClasses,
            fieldDataGetter: (record: any) => {
              let grade: KpiCalculateAlgorithm = record['calculateAlgorithm'];
              if (grade == KpiCalculateAlgorithm.FAILURE_BASED) return T('KPIs ngược (càng bé càng tốt)');
              if (grade == KpiCalculateAlgorithm.RATIO_BASED) return T('KPIs xuôi (càng lớn càng tốt)');
              return '-';
            },
          },
          {
            name: 'description', label: T('Mô tả'), width: 300, dataTooltip: true,
            computeCssClasses: computeCssClasses,
          },
          ...entity.DbEntityListConfigTool.FIELDS(!pageContext.hasUserAdminCapability(), [
            ...entity.DbEntityListConfigTool.FIELD_ENTITY,
          ])
        ],
        fieldGroups: {
          'info': {
            label: 'Thông tin chung',
            fields: [
              'employeeFullName', 'employeeCode', 'directlyManager', 'milestoneLabel', 'jobCode', 'jobTitle',
              'department', 'workplace', 'companyBranch', 'fromDate', 'toDate', 'status', 'kpiResult', 'grade', 'finalGrade',
            ]
          },
          'kpiItem': {
            label: T('Kpi Item'),
            fields: [
              'itemLabel', 'objectiveType', 'contributionWeight', 'unit', 'targetValue', 'actualValue',
              'complete', 'itemResult', 'employeeAdjustValue', 'employeeAdjustExplanation',
              'managerAdjustValue', 'managerAdjustExplanation', 'calculateAlgorithm', 'description'
            ]
          }
        },
      },
      toolbar: {
        actions: [
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_EXPORT_XLSX(!pageContext.hasUserModeratorCapability()),
        ],
        filters: entity.DbEntityListConfigTool.TOOLBAR_FILTERS(false),
      },
      view: {
        currentViewName: 'aggregation',
        availables: {
          table: {
            viewMode: 'table'
          },
          aggregation: {
            viewMode: 'aggregation', treeWidth: 250,
            createAggregationModel(_ctx: grid.VGridContext) {
              let model = new grid.AggregationDisplayModel(T('All'), false);
              model.addAggregation(new grid.ValueAggregation(T("Milestone"), "milestone", true)
                .withFieldGetter(record => record['milestoneLabel']));
              model.addAggregation(new grid.ValueAggregation(T("Department"), "department", true));
              model.addAggregation(new grid.ValueAggregation(T("Status"), "status", false)
                .withFieldGetter(record => getKpiStatusLabel(record['status'])));
              model.addAggregation(new grid.ValueAggregation(T("Employee"), "employeeAccountId", true)
                .withFieldGetter(record => record['employeeFullName']));
              model.addAggregation(new grid.ValueAggregation(T("Type"), "objectiveType", true)
                .withFieldGetter(record => record['objectiveType']));
              model.addAggregation(new grid.ValueAggregation(T("Manager"), "managerAccountId", false)
                .withFieldGetter(record => record['managerFullName']));
              model.addAggregation(new grid.ValueAggregation(T("Grade"), "grade", false)
                .withFieldGetter(record => record['finalGrade'] ? getKpiGradeLabel(record['finalGrade']) : getKpiGradeLabel(record['grade'])));
              model.addAggregation(new grid.ValueAggregation(T("Job Code"), "jobCode", false));
              return model;
            }
          }
        }
      },
    };
    return config;
  }
}