import React from 'react';
import * as FeatherIcon from 'react-feather';
import { app, bs, entity, grid, input, util } from '@datatp-ui/lib';

import { BBRefEmployee } from '../../company/hr';
import { DbEntityPluginButton, DbEntityPluginConfig } from '../../common/wfms';
import { KpiResult } from './KpiResult';
import { T } from './backend';
import { KpiObjectiveType, KpiStatus } from '../models';
import { DbEntityTaskForm } from 'module/common/wfms/task/DbEntityTask';
import { EntityTaskStatus } from 'module/common/wfms/task/backend';
import { EntityTaskApprovalStatus } from 'module/common/wfms/task/backend';
import { validateKpiItemList } from './utils';

const SESSION = app.host.DATATP_HOST.session;

export class KpiModel {
  kpi: any;
  workList: any[] = [];
  behaviorList: any[] = [];

  constructor(kpiModel: any) {
    this.kpi = kpiModel.kpi;
    this.workList = kpiModel.workList;
    this.behaviorList = kpiModel.behaviorList;
  }

  getKpi() { return this.kpi; }

  setKpi(kpi: any) { this.kpi = kpi; }

  getWorkList() { return this.workList; }
  setWorkList(workList: any[]) { this.workList = workList; }

  getBehaviorList() { return this.behaviorList; }
  setBehaviorList(behaviorList: any[]) { this.behaviorList = behaviorList; }

  getAllKpiItems() {
    return [...this.workList, ...this.behaviorList];
  }
}

class UIKpiForm extends entity.AppDbEntity {
  onInputChange = (bean: any, field: string, oldVal: any, newVal: any) => {
    let { observer, onModify } = this.props;
    if (onModify) onModify(bean, 'kpi', oldVal, observer.getMutableBean());
    else this.forceUpdate();
  }

  render() {
    let { appContext, pageContext, observer } = this.props;
    let writeCap = pageContext.canEditPage();
    let kpi = observer.getMutableBean();
    let statusOptions = [
      KpiStatus.Plan, KpiStatus.PlanReviewing, KpiStatus.Processing, KpiStatus.ResultReviewing,
      KpiStatus.LeaderApproved, KpiStatus.ManagerApproved, KpiStatus.DirectorApproved];
    let statusLabels = [
      'Lên kế hoạch', 'Review kế hoạch', 'Nhập kết quả', 'Review kết quả',
      'Leader đã duyệt', 'TBP đã duyệt', 'Giám đốc đã duyệt'];
    let html = (
      <div className="flex-vbox">
        <div className='text-center fw-bold fs-7 text-primary'>
          PHIẾU ĐÁNH GIÁ HIỆU QUẢ CÔNG VIỆC (KPI CÁ NHÂN) CỦA CHUYÊN VIÊN/NHÂN VIÊN/TRƯỞNG NHÓM
        </div>
        <div className='flex-hbox'>
          <div className='w-75 flex-vbox p-1'>
            <bs.Row>
              <bs.Col span={4}>
                <input.BBStringField bean={kpi} label='Họ và tên' field={'employeeFullName'} disable />
              </bs.Col>
              <bs.Col span={2}>
                <input.BBStringField bean={kpi} label='Chức vụ' field={'jobTitle'}
                  onInputChange={this.onInputChange} disable={!pageContext.hasUserModeratorCapability()} />
              </bs.Col>
              <bs.Col span={2}>
                <input.BBStringField bean={kpi} label='Phòng ban' field={'department'}
                  onInputChange={this.onInputChange} disable={!pageContext.hasUserModeratorCapability()} />
              </bs.Col>
              <bs.Col span={2}>
                <input.BBStringField bean={kpi} label='Milestone' field={'milestoneLabel'} disable />
              </bs.Col>
              <bs.Col span={2}>
                <input.BBSelectField bean={kpi} label='Status' field={'status'} disable={!pageContext.hasUserAdminCapability()}
                  options={statusOptions}
                  optionLabels={statusLabels}
                  onInputChange={this.onInputChange} />
              </bs.Col>
            </bs.Row>
            <bs.Row>
              <bs.Col span={4}>
                <div className='flex-hbox'>
                  <div className='w-50 me-2'>
                    <input.BBStringField bean={kpi} label='Mã nhân viên' field={'employeeCode'}
                      onInputChange={this.onInputChange} disable={!pageContext.hasUserModeratorCapability()} />
                  </div>
                  <div className='w-50'>
                    <input.BBStringField bean={kpi} label='Job Code' field={'jobCode'} disable />
                  </div>
                </div>
              </bs.Col>
              <bs.Col span={4}>
                <div className='flex-hbox'>
                  <div className='w-50 me-2'>
                    <input.BBStringField bean={kpi} label='Nơi làm việc' field={'workplace'}
                      onInputChange={this.onInputChange} disable={!pageContext.hasUserModeratorCapability()} />
                  </div>
                  <div className='w-50'>
                    <input.BBStringField bean={kpi} label='Chi nhánh' field={'companyBranch'}
                      onInputChange={this.onInputChange} disable={!pageContext.hasUserModeratorCapability()} />
                  </div>
                </div>
              </bs.Col>
              <bs.Col span={4}>
                <div className='flex-hbox'>
                  <div className='w-50 me-2'>
                    <BBRefEmployee label='Leader'
                      appContext={appContext} pageContext={pageContext} bean={kpi} disable={!pageContext.hasUserAdminCapability()}
                      beanIdField='leaderAccountId' beanLabelField='leaderFullName' selectedId='accountId'
                      placeholder='Input Leader'
                      onPostUpdate={(_inputUI, _bean, selOpt, _userInput) => {
                        kpi.leaderAccountId = selOpt.accountId;
                        kpi.leaderFullName = selOpt.label;
                        this.onInputChange(kpi, '', null, null);
                      }} />
                  </div>
                  <div className='w-50'>
                    <BBRefEmployee label='Manager'
                      appContext={appContext} pageContext={pageContext} bean={kpi} disable={!pageContext.hasUserAdminCapability()}
                      beanIdField='managerAccountId' beanLabelField='managerFullName' selectedId='accountId'
                      placeholder='Input Manager'
                      onPostUpdate={(_inputUI, _bean, selOpt, _userInput) => {
                        kpi.managerAccountId = selOpt.accountId;
                        kpi.managerFullName = selOpt.label;
                        this.onInputChange(kpi, '', null, null);
                      }} />
                  </div>
                </div>
              </bs.Col>
            </bs.Row>
          </div>
          <div className='w-25 flex-vbox p-1'>
            <input.BBTextField bean={kpi} label='Mô tả' field={'description'} disable={!writeCap}
              style={{ height: '6em' }} onInputChange={this.onInputChange} />
          </div>
        </div>
      </div>
    );
    return html;
  }
}

interface UIKpiModelProps extends app.AppComponentProps {
  kpiModel: KpiModel;
  space: 'user' | 'company' | 'system';
  onPostCommit?: entity.EntityOnPostCommit;
}
export class UIKpiModel extends app.AppComponent<UIKpiModelProps> {
  entityTaskConfig: DbEntityPluginConfig;
  constructor(props: UIKpiModelProps) {
    super(props);
    let { kpiModel } = this.props;

    this.entityTaskConfig = {
      entityType: 'kpi_kpi',
      entityLabel: 'Kpi',
      plugin: 'KpiTaskPlugin',
      newEntityTask: () => {
        return {
          'label': 'A New Task'
        };
      },
      getDbEntity: () => kpiModel.getKpi(),
      onModifyDbEntity: (dbEntity: any) => {
        kpiModel.setKpi(dbEntity);
        this.onPostCommit(dbEntity)
      }
    }
  }

  onRefresh = () => {
    let { appContext, kpiModel } = this.props;
    let kpi = kpiModel.getKpi();
    appContext.createHttpBackendCall('KpiService', 'getKpiModel', { kpiId: kpi.id })
      .withSuccessData((data: any) => {
        kpiModel.setKpi(data.kpi);
        kpiModel.setWorkList(data.workList);
        kpiModel.setBehaviorList(data.behaviorList);
        this.forceUpdateUI(true);
      })
      .call();
  }

  onPostCommit = (entity: any) => {
    let { onPostCommit } = this.props;
    if (onPostCommit) {
      onPostCommit(entity, this);
    } else {
      this.forceUpdate();
    }
  }

  onModify = (bean: any, field: string, oldVal: any, newVal: any) => {
    let { appContext, kpiModel } = this.props;
    if (field === 'kpi') kpiModel.setKpi(newVal)
    else if (field === 'workList') kpiModel.setWorkList(newVal)
    else if (field === 'behaviorList') kpiModel.setBehaviorList(newVal)
    let records = kpiModel.getAllKpiItems();

    let modifiedRecords: Array<any> = [];
    for (let i = 0; i < records.length; i++) {
      let record = records[i];
      let recordState = grid.getRecordState(record);
      if (recordState.isMarkDeleted()) {
        record.editState = 'DELETED';
        modifiedRecords.push(record);
      } else if (recordState.isMarkNew()) {
        record.editState = 'NEW';
        modifiedRecords.push(record);
      } else if (recordState.isMarkModified()) {
        record.editState = 'MODIFIED';
        modifiedRecords.push(record);
      }
    }

    appContext.createHttpBackendCall('KpiService', 'saveKpiModel', { model: kpiModel })
      .withSuccessData((data: any) => {
        kpiModel.setKpi(data.kpi);
        kpiModel.setWorkList(data.workList);
        kpiModel.setBehaviorList(data.behaviorList);
        this.onPostCommit(data);
        this.forceUpdate()
      })
      .withEntityOpNotification('commit', 'Kpi')
      .call();
  }

  onValidateItem = (): boolean => {
    let { kpiModel } = this.props;
    let records = kpiModel.getAllKpiItems();
    let workItemListValidated: boolean = validateKpiItemList(records, KpiObjectiveType.Work);
    if (!workItemListValidated) {
      return false;
    }
    let behaviorItemListValidated: boolean = validateKpiItemList(records, KpiObjectiveType.Behavior);
    if (!behaviorItemListValidated) {
      return false;
    }
    return true;
  }

  onSave = (callback?: (kpi: any) => void) => {
    let { appContext, kpiModel } = this.props;
    let records = kpiModel.getAllKpiItems();

    let modifiedRecords: Array<any> = [];
    for (let i = 0; i < records.length; i++) {
      let record = records[i];
      let recordState = grid.getRecordState(record);
      if (recordState.isMarkDeleted()) {
        record.editState = 'DELETED';
        modifiedRecords.push(record);
      } else if (recordState.isMarkNew()) {
        record.editState = 'NEW';
        modifiedRecords.push(record);
      } else if (recordState.isMarkModified()) {
        record.editState = 'MODIFIED';
        modifiedRecords.push(record);
      }
    }

    appContext.createHttpBackendCall('KpiService', 'saveKpiModel', { model: kpiModel })
      .withSuccessData((data: any) => {
        kpiModel.setKpi(data.kpi);
        kpiModel.setWorkList(data.workList);
        kpiModel.setBehaviorList(data.behaviorList);
        this.onPostCommit(data);
        this.forceUpdateUI(true);
        if (callback) callback(data.kpi);
      })
      .withEntityOpNotification('commit', 'Kpi')
      .call();
  }

  onSendApprovalRequest = (currentStatus: KpiStatus) => {
    let validateContributionWeight = this.onValidateItem();
    if (!validateContributionWeight) {
      bs.dialogShow('Tổng trọng số khác 100%',
        <div className="text-danger fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />
          {`Vui lòng kiểm tra lại trọng số}.`}
        </div>,
        { backdrop: 'static', size: 'sm' }
      );
      return;
    }
    this.onSave((kpi: any) => {
      let validated = this.onValidateItem();
      if (!validated) return;
      let { appContext, pageContext } = this.props;

      let taskObserver = new entity.BeanObserver({
        label: `Đánh giá ${currentStatus == KpiStatus.Plan ? 'kế hoạch' : 'kết quả'} KPI - ${kpi.employeeFullName}`,
        taskType: 'KPI',
        approvalStatus: EntityTaskApprovalStatus.Requested,
        status: EntityTaskStatus.Submitted,
        assigneeAccountId: kpi.employeeAccountId,
        assigneeFullName: kpi.employeeFullName,
        reporterAccountId: kpi.leaderAccountId ? kpi.leaderAccountId : kpi.managerAccountId,
        reporterFullName: kpi.leaderAccountId ? kpi.leaderFullName : kpi.managerFullName,
        taskRequest: '',
        dueDate: util.TimeUtil.javaCompactDateTimeFormat(new Date()),
        deadline: util.TimeUtil.javaCompactDateTimeFormat(new Date()),
      });

      let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
        kpi.status = currentStatus == KpiStatus.Plan ? KpiStatus.PlanReviewing : KpiStatus.ResultReviewing;

        let onSendRequest = () => {
          let params = {
            entity: kpi,
            task: taskObserver.getMutableBean(),
            status: EntityTaskStatus.Submitted
          }

          appContext
            .createHttpBackendCall('KpiTaskPlugin', 'handle', params)
            .withSuccessData(data => {
              pageCtx.back();
              this.onPostCommit(data);
              this.onRefresh();
            })
            .withSuccessNotification('success', 'Request Successfully!')
            .withFailNotification('danger', 'Request Failed!')
            .call();
        }
        return (
          <div className='flex-vbox'>
            <DbEntityTaskForm appContext={appContext} pageContext={pageContext} observer={taskObserver} />
            <bs.Toolbar>
              <bs.Button laf="success" onClick={() => onSendRequest()}>
                <FeatherIcon.Send size={12} /> Submit
              </bs.Button>
            </bs.Toolbar>
          </div>
        )
      }
      pageContext.createPopupPage("send-approval-request", T('Request'), createAppPage, { size: "md", backdrop: "static" })
    });
  }

  onApproveRequest = (newKpiStatus: KpiStatus) => {
    let validateContributionWeight = this.onValidateItem();
    if (!validateContributionWeight) {
      bs.dialogShow('Tổng trọng số khác 100%',
        <div className="text-danger fw-bold text-center py-3 border-bottom">
          <FeatherIcon.AlertCircle className="mx-2" />
          {`Vui lòng kiểm tra lại trọng số.`}
        </div>,
        { backdrop: 'static', size: 'sm' }
      );
      return;
    }
    this.onSave((kpi: any) => {
      let validated = this.onValidateItem();
      if (!validated) return;
      let { appContext, pageContext } = this.props;
      let currentStatus: KpiStatus = kpi.status;
      let taskObserver = new entity.BeanObserver({
        label: `Duyệt ${currentStatus == KpiStatus.PlanReviewing ? 'kế hoạch' : 'kết quả'} đánh giá KPI - ${kpi.employeeFullName} - ${kpi.milestoneLabel}`,
        taskType: 'KPI',
        approvalStatus: EntityTaskApprovalStatus.NA,
        status: EntityTaskStatus.Submitted,
        assigneeAccountId: SESSION.getAccountId() === kpi.leaderAccountId ? kpi.leaderAccountId : kpi.managerAccountId,
        assigneeFullName: SESSION.getAccountId() === kpi.leaderAccountId ? kpi.leaderFullName : kpi.managerFullName,
        reporterAccountId: kpi.employeeAccountId,
        reporterFullName: kpi.employeeFullName,
        taskRequest: '',
        dueDate: util.TimeUtil.javaCompactDateTimeFormat(new Date()),
        deadline: util.TimeUtil.javaCompactDateTimeFormat(new Date()),
      });

      if (currentStatus == KpiStatus.PlanReviewing && newKpiStatus == KpiStatus.Processing) {
        taskObserver.replaceBeanProperty('approvalStatus', EntityTaskApprovalStatus.Approved);
      } else if (currentStatus == KpiStatus.PlanReviewing && newKpiStatus == KpiStatus.Plan) {
        taskObserver.replaceBeanProperty('approvalStatus', EntityTaskApprovalStatus.Rejected);
      } else if (currentStatus == KpiStatus.ResultReviewing && newKpiStatus == KpiStatus.LeaderApproved) {
        taskObserver.replaceBeanProperty('approvalStatus', EntityTaskApprovalStatus.Approved);
      } else if (currentStatus == KpiStatus.ResultReviewing && newKpiStatus == KpiStatus.ManagerApproved) {
        taskObserver.replaceBeanProperty('approvalStatus', EntityTaskApprovalStatus.Approved);
      } else if (currentStatus == KpiStatus.ResultReviewing && newKpiStatus == KpiStatus.Processing) {
        taskObserver.replaceBeanProperty('approvalStatus', EntityTaskApprovalStatus.Rejected);
      }

      let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
        kpi.status = newKpiStatus;
        let onSendRequest = () => {
          let params = {
            entity: kpi,
            task: taskObserver.getMutableBean(),
            status: EntityTaskStatus.Submitted
          }

          appContext
            .createHttpBackendCall('KpiTaskPlugin', 'handle', params)
            .withSuccessData(data => {
              pageCtx.back();
              this.onPostCommit(data);
              this.onRefresh();
            })
            .withSuccessNotification('success', 'Request Successfully!')
            .withFailNotification('danger', 'Request Failed!')
            .call();
        }
        return (
          <div className='flex-vbox'>
            <DbEntityTaskForm appContext={appContext} pageContext={pageContext} observer={taskObserver} />
            <bs.Toolbar>
              <bs.Button laf="success" onClick={() => onSendRequest()}>
                <FeatherIcon.Send size={12} /> Submit
              </bs.Button>
            </bs.Toolbar>
          </div>
        )
      }
      pageContext.createPopupPage("send-approval-request", T('Request'), createAppPage, { size: "md", backdrop: "static" })
    });
  }

  renderToolBar = () => {
    let { appContext, pageContext, kpiModel, space, readOnly } = this.props;
    let writeCap = pageContext.hasUserAdminCapability() || pageContext.isPageOwner();
    let kpi = kpiModel.getKpi();
    let status: KpiStatus = kpi['status'] || KpiStatus.Plan;
    let statusButtons = null;
    if (space == 'user' && (status == KpiStatus.Plan || status == KpiStatus.Processing)) {
      statusButtons = (
        <bs.Button laf="success" onClick={() => this.onSendApprovalRequest(status)}>
          <FeatherIcon.Send size={12} /> Request Confirm
        </bs.Button>
      )
    } else if (space == 'company' && status == KpiStatus.PlanReviewing) {
      statusButtons = (
        <>
          <bs.Button laf="success" onClick={() => this.onApproveRequest(KpiStatus.Processing)}>
            <FeatherIcon.Send size={12} /> Approve
          </bs.Button>
          <bs.Button laf="danger" onClick={() => this.onApproveRequest(KpiStatus.Plan)}>
            <FeatherIcon.Send size={12} /> Reject
          </bs.Button>
        </>
      )
    } else if (space == 'company' && status == KpiStatus.ResultReviewing) {
      if (SESSION.getAccountId() == kpi.leaderAccountId) {
        statusButtons = (
          <>
            <bs.Button laf="success" onClick={() => this.onApproveRequest(KpiStatus.LeaderApproved)}>
              <FeatherIcon.Send size={12} /> Approve
            </bs.Button>
            <bs.Button laf="danger" onClick={() => this.onApproveRequest(KpiStatus.Processing)}>
              <FeatherIcon.Send size={12} /> Reject
            </bs.Button>
          </>
        )
      } else {
        statusButtons = (
          <>
            <bs.Button laf="success" onClick={() => this.onApproveRequest(KpiStatus.ManagerApproved)}>
              <FeatherIcon.Send size={12} /> Approve
            </bs.Button>
            <bs.Button laf="danger" onClick={() => this.onApproveRequest(KpiStatus.Processing)}>
              <FeatherIcon.Send size={12} /> Reject
            </bs.Button>
          </>
        )
      }
    } else if (space == 'company' && status == KpiStatus.LeaderApproved && (SESSION.getAccountId() == kpi.managerAccountId || pageContext.hasUserAdminCapability())) {
      statusButtons = (
        <>
          <bs.Button laf="success" onClick={() => this.onApproveRequest(KpiStatus.ManagerApproved)}>
            <FeatherIcon.Send size={12} /> Approve
          </bs.Button>
          <bs.Button laf="danger" onClick={() => this.onApproveRequest(KpiStatus.Processing)}>
            <FeatherIcon.Send size={12} /> Reject
          </bs.Button>
        </>
      )
    }
    return (
      <bs.Toolbar hide={!writeCap || readOnly}>
        {statusButtons}
        <entity.WButtonEntityWrite hide={!pageContext.hasUserAdminCapability()}
          appContext={appContext} pageContext={pageContext} label='Cập nhật' onClick={() => this.onSave()} />
        <DbEntityPluginButton appContext={appContext} pageContext={pageContext} config={this.entityTaskConfig} />
      </bs.Toolbar>
    );
  }

  checkPermission = () => {
    let { pageContext, kpiModel, space } = this.props;
    let kpi = kpiModel.getKpi();
    let editMode: entity.EntityEditMode = kpi['editMode'];
    let kpiStatus: KpiStatus = kpi['status'];
    if (space == 'user' && ![KpiStatus.Plan, KpiStatus.Processing].includes(kpiStatus)) {
      editMode = entity.EditMode.LOCKED;
    } else if (space == 'company' && ![KpiStatus.Plan, KpiStatus.PlanReviewing, KpiStatus.ResultReviewing, KpiStatus].includes(kpiStatus)) {
      editMode = entity.EditMode.LOCKED;
    }
    pageContext.setPageEditMode(editMode);

    if (SESSION.getAccountId() == kpi.employeeAccountId || (kpi.leaderAccountId ? SESSION.getAccountId() === kpi.leaderAccountId : SESSION.getAccountId() === kpi.managerAccountId)) {
      pageContext.setPageOwner(true);
    }
  }

  render() {
    let { appContext, pageContext, kpiModel, space } = this.props;
    this.checkPermission();
    let kpi = kpiModel.getKpi();
    let html = (
      <div className='flex-vbox'>
        <div className='flex-vbox'>
          <div className=' flex-vbox flex-grow-0 p-1'>
            <UIKpiForm key={`kpi-info-${this.componentId}`}
              appContext={appContext} pageContext={pageContext} observer={new entity.ComplexBeanObserver(kpi)}
              onModify={this.onModify} />
          </div>
          <div className='flex-vbox flex-grow-1'>
            <KpiResult key={`kpi-result-${this.componentId}`} kpiModel={kpiModel}
              appContext={appContext} pageContext={pageContext} space={space} onModify={this.onModify} />
          </div>
        </div>
        {this.renderToolBar()}
      </div>
    );
    return html;
  }
}