import React from 'react';
import { bs, entity, app } from '@datatp-ui/lib';

import { KpiObjectiveType, KpiStatus } from '../models';
import { KpiItemList, KpiItemListPlugin } from './KpiItemList';
import { calculateKpiItemComplete, getKpiGradeLabel, renderKpiNoted, validateKpiItemList } from './utils';
import { KpiModel } from './Kpi';

interface KpiResultProps extends app.AppComponentProps {
  kpiModel: KpiModel;
  space: 'user' | 'company' | 'system';
  onModify: (bean: any, field: string, oldVal: any, newVal: any) => void
}
export class KpiResult extends app.AppComponent<KpiResultProps> {

  onModifyItemList = (objType: KpiObjectiveType, records: any, _action: entity.ModifyBeanActions) => {
    if (!records || records.length == 0) return;
    let { kpiModel, onModify } = this.props;
    if (onModify) {
      if (objType == KpiObjectiveType.Work) {
        onModify(kpiModel, 'workList', kpiModel.getWorkList(), records);
      } else if (objType == KpiObjectiveType.Behavior) {
        onModify(kpiModel, 'behaviorList', kpiModel.getWorkList(), records);
      }
    }
    this.forceUpdate();
  }

  render() {
    let { appContext, pageContext, kpiModel, space } = this.props;
    let kpi = kpiModel.getKpi();
    let kpiStatus: KpiStatus = kpi['status'];
    let html = (
      <div className='flex-vbox'>
        <div className='flex-vbox border rounded'>
          <bs.ScrollableCards key={this.componentId} className='flex-vbox p-1'>
            <bs.Card className='flex-vbox flex-grow-0 mt-0 mb-1' header='Quy ước' collapse>
              {renderKpiNoted(space, 'kpi')}
            </bs.Card>
            <bs.Card className='flex-vbox mb-1' header='PHẦN 1: MỤC TIÊU CÔNG VIỆC'>
              <KpiItemList key={`work-${this.componentId}`} style={{ minHeight: 485 }} kpi={kpi}
                appContext={appContext} pageContext={pageContext} objectiveType={KpiObjectiveType.Work}
                plugin={new KpiItemListPlugin(kpiModel.getWorkList())} space={space}
                onModifyBean={(records: any, action: entity.ModifyBeanActions) => this.onModifyItemList(KpiObjectiveType.Work, records, action)} />
            </bs.Card>
            <bs.Card className='flex-vbox mb-1' header='PHẦN 2: THÁI ĐỘ - HÀNH VI'>
              <KpiItemList key={`behavior-${this.componentId}`} style={{ minHeight: 485 }} kpi={kpi}
                appContext={appContext} pageContext={pageContext} objectiveType={KpiObjectiveType.Behavior}
                plugin={new KpiItemListPlugin(kpiModel.getBehaviorList())} space={space}
                onModifyBean={(records: any, action: entity.ModifyBeanActions) => this.onModifyItemList(KpiObjectiveType.Behavior, records, action)} />
            </bs.Card>
          </bs.ScrollableCards>
          {(space == 'company' && ![KpiStatus.Plan, KpiStatus.PlanReviewing].includes(kpiStatus)) && (
            <bs.Card className='flex-vbox flex-grow-0 m-1 p-1' header='PHẦN 3: KẾT QUẢ ĐÁNH GIÁ' style={{ height: 140 }}>
              <KpiReport appContext={appContext} pageContext={pageContext} kpiModel={kpiModel} kpi={kpi} />
            </bs.Card>
          )}
          {(space == 'user' && KpiStatus.DirectorApproved == kpiStatus && kpi['finalGrade']) && (
            <bs.Card className='flex-vbox flex-grow-0 m-1 p-1 justify-item-end' header='PHẦN 3: KẾT QUẢ ĐÁNH GIÁ'>
              <div className='text-end fw-bold text-primary'>
                <div>
                  XẾP LOẠI FINAL
                </div>
                <div>
                  {getKpiGradeLabel(kpi['finalGrade'])}
                </div>
              </div>
            </bs.Card>
          )}

        </div>
      </div>
    );
    return html;
  }
}

export interface KpiReportProps extends app.AppComponentProps {
  kpi: any;
  kpiModel: KpiModel;
}

export class KpiReport extends app.AppComponent<KpiReportProps> {

  categoryConfig: bs.GridConfig = {
    showHeader: true,
    showBorder: true,
    header: {
      height: 40,
    },
    row: {
      height: 40,
    },
    columns: [
      { field: 'no', label: 'STT', width: 50, cssClass: 'fw-bold justify-content-center' },
      { field: 'category', label: 'HẠNG MỤC', width: 300, cssClass: 'fw-bold justify-content-start' },
      { field: 'weight', label: 'TRỌNG SỐ', width: 150, cssClass: 'fw-bold justify-content-center' },
      { field: 'result', label: 'KẾT QUẢ', width: 150, cssClass: 'fw-bold justify-content-center' },
      { field: 'finalResult', label: 'KẾT QUẢ CUỐI', width: 150, cssClass: 'fw-bold justify-content-center' },
    ]
  }

  resultConfig: bs.GridConfig = {
    showHeader: true,
    showBorder: true,
    header: {
      height: 40,
    },
    row: {
      height: 80,
    },
    columns: [
      { field: 'kpiResult', label: 'KPI', width: 200, cssClass: 'fw-bold text-primary justify-content-center align-items-center' },
      { field: 'grade', label: 'XẾP LOẠI', width: 200, cssClass: 'fw-bold text-primary justify-content-center align-items-center' },
      { field: 'finalResult', label: 'XẾP LOẠI FINAL', width: 200, cssClass: 'fw-bold text-primary justify-content-center align-items-center' },
    ]
  }

  calculateResult(items: any) {
    let result = 0;
    for (let item of items) {
      let weight = item['contributionWeight'] || 0;
      let complete = calculateKpiItemComplete(item)
      result += complete * weight;
    }
    return result;
  }

  calculateGrade = (kpi: number) => {
    if (kpi <= 70) {
      return 'Không đạt';
    } else if (kpi < 95) {
      return 'Cải thiện';
    } else if (kpi <= 115) {
      return 'Đạt';
    } else if (kpi < 135) {
      return 'Vượt mong đợi';
    } else if (kpi >= 135) {
      return 'Xuất sắc';
    }
    return '';
  }

  render() {
    let { kpiModel, kpi } = this.props;
    let workList = kpiModel.getWorkList();
    let behaviorList = kpiModel.getBehaviorList();

    let resultWork = this.calculateResult(workList);
    let resultBehavior = this.calculateResult(behaviorList);
    let resultWorkStr = resultWork ? `${resultWork.toFixed(2)}%` : '00,00%';
    let resultBehaviorStr = resultBehavior ? `${resultBehavior.toFixed(2)}%` : '00.00%';

    let finalResultWork = resultWork ? resultWork * 0.8 : 0;
    let finalResultBehavior = resultBehavior ? resultBehavior * 0.2 : 0;
    let finalResultWorkStr = finalResultWork ? `${finalResultWork.toFixed(2)}%` : '00,00%';
    let finalResultBehaviorStr = finalResultBehavior ? `${finalResultBehavior.toFixed(2)}%` : '00.00%';

    let categoryBeans = [
      { no: 1, category: 'PHẦN 1: MỤC TIÊU CÔNG VIỆC', weight: '80%', result: resultWorkStr, finalResult: finalResultWorkStr },
      { no: 2, category: 'PHẦN 2: THÁI ĐỘ - HÀNH VI', weight: '20%', result: resultBehaviorStr, finalResult: finalResultBehaviorStr },
    ];

    let kpiResult = finalResultWork + finalResultBehavior;
    let grade = this.calculateGrade(kpiResult);
    let resultBeans = [
      { kpiResult: `${kpiResult.toFixed(2)}%`, grade: grade, finalResult: getKpiGradeLabel(kpi?.finalGrade) },
    ];

    return (
      <div className='flex-hbox'>
        <bs.Grid config={this.categoryConfig} beans={categoryBeans} style={{ width: 700 }} />
        <bs.Grid config={this.resultConfig} beans={resultBeans} style={{ width: 200 }} />
      </div>
    );
  }
}