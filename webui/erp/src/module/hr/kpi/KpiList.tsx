import React from 'react';
import * as FeatherIcon from 'react-feather';
import { grid, sql, entity, util, bs, app } from '@datatp-ui/lib';

import { T } from './backend';
import { KpiGrade, KpiStatus } from '../models';
import { getKpiGradeLabel, getKpiStatusLabel } from './utils';
import { KpiModel, UIKpiModel } from './Kpi';
import { KpiRawDataList, KpiRawDataListPlugin } from './KpiRawDataList';

export class KpiListPlugin extends entity.DbEntityListPlugin {
  companyCode: string;
  employeeAccountId: number;
  managerAccountId: number;
  kpiTemplateId: number;

  constructor(space: 'user' | 'company' | 'system', kpiTemplateId?: number) {
    super([]);
    this.backend = {
      service: 'KpiService',
      searchMethod: 'searchKpis',
      changeStorageStateMethod: 'updateKpiStorageState'
    }

    this.searchParams = {
      params: {
        space: space,
        kpiTemplateId: kpiTemplateId,
      },
      filters: [
        ...sql.createSearchFilter()
      ],
      optionFilters: [
        sql.createStorageStateFilter([entity.StorageState.ACTIVE, entity.StorageState.ARCHIVED])
      ],
      rangeFilters: [
        ...sql.createCreatedTimeFilter(),
        ...sql.createModifiedTimeFilter()
      ],
      orderBy: {
        fields: ["modifiedTime"],
        fieldLabels: ["Modified Time"],
        selectFields: [],
        sort: "DESC"
      },
      maxReturn: 1000,
    }
    if (kpiTemplateId) this.kpiTemplateId = kpiTemplateId;
  }

  withCompany(companyCode: string) {
    if (companyCode) {
      this.addSearchParam("companyCode", companyCode);
      this.companyCode = companyCode;
    }
    return this;
  }

  loadData(uiList: entity.DbEntityList<any>) {
    let params = { params: this.searchParams }
    this.createBackendSearch(uiList, params).call();
  }
}

interface KpiListProps extends entity.DbEntityListProps {
  space: 'user' | 'company' | 'system';
  onAddEmployees?: (callback: () => void) => void;
}
export class KpiList extends entity.DbEntityList<KpiListProps> {
  createVGridConfig() {
    let { pageContext, appContext, type, space, onAddEmployees, plugin, readOnly } = this.props;
    let pluginImpl = plugin as KpiListPlugin;
    let computeCssClasses = (_ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
      let record = dRecord.record;
      let status: KpiStatus = record['status'];
      let cssClass = status == KpiStatus.DirectorApproved ? 'text-success' : 'text-secondary';
      return cssClass;
    }

    let onInputChange = (fieldCtx: grid.FieldContext, oldVal: any, newVal: any) => {
      let { gridContext, fieldConfig, displayRecord } = fieldCtx;
      let event: grid.VGridCellEvent = {
        row: displayRecord.row, field: fieldConfig, event: 'Modified', data: displayRecord
      }
      gridContext.broadcastCellEvent(event);
    };

    let config: grid.VGridConfig = {
      title: T("Kpi List"),
      record: {
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector()),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          {
            ...entity.DbEntityListConfigTool.FIELD_ON_SELECT('employeeFullName', T('Họ và tên'), 200),
            state: { showRecordState: true },
            fieldDataGetter: (record: any) => {
              let val = '-';
              if (record['employeeFullName']) val = record['employeeFullName'];
              return val.toLocaleUpperCase();
            }
          },
          {
            name: 'editMode', label: 'Edit Mode', width: 90, state: { visible: false },
            computeCssClasses: computeCssClasses,

          },
          {
            name: 'label', label: 'Kpi Name', width: 250, state: { visible: false },
            computeCssClasses: computeCssClasses,
            editor: { type: 'string', onInputChange: onInputChange }
          },
          {
            name: 'employeeCode', label: 'Mã nhân viên', width: 155,
            computeCssClasses: computeCssClasses,
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row === cell.getRow() && event.field.name === 'employeeFullName') {
                  cell.forceUpdate()
                }
              }
            }
          },
          {
            name: 'leaderAccountId', label: 'Leader', width: 180, filterable: true, filterableType: 'Options',
            computeCssClasses: computeCssClasses,
            fieldDataGetter: (record: any) => {
              let val = '-';
              if (record['leaderFullName']) val = record['leaderFullName'];
              return val.toLocaleUpperCase();
            }
          },
          {
            name: 'managerAccountId', label: 'Manager', width: 180, filterable: true, filterableType: 'Options',
            computeCssClasses: computeCssClasses,
            fieldDataGetter: (record: any) => {
              let val = '-';
              if (record['managerFullName']) val = record['managerFullName'];
              return val.toLocaleUpperCase();
            }
          },
          {
            name: 'jobCode', label: 'Job Code', width: 180, filterable: true, filterableType: 'Options',
            computeCssClasses: computeCssClasses,
          },
          {
            name: 'jobTitle', label: 'Chức vụ', width: 180, filterable: true, filterableType: 'Options',
            computeCssClasses: computeCssClasses,
          },
          {
            name: 'department', label: 'Phòng ban', width: 180, filterable: true, filterableType: 'Options',
            computeCssClasses: computeCssClasses,
          },
          {
            name: 'workplace', label: 'Nơi làm việc', filterable: true, filterableType: 'Options',
            computeCssClasses: computeCssClasses,
          },
          {
            name: 'companyBranch', label: 'Chi nhánh', filterable: true, filterableType: 'Options',
            computeCssClasses: computeCssClasses,
          },
          {
            name: 'milestoneLabel', label: 'Milestone', width: 100, filterable: true, filterableType: 'Options',
            computeCssClasses: computeCssClasses,
          },
          {
            name: 'fromDate', label: 'Từ ngày', format: util.text.formater.compactDate,
            computeCssClasses: computeCssClasses,
          },
          {
            name: 'toDate', label: 'Đến ngày', format: util.text.formater.compactDate,
            computeCssClasses: computeCssClasses,
          },
          {
            name: 'status', label: 'Status', width: 145, container: 'fixed-right', filterable: true, filterableType: 'Options',
            computeCssClasses: computeCssClasses,
            fieldDataGetter(record) {
              let status: KpiStatus = record['status'];
              return getKpiStatusLabel(status);
            },
          },
          ...entity.DbEntityListConfigTool.FIELD(space != 'company',
            {
              name: 'kpiResult', label: 'Kết quả', width: 100, format: util.text.formater.percent, container: 'fixed-right',
              computeCssClasses: (_ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
                let record = dRecord.record;
                let status: KpiStatus = record['status'];
                let cssClass = status == KpiStatus.DirectorApproved ? 'text-success cell-align-right' : 'text-secondary cell-align-right';
                return cssClass;
              },
              fieldDataGetter: (record: any) => {
                let status: KpiStatus = record['status'];
                if (space == 'user' && status != KpiStatus.DirectorApproved) {
                  return '-';
                } else {
                  if (![KpiStatus.DirectorApproved, KpiStatus.ManagerApproved].includes(status)) return '-';
                  return util.text.formater.percent(record['kpiResult']);
                }
              },
            }
          ),
          ...entity.DbEntityListConfigTool.FIELD(space != 'company',
            {
              name: 'grade', label: 'Xếp loại', width: 120, container: 'fixed-right',
              filterable: true, filterableType: 'Options',
              computeCssClasses: (_ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
                let record = dRecord.record;
                let status: KpiStatus = record['status'];
                let cssClass = status == KpiStatus.DirectorApproved ? 'fw-bold text-success' : 'fw-bold text-secondary';
                return cssClass;
              },
              fieldDataGetter: (record: any) => {
                let status: KpiStatus = record['status'];
                if (space == 'user' && status != KpiStatus.DirectorApproved) {
                  return '-';
                } else {
                  if (![KpiStatus.DirectorApproved, KpiStatus.ManagerApproved].includes(status)) return '-';
                  let grade: KpiGrade = record['grade'];
                  return getKpiGradeLabel(grade);
                }
              },
            }
          ),
          {
            name: 'finalGrade', label: 'Xếp loại Final', width: 140, container: 'fixed-right',
            filterable: true, filterableType: 'Options',
            computeCssClasses: (_ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
              let record = dRecord.record;
              if (record['finalGrade'] && record['finalGrade'] != record['grade']) {
                return 'fw-bold text-primary';
              }
              let status: KpiStatus = record['status'];
              let cssClass = status == KpiStatus.DirectorApproved ? 'fw-bold text-success' : 'fw-bold text-secondary';
              return cssClass;
            },
            fieldDataGetter: (record: any) => {
              let status: KpiStatus = record['status'];
              if (space == 'user' && status != KpiStatus.DirectorApproved) {
                return '-';
              } else {
                if (![KpiStatus.DirectorApproved, KpiStatus.ManagerApproved].includes(status)) return '-';
                let grade: KpiGrade = record['finalGrade'];
                return getKpiGradeLabel(grade);
              }
            },
          },
          {
            name: 'description', label: 'Description', dataTooltip: true,
            computeCssClasses: computeCssClasses,
          },
          ...entity.DbEntityListConfigTool.FIELDS(!pageContext.hasUserAdminCapability(), [
            ...entity.DbEntityListConfigTool.FIELD_ENTITY,
          ])
        ],
        editor: {
          supportViewMode: ['table'],
          enable: false
        }
      },

      toolbar: {
        actions: readOnly ? [] : [
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION(!pageContext.hasUserAdminCapability(),
            {
              name: 'view-raw-data', label: T('View Raw Data'), icon: FeatherIcon.Eye,
              onClick: (ctx: grid.VGridContext) => {
                let uiRoot = ctx.uiRoot as KpiList;
                uiRoot.viewRawData(ctx);
              }
            },
          ),
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_EXPORT_XLSX(!pageContext.hasUserModeratorCapability()),
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION(
            type == 'selector' || space != 'company' || !pageContext.hasUserModeratorCapability() || pageContext.userDataScope == app.AppDataScope.OWNER,
            {
              name: 'update-grade', label: T('Update Grade'), icon: FeatherIcon.Layers,
              createComponent(ctx) {
                let uiRoot = ctx.uiRoot as KpiList;
                return (
                  <bs.Popover className='flex-hbox-grow-0' closeOnTrigger=".btn">
                    <bs.PopoverToggle laf='primary' outline>
                      <FeatherIcon.Layers size={12} />{T('Update Grade')}
                    </bs.PopoverToggle>
                    <bs.PopoverContent>
                      <div className='flex-vbox' style={{ width: 180 }}>
                        <bs.Button laf='primary' className='m-1'
                          onClick={() => uiRoot.onUpdateGrade(KpiGrade.Failed)}> {T("Không đạt")} </bs.Button>
                        <bs.Button laf='primary' className='m-1'
                          onClick={() => uiRoot.onUpdateGrade(KpiGrade.NeedImprovement)}> {T("Cải thiện")} </bs.Button>
                        <bs.Button laf='primary' className='m-1'
                          onClick={() => uiRoot.onUpdateGrade(KpiGrade.Passed)}> {T("Đạt")} </bs.Button>
                        <bs.Button laf='primary' className='m-1'
                          onClick={() => uiRoot.onUpdateGrade(KpiGrade.ExceedExpectations)}> {T("Vượt mong đợi")} </bs.Button>
                        <bs.Button laf='primary' className='m-1'
                          onClick={() => uiRoot.onUpdateGrade(KpiGrade.Outstanding)}> {T("Xuất sắc")} </bs.Button>
                      </div>
                    </bs.PopoverContent>
                  </bs.Popover>
                )
              },
            },
          ),
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_STORAGE_STATES(
            [entity.StorageState.ACTIVE, entity.StorageState.ARCHIVED],
            !pageContext.hasUserModeratorCapability() || type == 'selector'
          ),
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_DELETE(!pageContext.hasUserModeratorCapability() || type == 'selector', T('Delete'))
        ],
        filters: entity.DbEntityListConfigTool.TOOLBAR_FILTERS(true),
        filterActions: [
          ...entity.DbEntityListConfigTool.TOOLBAR_AUTO_REFRESH('auto-refresh-kpi', T('Refresh')),
        ],
      },
      footer: {
        page: {
          render: (ctx: grid.VGridContext) => {
            let hide = type == 'selector' || space != 'company' || !!pluginImpl.kpiTemplateId ||
              !pageContext.hasUserModeratorCapability() || pageContext.userDataScope == app.AppDataScope.OWNER;
            let onClickAddEmployee = () => {
              if (onAddEmployees) {
                let callback = () => {
                  let thisUI = ctx.uiRoot as KpiList;
                  thisUI.reloadData();
                }
                onAddEmployees(callback);
              }
            }
            return (
              <bs.Toolbar className='border' hide={readOnly} >
                <entity.WButtonEntityWrite hide={!onAddEmployees}
                  appContext={appContext} pageContext={pageContext} label='Add Employee' onClick={onClickAddEmployee} />
                <entity.WButtonEntityWrite hide={hide}
                  appContext={appContext} pageContext={pageContext}
                  icon={FeatherIcon.Check} label={T('Approve')} onClick={this.onApprove} />
              </bs.Toolbar>
            )
          }
        },
        selector: entity.DbEntityListConfigTool.FOOTER_MULTI_SELECT(T("Select"), type)
      },
      view: {
        currentViewName: 'aggregation',
        availables: {
          table: {
            viewMode: 'table'
          },
          aggregation: {
            viewMode: 'aggregation', treeWidth: 250,
            createAggregationModel(_ctx: grid.VGridContext) {
              let model = new grid.AggregationDisplayModel(T('All'), false);
              model.addAggregation(new grid.ValueAggregation(T("Milestone"), "milestone", true)
                .withFieldGetter(record => record['milestoneLabel']));
              model.addAggregation(new grid.ValueAggregation(T("Department"), "department", true));
              model.addAggregation(new grid.ValueAggregation(T("Status"), "status", true)
                .withFieldGetter(record => getKpiStatusLabel(record['status'])));
              model.addAggregation(new grid.ValueAggregation(T("Manager"), "managerAccountId", false)
                .withFieldGetter(record => record['managerFullName']));
              model.addAggregation(new grid.ValueAggregation(T("Leader"), "leaderAccountId", false)
                .withFieldGetter(record => record['leaderFullName']));
              model.addAggregation(new grid.ValueAggregation(T("Grade"), "grade", false)
                .withFieldGetter(record => record['finalGrade'] ? getKpiGradeLabel(record['finalGrade']) : getKpiGradeLabel(record['grade'])));
              model.addAggregation(new grid.ValueAggregation(T("Job Code"), "jobCode", false));
              return model;
            }
          }
        }
      },
    };
    return config;
  }

  viewRawData = (_ctx: grid.VGridContext) => {
    let { pageContext, plugin, space } = this.props;
    let kpiListPlugin = plugin as KpiListPlugin;
    let kpiRawDataListPlugin = new KpiRawDataListPlugin(space)
      .withCompany(kpiListPlugin.companyCode);
    let createPageContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return <KpiRawDataList appContext={appCtx} pageContext={pageCtx}
        plugin={kpiRawDataListPlugin} />
    }
    pageContext.addPageContent('kpi-raw-data', T('KPI Raw Data'), createPageContent);
  }

  onUpdateGrade = (grade: KpiGrade) => {
    let { appContext } = this.props;
    let kpis = this.vgridContext.model.getSelectedRecords();
    for (let kpi of kpis) {
      if (kpi.status != KpiStatus.ManagerApproved) {
        bs.dialogShow('Không thể Update Xếp loại KPI này',
          <div className="text-danger fw-bold text-center py-3 border-bottom">
            <FeatherIcon.AlertCircle className="mx-2" />
            {`KPIs phải được TBP duyệt!.`}
          </div>,
          { backdrop: 'static', size: 'sm' }
        );
        return;
      }
    }
    let ids = this.vgridContext.model.getSelectedRecordIds();
    appContext.createHttpBackendCall('KpiService', 'updateKpiFinalGrade', { ids: ids, newGrade: grade })
      .withSuccessData((_data: any) => {
        appContext.addOSNotification("success", T(`Update Success`));
        this.reloadData();
      })
      .call();
  }

  onApprove = () => {
    let kpis = this.vgridContext.model.getSelectedRecords();
    let ids: any[] = [];
    for (let kpi of kpis) {
      if (kpi.status != KpiStatus.ManagerApproved) {
        bs.dialogShow('Không thể Approve KPI này',
          <div className="text-danger fw-bold text-center py-3 border-bottom">
            <FeatherIcon.AlertCircle className="mx-2" />
            {`KPIs phải được TBP duyệt!.`}
          </div>,
          { backdrop: 'static', size: 'sm' }
        );
        return;
      }
      ids.push(kpi.id);
    }

    if (ids.length === 0) {
      bs.notificationShow("warning", T("Chưa có KPIs được chọn!"));
      return;
    }

    const onConfirm = () => {
      let { appContext } = this.props;
      appContext.createHttpBackendCall('KpiService', 'approveKpiByManager', { ids: ids })
        .withSuccessData((_data: any) => {
          appContext.addOSNotification("success", T(`Approved Success`));
          this.reloadData();
        })
        .call();
    }
    let messageEle = (<div className="text-warning">Bạn muốn Approve KPIs này?</div>)
    bs.dialogConfirmMessage(T("Approve KPIs"), messageEle, onConfirm);
  }

  onDeleteAction(): void {
    let { appContext } = this.props;
    let kpis = this.vgridContext.model.getSelectedRecords();
    let ids: any[] = [];
    for (let kpi of kpis) {
      if (kpi.status != KpiStatus.Plan) {
        bs.dialogShow('Không thể xoá KPIs này',
          <div className="text-danger fw-bold text-center py-3 border-bottom">
            <FeatherIcon.AlertCircle className="mx-2" />
            {`Chỉ được xoá những KPI trong trạng thái Lập Kế Hoạch!.`}
          </div>,
          { backdrop: 'static', size: 'sm' }
        );
        return;
      }
      ids.push(kpi.id);
    }

    if (ids.length === 0) {
      bs.notificationShow("warning", T("Chưa có KPIs được chọn!"));
      return;
    }
    const onConfirmDelete = () => {
      appContext.createHttpBackendCall('KpiService', 'deleteKpis', { ids: ids })
        .withSuccessData((_data: any) => {
          this.reloadData();
        })
        .withEntityOpNotification('delete', T('KPI'), ids.length)
        .call();
    }

    let messageEle = (<div className="text-warning">Would you like to delete {ids.length} records?</div>)
    bs.dialogConfirmMessage(T("Confirm Delete"), messageEle, onConfirmDelete);
  }

  onDefaultSelect(dRecord: grid.DisplayRecord) {
    let rec = dRecord.record;
    let { appContext, pageContext, space, readOnly } = this.props;
    appContext.createHttpBackendCall('KpiService', 'getKpiModel', { kpiId: rec.id })
      .withSuccessData((data: any) => {
        let kpiModel: KpiModel = new KpiModel(data);
        let createPageContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return (
            <UIKpiModel appContext={appCtx} pageContext={pageCtx} space={space}
              kpiModel={kpiModel} readOnly={readOnly}
              onPostCommit={() => { this.reloadData() }} />
          )
        }
        pageContext.addPageContent('kpi', rec.label, createPageContent);
      })
      .call();
  }
}
