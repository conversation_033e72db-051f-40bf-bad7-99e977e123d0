import React from "react";
import { grid, entity, bs, input, util } from '@datatp-ui/lib';

import { BBRefUnit } from '../../settings';
import { KpiCalculateAlgorithm, KpiObjectiveType } from '../models';
import { T } from './backend';

export class KpiTemplateItemListPlugin extends entity.DbEntityListPlugin {
  constructor(items: any[]) {
    super(items);
  }

  loadData(uiList: entity.DbEntityList<any>) {
    uiList.markLoading(false);
    uiList.forceUpdate();
  }
}

interface KpiTemplateItemListProps extends entity.DbEntityListProps {
  kpiTemplate: any;
  objectiveType: KpiObjectiveType.Work | KpiObjectiveType.Behavior;
}
export class KpiTemplateItemList extends entity.DbEntityList<KpiTemplateItemListProps> {
  createVGridConfig() {
    let { appContext, pageContext, objectiveType } = this.props;
    let moderatorCap = objectiveType != KpiObjectiveType.Behavior && pageContext.hasUserModeratorCapability();
    let onInputChange = (fieldCtx: grid.FieldContext, oldVal: any, newVal: any) => {
      let { gridContext, fieldConfig, displayRecord } = fieldCtx;
      let event: grid.VGridCellEvent = {
        row: displayRecord.row, field: fieldConfig, event: 'Modified', data: displayRecord
      }
      gridContext.broadcastCellEvent(event);
      this.onModify();
    };

    let config: grid.VGridConfig = {
      record: {
        editor: {
          supportViewMode: moderatorCap ? ['table', 'aggregation'] : [],
          enable: true
        },
        dataCellHeight: 68,
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector() && objectiveType !== KpiObjectiveType.Behavior),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          {
            name: 'label', label: T('Mục tiêu đánh giá'), width: 400, state: { showRecordState: true },
            customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let height = ctx.config.record.dataCellHeight;
              let disable = !moderatorCap || record['objectiveType'] == KpiObjectiveType.Behavior ? true : false;
              return (
                <input.BBTextField bean={record} field={field.name} disable={disable}
                  onInputChange={onInputChange} style={{ height: height }} />
              );
            },
            editor: {
              type: 'text', enable: moderatorCap,
              onInputChange: onInputChange,
              renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
                const { fieldConfig, displayRecord, gridContext } = ctx;
                let record = displayRecord.record;
                let height = gridContext.config.record.dataCellHeight;
                let disable = !moderatorCap || record['objectiveType'] == KpiObjectiveType.Behavior ? true : false;
                return (
                  <input.BBTextField bean={record} field={fieldConfig.name} disable={disable}
                    onInputChange={onInputChange} style={{ height: height }} />
                )
              },
            }
          },
          {
            name: 'objectiveType', label: 'Thể loại', width: 130, state: { visible: false },
          },
          { name: 'jobCode', label: 'Job Code', state: { visible: false } },
          {
            name: 'contributionWeight', label: 'Trọng số', format: util.text.formater.percent,
            customHeaderRender: (ctx: grid.VGridContext, field: grid.FieldConfig, _headerEle: any) => {
              if (objectiveType == KpiObjectiveType.Behavior) {
                return (
                  <div className={`flex-hbox justify-content-center`}>Trọng số</div>
                );
              }
              let weight: number = 0;
              for (let rec of ctx.model.getRecords()) {
                if (rec['objectiveType'] == KpiObjectiveType.Work) weight += rec['contributionWeight'] || 0;
              }
              weight = Math.round(weight * 100);
              let cssClass = weight != 100 ? 'text-danger' : 'text-success';
              return (
                <div className={`flex-hbox justify-content-center`}>
                  Trọng số<span className={`fw-bold ms-1 ${cssClass}`}>({weight}%)</span>
                </div>
              );
            },
            editor: {
              type: 'percent', enable: moderatorCap,
              onInputChange: onInputChange,
              renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
                const { fieldConfig, displayRecord, tabIndex, focus } = ctx;
                let record = displayRecord.record;
                let disable = !moderatorCap || record['objectiveType'] == KpiObjectiveType.Behavior ? true : false;
                return (
                  <input.BBPercentField tabIndex={tabIndex} focus={focus} bean={record} field={fieldConfig.name}
                    disable={disable} onInputChange={onInputChange} />
                )
              },
            }
          },
          {
            name: 'unit', label: 'ĐVT', width: 120,
            editor: {
              type: 'String', enable: moderatorCap,
              renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
                const { displayRecord, tabIndex } = ctx;
                let record = displayRecord.record;
                let disable = !moderatorCap || record['objectiveType'] == KpiObjectiveType.Behavior ? true : false;
                return (
                  <BBRefUnit
                    appContext={appContext} pageContext={pageContext} tabIndex={tabIndex}
                    bean={record} beanIdField='unit' placeholder='Unit'
                    onPostUpdate={(_inputUI, _bean, selectOpt, _userInput) =>
                      onInputChange(record, 'unit', null, selectOpt['name'])} disable={disable} />
                );
              },
              onInputChange: onInputChange
            }
          },
          {
            name: 'targetValue', label: objectiveType == KpiObjectiveType.Work ? 'Kế hoạch' : 'Yêu cầu', width: 90,
            editor: {
              type: 'double', enable: moderatorCap,
              onInputChange: onInputChange,
              renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
                const { fieldConfig, displayRecord, tabIndex, focus } = ctx;
                let record = displayRecord.record;
                let disable = !moderatorCap || record['objectiveType'] == KpiObjectiveType.Behavior ? true : false;
                let maxPrecision = 2;
                let cssClass = '';
                if (objectiveType === KpiObjectiveType.Behavior) {
                  cssClass += ' text-center';
                  maxPrecision = 0;
                }
                if (['PERCENT', 'Percent', 'percent', '%'].includes(record['unit'])) {
                  return (
                    <input.BBPercentField tabIndex={tabIndex} focus={focus} bean={record} field={fieldConfig.name}
                      disable={disable} onInputChange={onInputChange} className={cssClass} />
                  );
                }
                return (
                  <input.BBNumberField className={cssClass}
                    tabIndex={tabIndex} focus={focus} bean={record} field={fieldConfig.name} maxPrecision={maxPrecision}
                    disable={disable} onInputChange={onInputChange} />
                );
              },
            }
          },
          {
            name: 'calculateAlgorithm', label: T('Cách tính kết quả'), width: 190,
            customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              return (
                <input.BBSelectField bean={record} field={field.name}
                  options={[KpiCalculateAlgorithm.RATIO_BASED, KpiCalculateAlgorithm.FAILURE_BASED]}
                  optionLabels={[T('KPIs xuôi (càng lớn càng tốt)'), T('KPIs ngược (càng bé càng tốt)')]}
                  onInputChange={onInputChange} disable />
              )
            },
            editor: {
              type: 'string', enable: moderatorCap,
              onInputChange: onInputChange,
              renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
                const { fieldConfig, displayRecord, tabIndex, focus } = ctx;
                let record = displayRecord.record;
                let disable = !moderatorCap || record['objectiveType'] == KpiObjectiveType.Behavior ? true : false;
                return (
                  <input.BBSelectField tabIndex={tabIndex} focus={focus} bean={record} field={fieldConfig.name}
                    options={[KpiCalculateAlgorithm.RATIO_BASED, KpiCalculateAlgorithm.FAILURE_BASED]}
                    optionLabels={[T('KPIs xuôi (càng lớn càng tốt)'), T('KPIs ngược (càng bé càng tốt)')]}
                    onInputChange={onInputChange} disable={disable} />
                )
              },
            }
          },
          {
            name: 'dataSource', label: T('Nguồn dữ liệu'),
            editor: { type: 'string', enable: moderatorCap, onInputChange: onInputChange }
          },
          {
            name: 'description', label: T('Mô tả'), width: 250,
            customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let height = ctx.config.record.dataCellHeight;
              return (
                <input.BBTextField bean={record} field={field.name} disable
                  onInputChange={onInputChange} style={{ height: height }} />
              );
            },
            editor: { type: 'text', enable: moderatorCap, onInputChange: onInputChange }
          },
          ...entity.DbEntityListConfigTool.FIELDS(!pageContext.hasUserAdminCapability(), [
            ...entity.DbEntityListConfigTool.FIELD_ENTITY,
          ])
        ],
      },

      toolbar: {
        actions: [
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_ADD(!moderatorCap, T('Add')),
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_DELETE(!moderatorCap, T('Del')),
        ],
        filters: entity.DbEntityListConfigTool.TOOLBAR_FILTERS(false)
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          },
        }
      },
      footer: {
      },
    };
    return config;
  }

  onModify = () => {
    const { onModifyBean } = this.props;
    let records = this.vgridContext.model.getRecords();
    if (onModifyBean) onModifyBean(records, entity.ModifyBeanActions.MODIFY);
  }

  onNewAction = () => {
    let { kpiTemplate, objectiveType } = this.props;
    let newRecord = { templateId: kpiTemplate.id, objectiveType: objectiveType, unit: 'UNIT' };
    grid.initRecordState(newRecord, 0).markNew();
    this.vgridContext.model.addRecord(newRecord);
    this.vgridContext.getVGrid().forceUpdateView();
    this.onModify();
  }

  onDeleteAction() {
    let { plugin, appContext } = this.props;
    let selectedIds = this.vgridContext.model.getSelectedRecordIds();
    if (selectedIds.length === 0) {
      bs.notificationShow("warning", T("No records were selected"));
      return;
    }

    appContext.createHttpBackendCall('KpiService', 'deleteKpiTemplateItems', { ids: selectedIds })
      .withSuccessData((_data: any) => {
        appContext.addOSNotification("success", T(`Delete Success`));
        plugin.getListModel().removeSelectedDisplayRecords();
        this.getVGridContext().getVGrid().forceUpdateView();
      })
      .call();
  }
}
