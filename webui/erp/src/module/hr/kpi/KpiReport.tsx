import React from 'react';
import { util, bs, app, input } from '@datatp-ui/lib';
import { K<PERSON><PERSON>ist, KpiListPlugin } from './KpiList';

const SESSION = app.host.DATATP_HOST.session;

export interface UIKPIReportState {
}
interface UIKPIReportProps extends app.AppComponentProps {
  space: 'user' | 'company' | 'system'
}
export class UIKPIReport extends app.AppComponent<UIKPIReportProps, UIKPIReportState> {
  reportParams: { companyCode: string };

  keyResultListView: React.RefObject<KpiList>;
  kpiListPlugin: KpiListPlugin;

  constructor(props: UIKPIReportProps) {
    super(props);
    this.kpiListPlugin = new KpiListPlugin(this.props.space);
    this.reportParams = { companyCode: SESSION.getAccountAcl().getCompanyAcl().companyCode }
    this.keyResultListView = React.createRef<KpiList>();
  }

  onSelectCompany = (_bean: any, _field: any, _oldVal: any, newVal: any) => {
    this.kpiListPlugin.withCompany(newVal);
    this.keyResultListView.current?.reloadData();
    this.setState({})
  }

  render() {
    let { appContext, pageContext, readOnly } = this.props;
    let isMobile: boolean = bs.ScreenUtil.isMobileScreen();
    let width: number = isMobile ? 130 : 130;
    return (
      <bs.VSplit updateOnResize={true} >
        <bs.VSplitPane width={width}>
          <div className='flex-vbox  align-items-center' key={util.IDTracker.next()} >
            <div className='m-1 p-1'>
              <div className={`text-muted my-1 fw-bold`} style={{ fontSize: '0.875rem' }}>{`Company`}</div>
              <input.BBRadioInputField
                bean={this.reportParams} field={'companyCode'} className='flex-vbox'
                options={['bee', 'beehph', 'beehan', 'beehcm', 'beedad']}
                optionLabels={['CORP', 'HPH', 'HAN', 'HCM', 'DAD']} onInputChange={this.onSelectCompany} />
            </div>
          </div >
        </bs.VSplitPane>
        <bs.VSplitPane className='ms-0'>
          {this.kpiListPlugin ?
            <div className='flex-vbox border rounded'>
              <div className='flex-vbox'>
                <KpiList key={util.IDTracker.next()}
                  appContext={appContext} pageContext={pageContext} plugin={this.kpiListPlugin} readOnly={readOnly}
                  space={'company'} />
              </div>
            </div>
            : <></>
          }
        </bs.VSplitPane>
      </bs.VSplit>
    )
  }
}