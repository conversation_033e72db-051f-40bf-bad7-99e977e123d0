import React from 'react';
import { grid, entity, input, bs } from '@datatp-ui/lib';

import { T } from './backend';
import { BBRefUnit } from '../../settings';
import { KpiCalculateAlgorithm, KpiObjectiveType, KpiStatus } from '../models';
import { BehaviorPointValidator, calculateKpiItemComplete } from './utils';
import { WPopupAttachments } from '../../../module/storage';

export class KpiItemListPlugin extends entity.DbEntityListPlugin {
  constructor(items: any[]) {
    super(items);
  }

  loadData(uiList: entity.DbEntityList<any>) {
    uiList.markLoading(false);
    uiList.forceUpdate();
  }
}

export interface KpiItemListProps extends entity.DbEntityListProps {
  kpi: any;
  objectiveType: KpiObjectiveType.Work | KpiObjectiveType.Behavior;
  space: 'user' | 'company' | 'system';
}

export class KpiItemList extends entity.DbEntityList<KpiItemListProps> {
  createVGridConfig() {
    let { appContext, pageContext, objectiveType, kpi, space } = this.props;
    let writeCap = pageContext.canEditPage();
    let kpiStatus: KpiStatus = kpi['status'];
    let actionsCap = space == 'company' && writeCap && objectiveType == KpiObjectiveType.Work && kpiStatus == KpiStatus.Plan;
    let onInputChange = (fieldCtx: grid.FieldContext, _oldVal: any, _newVal: any) => {
      let { gridContext, fieldConfig, displayRecord } = fieldCtx;
      let event: grid.VGridCellEvent = {
        row: displayRecord.row, field: fieldConfig, event: 'Modified', data: displayRecord
      }
      gridContext.broadcastCellEvent(event);
      this.onModify();
    };

    let computeCssClasses = (_ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
      let record = dRecord.record;
      let cssClass = record['kpiTemplateItemId'] ? 'text-secondary' : 'text-info';
      return cssClass;
    }

    let disablePlanFields = (record: any) => {
      return !writeCap || ![KpiStatus.Plan, KpiStatus.PlanReviewing].includes(kpiStatus);
    }

    let config: grid.VGridConfig = {
      title: T("Kpi Items"),
      record: {
        editor: {
          supportViewMode: writeCap ? ['table', 'aggregation'] : [],
          enable: true
        },
        dataCellHeight: 68,
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector() && objectiveType !== KpiObjectiveType.Behavior),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          {
            name: 'label', label: T('Mục tiêu đánh giá'), width: 450, state: { showRecordState: true }, container: 'fixed-left',
            computeCssClasses: computeCssClasses,
            customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let height = ctx.config.record.dataCellHeight;
              let cssClass = field.computeCssClasses?.(ctx, dRecord) || '';
              return (
                <input.BBTextField bean={record} field={field.name} disable
                  onInputChange={onInputChange} style={{ height: height }} className={`${cssClass} fw-bold`} />
              );
            },
            editor: {
              type: 'string', enable: writeCap,
              onInputChange: onInputChange,
              renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
                const { fieldConfig, displayRecord } = ctx;
                let record = displayRecord.record;
                let disable = disablePlanFields(record) || record['kpiTemplateItemId'];
                let height = ctx.gridContext.config.record.dataCellHeight;
                let cssClass = fieldConfig?.computeCssClasses?.(ctx.gridContext, displayRecord) || '';
                return (
                  <input.BBTextField bean={record} field={fieldConfig.name}
                    disable={disable} onInputChange={onInputChange} style={{ height: height }} className={`${cssClass} fw-bold`} />
                )
              },
            },
            listener: {
              onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                if (event.row === cell.getRow()) {
                  cell.forceUpdate()
                }
              }
            },
          },
          {
            name: 'objectiveType', label: 'Thể loại', width: 90, state: { visible: false }, container: 'fixed-left',
            computeCssClasses: computeCssClasses
          },
          {
            name: 'contributionWeight', label: 'Trọng số', width: 120, container: 'fixed-left',
            computeCssClasses: computeCssClasses,
            customHeaderRender: (ctx: grid.VGridContext, field: grid.FieldConfig, _headerEle: any) => {
              let weight: number = 0;
              for (let rec of ctx.model.getRecords()) {
                weight += rec['contributionWeight'] || 0;
              }
              weight = Math.round(weight * 100);
              let cssClass = weight != 100 ? 'text-danger' : 'text-success';
              return (
                <div className={`flex-hbox justify-content-center`}>
                  Trọng số<span className={`fw-bold ms-1 ${cssClass}`}>({weight}%)</span>
                </div>
              );
            },
            customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let cssClass = field.computeCssClasses?.(ctx, dRecord) || '';
              return (
                <input.BBPercentField bean={dRecord.record} field={field.name} disable className={cssClass} />
              );
            },
            editor: {
              type: 'percent', enable: writeCap,
              onInputChange: onInputChange,
              renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
                const { fieldConfig, displayRecord, tabIndex, focus } = ctx;
                let record = displayRecord.record;
                let disable = disablePlanFields(record) || space == 'user';
                let cssClass = fieldConfig?.computeCssClasses?.(ctx.gridContext, displayRecord) || '';
                return (
                  <input.BBPercentField tabIndex={tabIndex} focus={focus} bean={record} field={fieldConfig.name}
                    disable={disable} onInputChange={onInputChange} className={cssClass} />
                );
              },
            }
          },
          {
            name: 'unit', label: 'ĐVT', width: 120, container: 'fixed-left',
            computeCssClasses: computeCssClasses,
            editor: {
              type: 'string', enable: writeCap,
              onInputChange: onInputChange,
              renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
                const { fieldConfig, displayRecord, tabIndex } = ctx;
                let record = displayRecord.record;
                let disable = disablePlanFields(record) || record['kpiTemplateItemId'];
                let cssClass = fieldConfig?.computeCssClasses?.(ctx.gridContext, displayRecord) || '';
                return (
                  <BBRefUnit
                    appContext={appContext} pageContext={pageContext} tabIndex={tabIndex} disable={disable}
                    bean={record} beanIdField='unit' placeholder='Unit' className={cssClass}
                    onPostUpdate={(_inputUI, _bean, selectOpt, _userInput) =>
                      onInputChange(record, 'unit', null, selectOpt['name'])} />
                );;
              },
            },
          },
          {
            name: 'targetValue', label: objectiveType == KpiObjectiveType.Work ? 'Kế hoạch' : 'Điểm chuẩn', width: 90, container: 'fixed-left',
            computeCssClasses: computeCssClasses,
            editor: {
              type: 'double', enable: writeCap,
              onInputChange: onInputChange,
              renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
                const { fieldConfig, displayRecord, tabIndex, focus } = ctx;
                let record = displayRecord.record;
                let disable = disablePlanFields(record) || (space == 'user' && record['kpiTemplateItemId']);
                let cssClass = fieldConfig?.computeCssClasses?.(ctx.gridContext, displayRecord) || '';
                let maxPrecision = 2;
                let validators = []
                if (objectiveType === KpiObjectiveType.Behavior) {
                  cssClass += ' text-center';
                  maxPrecision = 0;
                  validators.push(new BehaviorPointValidator())
                }
                if (['PERCENT', 'Percent', 'percent', '%'].includes(record['unit'])) {
                  return (
                    <input.BBPercentField tabIndex={tabIndex} focus={focus} bean={record} field={fieldConfig.name}
                      disable={disable} onInputChange={onInputChange} className={cssClass} />
                  );
                }
                return (
                  <input.BBNumberField className={cssClass}
                    tabIndex={tabIndex} focus={focus} bean={record} field={fieldConfig.name} maxPrecision={maxPrecision}
                    disable={disable} onInputChange={onInputChange} validators={validators} />
                );
              },
            }
          },
          ...entity.DbEntityListConfigTool.FIELDS([KpiStatus.Plan, KpiStatus.PlanReviewing].includes(kpiStatus), [
            {
              name: 'actualValue', label: objectiveType == KpiObjectiveType.Work ? 'Thực hiện' : 'Điểm đánh giá',
              width: objectiveType == KpiObjectiveType.Work ? 90 : 120, container: 'fixed-left',
              computeCssClasses: computeCssClasses,
              editor: {
                type: 'double', enable: writeCap,
                onInputChange: onInputChange,
                renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
                  const { fieldConfig, displayRecord, tabIndex, focus } = ctx;
                  let record = displayRecord.record;
                  let disable = !writeCap || space !== 'user';
                  let cssClass = fieldConfig?.computeCssClasses?.(ctx.gridContext, displayRecord) || '';
                  let maxPrecision = 2;
                  let validators = []
                  if (objectiveType === KpiObjectiveType.Behavior) {
                    cssClass += ' text-center';
                    maxPrecision = 0;
                    validators.push(new BehaviorPointValidator())
                  }
                  if (['PERCENT', 'Percent', 'percent', '%'].includes(record['unit'])) {
                    return (
                      <input.BBPercentField tabIndex={tabIndex} focus={focus} bean={record} field={fieldConfig.name}
                        disable={disable} onInputChange={onInputChange} className={cssClass} />
                    );
                  }
                  return (
                    <input.BBNumberField className={cssClass}
                      tabIndex={tabIndex} focus={focus} bean={record} field={fieldConfig.name} maxPrecision={maxPrecision}
                      disable={disable} onInputChange={onInputChange} validators={validators} />
                  );
                },
              }
            },
            {
              name: 'complete', label: '(%) Hoàn thành', width: 120, container: 'fixed-right',
              computeCssClasses: computeCssClasses,
              fieldDataGetter: (record: any) => {
                let complete = calculateKpiItemComplete(record);
                return (
                  <div className='flex-hbox justify-content-end'>{`${complete.toFixed(2)}%`}</div>
                );
              },
              listener: {
                onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                  let fieldUpdate = ['targetValue', 'actualValue', 'employeeAdjustValue', 'managerAdjustValue'];
                  if (event.row === cell.getRow() && fieldUpdate.includes(event.field.name)) {
                    cell.forceUpdate()
                  }
                }
              }
            },
            {
              name: 'kpiResult', label: 'Kết quả KPI', width: 90, container: 'fixed-right',
              computeCssClasses: computeCssClasses,
              fieldDataGetter: (record: any) => {
                let complete = calculateKpiItemComplete(record);
                let weight = record['contributionWeight'] || 0;
                let kpiResult = complete * weight;
                return (
                  <div className='flex-hbox justify-content-end'>{`${kpiResult.toFixed(2)}%`}</div>
                );
              },
              listener: {
                onDataCellEvent: (cell: grid.VGridCell, event: grid.VGridCellEvent) => {
                  let fieldUpdate = ['targetValue', 'actualValue', 'employeeAdjustValue', 'managerAdjustValue'];
                  if (event.row === cell.getRow() && fieldUpdate.includes(event.field.name)) {
                    cell.forceUpdate()
                  }
                }
              }
            },

            ...entity.DbEntityListConfigTool.FIELDS(objectiveType == KpiObjectiveType.Behavior, [
              {
                name: 'employeeAdjustValue', label: 'Nhân viên', width: 90,
                computeCssClasses: computeCssClasses,
                editor: {
                  type: 'double', enable: writeCap,
                  onInputChange: onInputChange,
                  renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
                    const { fieldConfig, displayRecord, tabIndex, focus } = ctx;
                    let record = displayRecord.record;
                    let disable = !writeCap || space !== 'user';
                    let cssClass = fieldConfig?.computeCssClasses?.(ctx.gridContext, displayRecord) || '';
                    let maxPrecision = 2;
                    let validators = [];
                    if (objectiveType === KpiObjectiveType.Behavior) {
                      cssClass += ' text-center';
                      maxPrecision = 0;
                      validators.push(new BehaviorPointValidator())
                    }
                    if (['PERCENT', 'Percent', 'percent', '%'].includes(record['unit'])) {
                      return (
                        <input.BBPercentField tabIndex={tabIndex} focus={focus} bean={record} field={fieldConfig.name}
                          disable={disable} onInputChange={onInputChange} className={cssClass} />
                      );
                    }
                    return (
                      <input.BBNumberField className={cssClass}
                        tabIndex={tabIndex} focus={focus} bean={record} field={fieldConfig.name} maxPrecision={maxPrecision}
                        disable={disable} onInputChange={onInputChange} validators={validators} />
                    );
                  },
                }
              },
              {
                name: 'employeeAdjustExplanation', label: 'Lý do', width: 200,
                computeCssClasses: computeCssClasses,
                customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
                  let record = dRecord.record;
                  let height = ctx.config.record.dataCellHeight;
                  let cssClass = field.computeCssClasses?.(ctx, dRecord) || '';
                  return (
                    <input.BBTextField bean={record} field={field.name} disable
                      onInputChange={onInputChange} style={{ height: height }} className={cssClass} />
                  );
                },
                editor: {
                  type: 'string', enable: writeCap,
                  onInputChange: onInputChange,
                  renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
                    const { fieldConfig, displayRecord } = ctx;
                    let record = displayRecord.record;
                    let disable = !writeCap || space !== 'user';
                    let height = ctx.gridContext.config.record.dataCellHeight;
                    let cssClass = fieldConfig?.computeCssClasses?.(ctx.gridContext, displayRecord) || '';
                    return (
                      <input.BBTextField bean={record} field={fieldConfig.name}
                        disable={disable} onInputChange={onInputChange} style={{ height: height }} className={`${cssClass} fw-bold`} />
                    )
                  },
                },
              },
            ]),
            ...entity.DbEntityListConfigTool.FIELDS(
              space == 'user' && [KpiStatus.Plan, KpiStatus.PlanReviewing, KpiStatus.Processing, KpiStatus.ResultReviewing].includes(kpiStatus) ||
              space == 'company' && [KpiStatus.Plan, KpiStatus.PlanReviewing, KpiStatus.Processing].includes(kpiStatus),
              [
                {
                  name: 'managerAdjustValue', label: 'Q.L Trực tiếp', width: 110,
                  computeCssClasses: computeCssClasses,
                  editor: {
                    type: 'double', enable: writeCap,
                    onInputChange: onInputChange,
                    renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
                      const { fieldConfig, displayRecord, tabIndex, focus } = ctx;
                      let record = displayRecord.record;
                      let disable = !writeCap || kpiStatus != KpiStatus.ResultReviewing;
                      let cssClass = fieldConfig?.computeCssClasses?.(ctx.gridContext, displayRecord) || '';
                      let maxPrecision = 2;
                      let validators = [];
                      if (objectiveType === KpiObjectiveType.Behavior) {
                        cssClass += ' text-center';
                        maxPrecision = 0;
                        validators.push(new BehaviorPointValidator())
                      }
                      if (['PERCENT', 'Percent', 'percent', '%'].includes(record['unit'])) {
                        return (
                          <input.BBPercentField tabIndex={tabIndex} focus={focus} bean={record} field={fieldConfig.name}
                            disable={disable} onInputChange={onInputChange} className={cssClass} />
                        );
                      }
                      return (
                        <input.BBNumberField className={cssClass}
                          tabIndex={tabIndex} focus={focus} bean={record} field={fieldConfig.name} maxPrecision={maxPrecision}
                          disable={disable} onInputChange={onInputChange} validators={validators} />
                      );
                    },
                  }
                },
                {
                  name: 'managerAdjustExplanation', label: 'Lý do', width: 200,
                  computeCssClasses: computeCssClasses,
                  customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
                    let record = dRecord.record;
                    let height = ctx.config.record.dataCellHeight;
                    let cssClass = field.computeCssClasses?.(ctx, dRecord) || '';
                    return (
                      <input.BBTextField bean={record} field={field.name} disable
                        onInputChange={onInputChange} style={{ height: height }} className={cssClass} />
                    );
                  },
                  editor: {
                    type: 'string', enable: writeCap,
                    onInputChange: onInputChange,
                    renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
                      const { fieldConfig, displayRecord } = ctx;
                      let record = displayRecord.record;
                      let disable = !writeCap || kpiStatus != KpiStatus.ResultReviewing;
                      let height = ctx.gridContext.config.record.dataCellHeight;
                      let cssClass = fieldConfig?.computeCssClasses?.(ctx.gridContext, displayRecord) || '';
                      return (
                        <input.BBTextField bean={record} field={fieldConfig.name}
                          disable={disable} onInputChange={onInputChange} style={{ height: height }} className={`${cssClass} fw-bold`} />
                      )
                    },
                  },
                }
              ]
            ),
          ]),
          {
            name: 'calculateAlgorithm', label: T('Cách tính kết quả'), width: 190,
            computeCssClasses: computeCssClasses,
            customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let cssClass = field.computeCssClasses?.(ctx, dRecord) || '';
              return (
                <input.BBSelectField bean={record} field={field.name} className={cssClass}
                  options={[KpiCalculateAlgorithm.RATIO_BASED, KpiCalculateAlgorithm.FAILURE_BASED]}
                  optionLabels={[T('KPIs xuôi (càng lớn càng tốt)'), T('KPIs ngược (càng bé càng tốt)')]}
                  onInputChange={onInputChange} disable />
              )
            },
            editor: {
              type: 'string', enable: writeCap,
              onInputChange: onInputChange,
              renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
                const { fieldConfig, displayRecord, tabIndex, focus } = ctx;
                let record = displayRecord.record;
                let disable = disablePlanFields(record) || record['kpiTemplateItemId'];
                let cssClass = fieldConfig?.computeCssClasses?.(ctx.gridContext, displayRecord) || '';
                return (
                  <input.BBSelectField tabIndex={tabIndex} focus={focus} bean={record} field={fieldConfig.name}
                    options={[KpiCalculateAlgorithm.RATIO_BASED, KpiCalculateAlgorithm.FAILURE_BASED]}
                    optionLabels={[T('KPIs xuôi (càng lớn càng tốt)'), T('KPIs ngược (càng bé càng tốt)')]}
                    disable={disable} onInputChange={onInputChange} className={cssClass} />
                );
              },
            }
          },
          {
            name: 'measurementMethod', label: T('Phương pháp đo'), width: 450, state: { visible: false },
            computeCssClasses: computeCssClasses,
            customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let height = ctx.config.record.dataCellHeight;
              let cssClass = field.computeCssClasses?.(ctx, dRecord) || '';
              return (
                <input.BBTextField bean={record} field={field.name} disable
                  onInputChange={onInputChange} style={{ height: height }} className={cssClass} />
              );
            },
            editor: {
              type: 'string', enable: writeCap,
              onInputChange: onInputChange,
              renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
                const { fieldConfig, displayRecord, tabIndex, focus } = ctx;
                let record = displayRecord.record;
                let disable = disablePlanFields(record) || record['kpiTemplateItemId'];
                let height = ctx.gridContext.config.record.dataCellHeight;
                let cssClass = fieldConfig?.computeCssClasses?.(ctx.gridContext, displayRecord) || '';
                return (
                  <input.BBTextField tabIndex={tabIndex} focus={focus} bean={record} field={fieldConfig.name}
                    disable={disable} onInputChange={onInputChange} style={{ height: height }} className={cssClass} />
                )
              },
            },
          },
          ...entity.DbEntityListConfigTool.FIELDS(kpiStatus != KpiStatus.Processing, [
            {
              name: 'dataSource', label: T('Nguồn dữ liệu'),
              computeCssClasses: computeCssClasses,
              editor: {
                type: 'string', enable: writeCap,
                onInputChange: onInputChange,
                renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
                  const { fieldConfig, displayRecord, tabIndex, focus } = ctx;
                  let record = displayRecord.record;
                  let disable = disablePlanFields(record) || record['kpiTemplateItemId'];
                  let cssClass = fieldConfig?.computeCssClasses?.(ctx.gridContext, displayRecord) || '';
                  return (
                    <input.BBStringField tabIndex={tabIndex} focus={focus} bean={record} field={fieldConfig.name}
                      disable={disable} onInputChange={onInputChange} className={cssClass} />
                  )
                },
              },
            },
          ]),
          ...entity.DbEntityListConfigTool.FIELD(kpiStatus == KpiStatus.Plan || kpiStatus == KpiStatus.PlanReviewing,
            {
              name: 'attachments', label: T('File đính kèm'), width: 150,
              fieldDataGetter: (record: any) => {
                let disable = !writeCap || ![KpiStatus.Processing, KpiStatus.ResultReviewing].includes(kpiStatus);
                return (
                  <div className='flex-hbox justify-content-center'>
                    <WPopupAttachments className='flex-vbox btn-link'
                      appContext={appContext} pageContext={pageContext} readOnly={disable}
                      commitMethod={{
                        component: 'KpiService', endpoint: 'saveKpiItemAttachment', params: { kpiItemid: record.id }
                      }}
                      loadMethod={{
                        component: 'KpiService', endpoint: 'findKpiItemAttachments', params: { kpiItemid: record.id }
                      }} commitURL="" loadURL="" />
                  </div>
                );
              },
            }
          ),
          {
            name: 'description', label: T('Mô tả'), width: 300, dataTooltip: true,
            computeCssClasses: computeCssClasses,
            customRender: (ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let height = ctx.config.record.dataCellHeight;
              let cssClass = field.computeCssClasses?.(ctx, dRecord) || '';
              return (
                <input.BBTextField bean={record} field={field.name} disable
                  onInputChange={onInputChange} style={{ height: height }} className={cssClass} />
              );
            },
            editor: {
              type: 'text', enable: writeCap,
              onInputChange: onInputChange,
              renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
                const { fieldConfig, displayRecord, tabIndex, focus } = ctx;
                let record = displayRecord.record;
                let disable = !writeCap || [KpiStatus.DirectorApproved, KpiStatus.LeaderApproved, KpiStatus.ManagerApproved].includes(kpiStatus);
                let height = ctx.gridContext.config.record.dataCellHeight;
                let cssClass = fieldConfig?.computeCssClasses?.(ctx.gridContext, displayRecord) || '';
                return (
                  <input.BBTextField tabIndex={tabIndex} focus={focus} bean={record} field={fieldConfig.name}
                    disable={disable} onInputChange={onInputChange} style={{ height: height }} className={cssClass} />
                )
              },
            },
          },
          ...entity.DbEntityListConfigTool.FIELDS(!pageContext.hasUserAdminCapability(), [
            ...entity.DbEntityListConfigTool.FIELD_ENTITY,
          ])
        ],
        fieldGroups: {
          "adjust": {
            label: 'Điều chỉnh',
            fields: ['employeeAdjustValue', 'employeeAdjustExplanation', 'managerAdjustValue', 'managerAdjustExplanation']
          },
        },
      },
      toolbar: {
        actions: [
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_ADD(!actionsCap, T('Thêm')),
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_DELETE(!pageContext.hasUserAdminCapability() && !actionsCap, T('Xoá'))
        ],
        filters: entity.DbEntityListConfigTool.TOOLBAR_FILTERS(false)
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          },
          aggregation: {
            viewMode: 'aggregation', treeWidth: 150,

            createAggregationModel(_ctx: grid.VGridContext) {
              let model = new grid.AggregationDisplayModel(T('All'), false);
              model.addAggregation(new grid.ValueAggregation(T("Objective"), "objectiveType", true),);
              return model;
            }
          }
        }
      },

    };
    return config;
  }

  onModify = () => {
    const { onModifyBean } = this.props;
    let records = this.vgridContext.model.getRecords();
    if (onModifyBean) onModifyBean(records, entity.ModifyBeanActions.MODIFY);
  }

  onNewAction = () => {
    let { kpi, objectiveType } = this.props;
    let newRecord = { kpiId: kpi.id, objectiveType: objectiveType, unit: 'UNIT' };
    grid.initRecordState(newRecord, 0).markNew();
    this.vgridContext.model.addRecord(newRecord);
    this.vgridContext.getVGrid().forceUpdateView();
    this.onModify();
  }

  onDeleteAction() {
    let { plugin, appContext } = this.props;
    let records = plugin.getListModel().getSelectedRecords();
    let labelRecords: Array<string> = [];
    let ids = [];

    for (let record of records) {
      if (record['kpiTemplateItemId']) {
        labelRecords.push(record.label);
      } else {
        ids.push(record['id']);
      }
    }

    if (labelRecords.length > 0) {
      let label = `Can't delete KPI Items: (${labelRecords.join(', ')}) linked to template items`;
      if (labelRecords.length == 1) {
        label = `Can't delete KPI Item: ${labelRecords[0]}, linked to the template item`;
      }
      appContext.addOSNotification("danger", T(label));
      return;
    }

    appContext.createHttpBackendCall('KpiService', 'deleteKpiItems', { ids: ids })
      .withSuccessData((_data: any) => {
        appContext.addOSNotification("success", T(`Delete Success`));
        plugin.getListModel().removeSelectedDisplayRecords();
        this.getVGridContext().getVGrid().forceUpdateView();
      })
      .call();
  }
}

