import React from 'react';
import { app, bs, entity, grid, input } from '@datatp-ui/lib';

import { DbEntityPluginButton, DbEntityPluginConfig } from '../../common/wfms';
import { BBRefOKRMilestone } from '../../okr/milestone/BBOKRMilestone';
import { BBRefEmployee } from '../../company/hr';
import { T } from './backend';
import { KpiTemplateItemList, KpiTemplateItemListPlugin } from './KpiTemplateItemList';
import { KpiList, KpiListPlugin } from './KpiList';
import { onAddEmployeesToKpiTemplate, renderKpiNoted } from './utils';
import { KpiObjectiveType, KpiStatus } from '../models';

export class KpiTemplateModel {
  template: any;
  workList: any[] = [];
  behaviorList: any[] = [];

  constructor(model: any) {
    this.template = model.template;
    this.workList = model.workList;
    this.behaviorList = model.behaviorList;
  }

  getTemplate() { return this.template; }

  setTemplate(template: any) { this.template = template; }

  getWorkList() { return this.workList; }
  setWorkList(workList: any[]) { this.workList = workList; }

  getBehaviorList() { return this.behaviorList; }
  setBehaviorList(behaviorList: any[]) { this.behaviorList = behaviorList; }

  getAllKpiItems() {
    return [...this.workList, ...this.behaviorList];
  }
}

export class UIKpiTemplateForm extends entity.AppDbEntity {
  onInputChange = (bean: any, field: string, oldVal: any, newVal: any) => {
    let { observer, onModify } = this.props;
    if (onModify) onModify(bean, 'template', oldVal, observer.getMutableBean());
    else this.forceUpdate();
  }

  onInputChangeJobCode = (bean: any, field: string, oldVal: any, newVal: any) => {
    const { appContext, observer, onModify } = this.props;
    let template = observer.getMutableBean();
    if (!newVal || oldVal === newVal || newVal === template.jobCode) return;
    appContext.createHttpBackendCall("KpiService", 'getKpiTemplateByJobCode', { milestone: template.milestone, jobCode: newVal })
      .withSuccessData((data) => {
        if (data) {
          let detailErrorEle = (<div> <strong>{template.milestoneLabel}</strong> Job code <strong>{newVal} </strong> đã tồn tại!</div>);
          bs.notificationShow('warning', 'Input Job Code Error', detailErrorEle);
          bean[field] = oldVal;
          this.forceUpdate();
        } else if (onModify) {
          onModify(bean, 'template', oldVal, observer.getMutableBean());
        } else {
          this.forceUpdate();
        }
      })
      .call();
  }

  render() {
    let { appContext, pageContext, observer } = this.props;
    let kpiTemplate = observer.getMutableBean();
    let moderatorCap = pageContext.hasUserModeratorCapability();

    let html = (
      <div className="flex-vbox">
        <div className='flex-hbox'>
          <div className='w-75 flex-vbox p-1'>
            <bs.Row>
              <bs.Col span={2}>
                <input.BBStringField bean={kpiTemplate} label='Tên Job Code' field={'label'} disable={!moderatorCap}
                  onInputChange={this.onInputChange} />
              </bs.Col>
              <bs.Col span={2}>
                <input.BBStringField bean={kpiTemplate} label='Mã Job Code' field={'jobCode'} disable={!moderatorCap}
                  onInputChange={this.onInputChangeJobCode} />
              </bs.Col>
              <bs.Col span={2}>
                <BBRefOKRMilestone
                  appContext={appContext} pageContext={pageContext} placement="bottom-start"
                  label={T('Milestone')} placeholder="Enter Milestone" bean={kpiTemplate} beanIdField={'milestone'}
                  beanLabelField={'milestoneLabel'} refMilestoneField={'milestone'} disable={!moderatorCap}
                  onPostUpdate={(_inputUI, _bean, selOpt, _userInput) => {
                    kpiTemplate.milestone = selOpt.milestone;
                    kpiTemplate.milestoneLabel = selOpt.label;
                    if (!kpiTemplate.fromDate) kpiTemplate.fromDate = selOpt.startDate;
                    if (!kpiTemplate.toDate) kpiTemplate.toDate = selOpt.endDate;
                    this.onInputChange(kpiTemplate, '', null, null);
                  }} />
              </bs.Col>
              <bs.Col span={2}>
                <input.BBDateTimeField
                  bean={kpiTemplate} label='Từ ngày' field={'fromDate'} dateFormat={"DD/MM/YYYY"} timeFormat={false}
                  disable={!moderatorCap} onInputChange={this.onInputChange} />
              </bs.Col>
              <bs.Col span={2}>
                <input.BBDateTimeField
                  bean={kpiTemplate} label='Đến ngày' field={'toDate'} dateFormat={"DD/MM/YYYY"} timeFormat={false}
                  disable={!moderatorCap} onInputChange={this.onInputChange} />
              </bs.Col>
              <bs.Col span={2}>
                <entity.BBEntityEditMode bean={kpiTemplate} label={T('Mode')} field={'editMode'}
                  disable={!pageContext.hasUserAdminCapability()} onInputChange={this.onInputChange} />
              </bs.Col>
            </bs.Row>
            <bs.Row>
              <bs.Col span={2}>
                <BBRefEmployee label='Leader'
                  appContext={appContext} pageContext={pageContext} bean={kpiTemplate} disable={!moderatorCap}
                  beanIdField='leaderAccountId' beanLabelField='leaderFullName' selectedId='accountId'
                  placeholder='Input Leader'
                  onPostUpdate={(_inputUI, _bean, selOpt, _userInput) => {
                    kpiTemplate.leaderAccountId = selOpt.accountId;
                    kpiTemplate.leaderFullName = selOpt.label;
                    this.onInputChange(kpiTemplate, '', null, null);
                  }} />
              </bs.Col>
              <bs.Col span={2}>
                <BBRefEmployee
                  appContext={appContext} pageContext={pageContext} bean={kpiTemplate}
                  label='Quản lý' beanIdField='managerAccountId' beanLabelField='managerFullName' selectedId='accountId'
                  placeholder='Input Manager' disable={!moderatorCap}
                  onPostUpdate={(_inputUI, _bean, selOpt, _userInput) => {
                    kpiTemplate.managerAccountId = selOpt.accountId;
                    kpiTemplate.managerFullName = selOpt.label;
                    this.onInputChange(kpiTemplate, '', null, null);
                  }} />
              </bs.Col>
              <bs.Col span={2}>
                <input.BBStringField bean={kpiTemplate} label='Chức vụ' field={'jobTitle'} disable={!moderatorCap}
                  onInputChange={this.onInputChange} />
              </bs.Col>
              <bs.Col span={2}>
                <input.BBStringField bean={kpiTemplate} label='Phòng ban' field={'department'} disable={!moderatorCap}
                  onInputChange={this.onInputChange} />
              </bs.Col>
              <bs.Col span={2}>
                <input.BBStringField bean={kpiTemplate} label='Nơi làm việc' field={'workplace'} disable={!moderatorCap}
                  onInputChange={this.onInputChange} />
              </bs.Col>
              <bs.Col span={2}>
                <input.BBStringField bean={kpiTemplate} label='Chi nhánh' field={'companyBranch'}
                  disable={!moderatorCap} onInputChange={this.onInputChange} />
              </bs.Col>
            </bs.Row>
          </div>
          <div className='w-25 flex-vbox p-1'>
            <input.BBTextField bean={kpiTemplate} label='Mô tả' field={'description'} disable={!moderatorCap}
              style={{ height: '6em' }} onInputChange={this.onInputChange} />
          </div>
        </div>
      </div>
    );
    return html;
  }
}

interface UIKpiTemplateModelProps extends entity.UILoadableEntityProps {
  kpiTemplateModel: KpiTemplateModel;
  space: 'user' | 'company';
  onPostCommit?: entity.EntityOnPostCommit;
}
export class UIKpiTemplateModel extends app.AppComponent<UIKpiTemplateModelProps> {
  entityTaskConfig: DbEntityPluginConfig;

  constructor(props: UIKpiTemplateModelProps) {
    super(props)
    let { kpiTemplateModel } = this.props;
    this.entityTaskConfig = {
      entityType: 'kpi_template',
      entityLabel: 'Kpi Template',
      plugin: 'KpiTemplateTaskPlugin',
      newEntityTask: () => {
        return {
          'label': 'A New Task',
        };
      },
      getDbEntity: () => kpiTemplateModel.getTemplate(),
      onModifyDbEntity: (dbEntity: any) => {
        kpiTemplateModel.setTemplate(dbEntity);
        this.forceUpdateUI(true);
      }
    }
  }

  onPostCommit = (entity: any) => {
    let { onPostCommit } = this.props;
    if (onPostCommit) {
      onPostCommit(entity, this);
    } else {
      this.forceUpdate();
    }
  }

  onModify = (bean: any, field: string, oldVal: any, newVal: any) => {
    let { kpiTemplateModel } = this.props;
    if (field === 'template') kpiTemplateModel.setTemplate(newVal)
    else if (field === 'workList') kpiTemplateModel.setWorkList(newVal)
    this.onUpdate();
  }

  onUpdateKpis = () => {
    let { appContext, kpiTemplateModel } = this.props;
    let kpiTemplate = kpiTemplateModel.getTemplate();

    appContext.createHttpBackendCall('KpiService', 'updateKpiFromTemplate', { kpiTemplate: kpiTemplate })
      .withBackendNotification(T, 300)
      .call();
  }

  onUpdate = () => {
    let { appContext, kpiTemplateModel } = this.props;
    let records = kpiTemplateModel.getAllKpiItems();
    let modifiedRecords: Array<any> = [];
    for (let i = 0; i < records.length; i++) {
      let record = records[i];
      let recordState = grid.getRecordState(record);
      if (recordState.isMarkDeleted()) {
        record.editState = 'DELETED';
        modifiedRecords.push(record);
      } else if (recordState.isMarkNew()) {
        record.editState = 'NEW';
        modifiedRecords.push(record);
      } else if (recordState.isMarkModified()) {
        record.editState = 'MODIFIED';
        modifiedRecords.push(record);
      }
    }

    appContext.createHttpBackendCall('KpiService', 'saveKpiTemplateModel', { model: kpiTemplateModel })
      .withSuccessData((data: any) => {
        kpiTemplateModel.setTemplate(data.template);
        kpiTemplateModel.setWorkList(data.workList);
        kpiTemplateModel.setBehaviorList(data.behaviorList);
        this.onPostCommit(data);
        this.forceUpdateUI(true);
      })
      .withEntityOpNotification('commit', 'Kpi Template')
      .call();
  }

  onModifyTemplateItemList = (objType: KpiObjectiveType, records: any, _action: entity.ModifyBeanActions) => {
    if (!records || records.length == 0) return;
    let { kpiTemplateModel } = this.props;
    if (objType == KpiObjectiveType.Work) {
      this.onModify(kpiTemplateModel, 'workList', kpiTemplateModel.getWorkList(), records);
    } else if (objType == KpiObjectiveType.Behavior) {
      this.onModify(kpiTemplateModel, 'behaviorList', kpiTemplateModel.getWorkList(), records);
    }
    this.forceUpdate();
  }

  checkPermission = () => {
    let { pageContext, kpiTemplateModel } = this.props;
    let template = kpiTemplateModel.getTemplate();
    let editMode: entity.EntityEditMode = template['editMode'];
    pageContext.setPageEditMode(editMode);
  }

  renderToolBar = () => {
    let { appContext, pageContext } = this.props;
    let writeCap = pageContext.canEditPage();

    return (
      <bs.Toolbar hide={!writeCap}>
        <entity.WButtonEntityWrite
          appContext={appContext} pageContext={pageContext} label='Apply for KPIs' onClick={this.onUpdateKpis} />
        <entity.WButtonEntityWrite
          appContext={appContext} pageContext={pageContext} label='Cập nhật' onClick={this.onUpdate} />
        <DbEntityPluginButton appContext={appContext} pageContext={pageContext} config={this.entityTaskConfig} />
      </bs.Toolbar>
    );
  }

  render() {
    this.checkPermission();
    let { appContext, pageContext, kpiTemplateModel, space } = this.props;
    let template = kpiTemplateModel.getTemplate();
    let html = (
      <div className='flex-vbox'>
        <div className='flex-vbox'>
          <div className=' flex-vbox flex-grow-0 p-1'>
            <UIKpiTemplateForm
              appContext={appContext} pageContext={pageContext} observer={new entity.ComplexBeanObserver(template)}
              onModify={this.onModify} />
          </div>
          <div className='flex-vbox flex-grow-1'>
            <bs.ScrollableCards key={this.componentId} className='flex-vbox p-1 border rounded'>
              <bs.Card className='flex-vbox mt-0 mb-1' header='Quy ước' collapse>
                {renderKpiNoted(space, 'template')}
              </bs.Card>
              <bs.Card className='flex-vbox mb-1' header='MỤC TIÊU CÔNG VIỆC'>
                <KpiTemplateItemList appContext={appContext} pageContext={pageContext} style={{ minHeight: 465 }}
                  plugin={new KpiTemplateItemListPlugin(kpiTemplateModel.getWorkList())}
                  kpiTemplate={template} objectiveType={KpiObjectiveType.Work}
                  onModifyBean={(records: any, action: entity.ModifyBeanActions) =>
                    this.onModifyTemplateItemList(KpiObjectiveType.Work, records, action)} />
              </bs.Card>
              <bs.Card className='flex-vbox mb-1' header='THÁI ĐỘ - HÀNH VI'>
                <KpiTemplateItemList appContext={appContext} pageContext={pageContext} style={{ minHeight: 465 }}
                  plugin={new KpiTemplateItemListPlugin(kpiTemplateModel.getBehaviorList())}
                  kpiTemplate={template} objectiveType={KpiObjectiveType.Behavior} />
              </bs.Card>
            </bs.ScrollableCards>
          </div>
        </div>
        {this.renderToolBar()}
      </div>
    );
    return html;
  }
}

interface UIKpiTemplateProps extends app.AppComponentProps {
  kpiTemplateModel: KpiTemplateModel;
  space: 'user' | 'company';
  onPostCommit?: entity.EntityOnPostCommit;
}
export class UIKpiTemplate extends app.AppComponent<UIKpiTemplateProps> {
  onAddEmployees = (callback: () => void) => {
    let { kpiTemplateModel } = this.props;
    let kpiTemplate = kpiTemplateModel.getTemplate();
    onAddEmployeesToKpiTemplate(this, kpiTemplate, callback);
  }

  render() {
    let { appContext, pageContext, kpiTemplateModel, space, onPostCommit } = this.props;
    let kpiTemplate = kpiTemplateModel.getTemplate();
    let html = (
      <bs.TabPane>
        <bs.Tab name='kpi-template' label='Kpi Template' active>
          <UIKpiTemplateModel appContext={appContext} pageContext={pageContext} space={space}
            kpiTemplateModel={kpiTemplateModel} onPostCommit={onPostCommit} />
        </bs.Tab>
        <bs.Tab name='kpis' label='KPIs' >
          <KpiList appContext={appContext} pageContext={pageContext} plugin={new KpiListPlugin(kpiTemplate.id)}
            space={space} onAddEmployees={this.onAddEmployees} />
        </bs.Tab>
      </bs.TabPane>
    );
    return html;
  }
}