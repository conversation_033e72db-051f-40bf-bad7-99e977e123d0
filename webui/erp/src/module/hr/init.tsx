
import React from "react";
import * as icon from 'react-feather';
import { app } from '@datatp-ui/lib'

import { DbEntityTaskCalendarPluginManger } from '../common/wfms/task/DbEntityTaskCalendarPlugin'
import { KpiTaskCalendarPlugin } from './kpi/task/KpiTaskCalendarPlugin'
import {
  KpiItemRuleList, KpiItemRuleListPlugin,
  KpiTemplateList, KpiTemplateListPlugin,
  KpiList, KpiListPlugin,
} from './kpi'
import space = app.space;
import { UIKPIReport } from "./kpi/KpiReport";

class KPISpacePlugin extends space.SpacePlugin {
  constructor() {
    super('hr/kpi', 'KPI Project');
  }

  override createUserScreens(): space.ScreenConfig[] {
    let configs: space.ScreenConfig[] = [
      {
        id: "hr-user-kpi", label: "My KPIs", icon: icon.Table,
        checkPermission: {
          feature: { module: 'hr', name: 'user-kpi' },
          requiredCapability: app.READ,
        },
        renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return (<KpiList appContext={appCtx} pageContext={pageCtx} space="user"
            plugin={new KpiListPlugin('user')} />);
        }
      }
    ]
    return configs;
  }

  override createCompanyScreens(): space.ScreenConfig[] {
    let configs: space.ScreenConfig[] = [
      {
        id: "hr-company-kpi", label: "KPIs", icon: icon.Activity,
        checkPermission: {
          feature: { module: 'hr', name: 'company-kpi' },
          requiredCapability: app.READ,
        },
        renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          let plugin = new KpiListPlugin('company');
          return (<KpiList appContext={appCtx} pageContext={pageCtx} plugin={plugin} space="company" />);
        },
        screens: [
          {
            id: "hr-company-kpi-templates", label: "KPI Templates", icon: icon.Table,
            checkPermission: {
              feature: { module: 'hr', name: 'company-kpi' },
              requiredCapability: app.READ,
            },
            renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
              let plugin = new KpiTemplateListPlugin('company');
              return (<KpiTemplateList appContext={appCtx} pageContext={pageCtx} plugin={plugin} space="company" />);
            },
          },
          {
            id: "hr-company-kpi-item-rules", label: "KPI Item Rules", icon: icon.Table,
            checkPermission: {
              feature: { module: 'hr', name: 'company-kpi' },
              requiredCapability: app.READ,
            },
            renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
              return (<KpiItemRuleList appContext={appCtx} pageContext={pageCtx} plugin={new KpiItemRuleListPlugin()} />);
            },
          }
        ]
      }
    ]
    return configs;
  }

  createSystemScreens(): app.space.ScreenConfig[] {
    let configs: space.ScreenConfig[] = [
      {
        id: "kpi-report", label: "KPIs", icon: icon.BarChart2,
        checkPermission: {
          feature: { module: 'hr', name: 'company-kpi' },
          requiredCapability: app.MODERATOR,
        },
        renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return <UIKPIReport appContext={appCtx} pageContext={pageCtx} space="system" readOnly />
        },
      }
    ]
    return configs;
  }
}

export function init() {
  space.SpacePluginManager.register(new KPISpacePlugin());
}

DbEntityTaskCalendarPluginManger.register(new KpiTaskCalendarPlugin('kpi_kpi'));
DbEntityTaskCalendarPluginManger.register(new KpiTaskCalendarPlugin('kpi_template'));