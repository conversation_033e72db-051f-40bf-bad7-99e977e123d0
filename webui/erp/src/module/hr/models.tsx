export enum KpiObjectiveType {
  Work = 'Work',
  Behavior = 'Behavior',
};

export enum KpiCalculateAlgorithm {
  RATIO_BASED = 'RATIO_BASED',
  FAILURE_BASED = 'FAILURE_BASED'
}

export enum KpiGrade {
  Failed = 'Failed',
  NeedImprovement = 'NeedImprovement',
  Passed = 'Passed',
  ExceedExpectations = 'ExceedExpectations',
  Outstanding = 'Outstanding'
}

export enum KpiStatus {
  Plan = 'Plan',
  PlanReviewing = 'PlanReviewing',
  Processing = 'Processing',
  ResultReviewing = 'ResultReviewing',
  LeaderApproved = 'LeaderApproved',
  ManagerApproved = 'ManagerApproved',
  DirectorApproved = 'DirectorApproved',
}