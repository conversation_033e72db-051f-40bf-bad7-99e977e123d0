import { entity } from '@datatp-ui/lib';
import { T } from '../backend';

export interface BBRefMultiRecipientProps extends entity.BBRefMultiEntityProps {
  recipientsObserver: entity.IArrayObserver;
}
export class BBRefMultiAccountRecipient extends entity.BBRefMultiEntity<BBRefMultiRecipientProps> {

  createExcludeRecordFilter() {
    let { bean } = this.props;
    let refEntities = bean as Array<any>;
    return new entity.ExcludeRecordFilter(refEntities, 'recipientAccountId', 'id');
  }

  onRemove(idx: number): void {
    let { bean, recipientsObserver } = this.props;
    let refEntities = bean as Array<any>;
    let refEntity = refEntities[idx];
    recipientsObserver.removeRecord(refEntity);
    super.onRemove(idx);
  }

  createPlugin() {
    let { recipientsObserver } = this.props;
    let config: entity.BBRefEntityPluginConfig = {
      backend: {
        context: 'company',
        service: 'CommunicationMessageService',
        searchMethod: 'searchCommunicationAccounts',
        loadMethod: 'getCommunicationAccount',
        createLoadParams: (idValue) => {
          let params = { 'loginId': idValue };
          return params;
        },
        createSearchParams(origParams, userInput) {
          origParams['maxReturn'] = 1000;
          return origParams;
        }
      },
      bean: {
        idField: 'recipientId',
        labelField: 'recipientDisplayName',
        mapSelect: (ui: entity.BBRefEntity, bean: any, selectOpt: any, idValue: any, labelValue: any) => {
          let refEntities = bean as Array<any>;
          if (selectOpt) {
            let target = {
              deliverType: 'Private',
              recipientDisplayName: selectOpt.loginId,
              recipientId: selectOpt.loginId,
              forwardEmail: selectOpt.autoForward
            }
            recipientsObserver.addRecord(target);
            refEntities.push(target)
            return 'success';
          } else {
            return 'fail';
          }
        }
      },
      refEntity: {
        idField: 'id',
        labelField: 'fullName',
        labelFunc: (opt: any) => {
          let email = opt['forwardEmail'] ? opt['forwardEmail'] : '';
          return `${opt['fullName']} ${email}`;
        },
        vgridRecordConfig: {
          fields: [
            entity.DbEntityListConfigTool.FIELD_INDEX(),
            entity.DbEntityListConfigTool.FIELD_ON_SELECT('id', T('Account ID'), 250),
            { name: 'fullName', label: T('Full Name'), width: 250 },
            { name: 'forwardEmail', label: T('Forward Email Address'), width: 250 },
          ]
        }
      },
    }
    return new entity.BBRefMultiEntityPlugin(config);
  }
}

export class BBRefMultiChannelRecipient extends entity.BBRefMultiEntity<BBRefMultiRecipientProps> {

  createExcludeRecordFilter() {
    let { bean } = this.props;
    let refEntities = bean as Array<any>;
    return new entity.ExcludeRecordFilter(refEntities, 'recipientAccountId', 'name');
  }

  onRemove(idx: number): void {
    let { bean, recipientsObserver } = this.props;
    let refEntities = bean as Array<any>;
    let refEntity = refEntities[idx];
    recipientsObserver.removeRecord(refEntity);
    super.onRemove(idx);
  }

  createPlugin() {
    let { recipientsObserver } = this.props;

    let config: entity.BBRefEntityPluginConfig = {
      backend: {
        context: 'system',
        service: 'CommunicationMessageService',
        searchMethod: 'searchCommunicationChannels',
        loadMethod: 'getCommunicationChannel',
        createLoadParams: (idValue) => {
          let params = { 'name': idValue };
          return params;
        },
      },
      bean: {
        idField: 'recipientId',
        labelField: 'recipientDisplayName',
        mapSelect: (ui: entity.BBRefEntity, bean: any, selectOpt: any, idValue: any, labelValue: any) => {
          let refEntities = bean as Array<any>;
          if (selectOpt) {
            let target = {
              deliverType: 'Channel',
              recipientDisplayName: selectOpt.label,
              recipientId: selectOpt.name
            }
            recipientsObserver.addRecord(target);
            refEntities.push(target)
            return 'success';
          } else {
            return 'fail';
          }
        }
      },
      refEntity: {
        idField: 'name',
        labelField: 'label',
        vgridRecordConfig: {
          fields: [
            entity.DbEntityListConfigTool.FIELD_INDEX(),
            entity.DbEntityListConfigTool.FIELD_ON_SELECT('name', T('Name'), 250),
            { name: 'type', label: T('Type'), width: 200 },
            ...entity.DbEntityListConfigTool.FIELD_ENTITY
          ]
        }
      },
    }
    return new entity.BBRefMultiEntityPlugin(config);
  }
}

export interface BBRefEmailRecipientProps extends BBRefMultiRecipientProps {
  excludeForwardEmailIsNull?: boolean;
  type: 'CCEmail' | 'Email';
}

export class BBRefEmailRecipient extends entity.BBRefMultiEntity<BBRefEmailRecipientProps> {

  createExcludeRecordFilter() {
    let { bean } = this.props;
    let refEntities = bean as Array<any>;
    return new entity.ExcludeRecordFilter(refEntities, 'recipientAccountId', 'email');
  }

  onRemove(idx: number): void {
    let { bean, recipientsObserver } = this.props;
    let refEntities = bean as Array<any>;
    let refEntity = refEntities[idx];
    recipientsObserver.removeRecord(refEntity);
    super.onRemove(idx);
  }

  createPlugin() {
    let { excludeForwardEmailIsNull, type, recipientsObserver } = this.props;
    let config: entity.BBRefEntityPluginConfig = {
      backend: {
        context: 'system',
        service: 'CommunicationMessageService',
        searchMethod: 'searchCommunicationAccounts',
        loadMethod: 'getCommunicationAccount',
        createLoadParams: (idValue) => {
          let params = { 'loginId': idValue };
          return params;
        },
        createSearchParams(origParams, userInput) {
          origParams["params"] = {
            excludeForwardEmailIsNull: excludeForwardEmailIsNull
          };
          origParams['maxReturn'] = 1000;
          return origParams;
        }
      },
      bean: {
        idField: 'recipientId',
        labelField: 'recipientDisplayName',
        mapSelect: (ui: entity.BBRefEntity, bean: any, selectOpt: any, idValue: any, labelValue: any) => {
          let { allowUserInput } = this.props;

          let refEntities = bean as Array<any>;
          if (selectOpt) {
            let target = {
              deliverType: type,
              recipientDisplayName: selectOpt.email,
              recipientId: selectOpt.email
            }
            recipientsObserver.addRecord(target);
            refEntities.push(target)
            return 'success';
          } else {
            let val: string = labelValue.trim();
            if (allowUserInput && val.length > 0) {
              let target = {
                deliverType: type, recipientDisplayName: labelValue, recipientId: labelValue
              }
              recipientsObserver.addRecord(target);
              refEntities.push(target)
              return 'success';
            }
            return 'fail';
          }
        }
      },
      refEntity: {
        idField: 'loginId',
        labelField: 'email',
        vgridRecordConfig: {
          fields: [
            entity.DbEntityListConfigTool.FIELD_INDEX(),
            entity.DbEntityListConfigTool.FIELD_ON_SELECT('loginId', T('Login Id'), 250),
            { name: 'fullName', label: T('Full Name'), width: 250 },
            { name: 'forwardEmail', label: T('Forward Email Address'), width: 250 },
          ]
        }
      },
    }
    return new entity.BBRefMultiEntityPlugin(config);
  }
}
