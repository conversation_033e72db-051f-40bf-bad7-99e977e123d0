import React from 'react';
import * as FeatherIcon from 'react-feather';
import {bs, entity, grid, util} from '@datatp-ui/lib';
import {T} from "../backend";

export class FeeDocChargeSummaryItemList extends entity.DbEntityList {
  createVGridConfig() {
    let _ = this;
    let config: grid.VGridConfig = {
      record: {
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(),
          {
            name: 'index', label: "Số TT", container: 'fixed-left',
          },
          {
            name: 'fileNo', label: "SỐ FILE / BOOKING / CUS ĐẶT XE", container: 'fixed-left',
          },
          {
            name: 'customer', label: "KHÁCH HÀNG", container: 'fixed-left',
          },
          {
            name: 'contNo', label: "SỐ CONT", container: 'fixed-left',
          },
          { name: 'invoiceNo', label: 'SỐ HÓA ĐƠN', width: 150, container: 'fixed-left'},
          { name: 'serialNo', label: 'SỐ SERI', width: 150, container: 'fixed-left'},
          {
            name: 'invoiceDate', label: 'NGÀY HÓA ĐƠN', format: util.text.formater.compactDate, fieldDataGetter(record) {
              if (!record['invoiceDate']) return '';
              return util.text.formater.compactDate(record['invoiceDate']);
            },
          },
          {
            name: 'total', label: 'TIỀN TRC VAT', dataType: 'double',
            format: (val) => util.text.formater.currency(val, 0)
          },
          {
            name: 'vatCharge', label: 'VAT', dataType: 'double',
            format: (val) => util.text.formater.currency(val, 0)
          },
          {
            name: 'finalCharge', label: 'TIỀN SAU VAT', dataType: 'double',
            format: (val) => util.text.formater.currency(val, 0)
          },
          { name: 'feeName', label: 'TÊN PHÍ', width: 200 },
          { name: 'sellerTaxCode', label: 'MST ĐƠN VỊ BÁN', width: 200 },
          { name: 'buyerTaxCode', label: 'MST ĐƠN VỊ MUA', width: 200 },
          { name: 'sourceFile', label: 'Source' },
        ]
      },
      toolbar: {
        actions: [
        ]
      },
      footer: {
        page: {
          render(ctx: grid.VGridContext) {
            let _ = ctx.uiRoot as entity.DbEntityList;
            let { appContext, pageContext } = _.props;
            return (
              <bs.Toolbar>
                <XlsxExportButton appContext={appContext} pageContext={pageContext} context={ctx}
                   options={{ fileName: 'Summary.xlsx', modelName: 'document' }}
                />
              </bs.Toolbar>
            )
          },
        }
      },

      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table',
            footer: {
              createRecords: (ctx: grid.VGridContext) => this.createRecords(ctx)
            }
          },
        }

      }
    };

    return config;
  }

  createRecords(_ctx: grid.VGridContext) {
    let { plugin } = this.props;
    let records = plugin.getListModel().getFilterRecords();
    let footerRecords = new Array<any>();
    let actives = util.CollectionMath.sum(records, ['finalCharge', 'vatCharge', 'total'], { label: 'Total Selected' });
    footerRecords.push(actives);
    return footerRecords;
  }
}

class XlsxExportButton extends entity.XlsxExportButton {
  initDataSelectModel = (_model: grid.DataSelectModel) => {
    _model.selectMode = 'all'
  }

  override customDataListExportModel = (model: entity.DataListExportModel) => {
    let doubleField =  ['finalCharge', 'vatCharge', 'total']

    for (let field of model.fields) {
      if (doubleField.includes(field.name)) {
        field.dataType = 'double';
      }
    }
    return model;
  }

  render() {
    const { style, disable } = this.props;
    return (
      <bs.Button
        laf={"info"} onClick={this.onExportCustomization}
         style={style} outline disabled={disable} >
        <FeatherIcon.Download size={12} /> {T('XLSX Export')}
      </bs.Button>
    )
  }
}