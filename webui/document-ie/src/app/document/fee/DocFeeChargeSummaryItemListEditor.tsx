import React, {ReactElement} from 'react';
import {module} from "@datatp-ui/erp";
import { bs, input, entity, grid, app, util } from '@datatp-ui/lib';
import * as icon from 'react-feather';
import { T } from "../backend";
import {Document} from "../Document";

const SESSION = app.host.DATATP_HOST.session;

interface DocFeeChargeSummaryItemListEditorProps extends entity.VGridEntityListEditorProps {
  documentType: string
}

export class DocFeeChargeSummaryItemListEditor extends entity.VGridEntityListEditor<DocFeeChargeSummaryItemListEditorProps> {
  onShowInvoice = (item: any) => {
    let { appContext, pageContext } = this.props;
    const companyCode = SESSION.getAccountAcl().getCompanyAcl().companyCode.toUpperCase();

    appContext
      .createHttpBackendCall('DocumentService', 'getDocument', { id: item['documentId'] })
      .withSuccessData((data: any) => {
        let createContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return (
            <Document
              appContext={appCtx} pageContext={pageCtx} storage={new module.storage.CompanyStorage(companyCode)} documentSet={null}
              observer={new entity.BeanObserver(data)}
            />
          );
        }
        pageContext.createPopupPage('show-invoice', 'Invoice', createContent, { size: 'xl' })
      })
      .call();
  }

  showCheckMissingStatusToolTip(record: any): ReactElement {
    let missingField: string = record['missingField']

    if (missingField) {
      if (missingField.includes("-")) {
        let count = missingField.split("-")[0]
        let notFound: string = missingField.split("-")[1]
        const [res, expect] = count.slice(1, -1).split("/").map(Number);
        if (res == 0) {
          return (
            <icon.AlertCircle size={18} className='text-danger' />
          )
        }
        return (
          <bs.Tooltip tooltip={notFound} className='text-warning flex-hbox align-items-center'>
            <icon.CheckCircle size={18} />
            <span className='ms-1'>{count}</span>
          </bs.Tooltip>
        )
      } else {
        return (
          <icon.CheckCircle size={18} className='text-success' />
        )
      }

    }
    return <></>
  }


  createVGridConfig() {
    let onInputChange = (ctx: grid.FieldContext, _oldVal: any, _newVal: any) => {
      const { displayRecord, fieldConfig, gridContext } = ctx;
      let event: grid.VGridCellEvent = {
        row: displayRecord.row,
        field: fieldConfig,
        event: 'Modified',
        data: displayRecord.record
      }
      gridContext.broadcastCellEvent(event);
    }

    let { appContext, pageContext } = this.props;
    let writeCap = pageContext.hasUserWriteCapability();
    let _ = this;
    let config: grid.VGridConfig = {
      record: {
        editor: {
          supportViewMode: ['table'],
          enable: true,
        },
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector()),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          {
            name: 'fileNo', label: T('File No'), width: 160, state: { showRecordState: true },
            editor: {
              type: 'string',
              onInputChange: onInputChange,
              renderCustom(fieldCtx: grid.FieldContext, _onInputChange: grid.OnInputChange) {
                const { fieldConfig, displayRecord, tabIndex, focus } = fieldCtx;
                let item = displayRecord.record;
                return (
                  <input.BBStringField
                    bean={item} field={fieldConfig.name} tabIndex={tabIndex} focus={focus} onInputChange={_onInputChange}/>
                )
              },
            }
          },
          {
            name: 'customer', label: T('Customer'),  width: 250
          },
          {
            name: 'invoiceDate', label: 'Invoice Date', format: util.text.formater.compactDate, fieldDataGetter(record) {
              if (!record['invoiceDate']) return '';
              return util.text.formater.compactDate(record['invoiceDate']);
            },
          },
          {
            name: 'contNo', label: T('Cont No'),
            editor: {
              onInputChange: onInputChange,
              type: 'string',
            }
          },
          {
            name: 'invoiceNo', label: T('Invoice No'),
            editor: {
              onInputChange: onInputChange,
              type: 'string',
            }
          },
          {
            name: 'serialNo', label: T('Serial No'),
            editor: {
              onInputChange: onInputChange,
              type: 'string',
            }
          },
          {
            name: 'sellerTaxCode', label: T('S.Tax Code'), hint: T('Seller Tax Code'),
            editor: {
              onInputChange: onInputChange,
              type: 'string',
            }
          },
          {
            name: 'buyerTaxCode', label: T('B.Tax Code'), hint: T('Buyer Tax Code'),
            editor: {
              onInputChange: onInputChange,
              type: 'string',
            }
          },
          {
            name: 'total', label: T('Total'), hint: T('Total'),
            format: (val) => util.text.formater.currency(val, 0),
            editor: {
              onInputChange: onInputChange,
              type: 'double',
            }
          },
          {
            name: 'vatCharge', label: T('Vat Charge'), hint: T('Vat Charge'),
            format: (val) => util.text.formater.currency(val, 0),
            editor: {
              onInputChange: onInputChange,
              type: 'double',
            }
          },
          {
            name: 'finalCharge', label: T('Final Charge'), hint: T('Final Charge'), dataType: 'double',
            format: (val) => util.text.formater.currency(val, 0),
            editor: {
              onInputChange: onInputChange,
              type: 'double',
            }
          },
          {
            name: 'feeName', label: T('Fee Name'),
            editor: {
              onInputChange: onInputChange,
              type: 'string',
            }
          },
          {
            name: 'sourceFile', label: T('Source File'), hint: T('Source File'), state: { visible: false }, container: 'fixed-right', width: 200
          },
          {
            name: 'processStatus', label: T(''), width: 100, container: 'fixed-right', state: { showRecordState: true },
            filterable: true, filterableType: 'options',
            editor: {
              type: 'string',
              renderCustom(fieldCtx: grid.FieldContext, _onInputChange: grid.OnInputChange) {
                const { fieldConfig, displayRecord, tabIndex, focus } = fieldCtx;
                let item = displayRecord.record;
                if (!item['documentId']) {
                  return (
                    <></>
                  )
                } else {
                  return (
                    <bs.Button laf='link' className='p-0 m-0' onClick={() => _.onShowInvoice(item)}>
                      {_.showCheckMissingStatusToolTip(item)}
                    </bs.Button>
                  )
                }
              },
            }
          },
        ]
      },
      toolbar: {
        actions: [
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_DELETE(!writeCap, 'Delete'),
        ]
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          }
        }

      }
    }

    return config
  }
}