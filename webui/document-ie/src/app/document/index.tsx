import { DOCUMENT_PLUGIN_REGISTRY } from "./DocumentPlugin";
import { DOCUMENT_SET_PLUGIN_REGISTRY } from "./DocumentSetPlugin";

import {DocAccountingChargeSummaryPlugin, DocReceiptChargeSummaryPlugin} from "./accounting/DocAccountingChargeSummary";
import {FCLDocTmsChargeSummary, LCLDocTmsChargeSummary } from "./tms/DocTmsChargeSummary";
import {DocInvoicePlugin, DocReceiptPlugin} from "./inv/DocInvoice";
import {FeeChargeSummaryPlugin, TMSDocChargeSummaryPlugin} from "./tms/TMSDocumentSetAction";
import {AccountingDocChargeSummaryPlugin, ReceiptDocChargeSummaryPlugin} from "./accounting/AccountingDocumentSetAction";
import {DocImagePlugin} from "./img/DocImage";
import {DocFeeChargeSummaryPlugin} from "./fee/DocFeeChargeSummary";

DOCUMENT_SET_PLUGIN_REGISTRY.register(new TMSDocChargeSummaryPlugin())
DOCUMENT_SET_PLUGIN_REGISTRY.register(new FeeChargeSummaryPlugin())
DOCUMENT_SET_PLUGIN_REGISTRY.register(new AccountingDocChargeSummaryPlugin())
DOCUMENT_SET_PLUGIN_REGISTRY.register(new ReceiptDocChargeSummaryPlugin())

DOCUMENT_PLUGIN_REGISTRY.register(new FCLDocTmsChargeSummary())
DOCUMENT_PLUGIN_REGISTRY.register(new LCLDocTmsChargeSummary())
DOCUMENT_PLUGIN_REGISTRY.register(new DocInvoicePlugin())
DOCUMENT_PLUGIN_REGISTRY.register(new DocReceiptPlugin())
DOCUMENT_PLUGIN_REGISTRY.register(new DocAccountingChargeSummaryPlugin())
DOCUMENT_PLUGIN_REGISTRY.register(new DocReceiptChargeSummaryPlugin())
DOCUMENT_PLUGIN_REGISTRY.register(new DocFeeChargeSummaryPlugin())
DOCUMENT_PLUGIN_REGISTRY.register(new DocImagePlugin())

export * from './DocumentSet'
export * from './DocumentList'
export * from './BBRefDocumentSetCategory'
export * from './BBRefDocumentSet'
export * from './models'
export * from './init'
