import React from 'react';
import { bs, input, entity, util, app } from '@datatp-ui/lib';
import { T } from '../backend';
import * as icon from 'react-feather';
import { DocAccountingChargeSummaryItemListEditor } from "./DocAccountingChargeSummaryItemListEditor";
import { AccountingDocChargeSummaryItemList } from "./AccountingDocChargeSummaryItemList";
import {DOCUMENT_PLUGIN_REGISTRY, UIDocumentPlugin} from "../DocumentPlugin";
import {DocReceiptChargeSummaryItemListEditor} from "../receipt/DocReceiptChargeSummaryItemListEditor";
import {ReceiptDocChargeSummaryItemList} from "../receipt/ReceiptDocChargeSummaryItemList";

interface DocAccountingChargeSummaryEditorProps extends entity.AppComplexEntityEditorProps {
  doc: any,
  documentSet: any,
}

export class DocAccountingChargeSummaryEditor extends entity.AppDbComplexEntityEditor<DocAccountingChargeSummaryEditorProps> {

  toAccountingTemplate = () => {
    let { pageContext, observer, doc } = this.props;

    let plugin: UIDocumentPlugin | null;
    plugin = DOCUMENT_PLUGIN_REGISTRY.getPluginByName(doc['type'])

    let createContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      let beans: Array<any> = observer.getComplexArrayProperty('items', []);
      beans.forEach((bean, index) => {
        bean['index'] = index + 1
      })

      if (plugin) {
        return plugin.renderAccountingTemplate(appCtx, pageContext, beans);
      }

      return <div>TODO</div>;
    }
    pageContext.createPopupPage('bfs-one-template', 'BFSOne Template', createContent, { size: 'lg' });
  }

  updateTemplate = () => {
    let { appContext, pageContext, observer } = this.props;
    let summary = observer.getMutableBean()

    appContext
      .createHttpBackendCall('DocumentService', 'updateChargeSummary', { summary: summary })
      .withSuccessData((data: any) => {
        appContext.addOSNotification("success", T("Update Summary Success"));
        observer.replaceWith(data)
        this.forceUpdate();
        this.nextViewId();
      })
      .withFailNotification('danger', "Update Summary Fail!!")
      .call();
  }

  onSave = () => {
    let { appContext, observer } = this.props;
    let accountingDoc = observer.commitAndGet();
    this.loading = true;
    this.forceUpdate();
    appContext
      .createHttpBackendCall('DocumentService', 'saveDocAccountingChargeSummary', { tmsDoc: accountingDoc })
      .withSuccessData(data => {
        observer.replaceWith(data);
        this.onPostCommit(data);
        this.loading = false;
        this.forceUpdate();
      })
      .withFail(() => {
        this.loading = false;
        this.forceUpdate();
      })
      .withEntityOpNotification('commit', 'Save Doc Accounting Summary')
      .call();
  }

  render() {
    let { appContext, pageContext, observer, doc } = this.props;
    const writeCap = pageContext.hasUserWriteCapability();
    let chargeSummary = observer.getMutableBean();

    let plugin: UIDocumentPlugin | null;
    plugin = DOCUMENT_PLUGIN_REGISTRY.getPluginByName(doc['type'])

    return (
      <div className='flex-vbox' key={`tms-doc-${util.IDTracker.next()}`}>
        <bs.VSplit className='flex-grow-1'>
          <bs.VSplitPane className='pr-1' width={'30%'}>
            {plugin?.renderInfoTab(appContext, pageContext, observer)}
            {plugin?.renderSummaryTab(appContext, pageContext, observer)}
            <input.BBTextField
            bean={chargeSummary} field={'description'} label={T("Description")} disable={!writeCap} style={{ height: '8em' }} />
          </bs.VSplitPane>
          <bs.VSplitPane className='flex-vbox border rounded p-1'>
            {plugin?.renderListItem(appContext, pageContext, doc, observer)}
          </bs.VSplitPane>
        </bs.VSplit>

        <bs.Toolbar hide={!writeCap}>
          <entity.WButtonEntityWrite
            appContext={appContext} pageContext={pageContext} label='Update Template'
            onClick={this.updateTemplate} icon={icon.Activity}
          />
          <entity.WButtonEntityWrite appContext={appContext} pageContext={pageContext} label='Create Template'
            onClick={this.toAccountingTemplate} icon={icon.Activity} />
          <entity.WButtonEntityWrite disable={this.loading} icon={icon.Save} label={observer.isNewBean() ? T('Create') : T('Save')}
            appContext={appContext} pageContext={pageContext} onClick={this.onSave} />
        </bs.Toolbar>
      </div>
    )
  }
}

interface DocAccountingChargeSummaryLoadableProps extends app.AppComponentProps {
  data: any;
  onPostCommit?(entity: any): void;
}

export class DocAccountingChargeSummaryLoadable extends app.AppComponent<DocAccountingChargeSummaryLoadableProps> {
  entity: any = null;

  componentDidMount(): void {
    let { appContext, data } = this.props;
    let callback = (result: any) => {
      if (result) {
        this.entity = result;
      } else {
        this.entity = { documentId: result.document['id'], enrichPlugin: data.plugin };
      }
      this.forceUpdate();
    }
    appContext
      .createHttpBackendCall('DocumentService', 'getDocumentIEModel', { docId: data.document['id'] })
      .withSuccessData(callback)
      .call();
  }

  onPostCommit = (entity: any) => {
    let { onPostCommit } = this.props;
    if (onPostCommit) onPostCommit(entity);
  }

  renderEntity(_entity: any): React.ReactNode {
    let { appContext, pageContext, data } = this.props;
    return (
      <DocAccountingChargeSummaryEditor key={`${util.IDTracker.next()}-charge-summary`}
        onPostCommit={this.onPostCommit} doc={data.document} documentSet={data.documentSet}
        appContext={appContext} pageContext={pageContext} observer={new entity.ComplexBeanObserver(_entity)} />
    )
  };

  render(): React.ReactNode {
    if (!this.entity) return this.renderLoading();
    return this.renderEntity(this.entity);
  }
}

export class DocAccountingChargeSummaryPlugin extends UIDocumentPlugin {
  constructor() {
    super('accounting-inv-summary');
  }

  hasPreviewTab(): boolean {
    return false;
  }

  renderAccountingTemplate(_appContext: app.AppContext, _pageContext: app.PageContext, beans: Array<any>): React.JSX.Element {
    return (
      <AccountingDocChargeSummaryItemList
        appContext={_appContext} pageContext={_pageContext} readOnly
        plugin={new entity.DbEntityListPlugin(beans)} />
    );
  }

  renderListItem(_appContext: app.AppContext, _pageContext: app.PageContext, document: any, observer: entity.ComplexBeanObserver): React.JSX.Element {
    return (
      <DocAccountingChargeSummaryItemListEditor className='flex-grow-1 h-100'
        appContext={_appContext} pageContext={_pageContext} documentType={document['type']}
        plugin={observer.createVGridEntityListEditorPlugin('items', [])}
        editorTitle={T('Charge Summary Item')} dialogEditor={true}
      />
    );
  }

  renderInfoTab(_appContext: app.AppContext, _pageContext: app.PageContext, observer: entity.ComplexBeanObserver): React.JSX.Element {
    const writeCap = _pageContext.hasUserWriteCapability();
    let chargeSummary = observer.getMutableBean();

    return (
      <bs.Card header={T("Info")}>
        <bs.Row>
          <bs.Col span={6}>
            <input.BBStringField
              bean={chargeSummary} field={'sellerName'} label={T("Seller Name")} disable={!writeCap} />
          </bs.Col>
          <bs.Col span={6}>
            <input.BBStringField
              bean={chargeSummary} field={'sellerTaxCode'} label={T("TaxCode")} disable={!writeCap} />
          </bs.Col>
        </bs.Row>
        <bs.Row>
          <bs.Col span={6}>
            <input.BBStringField
              bean={chargeSummary} field={'buyerName'} label={T("Buyer Name")} disable={!writeCap} />
          </bs.Col>
          <bs.Col span={6}>
            <input.BBStringField
              bean={chargeSummary} field={'buyerTaxCode'} label={T("TaxCode")} disable={!writeCap} />
          </bs.Col>
        </bs.Row>
      </bs.Card>
    )
  }

  renderSummaryTab(_appContext: app.AppContext, _pageContext: app.PageContext, observer: entity.ComplexBeanObserver): React.JSX.Element {
    const writeCap = _pageContext.hasUserWriteCapability();
    let chargeSummary = observer.getMutableBean();

    return (
      <bs.Card header={T("Charge")}>
        <bs.Row>
          <bs.Col span={4}>
            <input.BBCurrencyField
              bean={chargeSummary} field={'total'} label={T("Total")} disable={!writeCap} />
          </bs.Col>
          <bs.Col span={4}>
            <input.BBCurrencyField
              bean={chargeSummary} field={'totalVatCharge'} label={T("Vat Charge")} disable={!writeCap} />
          </bs.Col>
          <bs.Col span={4}>
            <input.BBCurrencyField
              bean={chargeSummary} field={'finalCharge'} label={T("Final Charge")} disable={!writeCap} />
          </bs.Col>
        </bs.Row>
      </bs.Card>
    )
  }

  render(_appContext: app.AppContext, _pageContext: app.PageContext, data: any, onCommit: (bean: any) => void): React.JSX.Element {
    return (
      <DocAccountingChargeSummaryLoadable key={`accounting-charge-summary-${util.IDTracker.next()}`}
        appContext={_appContext} pageContext={_pageContext} data={data}
        onPostCommit={onCommit} />
    )
  }
}

export class DocReceiptChargeSummaryPlugin extends UIDocumentPlugin {
  constructor() {
    super('receipt-inv-summary');
  }

  hasPreviewTab(): boolean {
    return false;
  }

  renderAccountingTemplate(_appContext: app.AppContext, _pageContext: app.PageContext, beans: Array<any>): React.JSX.Element {
    return (
      <ReceiptDocChargeSummaryItemList
        appContext={_appContext} pageContext={_pageContext} readOnly
        plugin={new entity.DbEntityListPlugin(beans)} />
    );
  }

  renderListItem(_appContext: app.AppContext, _pageContext: app.PageContext, document: any, observer: entity.ComplexBeanObserver): React.JSX.Element {
    return (
      <DocReceiptChargeSummaryItemListEditor className='flex-grow-1 h-100'
        appContext={_appContext} pageContext={_pageContext} documentType={document['type']}
        plugin={observer.createVGridEntityListEditorPlugin('items', [])}
        editorTitle={T('Charge Summary Item')} dialogEditor={true}
      />
    );
  }

  renderInfoTab(_appContext: app.AppContext, _pageContext: app.PageContext, observer: entity.ComplexBeanObserver): React.JSX.Element {
    const writeCap = _pageContext.hasUserWriteCapability();
    let chargeSummary = observer.getMutableBean();

    return (
      <bs.Card header={T("Info")}>
        <bs.Row>
          <bs.Col span={6}>
            <input.BBStringField
              bean={chargeSummary} field={'sellerName'} label={T("Seller Name")} disable={!writeCap} />
          </bs.Col>
          <bs.Col span={6}>
            <input.BBStringField
              bean={chargeSummary} field={'sellerTaxCode'} label={T("TaxCode")} disable={!writeCap} />
          </bs.Col>
        </bs.Row>
        <bs.Row>
          <bs.Col span={6}>
            <input.BBStringField
              bean={chargeSummary} field={'payerName'} label={T("Payer Name")} disable={!writeCap} />
          </bs.Col>
          <bs.Col span={6}>
            <input.BBStringField
              bean={chargeSummary} field={'payerTaxCode'} label={T("TaxCode")} disable={!writeCap} />
          </bs.Col>
        </bs.Row>
      </bs.Card>
    )
  }

  renderSummaryTab(_appContext: app.AppContext, _pageContext: app.PageContext, observer: entity.ComplexBeanObserver): React.JSX.Element {
    const writeCap = _pageContext.hasUserWriteCapability();
    let chargeSummary = observer.getMutableBean();

    return (
      <bs.Card header={T("Charge")}>
        <bs.Row>
          <bs.Col span={12}>
            <input.BBCurrencyField
              bean={chargeSummary} field={'receiptTotal'} label={T("Receipt Total")} disable={!writeCap} />
          </bs.Col>
        </bs.Row>
      </bs.Card>
    )
  }

  render(_appContext: app.AppContext, _pageContext: app.PageContext, data: any, onCommit: (bean: any) => void): React.JSX.Element {
    return (
      <DocAccountingChargeSummaryLoadable key={`receipt-charge-summary-${util.IDTracker.next()}`}
        appContext={_appContext} pageContext={_pageContext} data={data}
        onPostCommit={onCommit} />
    )
  }
}