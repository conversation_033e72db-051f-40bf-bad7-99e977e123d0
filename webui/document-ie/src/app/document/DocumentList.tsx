import React, {ReactElement} from 'react';

import {module} from '@datatp-ui/erp';
import {app, bs, component, entity, grid, sql} from '@datatp-ui/lib';
import * as FeatherIcon from 'react-feather'
import * as icon from 'react-feather'

// import * as common from '../common';
import {T} from "./backend";
import {Document} from "./Document";
import {UIDocumentPrintView} from './DocumentPrintView';
import {IEProcessReport} from './IEProcessReport';
import {DOCUMENT_SET_PLUGIN_REGISTRY, UIDocumentSetPlugin} from "./DocumentSetPlugin";

export class DocumentListPlugin extends entity.DbEntityListPlugin {
  documentSetId: number;

  constructor() {
    super([]);
    this.backend = {
      context: 'company',
      service: 'DocumentService',
      searchMethod: 'searchDocuments',
      deleteMethod: 'deleteDocuments'
    }

    this.searchParams = {
      filters: [
        ...sql.createSearchFilter()
      ],
      optionFilters: [
        sql.createStorageStateFilter([entity.StorageState.ACTIVE, entity.StorageState.ARCHIVED])
      ],
      orderBy: {
        fields: ["modifiedTime"], fieldLabels: ["Modified Time"], selectFields: ["modifiedTime"],
        sort: "DESC"
      },
      maxReturn: 1000,
    }
  }

  withScope(scope: any) {
    this.addSearchParam('scope', scope);
    return this;
  }

  withDocumentSet(documentSetId: number) {
    if (documentSetId) {
      this.documentSetId = documentSetId;
      this.addSearchParam('documentSetId', documentSetId);
    }
    return this;
  }

  withDocumentRoot(documentRootId: number) {
    if (documentRootId) {
      this.addSearchParam('rootId', documentRootId);
    }
    return this;
  }

  withRequestType(requestType: 'None' | 'IE') {
    if (requestType) {
      this.addSearchParam('requestType', requestType);
    }
    return this;
  }

  loadData(uiList: entity.DbEntityList<any>) {
    let params = {
      'params': this.searchParams
    }
    this.createBackendSearch(uiList, params).call();
  }

  backendDelete(uiList: entity.DbEntityList<entity.DbEntityListProps>, targetIds: Array<number>) {
    let params = { docIds: targetIds }
    this.createBackendDelete(uiList, params, targetIds).call();
  }
}

interface DocumentListProps extends entity.DbEntityListProps {
  storage: module.storage.IStorage;
  documentSet?: any;
  viewName?: 'table' | 'aggregation',
  viewType: 'DocumentSet' | 'RequestIE' | 'Cache';
  createDocument?: (document: any) => any;
}
export class DocumentList extends entity.DbEntityList<DocumentListProps> {

  showCheckProcessStatusToolTip(record: any): ReactElement {
    let processNote: string = record['processNote']

    let _INCORRECT = "INCORRECT"
    let _CORRECT = "CORRECT"
    let _PARTIAL = "PARTIAL"

    if (processNote) {
      if (processNote.includes(_INCORRECT)) {
        return (
          <icon.Server size={18} className='text-danger' />
        )
      }

      if (processNote.includes(_CORRECT)) {
        return (
          <icon.Server size={18} className='text-success' />
        )
      }

      if (processNote.includes(_PARTIAL)) {
        let count = processNote.split("|")[0]
        let notFound: string = processNote.split("-")[1]
        return (
          <bs.Tooltip tooltip={notFound} className='text-warning flex-hbox align-items-center'>
            <icon.Server size={18} />
            <span className='ms-1'>{count}</span>
          </bs.Tooltip>
        )
      }
    }
    return <></>
  }

  showCheckMissingStatusToolTip(record: any): ReactElement {
    let missingField: string = record['missingField']

    if (missingField) {
      if (missingField.includes("-")) {
        let count = missingField.split("-")[0]
        let notFound: string = missingField.split("-")[1]
        return (
          <bs.Tooltip tooltip={notFound} className='text-warning flex-hbox align-items-center'>
            <icon.CheckCircle size={18} />
            <span className='ms-1'>{count}</span>
          </bs.Tooltip>
        )
      } else {
        return (
          <icon.CheckCircle size={18} className='text-success' />
        )
      }

    }
    return <></>
  }

  createVGridConfig() {
    let { pageContext, documentSet, viewType, viewName, plugin } = this.props;
    let writeCap = pageContext.hasUserWriteCapability();
    let modCap = pageContext.hasUserModeratorCapability();
    let _ = this;

    let fieldConfig = entity.DbEntityListConfigTool.FIELD_ON_SELECT('name', T('Name'), 250);
    delete fieldConfig.editor;
    let config: grid.VGridConfig = {
      record: {
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector()),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          {
            name: 'missingField', label: T(''), width: 100, container: 'fixed-left', state: { showRecordState: true },
            editor: {
              type: 'string',
              renderCustom(fieldCtx: grid.FieldContext, _onInputChange: grid.OnInputChange) {
                const { fieldConfig, displayRecord, tabIndex, focus } = fieldCtx;
                let item = displayRecord.record;

                return (
                  _.showCheckMissingStatusToolTip(item)
                )
              }
            }
          },
          {
            ...fieldConfig, state: { showRecordState: true }
          },
          { name: 'path', label: T('Path'), width: 250, state: { visible: false } },
          {
            name: 'type', label: T('Document Type'), width: 140, filterable: true, filterableType: 'Options',
            editor: { enable: true, type: 'string', }
          },
          {
            name: 'mediaType', label: T('Media Type'), width: 120, filterable: true, filterableType: 'Options',
            editor: { enable: true, type: 'string' }
          },
          { name: 'requestProcessType', label: T('Req Process'), width: 90, state: { visible: false } },
          {
            name: 'tasks', label: T('Tasks'), width: 300, fieldDataGetter: (record) => {
              if (!record['tasks']) return '';
              let values: Array<string> = [];
              let tasks: Array<any> = record['tasks'];
              for (let task of tasks) {
                let processStatus = task['processStatus'];
                if (processStatus) values.push(processStatus);
              }
              return values.join(', ')
            },
            dataTooltip: true
          },
          {
            name: 'description', label: T('Description'), width: 300,
            editor: { enable: true, type: 'string' }
          },
          {
            name: 'searchableWords', label: T('Searchable Words'), width: 300, dataTooltip: true, state: { visible: false }
          },
          { name: 'documentSetName', label: T('D.Set Name'), width: 150, state: { visible: false } },
          // ...entity.DbEntityListConfigTool.FIELD_ENTITY,
          {
            name: 'processNote', label: T('Note'), width: 100, container: 'fixed-right', state: { showRecordState: true },
            editor: {
              type: 'string',
              renderCustom(fieldCtx: grid.FieldContext, _onInputChange: grid.OnInputChange) {
                const { fieldConfig, displayRecord, tabIndex, focus } = fieldCtx;
                let item = displayRecord.record;

                return (
                  _.showCheckProcessStatusToolTip(item)
                )
              }
            }
          },
        ],
        editor: {
          supportViewMode: ['table'],
          enable: true
        }
      },
      toolbar: {
        actions: [
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_DELETE(!writeCap && viewType == 'RequestIE', "Del"),
        ],
        filters: entity.DbEntityListConfigTool.TOOLBAR_FILTERS(plugin.searchParams ? true : false)
      },
      footer: {
        page: {
          render: (ctx: grid.VGridContext) => {
            let uiList = ctx.uiRoot as DocumentList;
            let { appContext, pageContext, documentSet } = uiList.props;

            let plugin: UIDocumentSetPlugin | null;

            if (documentSet) {
              plugin = DOCUMENT_SET_PLUGIN_REGISTRY.getPluginByName(documentSet['type'])
            } else {
              plugin = null;
            }

            return (
              <bs.Toolbar hide={!writeCap}>
                <entity.WButtonEntityWrite
                  appContext={appContext} pageContext={pageContext} icon={FeatherIcon.File} label={T('Rename File')} onClick={this.onRename} />
                <entity.WButtonEntityWrite
                  appContext={appContext} pageContext={pageContext} icon={FeatherIcon.File} label={T('Organize by HBL')} onClick={this.organizeByHBL} />
                <bs.Popover title={'Document Classification'} className="flex-hbox-grow-0" closeOnTrigger=".btn" offset={[0, 5]}>
                  <bs.PopoverToggle laf='primary'>
                    <FeatherIcon.Layers size={12} /> {'Classify'}
                  </bs.PopoverToggle>
                  <bs.PopoverContent className='flex-vbox'>
                    <bs.Button className='text-start p-2' laf='primary' outline onClick={() => this.onUpdateDocumentTypes(ctx, 'invoice')}>
                      <FeatherIcon.FileText size={12} /> {T('Invoice')}
                    </bs.Button>
                    <bs.Button className='text-start p-2 my-1' laf='primary' outline onClick={() => this.onUpdateDocumentTypes(ctx, 'receipt')}>
                      <FeatherIcon.FileText size={12} /> {T('Receipt')}
                    </bs.Button>
                    <bs.Button className='text-start p-2 my-1' laf='primary' outline onClick={() => this.onUpdateDocumentTypes(ctx, 'tms-fcl-inv-summary')}>
                      <FeatherIcon.FileText size={12} /> {T('FCL Inv Summary')}
                    </bs.Button>
                    <bs.Button className='text-start p-2 my-1' laf='primary' outline onClick={() => this.onUpdateDocumentTypes(ctx, 'tms-lcl-inv-summary')}>
                      <FeatherIcon.FileText size={12} /> {T('LCL Inv Summary')}
                    </bs.Button>
                    <bs.Button className='text-start p-2 my-1' laf='primary' outline onClick={() => this.onUpdateDocumentTypes(ctx, 'fee-summary')}>
                      <FeatherIcon.FileText size={12} /> {T('Fee Summary')}
                    </bs.Button>
                    <bs.Button className='text-start p-2 my-1' laf='primary' outline onClick={() => this.onUpdateDocumentTypes(ctx, 'image')}>
                      <FeatherIcon.FileText size={12} /> {T('Image')}
                    </bs.Button>
                  </bs.PopoverContent>
                </bs.Popover>

                <bs.Popover title={'Document IE'} className="flex-hbox-grow-0" closeOnTrigger=".btn" offset={[0, 5]}>
                  <bs.PopoverToggle laf='primary'>
                    <FeatherIcon.Layers size={12} /> {'Document IE'}
                  </bs.PopoverToggle>
                  <bs.PopoverContent className='flex-vbox-grow-0' style={{ minWidth: 150 }}>
                    <bs.Button laf='primary'
                               outline className='text-start p-2 my-1'
                               onClick={uiList.onBatchRequestIE} hidden={viewType != 'DocumentSet'}>
                      <FeatherIcon.Send size={12} /> {T("Request IE")}
                    </bs.Button>
                    <bs.Button laf='primary' outline className='text-start p-2 my-1' onClick={uiList.onCancelIE}>
                      <FeatherIcon.Send size={12} /> {T("Cancel IE")}
                    </bs.Button>
                    <bs.Button laf='primary' outline className='text-start p-2 my-1' onClick={uiList.onProcessIE} hidden={!modCap || viewType != 'RequestIE'}>
                      <FeatherIcon.Command size={12} /> {T("Process IE")}
                    </bs.Button>
                  </bs.PopoverContent>
                </bs.Popover>

                {plugin ? plugin.renderActionButton(appContext, pageContext, uiList, viewType) : <></>}

                <bs.Popover title={'Upload/Download'} className="flex-hbox-grow-0" closeOnTrigger=".btn" offset={[-10, 5]}>
                  <bs.PopoverToggle laf='primary'>
                    <FeatherIcon.Layers size={12} /> {'Transfer'}
                  </bs.PopoverToggle>
                  <bs.PopoverContent className='flex-vbox-grow-0' style={{ minWidth: 150 }}>
                    <bs.Button laf='primary' outline className='text-start p-2 my-1' onClick={uiList.onMergePdfDocuments}>
                      <FeatherIcon.Printer size={12} /> {T("Merge PDFs")}
                    </bs.Button>
                    <bs.Button laf='primary' outline className='text-start p-2 my-1' onClick={uiList.onDownload}>
                      <FeatherIcon.Download size={12} /> {T("Download")}
                    </bs.Button>
                    <bs.Button
                      laf='primary' outline className='text-start p-2 my-1'
                      onClick={uiList.onShowUpload} hidden={!documentSet}>
                      <FeatherIcon.Upload size={12} /> {T("Upload")}
                    </bs.Button>
                  </bs.PopoverContent>
                </bs.Popover>

                <entity.WButtonEntityWrite
                  appContext={appContext} pageContext={pageContext} icon={FeatherIcon.Save} label={T('Save')} onClick={this.onSave} />
              </bs.Toolbar>
            );
          }
        },
      },
      view: {
        currentViewName: viewName ? viewName : 'table',
        availables: {
          table: {
            viewMode: 'table'
          },
          aggregation: {
            viewMode: 'aggregation',
            treeWidth: 150,
            createAggregationModel(_ctx: grid.VGridContext) {
              let model = new grid.AggregationDisplayModel('All', false);
              model.addAggregation(new grid.ValueAggregation(T('Document Set'), 'documentSetLabel', true).withSortBucket('desc'));
              return model;
            },
          },
        }
      }
    };
    return config;
  }

  organizeByHBL = () => {
    let { appContext } = this.props;
    let records = this.getVGridContext().model.getSelectedRecords();
    if (records.length === 0) {
      bs.notificationShow("warning", T("No records were selected"));
      return;
    }
    for (let record of records) {
      if (record.type != 'invoice') {
        bs.notificationShow("danger", "Error", T("Only invoice document can be organized by House Bill"));
        return;
      }
    }
    let ids = records.map((record) => record['id']);

    appContext.createHttpBackendCall('DocumentService', 'linkInvDocumentsToHouseBillDocSets', { docIds: ids })
      .withSuccessNotification("success", T('Success'))
      .call();
  }

  onSave = () => {
    let { appContext, plugin } = this.props;
    let records = plugin.getListModel().getModifiedRecords();
    appContext.createHttpBackendCall('DocumentService', 'bulkSaveDocuments', { docs: records })
      .withSuccessData((_data: any) => {
        appContext.addOSNotification("success", T('Save Success'));
        this.reloadData();
      })
      .call();
  }


  onRename = () => {
    let { appContext } = this.props;
    let records = this.getVGridContext().model.getSelectedRecords();
    if (records.length === 0) {
      bs.notificationShow("warning", T("No records were selected"));
      return;
    }
    for (let record of records) {
      let missingField: string = record['missingField']
      if (missingField && missingField.includes("-")) {
        bs.notificationShow("danger", T("Error"), T(`Document ${record['name']} has not been verified and therefore cannot be renamed.`));
        return;
      }
    }
    let ids = records.map((record) => record['id']);
    appContext.createHttpBackendCall('DocumentService', 'renameDocuments', { docIds: ids })
      .withSuccessData((data: any) => {
        let docs: Array<any> = data as Array<any>;
        for (let record of records) {
          for (let doc of docs) {
            if (record.id === doc.id) {
              record.name = doc.name;
              record.path = doc.path;
            }
          }
        }
        this.getVGridContext().getVGrid().forceUpdateView();
      }).withSuccessNotification("success", T('Success'))
      .call();
  }

  onUpdateDocumentTypes(ctx: grid.VGridContext, docType: string) {
    let uiList = ctx.uiRoot as DocumentList;
    let { appContext, plugin } = uiList.props;
    if (!this._ensureSelectedRecords()) return;
    let selectedIds = plugin.getListModel().getSelectedRecordIds();
    appContext.createHttpBackendCall('DocumentService', 'updateDocumentTypes', { docIds: selectedIds, docType: docType })
      .withSuccessData(_data => {
        plugin.getListModel().getSelectedRecords().forEach(sel => sel['type'] = docType);
        this.getVGridContext().getVGrid().forceUpdateView();
      })
      .call();
  }

  onProcessIE = () => {
    let { appContext, pageContext } = this.props;
    if (!this._ensureSelectedRecords()) return;
    let selectedIds = this.getVGridContext().model.getSelectedRecordIds();
    let acceptMediaTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel', 'application/pdf'
    ]
    for (let record of this.getVGridContext().model.getSelectedRecords()) {
      if (!acceptMediaTypes.includes(record.mediaType)) {
        bs.notificationShow("warning", T("Select xlsx only!"));
        return;
      }
    }

    appContext
      .createHttpBackendCall('DocumentService', 'processIE', { documentIds: selectedIds })
      .withSuccessData((report) => {
        this.nextViewId();
        this.forceUpdate();
        let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return (
            <IEProcessReport appContext={appCtx} pageContext={pageCtx} report={report} />
          );
        }
        pageContext.createPopupPage("ie-process-report", T("IE Process Report"), createAppPage, { size: 'xl' });

      })
      .withSuccessNotification('success', 'Process Success')
      .withFailNotification('danger', 'Process Failed')
      .call();
  }

  onDownload = () => {
    let { appContext, documentSet } = this.props;
    let selectedIds = this.getVGridContext().model.getSelectedRecordIds();
    if (selectedIds.length === 0) {
      bs.notificationShow("warning", T("No records were selected"));
      return;
    }
    let params = { documentIds: selectedIds, keepFileName: documentSet ? true : false }
    appContext
      .createHttpBackendCall('DocumentService', 'downloadDocuments', params)
      .withSuccessData((data: any) => {
        module.common.StoreInfo.privateDownload(data);
      })
      .withSuccessNotification('success', 'Request IE successfully')
      .call();
  }

  onShowUpload = () => {
    let { appContext, pageContext } = this.props;
    let rest = appContext.getServerContext().getRestClient();
    let onUpload = this.onUpload;
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <div className='flex-vbox' style={{ minHeight: 450 }}>
          <component.WUploadResourceList
            appContext={appCtx} pageContext={pageCtx}
            multiple={true} rest={rest} onUpload={onUpload} />
        </div>
      );
    }
    pageContext.createPopupPage("upload", T("Upload"), createAppPage);
  }

  onUpload = (uploadResources: Array<component.UploadResource>) => {
    let { appContext, documentSet, storage, createDocument, onModifyBean } = this.props;
    let params = { documentSetId: documentSet.id, uploadResources: uploadResources };
    appContext
      .createHttpBackendCall('DocumentService', 'uploadDocuments', params)
      .withSuccessData((_data: any) => {
        if (onModifyBean) onModifyBean(null);
      })
      .withSuccessNotification('info', 'Upload Successfully!')
      .call();
  }

  onRenderLoading() {
    return (
      <div className='flex-vbox'>
        <div className='mx-auto my-auto text-center'>
          <div
            className="spinner-border text-primary mb-3"
            role="status"
            style={{ width: '4rem', height: '4rem' }}
          >
            <span className="visually-hidden">Loading...</span>
          </div>
          <h5 className="text-muted">Processing your request...</h5>
          <small className="text-secondary">Please wait a moment ✨</small>
        </div>
      </div>
    );
  }

  renderDialogConfirmMessage(): ReactElement {
    return (
      <div className="mb-3 d-flex align-items-start">
        <div>
          <strong>Warning:</strong> Verified data will be <strong>deleted</strong>. You will need to verify again.
        </div>
      </div>
    )
  }

  onCreateAccountingSummary = () => {
    let { pageContext } = this.props;
    if (!this._ensureSelectedRecords()) return;
    const createContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      let selectedIds = this.getVGridContext().model.getSelectedRecordIds();
      return (
        <UIConfirmMessage
          appContext={appCtx} pageContext={pageCtx} docIds={selectedIds}
          processMethod={'createAccountingChargeSummary'}
          onConfirm={() => {
            pageCtx.back();
            this.nextViewId();
            this.reloadData();
          }} />
      )
    }
    pageContext.createPopupPage('', '', createContent, { backdrop: 'static' });
  }

  onCreateReceiptSummary = () => {
    let { pageContext } = this.props;
    if (!this._ensureSelectedRecords()) return;
    const createContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      let selectedIds = this.getVGridContext().model.getSelectedRecordIds();
      return (
        <UIConfirmMessage
          appContext={appCtx} pageContext={pageCtx} docIds={selectedIds}
          processMethod={'createReceiptChargeSummary'}
          onConfirm={() => {
            pageCtx.back();
            this.nextViewId();
            this.reloadData();
          }} />
      )
    }
    pageContext.createPopupPage('', '', createContent, { backdrop: 'static' });
  }

  onCreateFeeSummary = () => {
    let { pageContext } = this.props;
    if (!this._ensureSelectedRecords()) return;
    const createContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      let selectedIds = this.getVGridContext().model.getSelectedRecordIds();
      return (
        <UIConfirmMessage
          appContext={appCtx} pageContext={pageCtx} docIds={selectedIds}
          processMethod={'createFeeChargeSummary'}
          onConfirm={() => {
            pageCtx.back();
            this.nextViewId();
            this.reloadData();
          }} />
      )
    }
    pageContext.createPopupPage('', '', createContent, { backdrop: 'static' });
  }

  onBatchRequestIE = () => {
    let { pageContext } = this.props;
    if (!this._ensureSelectedRecords()) return;
    const createContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      let selectedIds = this.getVGridContext().model.getSelectedRecordIds();
      return (
        <UIConfirmMessage
          appContext={appCtx} pageContext={pageCtx} docIds={selectedIds}
          processMethod={'batchRequestDocumentIE'}
          onConfirm={() => {
            pageCtx.back();
            this.nextViewId();
            this.reloadData();
          }} />
      )
    }
    pageContext.createPopupPage('', '', createContent, { backdrop: 'static' });
  }

  onRequestIE = () => {
    let { appContext, pageContext } = this.props;
    if (!this._ensureSelectedRecords()) return;
    let selectedIds = this.getVGridContext().model.getSelectedRecordIds();

    appContext
      .createHttpBackendCall('DocumentService', 'requestDocumentIE', { ids: selectedIds })
      .withSuccessData((report: any) => {
        this.nextViewId();
        this.forceUpdate();
        let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return (
            <IEProcessReport appContext={appCtx} pageContext={pageCtx} report={report} />
          );
        }
        pageContext.createPopupPage("ie-process-report", T("IE Process Report"), createAppPage, { size: 'xl' });
      })
      .withSuccessNotification('success', 'Request IE successfully')
      .withFailNotification('danger', 'Process Failed')
      .call();
  }

  onCancelIE = () => {
    let { appContext } = this.props;
    if (!this._ensureSelectedRecords()) return;
    let selectedIds = this.getVGridContext().model.getSelectedRecordIds();

    appContext
      .createHttpBackendCall('DocumentService', 'cancelDocumentIE', { ids: selectedIds })
      .withSuccessData((data: any) => {
        this.nextViewId();
        this.reloadData();
      })
      .withSuccessNotification('success', 'Request IE successfully')
      .call();
  }

  onMergePdfDocuments = () => {
    let { pageContext, documentSet } = this.props;
    let selectedIds = this.vgridContext.model.getSelectedRecordIds();
    if (!this._ensureSelectedRecords()) return;

    for (let document of this.vgridContext.model.getSelectedRecords()) {
      if (document.mediaType !== 'application/pdf') {
        bs.notificationShow("warning", T("Select PDF file only!"));
        return;
      }
    }
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <UIDocumentPrintView
          appContext={appCtx} pageContext={pageCtx} title={documentSet ? documentSet.name : "Document"} documentIds={selectedIds} />
      );
    }
    pageContext.createPopupPage("merged-pdf-documents", T("Merged PDF Documents"), createAppPage, { size: 'xl', zIndex: 9999 });
  }

  _ensureSelectedRecords() {
    let selectedIds = this.vgridContext.model.getSelectedRecordIds();
    if (selectedIds.length === 0) {
      bs.notificationShow("warning", T("No records were selected"));
      return false;
    }
    return true;
  }

  onDefaultSelect(dRecord: grid.DisplayRecord) {
    let { appContext, pageContext, storage, documentSet } = this.props;
    appContext
      .createHttpBackendCall('DocumentService', 'getDocument', { id: dRecord.record.id })
      .withSuccessData((data: any) => {
        let createPageContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return (
            <Document
              appContext={appCtx} pageContext={pageCtx} storage={storage} documentSet={documentSet}
              observer={new entity.BeanObserver(data)}
              onPostCommit={(_entity) => {
                dRecord.record['hashtag'] = 'Record modified';
                this.getVGridContext().getVGrid().forceUpdateView();
              }}
            />
          );
        }
        pageContext.addPageContent('document', T(`Document`), createPageContent);
      })
      .call();
  }
}

interface UIConfirmMessageProps extends app.AppComponentProps {
  docIds: any[];
  processMethod: string;
  onConfirm?: (entity: any) => void;
}
class UIConfirmMessage extends app.AppComponent<UIConfirmMessageProps> {
  renderLoading() {
    return (
      <div className='flex-vbox'>
        <div className='mx-auto my-auto text-center'>
          <div
            className="spinner-border text-primary mb-3"
            role="status"
            style={{ width: '4rem', height: '4rem' }}
          >
            <span className="visually-hidden">Loading...</span>
          </div>
          <h5 className="text-muted">Processing your request...</h5>
          <small className="text-secondary">Please wait a moment ✨</small>
        </div>
      </div>
    );
  }

  onConfirm = () => {
    let { appContext, docIds, processMethod, pageContext, onConfirm } = this.props;
    appContext
      .createHttpBackendCall('DocumentService', processMethod, { ids: docIds })
      .withSuccessData((data: any) => {
        if (onConfirm) onConfirm(data);
      })
      .withSuccessNotification('success', 'Request IE successfully')
      .withFail(() => {
        pageContext.back();
      })
      .call();
    this.markLoading(true);
    this.forceUpdate();
  }

  onCancel = (_evt: React.MouseEvent) => {
    let { pageContext } = this.props;
    pageContext.back();
  }

  render(): React.ReactNode {
    if (this.isLoading()) return this.renderLoading();
    return (
      <div className='flex-vbox'>
        <div className="modal-header">
          <h5 className="modal-title">{T('Confirm Request')}</h5>
          <button className="btn p-1" type="button" data-bs-dismiss="modal" aria-label="Close">
            <FeatherIcon.XSquare size={16} />
          </button>
        </div>
        <div>
          <strong>Warning:</strong> Verified data will be <strong>deleted</strong>. You will need to verify again.
        </div>
        <div className='flex-hbox-grow-0 justify-content-end my-2'>
          <bs.Button laf='secondary' outline className="px-2 py-1 mx-1" onClick={this.onCancel}>
            <FeatherIcon.X size={12} /> Cancel
          </bs.Button>
          <bs.Button laf='secondary' outline className="px-2 py-1 mx-1" onClick={this.onConfirm}>
            <FeatherIcon.Check size={12} /> OK
          </bs.Button>
        </div>
      </div>
    )
  }
}