import React from 'react';
import { module } from '@datatp-ui/erp';
import { bs, input, entity } from '@datatp-ui/lib';
import { T } from "./backend";
import { DocumentList, DocumentListPlugin } from './DocumentList';
import {BBDocumentSetTagSelector} from "./WDocumentSetTag";


export class DocumentSetEditor extends entity.AppDbEntityEditor {
  render() {
    let { observer, readOnly, appContext, pageContext } = this.props;
    let bean = observer.getMutableBean();

    let writeCap = pageContext.hasUserWriteCapability();

    let typeOpts = ['tms-inv-summary', 'inv-summary', 'receipt-summary', 'fee-summary', 'other'];
    let typeLabelOpts = ['TMS Invoice Summary', 'Invoice Summary', 'Receipt Summary', 'Fee Summary', 'Other'];

    let html = (
      <div className='flex-vbox'>
        <div className='flex-vbox p-1'>
          <h5>Info</h5>
          <div>
            <input.BBStringField bean={bean} field={'name'} label={T('Name')} inputObserver={observer} disable={!writeCap} />
            <input.BBStringField bean={bean} field={'path'} label={T('Path')} inputObserver={observer} disable />
            <input.BBSelectField
              disable={!writeCap} label='Type'
              bean={bean} field={'type'} options={typeOpts} optionLabels={typeLabelOpts} />
            <div className='flex-hbox-grow-0 align-items-center'>
              <label className='form-label'>{T('Tags')}</label>
              <BBDocumentSetTagSelector
                appContext={appContext} pageContext={pageContext}
                labelField={'name'} labelBeans={bean['tags'] ? bean['tags']: []}
              />
            </div>
            <input.BBTextField bean={bean} field={'description'} label={T('Description')} inputObserver={observer} disable={!writeCap} />
          </div>
        </div>
        <bs.Toolbar hide={!writeCap || readOnly}>
          <entity.ButtonEntityCommit
            appContext={appContext} pageContext={pageContext} observer={observer}
            commit={{
              entityLabel: 'DocumentSet', context: 'company', service: 'DocumentService', commitMethod: 'saveDocumentSet'
            }}
            onPostCommit={this.onPostCommit} />
        </bs.Toolbar>
      </div>
    );
    return html;
  }
}

interface DocumentSetProps extends entity.AppDbEntityEditorProps {
  storage: module.storage.IStorage;
}

export class DocumentSet extends entity.AppDbEntityEditor<DocumentSetProps> {

  onModifyBean = (bean: any, action?: entity.ModifyBeanActions) => {
    this.nextViewId();
    this.forceUpdate();
  }

  render() {
    let { appContext, pageContext, observer, storage } = this.props;
    let documentSet = observer.getMutableBean();
    const writeCap = pageContext.hasUserWriteCapability();
    let html = (
      <div className='flex-vbox'>
        <bs.VSplit>
          <bs.VSplitPane className='pr-1' width={300}>
            <DocumentSetEditor appContext={appContext} pageContext={pageContext} observer={observer} readOnly={!writeCap} />
          </bs.VSplitPane>
          <bs.VSplitPane>
            <h5>Documents</h5>
            <DocumentList key={`${this.viewId}-documents`}
              appContext={appContext} pageContext={pageContext} documentSet={documentSet} viewType='DocumentSet'
              plugin={new DocumentListPlugin().withDocumentSet(documentSet.id)} storage={storage}
              onModifyBean={this.onModifyBean} />
          </bs.VSplitPane>
        </bs.VSplit>
      </div>
    )
    return html;
  }
}