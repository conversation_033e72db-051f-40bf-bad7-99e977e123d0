import React from 'react';

import { module } from '@datatp-ui/erp';
import { grid, bs, sql, app, entity } from '@datatp-ui/lib';

import { T } from "./backend";
import { DocumentSet, DocumentSetEditor } from "./DocumentSet";

export class DocumentSetListPlugin extends entity.DbEntityListPlugin {
  categoryId: number;
  constructor() {
    super([]);
    this.backend = {
      context: 'company',
      service: 'DocumentService',
      searchMethod: 'searchDocumentSets',
      deleteMethod: 'deleteDocumentSets'
    }

    this.searchParams = {
      filters: [
        ...sql.createSearchFilter()
      ],
      optionFilters: [
        sql.createStorageStateFilter([entity.StorageState.ACTIVE, entity.StorageState.ARCHIVED])
      ],
      orderBy: {
        fields: ["modifiedTime"], fieldLabels: ["Modified Time"], selectFields: ["modifiedTime"],
        sort: "DESC"
      },
      maxReturn: 1000,
    }
  }

  loadData(uiList: entity.DbEntityList<any>) {
    let params = {
      'params': this.searchParams
    }
    this.createBackendSearch(uiList, params).call();
  }

  withScope(scope: any) {
    this.addSearchParam('scope', scope);
    return this;
  }

  withCategory(categoryId: number) {
    this.categoryId = categoryId;
    this.addSearchParam('documentCategoryId', categoryId);
    return this;
  }

  backendDelete(uiList: entity.DbEntityList<entity.DbEntityListProps>, targetIds: Array<number>) {
    let params = { docSetIds: targetIds }
    this.createBackendDelete(uiList, params, targetIds).call();
  }
}

interface DocumentSetListProps extends entity.DbEntityListProps {
  storage: module.storage.IStorage;
}
export class DocumentSetList extends entity.DbEntityList<DocumentSetListProps> {

  createVGridConfig() {
    let { pageContext } = this.props;
    let writeCap = pageContext.hasUserWriteCapability();
    let modCap = pageContext.hasUserModeratorCapability();
    let config: grid.VGridConfig = {
      title: T('Document Sets'),
      record: {
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector()),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          entity.DbEntityListConfigTool.FIELD_ON_SELECT('name', T('Name'), 250),
          { name: 'path', label: T('Path'), width: 200, state: { visible: false } },
          {
            name: 'type', label: T('Type'), width: 150, filterable: true, filterableType: 'Options',
            fieldDataGetter: (record: any) => {
              let typeEnum = {
                'tms-inv-summary': 'Tms Inv Summary',
                'inv-summary': 'Inv Summary',
                'receipt-summary': 'Receipt Summary',
                'fee-summary': 'Fee Summary',
                'other': 'Other'
              };

              return typeEnum[record['type'] as keyof typeof typeEnum]
            }
          },
          { name: 'tags', label: T('Tags'), width: 100, filterable: true, filterableType: 'Options' },
          { name: 'description', label: T('Description'), width: 200 },
          ...entity.DbEntityListConfigTool.FIELD_ENTITY,
        ],
      },
      toolbar: {
        actions: [
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_ADD(!writeCap, 'Add'),
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_DELETE(!modCap, 'Del')
        ],
        filters: entity.DbEntityListConfigTool.TOOLBAR_FILTERS(false)
      },
      // control: {
      //   width: 250,
      //   render: (ctx: grid.VGridContext) => {
      //     let uiList = ctx.uiRoot as DocumentSetList;
      //     let { appContext, pageContext, plugin } = uiList.props;
      //     let pluginImpl = plugin as DocumentSetListPlugin;
      //     return (
      //       <DocumentSetCategoryDocumentSetExplorer
      //         appContext={appContext} pageContext={pageContext} plugin={pluginImpl} context={ctx} />
      //     );
      //   }
      // },
      footer: {
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          },
          aggregation: {
            viewMode: 'aggregation',
            treeWidth: 150,
            createAggregationModel(_ctx: grid.VGridContext) {
              let model = new grid.AggregationDisplayModel('All', false);
              model.addAggregation(new grid.ValueAggregation(T('Document Set Type'), 'type', true));
              return model;
            },
          },
        }
      }
    };
    return config;
  }

  onAddDocumentSet = (entity: any, uiEditor?: app.AppComponent) => {
    let { plugin } = this.props;
    if (uiEditor) {
      let uiEditorPageCtx = uiEditor.props.pageContext;
      uiEditorPageCtx.back({ reload: true });
    }
    plugin.getListModel().addRecord(entity);
    this.getVGridContext().getVGrid().forceUpdateView(true);
  }

  onDefaultSelect(dRecord: grid.DisplayRecord) {
    let { appContext, pageContext, storage } = this.props;
    appContext
      .createHttpBackendCall('DocumentService', 'getDocumentSet', { id: dRecord.record.id })
      .withSuccessData((data: any) => {
        let createPageContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return (
            <DocumentSet
              appContext={appCtx} pageContext={pageCtx} storage={storage}
              observer={new entity.BeanObserver(data)} />
          );
        }
        pageContext.addPageContent('document-set', T(`Document Set`), createPageContent);
      })
      .call();
  }

  onNewAction = () => {
    let { pageContext, plugin, storage } = this.props;
    let pluginImpl = plugin as DocumentSetListPlugin;

    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      let newDocumentSet = {}
      return (
        <DocumentSetEditor
          appContext={appCtx} pageContext={pageCtx}
          observer={new entity.BeanObserver(newDocumentSet)} onPostCommit={this.onAddDocumentSet} />
      );
    }
    pageContext.createPopupPage("document-set", T("Document Set"), createAppPage, { backdrop: "static" });
  }

  filterByCategory(category: any) {
    let { plugin } = this.props;
    let categoryId = category ? category.id : null;
    (plugin as DocumentSetListPlugin).withCategory(categoryId);
    this.reloadData();
  }

}