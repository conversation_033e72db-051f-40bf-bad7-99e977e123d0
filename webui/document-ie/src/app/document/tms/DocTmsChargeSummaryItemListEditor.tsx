import React, { ReactElement } from 'react';
import { module } from "@datatp-ui/erp";
import { bs, input, entity, grid, app, util } from '@datatp-ui/lib';
import * as icon from 'react-feather';
import { T } from "../backend";
import { BBRefNameFeeDesc } from './BBRefNameFeeDesc';
import { BFSOneHwbList } from './BFSOneHwbList';
import { DocInvChargeSummaryPluginManager } from './DocInvChargeSummaryManager';
import { Document } from "../Document";

const SESSION = app.host.DATATP_HOST.session;

export class DocTmsChargeSummaryItemForm extends entity.AppDbEntity {
  render() {
    let { pageContext, observer, readOnly } = this.props;
    let writeCap = pageContext.hasUserWriteCapability() && !readOnly;
    let chargeSummaryItem = observer.getMutableBean();

    return (
      <div className='container-fluid'>
        <bs.Row>
          <bs.Col span={6}>
            <input.BBStringField
              bean={chargeSummaryItem} field='fileNo' label={T("File No")} disable={!writeCap} />
          </bs.Col>
          <bs.Col span={6}>
            <input.BBStringField
              bean={chargeSummaryItem} field='customerName' label={T("Customer Name")} disable={!writeCap} />
          </bs.Col>
        </bs.Row>
        <bs.Row>
          <bs.Col span={12}>
            <input.BBStringField
              bean={chargeSummaryItem} field='declarationNumber' label={T("Declaration Number")} disable={!writeCap} />
          </bs.Col>
        </bs.Row>
        <bs.Row>
          <bs.Col span={6}>
            <input.BBStringField
              bean={chargeSummaryItem} field='containerNo' label={T("Container No")} disable={!writeCap} />
          </bs.Col>
          <bs.Col span={6}>
            <input.BBStringField
              bean={chargeSummaryItem} field='invoiceNo' label={T("External Invoice Number")} disable={!writeCap} />
          </bs.Col>
        </bs.Row>

        <bs.Row>
          <bs.Col span={12}>
            <input.BBDoubleField
              bean={chargeSummaryItem} field='total' label={T("Total")} disable={!writeCap} />
          </bs.Col>
        </bs.Row>

        <input.BBTextField bean={chargeSummaryItem} field='description' label={T("Description")} disable={!writeCap} style={{ height: '4em' }} />
      </div>
    )
  }
}

interface DocTmsChargeSummaryItemListEditorProps extends entity.VGridEntityListEditorProps {
  documentType: string
}
export class DocTmsChargeSummaryItemListEditor extends entity.VGridEntityListEditor<DocTmsChargeSummaryItemListEditorProps> {
  bfsOneHeaderRender(_ctx: grid.VGridContext, _field: grid.FieldConfig, headerEle: any) {
    return (
      <div className='flex-hbox align-items-center text-warning'>
        <icon.Bookmark className='mx-1' size={15} />
        {headerEle}
      </div>
    )
  }

  cssCheckField = (record: any, field: string) => {
    if (!record[field]) return 'bg-danger bg-opacity-25';
    return '';
  }

  onShowInvoice = (item: any) => {
    let { appContext, pageContext } = this.props;
    const companyCode = SESSION.getAccountAcl().getCompanyAcl().companyCode.toUpperCase();

    appContext
      .createHttpBackendCall('DocumentService', 'getDocument', { id: item['invoiceDocId'] })
      .withSuccessData((data: any) => {
        let createContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return (
            <Document
              appContext={appCtx} pageContext={pageCtx} storage={new module.storage.CompanyStorage(companyCode)} documentSet={null}
              observer={new entity.BeanObserver(data)}
            />
          );
        }
        pageContext.createPopupPage('show-invoice', 'Invoice', createContent, { size: 'xl' })
      })
      .call();
  }

  onSearchBFSOneHwb = (item: any) => {
    let { pageContext, documentType } = this.props;
    let plugin = DocInvChargeSummaryPluginManager.getPlugin(documentType);
    let listPlugin = new entity.DbEntityListPlugin();
    if (plugin) {
      listPlugin = plugin.getBFSOneHwbListPlugin(item);
    }
    let createContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <bs.VSplit>
          <bs.VSplitPane width={450} >
            <DocTmsChargeSummaryItemForm
              appContext={appCtx} pageContext={pageCtx} readOnly
              observer={new entity.BeanObserver(item)} />
          </bs.VSplitPane>
          <bs.VSplitPane>
            <BFSOneHwbList type='selector'
              appContext={appCtx} pageContext={pageCtx} plugin={listPlugin}
              onSelect={(_appCtx: app.AppContext, _pageCtx: app.PageContext, _bean: any) => {
                item['fileNo'] = _bean['transId'];
                item['hwbNo'] = _bean['hwbNo'];
                item['bfsTransDate'] = _bean['transDate'];
                item['enrichStatus'] = 'MANUAL';
                pageCtx.back();
                this.vgridContext.getVGrid().forceUpdateView();
              }}
            />
          </bs.VSplitPane>
        </bs.VSplit>
      )
    }
    pageContext.createPopupPage('bfs-one-hwb', 'BFS One HWB', createContent, { size: 'lg' })
  }

  buildPluginCol() {
    let { documentType } = this.props;
    let plugin = DocInvChargeSummaryPluginManager.getPlugin(documentType);
    if (plugin) {
      return plugin.buildGridColumnConfig(this.vgridContext);
    }
    return [];
  }

  onInputChange = (ctx: grid.FieldContext, _oldVal: any, _newVal: any) => {
    const { displayRecord, fieldConfig, gridContext } = ctx;
    grid.getRecordState(displayRecord.record).markModified();
    let event: grid.VGridCellEvent = {
      row: displayRecord.row,
      field: fieldConfig,
      event: 'Modified',
      data: displayRecord.record
    }
    gridContext.broadcastCellEvent(event);
    this.forceUpdate();
  }

  onInputChangeSellerTaxCode = (item: any, field: string, _oldVal: any, _newVal: any) => {
    item['bfsOneSellerPartnerId'] = null;
    grid.getRecordState(item).markModified();
    this.forceUpdate();
  }

  onInputChangeAndClearHwb = (item: any, field: string, _oldVal: any, _newVal: any) => {
    if (field == 'fileNo') item['originFileNo'] = item['fileNo'];
    item['hwbNo'] = null;
    item['enrichStatus'] = null;
    grid.getRecordState(item).markModified();
    this.forceUpdate();
  };

  showCheckVerifyStatusToolTip(record: any) {
    let verifyNote: string = record['verifyNote']

    const countOccurrences = (text: string, search: string) => {
      let count = 0;
      let pos = text.indexOf(search);
      while (pos !== -1) {
        count++;
        pos = text.indexOf(search, pos + 1);
      }
      return count;
    };

    let element: ReactElement = <></>

    const notVerifyCount = countOccurrences(verifyNote, "Not Verify")
    const botVerifyCount = countOccurrences(verifyNote, "Bot Verify")
    const botFillCount = countOccurrences(verifyNote, "Bot Fill")

    console.log(verifyNote)

    if (botFillCount > 0) {
      // Yellow
      element = (
        <bs.Button laf='link' className='p-0 m-0' onClick={() => this.onShowInvoice(record)}>
          <icon.UserCheck size={12} style={{ marginRight: 5 }} className='text-warning' />
          {botFillCount > 0 && (
            <div className="badge bg-warning text-dark p-1 mb-1" style={{ fontSize: '0.6rem', marginRight: 2 }}>
              {botFillCount}
            </div>
          )}
          {notVerifyCount > 0 && (
            <div className="badge bg-danger p-1" style={{ fontSize: '0.6rem' }}>
              {notVerifyCount}
            </div>
          )}
        </bs.Button>
      )
    }

    if (notVerifyCount > 0) {
      // Red
      element = (
        <bs.Button laf='link' className='p-0 m-0' onClick={() => this.onShowInvoice(record)}>
          <icon.UserCheck size={12} style={{ marginRight: 5 }} className='text-danger' />
          {botFillCount > 0 && (
            <div className="badge bg-warning text-dark p-1 mb-1" style={{ fontSize: '0.6rem', marginRight: 2 }}>
              {botFillCount}
            </div>
          )}
          {notVerifyCount > 0 && (
            <div className="badge bg-danger p-1" style={{ fontSize: '0.6rem' }}>
              {notVerifyCount}
            </div>
          )}
        </bs.Button>
      )
    }

    // Green
    element = (
      <bs.Button laf='link' className='p-0 m-0' onClick={() => this.onShowInvoice(record)}>
        <icon.UserCheck size={12} style={{ marginRight: 5 }} className='text-success' />
        {botFillCount > 0 && (
          <div className="badge bg-warning text-dark p-1 mb-1" style={{ fontSize: '0.6rem', marginRight: 2 }}>
            {botFillCount}
          </div>
        )}
        {notVerifyCount > 0 && (
          <div className="badge bg-danger p-1" style={{ fontSize: '0.6rem' }}>
            {notVerifyCount}
          </div>
        )}
      </bs.Button>
    )

    const lines = verifyNote.split('\n');
    const notVerifyList = lines.filter(line => line.includes('[Not Verify]'));
    const botFillList = lines.filter(line => line.includes('[Bot Fill]'));

    let toolTipContent: ReactElement = (
      <div className="d-flex" style={{ gap: '1rem' }}>
        {notVerifyList.length > 0 && (
          <div className="flex-fill">
            <div className="fw-bold text-danger mb-2">Not Verify</div>
            <ul className="list-unstyled mb-0">
              {notVerifyList.map((item, idx) => (
                <li key={idx} className="text-danger small">{item.replace('[Not Verify]', '')}</li>
              ))}
            </ul>
          </div>
        )}

        {botFillList.length > 0 && (
          <div className="flex-fill">
            <div className="fw-bold text-warning mb-2">Bot Fill</div>
            <ul className="list-unstyled mb-0">
              {botFillList.map((item, idx) => (
                <li key={idx} className="text-warning small">{item.replace('[Bot Fill]', '')}</li>
              ))}
            </ul>
          </div>
        )}
      </div>
    )

    if (notVerifyCount || botFillCount) {
      return (
        <bs.CssTooltip style={{ width: 30, height: 50 }}>
          <bs.CssTooltipToggle style={{
            display: 'inline-flex',
            alignItems: 'center',
            justifyContent: 'flex-start',
            textOverflow: 'ellipsis',
            overflow: 'hidden'
          }}>
            {element}
          </bs.CssTooltipToggle>
          <bs.CssTooltipContent
            className={`align-items-end text-secondary`}
            style={{ whiteSpace: 'break-spaces', transform: `translate(50, 50)` }}>
            <div className='p-2'>
              {toolTipContent}
            </div>
          </bs.CssTooltipContent>
        </bs.CssTooltip>
      )
    } return element
  }

  createVGridConfig() {
    let { appContext, pageContext } = this.props;
    let writeCap = pageContext.hasUserWriteCapability();
    let _ = this;
    let config: grid.VGridConfig = {
      record: {
        editor: {
          supportViewMode: ['table'],
          enable: true,
        },
        control: {
          width: 30,
          items: [
            {
              name: 'add', hint: 'Add', icon: icon.Plus,
              customRender: (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
                let create = (_ctx: grid.VGridContext, atRecord: grid.DisplayRecord) => {
                  let newRecord = atRecord.cloneRecord();
                  newRecord.id = null;
                  return newRecord;
                }
                let allowInsert = (_ctx: grid.VGridContext, atRecord: grid.DisplayRecord) => {
                  let recState = atRecord.getRecordState(false);
                  if (!recState) return false;
                  return true;
                }
                return (
                  <grid.WGridInsertRow
                    key={'add'} color='link' context={ctx} row={dRecord.row}
                    createInsertRecord={create} allowInsert={allowInsert} />
                );
              },
            }
          ],
        },
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector()),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          {
            name: 'fileNo', label: T('File No'), width: 150, state: { showRecordState: true },
            editor: {
              type: 'string',
              renderCustom(fieldCtx: grid.FieldContext, _onInputChange: grid.OnInputChange) {
                const { fieldConfig, displayRecord, tabIndex, focus } = fieldCtx;
                let item = displayRecord.record;
                return (
                  <input.BBStringField
                    bean={item} field={fieldConfig.name} tabIndex={tabIndex} focus={focus} onInputChange={_.onInputChangeAndClearHwb} />
                )
              },

            }
          },
          {
            name: 'originFileNo', label: T('Org. File No'), width: 150, state: { visible: false }
          },
          {
            name: 'conversionError', label: T('Error'), width: 150
          },
          {
            name: 'customerName', label: T('Customer Name'),
            editor: {
              onInputChange: _.onInputChange,
              type: 'string',
            }
          },
          {
            name: 'bfsTransDate', label: T('Trans Date'), format: util.text.formater.compactDate, container: 'fixed-right', width: 90
          },
          {
            name: 'hwbNo', label: T('HWB No'),
            customHeaderRender: _.bfsOneHeaderRender,
            listener: {
              onDataCellEvent(cell, event) {
                let fields: Array<string> = ['fileNo', 'containerNo', 'extractedContainerNo'];
                let fieldName = event.field.name;
                if (cell.getRow() == event.row && fields.includes(fieldName)) {
                  cell.forceUpdate();
                }
              },
            },
            computeCssClasses: (_ctx, dRecord) => _.cssCheckField(dRecord.record, 'hwbNo'),
            editor: {
              onInputChange: _.onInputChange,
              type: 'string',
            },
          },
          {
            name: 'enrichStatus', label: '', width: 50,
            listener: {
              onDataCellEvent(cell, event) {
                let fields: Array<string> = ['fileNo', 'containerNo', 'extractedContainerNo'];
                let fieldName = event.field.name;
                if (cell.getRow() == event.row && fields.includes(fieldName)) {
                  cell.forceUpdate();
                }
              },
            },
            customRender: (_ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let enrichStatus = record['enrichStatus'];
              let btn = (
                <bs.Button laf='link' className='p-0 m-0' onClick={() => this.onSearchBFSOneHwb(record)}>
                  <icon.Search size={16} />
                </bs.Button>
              )
              if (enrichStatus === "MATCH") {
                btn = (
                  <bs.Button laf='link' className='p-0 m-0' onClick={() => this.onSearchBFSOneHwb(record)}>
                    <icon.CheckCircle className='text-success' size={16} />
                  </bs.Button>
                )
              }
              if (enrichStatus === "PARTIAL_MATCH") {
                btn = (
                  <bs.Button laf='link' className='p-0 m-0' onClick={() => this.onSearchBFSOneHwb(record)}>
                    <icon.CheckCircle className='text-warning' size={16} />
                  </bs.Button>
                )
              }
              if (enrichStatus === "MATCH_BY_FILE_NO") {
                btn = (
                  <bs.Button laf='link' className='p-0 m-0' onClick={() => this.onSearchBFSOneHwb(record)}>
                    <icon.AlertTriangle className='text-danger' size={16} />
                  </bs.Button>
                )
              }
              if (enrichStatus === "MANUAL") {
                btn = (
                  <bs.Button laf='link' className='p-0 m-0' onClick={() => this.onSearchBFSOneHwb(record)}>
                    <icon.CheckSquare className='text-primary' size={16} />
                  </bs.Button>
                )
              }

              const totalSearchResult = record['totalSearchResult'];
              return (
                <div className='flex-hbox'>
                  {btn}
                  {totalSearchResult && totalSearchResult > 1 ?
                    <bs.Tooltip tooltip={record['searchResult']} className='text-danger mx-1'>
                      {totalSearchResult}
                    </bs.Tooltip>
                    : null
                  }
                </div>
              );
            }
          },
          ...this.buildPluginCol(),
          {
            name: 'containerType', label: T('Container Type'), state: { visible: false },
            editor: {
              onInputChange: _.onInputChange,
              type: 'string',
            }
          },
          {
            name: 'invoiceNo', label: T('Invoice No'),
            customHeaderRender: _.bfsOneHeaderRender,
            editor: {
              onInputChange: _.onInputChange,
              type: 'string',
            }
          },
          {
            name: 'seriesNo', label: T('Series No'),
            customHeaderRender: _.bfsOneHeaderRender,
            computeCssClasses: (_ctx, dRecord) => {
              if (!dRecord.getValue('obh')) return _.cssCheckField(dRecord.record, 'seriesNo');
              return ''
            },
            editor: {
              onInputChange: _.onInputChange,
              type: 'string',
            }
          },
          {
            name: 'invoiceDate', label: T('Invoice Date'),
            customHeaderRender: _.bfsOneHeaderRender,
            computeCssClasses: (_ctx, dRecord) => {
              if (!dRecord.getValue('obh')) return _.cssCheckField(dRecord.record, 'invoiceDate');
              return ''
            },
            editor: {
              onInputChange: _.onInputChange,
              type: 'date',
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let field = fieldCtx.fieldConfig;
                let dRecord = fieldCtx.displayRecord;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let item = dRecord.record;
                let cssClass = field.cssClass;
                if (field.computeCssClasses) cssClass += ' ' + field.computeCssClasses(fieldCtx.gridContext, dRecord)
                return (
                  <div className={`flex-hbox`}>
                    <input.BBDateInputMask className={cssClass} format='DD/MM/YYYY'
                      bean={item} field={field.name} tabIndex={tabIndex} focus={focus} onInputChange={onInputChange} />
                  </div>
                )
              },
            }
          },
          {
            name: 'quantity', label: T('Quantity'), width: 80,
            customHeaderRender: _.bfsOneHeaderRender,
            editor: {
              onInputChange: _.onInputChange,
              type: 'double',
            }
          },
          {
            name: 'unit', label: T('Unit'), width: 80,
            customHeaderRender: _.bfsOneHeaderRender,
            editor: {
              onInputChange: _.onInputChange,
              type: 'string',
            }
          },
          {
            name: 'invLiftOn', label: T('Inv Lift On'), state: { visible: false },
          },
          {
            name: 'liftOnCharge', label: T('Lift On Charge'), state: { visible: false },
            editor: {
              onInputChange: _.onInputChange,
              type: 'currency',
            }
          },
          {
            name: 'invLiftOff', label: T('Inv Lift Off'), state: { visible: false },
          },
          {
            name: 'liftOffCharge', label: T('Lift Off Charge'), state: { visible: false },
            editor: {
              onInputChange: _.onInputChange,
              type: 'currency',
            }
          },
          {
            name: 'invOtherCharge', label: T('Inv Other Charge'), state: { visible: false },
          },
          {
            name: 'otherCharge', label: T('Other Charge'), state: { visible: false },
            editor: {
              onInputChange: _.onInputChange,
              type: 'currency',
            }
          },
          {
            name: 'total', label: T('Total'),
            customHeaderRender: _.bfsOneHeaderRender,
            editor: {
              onInputChange: _.onInputChange,
              type: 'currency',
            }
          },
          {
            name: 'vatCharge', label: T('VAT Charge'),
            computeCssClasses: (_ctx, dRecord) => {
              if (dRecord.getValue('obh')) return 'text-danger text-decoration-line-through';
              return ''
            },
            editor: {
              onInputChange: _.onInputChange,
              type: 'currency',
            }
          },
          {
            name: 'vat', label: T('Vat'),
            customHeaderRender: _.bfsOneHeaderRender,
            computeCssClasses: (_ctx, dRecord) => {
              if (!dRecord.getValue('obh')) return _.cssCheckField(dRecord.record, 'vat');
              return 'text-danger text-decoration-line-through'
            },
            editor: {
              onInputChange: _.onInputChange,
              type: 'percent',
            }
          },
          {
            name: 'finalCharge', label: T('Final Charge'),
            editor: {
              onInputChange: _.onInputChange,
              type: 'currency',
            }
          },
          {
            name: 'feeName', label: T('Fee Name'),
            editor: {
              onInputChange: _.onInputChange,
              type: 'string',
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let field = fieldCtx.fieldConfig;
                let dRecord = fieldCtx.displayRecord;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let item = dRecord.record;
                let cssClass = field.cssClass;
                return (
                  <input.BBOptionAutoComplete tabIndex={tabIndex} autofocus={focus} className={cssClass} allowUserInput
                    bean={item} field={field.name}
                    options={['NANG', 'HA', 'PHI KHAC']}
                    onInputChange={onInputChange} />
                )
              },
            }
          },
          {
            name: 'bfsOneFeeCode', label: T('BFS Fee Code'), state: { visible: false },
            customHeaderRender: _.bfsOneHeaderRender,
            computeCssClasses: (_ctx, dRecord) => _.cssCheckField(dRecord.record, 'bfsOneFeeCode'),
          },
          {
            name: 'bfsOneFeeName', label: T('BFS Fee Name'), width: 150,
            customHeaderRender: _.bfsOneHeaderRender,
            editor: {
              onInputChange: _.onInputChange,
              type: 'string',
              renderCustom(fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) {
                let field = fieldCtx.fieldConfig;
                let dRecord = fieldCtx.displayRecord;
                let tabIndex = fieldCtx.tabIndex;
                let focus = fieldCtx.focus;
                let item = dRecord.record;
                let cssClass = field.cssClass;
                return (
                  <BBRefNameFeeDesc className={cssClass} autofocus={focus} tabIndex={tabIndex} minWidth={400}
                    appContext={appContext} pageContext={pageContext} bean={item} allowUserInput
                    beanIdField='bfsOneFeeCode' beanLabelField='bfsOneFeeName' placeholder='Fee Code'
                    onPostUpdate={(_inputUI: React.Component, _bean: any, _selectOpt: any, _userInput: string) =>
                      onInputChange(_bean, field.name, '', _bean[field.name])
                    } />
                )
              },
            }
          },
          {
            name: 'bfsOneSellerPartnerId', label: T('BFS Seller Id'),
            customHeaderRender: _.bfsOneHeaderRender,
            computeCssClasses: (_ctx, dRecord) => _.cssCheckField(dRecord.record, 'bfsOneSellerPartnerId'),
            editor: {
              onInputChange: _.onInputChange,
              type: 'string',
            }
          },
          {
            name: 'bfsOneSellerPartnerName', label: T('BFS Seller'), width: 150,
            customHeaderRender: _.bfsOneHeaderRender,
            editor: {
              onInputChange: _.onInputChange,
              type: 'string',
            }
          },
          {
            name: 'sellerTaxCode', label: T('S.Tax Code'), hint: T('Seller Tax Code'),
            editor: {
              type: 'string',
              renderCustom(fieldCtx: grid.FieldContext, _onInputChange: grid.OnInputChange) {
                const { fieldConfig, displayRecord, tabIndex, focus } = fieldCtx;
                let item = displayRecord.record;
                return (
                  <input.BBStringField
                    bean={item} field={fieldConfig.name} tabIndex={tabIndex} focus={focus} onInputChange={_.onInputChangeSellerTaxCode} />
                )
              },
            }
          },
          {
            name: 'obh', label: T('OBH'), cssClass: 'flex-vbox align-items-center', width: 65,
            customHeaderRender: _.bfsOneHeaderRender,
            editor: {
              onInputChange: _.onInputChange,
              type: 'boolean',
            }
          },
          {
            name: 'buyerTaxCode', label: T('B.Tax Code'), hint: T('Buyer Tax Code'),
            editor: {
              onInputChange: _.onInputChange,
              type: 'string',
            }
          },
          {
            name: 'description', label: T('Description'),
            editor: {
              onInputChange: _.onInputChange,
              type: 'string',
            }
          },
          {
            name: '_status', label: '', width: 40, container: 'fixed-right',
            customRender: (_ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, focus: boolean) => {
              let item = dRecord.record;
              let errorFields = DocTmsChargeSummaryItemListEditor.recordErrorFields(item);
              if (errorFields.length > 0) {
                return (
                  <bs.Tooltip tooltip={errorFields.join(',')} className='text-danger flex-hbox align-items-center'>
                    <icon.XCircle size={15} />
                    {errorFields.length}
                  </bs.Tooltip>
                )
              }
              return (
                <icon.CheckCircle className='text-success' size={15} />
              )
            }
          },
          {
            name: 'verifyNote', label: T('Note'), width: 60, container: 'fixed-right', state: { showRecordState: true },
            editor: {
              type: 'string',
              renderCustom(fieldCtx: grid.FieldContext, _onInputChange: grid.OnInputChange) {
                const { fieldConfig, displayRecord, tabIndex, focus } = fieldCtx;
                let item = displayRecord.record;
                if (!item['invoiceDocId']) {
                  return (
                    <icon.UserCheck size={12} style={{ marginRight: 5 }} />
                  )
                } else {
                  if (!item['verifyNote']) {
                    return (
                      <icon.UserCheck size={12} style={{ marginRight: 5 }} />
                    )
                  } else {
                    return _.showCheckVerifyStatusToolTip(item)
                  }
                }
              },

            }
          },
        ],
        fieldGroups: {
          "info": {
            label: T('Info'),
            fields: ['fileNo', 'customerName', 'conversionError']
          },
          "hwb": {
            label: T('HWB'),
            fields: ['hwbNo', 'containerNo', 'containerType', 'extractedContainerNo', 'enrichStatus']
          },
          "inv": {
            label: T('Invoice'),
            fields: ['invoiceNo', 'seriesNo', 'invoiceDate']
          },
          "fee": {
            label: T('Fee'),
            fields: ['quantity', 'unit', 'vatCharge', 'vat', 'total', 'finalCharge', 'bfsOneFeeCode', 'bfsOneFeeName', 'feeName']
          },
          "seller-buyer": {
            label: T('Seller/Buyer'),
            fields: ['sellerTaxCode', 'bfsOneSellerPartnerId', 'bfsOneSellerPartnerName', 'obh', 'buyerTaxCode']
          },
        }
      },
      toolbar: {
        actions: [
          {
            name: 'converter', label: T('Converter'),
            onClick: (ctx) => {
              let uiRoot = ctx.uiRoot as DocTmsChargeSummaryItemListEditor;
              let { pageContext, documentType } = uiRoot.props;
              console.log(documentType);

              let createContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
                let plugin = DocInvChargeSummaryPluginManager.getPlugin(documentType);
                let defaultBeanMapping = plugin ? plugin.mappingToBFSOneFields() : [];
                return (
                  <DocumentConverter ctx={ctx} defaultBeanMapping={defaultBeanMapping}
                    appContext={appCtx} pageContext={pageCtx} observer={new entity.BeanObserver({})}
                    onCommit={(records) => {
                      pageCtx.back();
                      this.onAddRecordConverted(records);
                      ctx.getVGrid().forceUpdateView();
                    }}
                  />
                )
              }
              pageContext.createPopupPage('converter', T('Converter'), createContent, { size: 'xl' })
            },
          },
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_ADD(!writeCap, 'Add'),
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_DELETE(!writeCap, 'Delete'),
        ]
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          }
        }

      }
    };
    return config;
  }

  static recordErrorFields = (item: any) => {
    let checkFields: Array<any> = ['hwbNo', 'bfsOneFeeCode', 'bfsOneSellerPartnerId', 'invoiceNo'];
    let errorFields: Array<any> = [];
    for (let field of checkFields) {
      if (!item[field]) errorFields.push(field);
    }

    let checkObhFields = ['vat', 'seriesNo', 'invoiceDate'];
    if (!item['obh']) {
      for (let field of checkObhFields) {
        if (!item[field]) errorFields.push(field);
      }
    }
    return errorFields;
  }

  onAddRecordConverted = (records: Array<any>) => {
    let { plugin } = this.props;
    for (let record of records) {
      let liftOnCharge = record['liftOnCharge'];
      let liftOffCharge = record['liftOffCharge'];
      let otherCharge = record['otherCharge'];
      let vat = record['vat'];
      if (vat > 1) record['vat'] = vat / 100;
      let isAdded = false;
      if (liftOnCharge) {
        let clone = entity.EntityUtil.clone(record, false);
        clone['finalCharge'] = liftOnCharge;
        clone['feeName'] = 'liftOnCharge';
        clone['invoiceNo'] = record['invLiftOn'];
        plugin.getModel().addRecord(clone);
        isAdded = true;
      }
      if (liftOffCharge) {
        let clone = entity.EntityUtil.clone(record, false);
        clone['finalCharge'] = liftOffCharge;
        clone['feeName'] = 'liftOffCharge';
        clone['invoiceNo'] = record['invLiftOff'];
        plugin.getModel().addRecord(clone);
        isAdded = true;
      }
      if (otherCharge) {
        let clone = entity.EntityUtil.clone(record, false);
        clone['finalCharge'] = otherCharge;
        clone['feeName'] = 'otherCharge';
        clone['invoiceNo'] = record['invOtherCharge'];
        plugin.getModel().addRecord(clone);
        isAdded = true;
      }
      if (!isAdded) plugin.getModel().addRecord(record);
    }
  }

  onNewAction(): void {
    let { plugin } = this.props;
    plugin.getModel().addRecord({ 'fileNo': '/' });
    this.vgridContext.getVGrid().forceUpdateView();
  }
}

interface DocumentConverterProps extends entity.AppDbEntityProps {
  ctx: grid.VGridContext;
  defaultBeanMapping?: any;
  onCommit: (records: Array<any>) => void;
}

class DocumentConverter extends entity.AppDbEntity<DocumentConverterProps> {
  beanMapping: any;
  documentMappingData: Array<any>;
  documentMappingFields: Set<any>;

  analysis = (input: string) => {
    let { defaultBeanMapping } = this.props;
    input = input.replace(/"([^"]*)"/g, (_match: any, oldText: string) => {
      oldText = oldText.replace("\"", "");
      let split = oldText.split('\n');
      return split.join(',');
    });
    let rows = input.split('\n');
    let plugin: Array<any> = [];
    let fields: Set<any> = new Set<any>();
    let beanMapping: any = {};
    for (let row of rows) {
      let cells = row.split('\t');
      let rowData: any = {};
      for (let i = 0; i < cells.length; i++) {
        let cell = cells[i];
        if (cell) cell = cell.trim();
        rowData[`${i}`] = cell;
        fields.add(`${i}`);
      }
      plugin.push(rowData);
    }
    fields.forEach(sel => beanMapping[`${sel}`] = sel);
    this.beanMapping = beanMapping;
    this.documentMappingData = plugin;
    this.documentMappingFields = fields;
    if (defaultBeanMapping) {
      for (let key in defaultBeanMapping) {
        this.beanMapping[key] = defaultBeanMapping[key];
      }
    }
    this.forceUpdate();
  }

  render(): React.ReactNode {
    let { ctx, appContext, pageContext, observer, onCommit } = this.props;
    let bean = observer.getMutableBean();
    return (
      <div className='flex-vbox'>
        <div className='flex-grow-0 flex-vbox h-30'>
          <input.BBTextField bean={bean} field='input' label='Input' className='h-100'
            onKeyDown={(_winput, event, _currInput) => {
              if (event.key === 'Tab' || event.key === 'Enter') {
                this.analysis(_currInput);
              }
            }}
          />
          <bs.Toolbar>
            <bs.Button laf='info' onClick={() => this.analysis(bean['input'])}>
              {T('Analysis')}
            </bs.Button>
          </bs.Toolbar>
        </div>
        <div className='flex-grow-1'>
          {this.documentMappingData && this.documentMappingFields ?
            <DocumentMapping key={`${util.IDTracker.next()}-document-mapping`} className='h-100'
              appContext={appContext} pageContext={pageContext}
              fields={this.documentMappingFields}
              beanMapping={this.beanMapping}
              initFieldDataMapping={ctx.getVGridConfigModel().getRecordConfigModel().fieldMap}
              plugin={new entity.DbEntityListPlugin(this.documentMappingData)}
              onRecordConvertedCommit={onCommit}
            />
            :
            <div>
              {T('No Data')}
            </div>
          }
        </div>
      </div>
    )
  }
}

interface DocumentMappingProps extends entity.DbEntityListProps {
  fields: Set<any>;
  beanMapping: any;
  initFieldDataMapping: util.RecordMap<grid.FieldConfig>;
  onRecordConvertedCommit: (records: Array<any>) => void;
}
class DocumentMapping extends entity.DbEntityList<DocumentMappingProps> {
  onBuildFieldConfig(beanMapping: any, initFieldDataMapping: util.RecordMap<grid.FieldConfig>, fields: Set<any>): grid.FieldConfig[] {
    let fieldConfigs: Array<grid.FieldConfig> = [];
    let options: Array<any> = [''];
    let optionLabels: Array<any> = [''];
    for (let field of initFieldDataMapping.getAll()) {
      if (field.name.startsWith('_')) continue;
      options.push(field.name);
      optionLabels.push(field.label);
    }
    for (let field of fields) {
      fieldConfigs.push(
        {
          name: field, label: field,
          customHeaderRender(ctx, field, headerEle) {
            return (
              <div className='flex-vbox'>
                {headerEle}
                <input.BBSelectField bean={beanMapping} field={field.name} options={options} optionLabels={optionLabels} />
              </div>
            )
          },
        }
      )
    }
    return fieldConfigs;
  }

  createVGridConfig() {
    let { fields, beanMapping, initFieldDataMapping } = this.props;
    let config: grid.VGridConfig = {
      record: {
        headerCellHeight: 50,
        fields: [
          ...this.onBuildFieldConfig(beanMapping, initFieldDataMapping, fields)
        ],
      },
      toolbar: {
        actions: [],
      },
      footer: {
        page: {
          render: (_ctx) => {
            return (
              <bs.Toolbar>
                <bs.Button laf='info' onClick={() => this.onConvert(_ctx)}>
                  {T('Convert')}
                </bs.Button>
              </bs.Toolbar>
            )
          },
        }
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          },
        }
      }
    }
    return config;
  }

  onConvert(_ctx: grid.VGridContext) {
    let uiRoot = _ctx.uiRoot as DocumentMapping;
    let { beanMapping, plugin, initFieldDataMapping, onRecordConvertedCommit } = uiRoot.props;
    let recordConverted: Array<any> = [];
    for (let rec of plugin.getRecords()) {
      let converted: any = {};
      for (let [key, val] of Object.entries(beanMapping)) {
        if (rec[key]) {
          let fieldConfig = initFieldDataMapping.get(`${val}`);
          let value = rec[key];
          if (fieldConfig) {
            if (fieldConfig.dataType) {
              let dataType = fieldConfig.dataType;
              dataType = dataType.toLowerCase();
              if ((dataType == 'integer' || dataType == 'double') && typeof value != 'number') {
                value = Number(value.replace(/[^0-9]/g, ""))
              }
            } else if (fieldConfig.editor) {
              let type = fieldConfig.editor.type;
              type = type.toLowerCase();
              let isNumber =
                type == 'integer' || type == 'int' ||
                type == 'double' || type === 'long' ||
                type == 'number' || type == 'currency' || type == 'percent';
              if (isNumber && typeof value != 'number') {
                value = Number(value.replace(/[^0-9]/g, ""))
              }

              if (type == 'date') {
                let timeZone = '00:00:00+0000';
                try {
                  let date = util.TimeUtil.parseDateTimeFormat(`${value}@${timeZone}`, 'DD/MM/YYYY@HH:mm:ssZZZZZ');
                  value = util.TimeUtil.toCompactDateTimeFormat(date);
                  if (value === 'Invalid dte') value = null;
                } catch (error) {
                  value = null;
                }
              }
            }
            converted[`${val}`] = value;
          }
        }
      }
      recordConverted.push(converted);
    }
    onRecordConvertedCommit(recordConverted)
  }
}



