import React from 'react';
import { bs, input, entity, util, app } from '@datatp-ui/lib';

import {UIDocumentSetPlugin} from "../DocumentSetPlugin";
import * as FeatherIcon from "react-feather";
import {T} from "../backend";
import {DocumentList} from "../DocumentList";
import {Document} from "../Document";


export class TMSDocChargeSummaryPlugin extends UIDocumentSetPlugin {
  constructor() {
    super('tms-inv-summary');
  }

  renderActionButton(_appContext: app.AppContext, _pageContext: app.PageContext, uiList: DocumentList, viewType: string) {
    let modCap = _pageContext.hasUserModeratorCapability();

    return (
      <></>
    )
  }
}

export class FeeChargeSummaryPlugin extends UIDocumentSetPlugin {
  constructor() {
    super('fee-summary');
  }

  getFeeInvoiceSummary = (uiList: DocumentList) => {
    let { appContext, pageContext, storage, documentSet } = uiList.props;
    appContext
      .createHttpBackendCall('DocumentService', 'getFeeChargeSummary', { docSetId: documentSet['id'] })
      .withSuccessData((data: any) => {
        if (data) {
          let createPageContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
            return (
              <Document
                appContext={appCtx} pageContext={pageCtx} storage={storage} documentSet={documentSet}
                observer={new entity.BeanObserver(data)}
              />
            );
          }
          pageContext.addPageContent('document', T(`Document`), createPageContent);
        } else {
          bs.notificationShow("warning", T("Not Found Charge Summary"));
        }
      })
      .withFailNotification('danger', "Get Summary Failed!!!")
      .call();
  }

  renderActionButton(_appContext: app.AppContext, _pageContext: app.PageContext, uiList: DocumentList, viewType: string) {
    return (
      <bs.Popover title={'Fee'} className="flex-hbox-grow-0" closeOnTrigger=".btn" offset={[0, 5]}>
        <bs.PopoverToggle laf='primary'>
          <FeatherIcon.Layers size={12} /> {'Fee'}
        </bs.PopoverToggle>
        <bs.PopoverContent className='flex-vbox-grow-0' style={{ minWidth: 150 }}>
          <bs.Button laf='primary'
                     outline className='text-start p-2 my-1'
                     onClick={() => this.getFeeInvoiceSummary(uiList)} hidden={viewType != 'DocumentSet'}>
            <FeatherIcon.Send size={12} /> {T("Open Summary")}
          </bs.Button>
          <bs.Button laf='primary'
                     outline className='text-start p-2 my-1'
                     onClick={uiList.onCreateFeeSummary} hidden={viewType != 'DocumentSet'}>
            <FeatherIcon.Send size={12} /> {T("Request Summary")}
          </bs.Button>
        </bs.PopoverContent>
      </bs.Popover>
    )
  }
}