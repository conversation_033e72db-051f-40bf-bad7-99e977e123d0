import React from 'react';
import { bs, input, entity, grid ,app} from '@datatp-ui/lib';
import { T } from "../backend";

export class DocInvoiceItemForm extends entity.AppDbEntity {
  render() {
    let { pageContext, observer, readOnly } = this.props;
    let writeCap = pageContext.hasUserWriteCapability() && !readOnly;
    let invItem = observer.getMutableBean();

    return (
      <div className='container-fluid' >
        <input.BBStringField
          bean={invItem} field='name' label={T("Name")} disable={!writeCap} />
        <bs.Row>
          <bs.Col span={6}>
            <input.BBStringField
              bean={invItem} field='unit' label={T("Unit")} disable={!writeCap} />
          </bs.Col>
          <bs.Col span={6}>
            <input.BBDoubleField
              bean={invItem} field='qty' label={T("Quantity")} disable={!writeCap} />
          </bs.Col>
        </bs.Row>

        <bs.Row>
          <bs.Col span={6}>
            <input.BBDoubleField
              bean={invItem} field='unitPrice' label={T("Unit Price")} disable={!writeCap} />
          </bs.Col>
          <bs.Col span={6}>
            <input.BBDoubleField
              bean={invItem} field='total' label={T("Total")} disable={!writeCap} />
          </bs.Col>
        </bs.Row>
      </div>
    )
  }
}

export class DocInvoiceItemListEditor extends entity.VGridEntityListEditor {
  renderBeanEditor() {
    let { appContext, pageContext } = this.props;
    let writeCap = pageContext.hasUserWriteCapability();
    return (
      <DocInvoiceItemForm appContext={appContext} pageContext={pageContext} observer={this.createBeanObserver()} readOnly={!writeCap} />
    );
  }

  renderAttributesEditor() {
    let { appContext, pageContext } = this.props;
    let writeCap = pageContext.hasUserWriteCapability();
    let observer = this.createBeanObserver('complex')
  }

  createVGridConfig() {
    let { pageContext } = this.props;
    let writeCap = pageContext.hasUserWriteCapability();
    let config: grid.VGridConfig = {
      record: {
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector()),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          { name: 'name', label: T('Name'), width: 250, editor: { enable: true, type: 'string' } },
          { name: 'unit', label: T('Unit'), width: 90, editor: { enable: true, type: 'string' } },
          { name: 'qty', label: T('Quantity'), width: 90, editor: { enable: true, type: 'double' } },
          { name: 'unitPrice', label: T('Unit Price'), editor: { enable: true, type: 'double' } },
          { name: 'total', label: T('Total'), editor: { enable: true, type: 'double' } },
          { name: '', label: T('Attrs'), width: 100, container: 'fixed-right',
            customRender: (_ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, focus: boolean) => {
              let record = dRecord.record
              let itemStr = record['attributesJson'];
              let attributes = {};
              if(itemStr) attributes = JSON.parse(itemStr);
              return (
                <bs.Button laf={'link'} onClick={ () => {
                  const createContext = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
                    return (
                      <AttributesJson
                        appContext={appCtx} pageContext={pageCtx}
                        observer={new entity.BeanObserver(attributes)}
                        onPostCommit={(bean) => {
                          console.log(bean)
                          record['attributesJson'] = JSON.stringify(bean)
                          _ctx.getVGrid().forceUpdateView();
                          appCtx.addOSNotification("success", T("Update Item Attributes Success"));
                          pageCtx.back()
                        }}
                      />
                    )
                  }
                  pageContext.createPopupPage('edit-attributes','Edit Attributes',createContext);
                }}>
                  {Object.values(attributes).join(' / ')}
                </bs.Button>
              )
            }
          },
        ],
        editor: {
          supportViewMode: ['table'],
          enable: true,
        }
      },
      toolbar: {
        actions: [
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_ADD(!writeCap, 'Add'),
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_DELETE(!writeCap, 'Del')
        ]
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          }
        }
      }
    };
    return config;
  }

}


export class DocInvoiceHblDistributionListEditor extends entity.VGridEntityListEditor {
  renderBeanEditor() {
    let { appContext, pageContext } = this.props;
    let writeCap = pageContext.hasUserWriteCapability();
    return (
      <DocInvoiceHblDistributionForm appContext={appContext} pageContext={pageContext} observer={this.createBeanObserver()} readOnly={!writeCap} />
    );
  }

  createVGridConfig() {
    let { pageContext } = this.props;
    let writeCap = pageContext.hasUserWriteCapability();
    let config: grid.VGridConfig = {
      record: {
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector()),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          { name: 'name', label: T('Name'), width: 250, editor: { enable: true, type: 'string' } },
          {
            name: 'date', label: T('Date'), width: 150, editor: {
              enable: true, type: 'date', renderCustom: (fieldCtx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
                let { displayRecord, fieldConfig, tabIndex, focus } = fieldCtx;
                return (
                  <input.BBDateInputMask tabIndex={tabIndex} focus={focus}
                    bean={displayRecord.record} field={fieldConfig.name} format={"DD/MM/YYYY"} disabled={!writeCap} onInputChange={onInputChange} />
                )
              },
            }
          },
          { name: 'hblNo', label: T('Hbl No'), width: 150, editor: { enable: true, type: 'string' } },
          { name: 'vat', label: T('Vat'), editor: { enable: true, type: 'percent' } },
          { name: 'vatCharge', label: T('Vat Charge'), editor: { enable: true, type: 'currency' } },
          { name: 'finalCharge', label: T('Final Charge'), editor: { enable: true, type: 'currency' } },
          { name: 'total', label: T('Total'), editor: { enable: true, type: 'currency' } },
        ],
        editor: {
          supportViewMode: ['table'],
          enable: true,
        }
      },
      toolbar: {
        actions: [
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_ADD(!writeCap, 'Add'),
          ...entity.DbEntityListConfigTool.TOOLBAR_ACTION_DELETE(!writeCap, 'Del')
        ]
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          }
        }
      }
    };
    return config;
  }

}


export class DocInvoiceHblDistributionForm extends entity.AppDbEntity {
  render() {
    let { pageContext, observer, readOnly } = this.props;
    let writeCap = pageContext.hasUserWriteCapability() && !readOnly;
    let distribution = observer.getMutableBean();

    return (
      <div className='container-fluid' >
        <input.BBStringField
          bean={distribution} field='name' label={T("Name")} disable={!writeCap} />
        <bs.Row>
          <bs.Col span={6}>
            <input.BBStringField
              bean={distribution} field='hblNo' label={T("Hbl No")} disable={!writeCap} />
          </bs.Col>
          <bs.Col span={6}>
            <input.BBDateInputMask label='Date'
              bean={distribution} field={'date'} format={"DD/MM/YYYY"} disabled={!writeCap} />
          </bs.Col>
        </bs.Row>
        <bs.Row>
          <bs.Col span={6}>
            <input.BBPercentField
              bean={distribution} field='vat' label={T("Vat")} disable={!writeCap} />
          </bs.Col>
          <bs.Col span={6}>
            <input.BBCurrencyField
              bean={distribution} field='vatCharge' label={T("Vat Charge")} disable={!writeCap} />
          </bs.Col>
        </bs.Row>
        <bs.Row>
          <bs.Col span={6}>
            <input.BBCurrencyField
              bean={distribution} field='finalCharge' label={T("Final Charge")} disable={!writeCap} />
          </bs.Col>
          <bs.Col span={6}>
            <input.BBCurrencyField
              bean={distribution} field='total' label={T("Total")} disable={!writeCap} />
          </bs.Col>
        </bs.Row>
      </div>
    )
  }
}

class AttributesJson extends entity.AppDbEntityEditor {
  renderInput = ()=> {
    const { observer } = this.props;
    let att = observer.getMutableBean();
    let contents : Array<any> = [];
    for(let [key, value] of Object.entries(att)) {
      contents.push(
        <div className={'flex-hbox'} key={key}>
          <input.BBStringField bean={att} field={key} label={key}/>
        </div>
      );
    }
    return contents;
  }

  render() {
    let { appContext, pageContext, observer, onPostCommit } = this.props;
    const writeCap = pageContext.hasUserWriteCapability();

    return(
      <div className={'flex-vbox'}>
        {this.renderInput()}
        <bs.Toolbar className='border' hide={!writeCap}>
          <bs.Button laf={'info'} className={'mr-4'} onClick={() => {
            pageContext.back()
          }}>
            Cancel
          </bs.Button>
          <bs.Button laf={'info'} onClick={() => {
            if (onPostCommit) onPostCommit(observer.getMutableBean())
          }}>
            Save
          </bs.Button>
        </bs.Toolbar>
      </div>
    );
  }
}

