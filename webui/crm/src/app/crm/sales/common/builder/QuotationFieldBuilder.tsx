import React from 'react';

import { grid, util, input, entity, bs } from "@datatp-ui/lib";
import { module } from '@datatp-ui/erp';
import { T } from '../../backend';

import settings = module.settings;
import BBRefLocation = settings.BBRefLocation;
import LocationType = settings.LocationType;
import { FieldBuilder } from 'app/crm/price';

export type onInputChange = (ctx: grid.FieldContext, oldVal: any, newVal: any) => void;

const CELL_HEIGHT: number = 40;

export function isValidity(record: any): boolean {
  let validity: any = record['validity'] ? util.TimeUtil.parseCompactDateTimeFormat(record['validity']) : undefined;
  let disabled: boolean = true;
  if (validity) {
    let today = new Date();
    today.setHours(0, 0, 0, 0);
    if (validity.getTime() >= today.getTime()) {
      disabled = false;
    }
  }
  return disabled;
}


export class SalesQuoteFieldBuilder {

  observer: entity.ComplexBeanObserver;
  onInputChange: onInputChange;
  fields: grid.FieldConfig[] = [];

  constructor(observer: entity.ComplexBeanObserver, onInputChange?: onInputChange) {
    this.observer = observer;
    if (onInputChange) {
      this.onInputChange = onInputChange;
    } else {
      this.onInputChange = (ctx: grid.FieldContext, oldVal: any, newVal: any) => {
        if (oldVal !== newVal) {
          ctx.displayRecord.getRecordState().markModified();
          ctx.gridContext.getVGrid().forceUpdateView();
        }
      }
    }
  }

  addLocationFields(types: LocationType[], enable: boolean = false): SalesQuoteFieldBuilder {
    this.fields.push(this.addLocationField(types, 'fromLocationCode', 'fromLocationLabel', 'POL', 'Port of Loading', enable));
    this.fields.push(this.addLocationField(types, 'toLocationCode', 'toLocationLabel', 'POD', 'Port of Discharge', enable));
    return this;
  }

  addBasicFields(type: 'Air' | 'Sea' | 'Truck'): SalesQuoteFieldBuilder {

    const toggleSelectAllRow = (ctx: grid.VGridContext, checked: boolean) => {
      let state = grid.VGridConfigUtil.getRecordConfigState(ctx.config);
      state.selectAll = !state.selectAll;
      ctx.model.getDisplayRecordList().markSelectAllDisplayRecords(checked);
      if (this.observer) {
        if (checked) {
          let records: Array<any> = ctx.model.getRecords();
          this.observer.replaceBeanProperty('quoteListSelector', records)
        } else {
          this.observer.replaceBeanProperty('quoteListSelector', [])
        }
      }
      ctx.getVGrid().forceUpdateView();
    }

    const onToggle = (ctx: grid.VGridContext, row: number, check: boolean) => {
      ctx.model.getDisplayRecordList().markSelectDisplayRecord(row, check)
      if (this.observer) {
        let records: any[] = ctx.model.getSelectedRecords()
        if (records.length > 0) {
          this.observer.replaceBeanProperty('quoteListSelector', records)
        } else {
          this.observer.replaceBeanProperty('quoteListSelector', [])
        }
      }
      ctx.getVGrid().forceUpdateView();
    }

    this.fields.push(
      entity.DbEntityListConfigTool.FIELD_INDEX(),
      {
        name: '_selector_', label: 'Sel', width: 30, cssClass: 'cell-text-center',
        container: 'fixed-left', removable: false, resizable: false,
        customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, focus: boolean) => {
          let model = ctx.model;
          let selected = model.getDisplayRecordList().isSelectDisplayRecord(dRecord.row);
          return (
            <input.WCheckboxInput className={`d-inline-block m-auto`}
              name='row_selector' checked={selected}
              onInputChange={(check: boolean) => onToggle(ctx, dRecord.row, check)} focus={focus} />
          )
        },
        customHeaderRender: (ctx: grid.VGridContext, field: grid.FieldConfig, _headerEle: any) => {
          let state = grid.VGridConfigUtil.getRecordConfigState(ctx.config);
          return (<div className='flex-hbox justify-content-center'>
            <input.WCheckboxInput className='m-0 p-0' name='selectAll' checked={state.selectAll}
              onInputChange={(checked: boolean) => toggleSelectAllRow(ctx, checked)} />
          </div>)
        }
      },
    );
    return this;
  }

  addCustomFields(fields: grid.FieldConfig[]): SalesQuoteFieldBuilder {
    this.fields.push(...fields);
    return this;
  }


  addLocationField(types: LocationType[], fieldName: string, labelField: string, label: string, hint: string, enable: boolean = false): grid.FieldConfig {

    let fieldConfig: grid.FieldConfig = {
      name: fieldName, label: T(label), width: 220, filterable: true,
      fieldDataGetter(record: any) {
        return record[labelField] || 'N/A'
      },
      editor: {
        type: "string", enable: enable,
        renderCustom(ctx: grid.FieldContext, onInputChange: grid.OnInputChange) {
          const { displayRecord, gridContext, fieldConfig, focus, tabIndex } = ctx;
          let uiList = gridContext.uiRoot as entity.DbEntityList;
          const { appContext, pageContext } = uiList.props;
          let record = displayRecord.record;
          let oldVal = record[fieldConfig.name];

          return (
            <BBRefLocation style={{ minHeight: record['rowHeight'] || CELL_HEIGHT }} minWidth={400}
              placeholder={hint} autofocus={focus} appContext={appContext} pageContext={pageContext}
              hideMoreInfo bean={record} beanIdField={fieldName} beanLabelField={labelField}
              tabIndex={tabIndex} locationTypes={types} refLocationBy='code' disable={!enable}
              onPostUpdate={(_inputUI, bean, _selectOpt, _userInput) =>
                onInputChange(bean, fieldName, oldVal, bean[fieldName])} />
          );
        },
        onInputChange: this.onInputChange
      },
    }
    return fieldConfig;
  }


  addCarrierField(enableEditor: boolean = false): SalesQuoteFieldBuilder {
    let fieldConfig: grid.FieldConfig = {
      name: 'carrierLabel', label: 'Carrier', width: 150, removable: false,
      onClick: function (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) {
        let uiRoot = ctx.uiRoot as entity.VGridEntityListEditor;
        uiRoot.onSelect(dRecord.row, dRecord.record)
      },
      editor: {
        type: "string", enable: enableEditor,
        renderCustom(ctx: grid.FieldContext, onInputChange: grid.OnInputChange) {
          const { displayRecord, tabIndex, focus } = ctx;
          let record = displayRecord.record;
          return (
            <input.BBStringField tabIndex={tabIndex} focus={focus} style={{ height: CELL_HEIGHT }}
              disable={isValidity(record)}
              bean={record} field={'carrierLabel'} onInputChange={onInputChange} />
          )
        },
        onInputChange: this.onInputChange
      },
    }
    this.fields.push(fieldConfig);
    return this;
  }

  addCurrencyField(enableEditor: boolean = false): SalesQuoteFieldBuilder {
    this.fields.push({
      name: 'currency', label: T('Curr'), width: 80,
      editor: {
        type: "string",
        enable: enableEditor,
        renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
          const { displayRecord, gridContext, fieldConfig, focus, tabIndex } = ctx;
          let uiList = gridContext.uiRoot as entity.DbEntityList;
          const { appContext, pageContext } = uiList.props;
          let record = displayRecord.record;
          return (
            <module.settings.BBRefCurrency tabIndex={tabIndex} autofocus={focus} style={{ minHeight: record['rowHeight'] || 40 }}
              appContext={appContext} pageContext={pageContext} required hideMoreInfo disable={isValidity(record)}
              bean={record} beanIdField={fieldConfig.name} placeholder='Curr'
              onPostUpdate={(_inputUI, _bean, _selectOpt, _userInput) => onInputChange(record, '', null, null)} />
          )
        },
        onInputChange: this.onInputChange
      },
    })
    return this;
  }

  addFrequencyField(enableEditor: boolean = false): SalesQuoteFieldBuilder {
    this.fields.push({
      name: 'frequency', label: T(`Frequency`), width: 180, style: { height: CELL_HEIGHT },
      editor: {
        type: 'string', enable: enableEditor, onInputChange: this.onInputChange
      },
    })
    return this;
  }

  addTransitTimeField(enableEditor: boolean = false): SalesQuoteFieldBuilder {
    this.fields.push({
      name: 'transitTime', label: T(`Services`), width: 200, style: { height: CELL_HEIGHT },
      editor: {
        type: 'string', enable: enableEditor, onInputChange: this.onInputChange,
        renderCustom: (ctx: grid.FieldContext, _onInputChange: grid.OnInputChange) => {
          const { displayRecord, tabIndex, focus } = ctx;
          let record = displayRecord.record;
          let val = record['transitTime'] || '';
          if (!val) {
            return <div className=''>{val}</div>
          }
          const htmlVal = val.split('\n').map((line: any, i: any) =>
            <div key={i}>{line}</div>
          );

          return (
            <bs.CssTooltip width={250} position='bottom-right'>
              <bs.CssTooltipToggle>
                <input.BBTextField style={{ height: '40px' }} className='bg-white' disable={isValidity(record)}
                  bean={record} field={'transitTime'} tabIndex={tabIndex} focus={focus} onInputChange={_onInputChange} />
              </bs.CssTooltipToggle>
              <bs.CssTooltipContent className="flex-vbox p-2 rounded" >
                <div className="tooltip-body">
                  {htmlVal}
                </div>
              </bs.CssTooltipContent>
            </bs.CssTooltip>

          );
        }
      },
    })
    return this;
  }

  addTransitPortField(enableEditor: boolean = false): SalesQuoteFieldBuilder {
    this.fields.push({
      name: 'transitPort', label: T(`Via`), width: 150, style: { height: CELL_HEIGHT },
      editor: {
        type: 'string', enable: enableEditor, onInputChange: this.onInputChange
      },
    })
    return this;
  }

  addValidityField(enableEditor: boolean = false): SalesQuoteFieldBuilder {
    this.fields.push({
      name: 'effectiveDate', label: T(`Effective Date`), width: 120, style: { height: CELL_HEIGHT },
      format: util.text.formater.compactDate,
      editor: {
        type: 'date', enable: enableEditor, onInputChange: this.onInputChange,
        renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
          const { displayRecord, focus, tabIndex } = ctx;
          let record = displayRecord.record;
          return (
            <input.BBDateInputMask tabIndex={tabIndex} focus={focus}
              style={{ minHeight: record['rowHeight'] || CELL_HEIGHT }} disabled={isValidity(record)}
              bean={record} field="effectiveDate" format="DD/MM/YYYY" onInputChange={onInputChange} />
          )
        },
      },
    })
    this.fields.push({
      name: 'validity', label: T(`Validity`), width: 120, style: { height: CELL_HEIGHT },
      format: util.text.formater.compactDate,
      editor: {
        type: 'date', enable: enableEditor, onInputChange: this.onInputChange,
        renderCustom: (ctx: grid.FieldContext, onInputChange: grid.OnInputChange) => {
          const { displayRecord, focus, tabIndex } = ctx;
          let record = displayRecord.record;
          return (
            <input.BBDateInputMask tabIndex={tabIndex} focus={focus}
              style={{ minHeight: record['rowHeight'] || CELL_HEIGHT }} disabled={isValidity(record)}
              bean={record} field="validity" format="DD/MM/YYYY" onInputChange={onInputChange} />
          )
        },
      },
    })
    return this;
  }

  addAgentField(enableEditor: boolean = false): SalesQuoteFieldBuilder {
    this.fields.push({
      name: 'handlingAgentPartnerLabel', label: T(`Agent`), width: 200, style: { height: CELL_HEIGHT },
      format: (val: any) => util.text.formater.truncate(val, 180),
      editor: {
        type: 'string', enable: enableEditor, onInputChange: this.onInputChange
      },
    })
    return this;
  }

  build(): grid.FieldConfig[] {
    return this.fields;
  }

}