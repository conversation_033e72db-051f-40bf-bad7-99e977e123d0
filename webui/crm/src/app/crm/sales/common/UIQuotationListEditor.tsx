import React from 'react';
import * as FeatherIcon from 'react-feather'
import { server, grid, bs, entity, app, util } from '@datatp-ui/lib';

import { T } from '../backend';
import {
  calculator,
  CalculatorContextType
} from '../calculator';

import { GridConfigFactory, responsiveGridConfig } from './builder/QuoteGridConfigFactory';
import { PricePlugin, SearchParams, UITransportPriceList } from '../../price/common';
import { UISimpleMarginConfig } from './chargemodel/UICustomerChargeModel';
import { AddMethod } from '../../price';
import { CustomerEntityUtil } from './CustomerChargeUtils';
import { ContainerType } from '../../common/ContainerTypeUtil';

import { SQuotationCreation } from '../quotation/QuotationUtils';
import { TransportationMode, TransportationTool } from 'app/crm/common';

export interface UIQuoteListEditorProps extends entity.VGridEntityListEditorProps {
  observer: entity.ComplexBeanObserver;
  initContainers?: ContainerType[]
}

export class UIQuotationListEditor extends entity.VGridEntityListEditor<UIQuoteListEditorProps> {
  purpose: 'IMPORT' | 'EXPORT' = 'EXPORT'
  mode: TransportationMode;

  private scrollContainerRef: React.RefObject<HTMLDivElement> = React.createRef();

  getScrollContainerDimensions = (): { width: number, height: number } => {
    const container = this.scrollContainerRef.current;
    return {
      width: container?.clientWidth || 0,
      height: container?.clientHeight || 0
    };
  }

  constructor(props: UIQuoteListEditorProps) {
    super(props);
    const { observer, plugin, appContext } = this.props;
    let inquiry = observer.getComplexBeanProperty('inquiry', {});
    this.purpose = inquiry['purpose'] || 'EXPORT';
    this.mode = inquiry['mode'];
    let records: any[] = plugin.getModel().getRecords() || [];
    if (records.length === 0) {
      const inquiry: any = observer.getComplexBeanProperty('inquiry', {});
      let quote: any = {
        specificQuotationId: undefined,
        referenceCode: undefined,
        purpose: inquiry['purpose'],
        mode: inquiry['mode'],
        fromLocationCode: inquiry['fromLocationCode'],
        fromLocationLabel: inquiry['fromLocationLabel'],
        toLocationCode: inquiry['toLocationCode'],
        toLocationLabel: inquiry['toLocationLabel'],
        finalDestination: inquiry['finalDestination'],
        validity: util.TimeUtil.javaCompactDateTimeFormat(new Date()),
        refCurrency: 'USD',
        currency: 'USD',
        note: '',
        priceGroup: {}
      }
      appContext.addOSNotification('info', "The first quote is created by default");
      // this.vgridContext.model.addRecord(quote);
      plugin.getModel().addRecord(quote);
    }
  }

  componentDidUpdate(prevProps: UIQuoteListEditorProps) {
    if (prevProps.initContainers !== this.props.initContainers) {
      this.nextEditorViewId();
      this.forceUpdate();
    }
  }

  onSelect(_row: number, record: any) {
    super.onSelect(_row, record)
    this.onModify();
  }

  onModify() {
    const { observer, onModifyBean } = this.props;
    let records: Array<any> = this.vgridContext.model.getRecords();
    observer.replaceBeanProperty('quoteList', records)
    observer.updateMutableBean();
    if (onModifyBean) onModifyBean(records, entity.ModifyBeanActions.MODIFY);
  }

  createVGridConfig(): grid.VGridConfig {
    const { observer, initContainers } = this.props;
    let inquiry = observer.getComplexBeanProperty('inquiry', {});
    this.mode = inquiry['mode']

    const fieldConfigs: grid.FieldConfig[] = TransportationTool.isSeaLCL(this.mode)
      ? GridConfigFactory.createLCLConfig(observer, this)
      : TransportationTool.isSeaFCL(this.mode)
        ? GridConfigFactory.createFCLConfig(observer, this, initContainers)
        : GridConfigFactory.createAirConfig(observer, this)

    let config: grid.VGridConfig = {
      record: {
        dataCellHeight: 40,
        editor: {
          supportViewMode: ['table'],
          enable: true
        },
        fields: fieldConfigs,
      },
      toolbar: {
        hide: true,
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          }
        }
      },
    }
    return responsiveGridConfig(config);
  }

  override renderBeanEditor() {
    return (<div>Nothing to render</div>);
  }

  onAddQuote = () => {
    let { observer } = this.props;
    let quotationId: number = observer.getBeanProperty('id', null);
    if (!quotationId) {
      let message = (<div className="ms-1 text-info py-3 border-bottom">Save Quotation First!!!</div>);
      bs.dialogShow('Message', message, { backdrop: 'static', size: 'md' });
      return;
    }
    const inquiry: any = observer.getComplexBeanProperty('inquiry', {});
    let quote: any = {
      specificQuotationId: quotationId,
      referenceCode: undefined,
      purpose: inquiry['purpose'],
      mode: inquiry['mode'],
      fromLocationCode: inquiry['fromLocationCode'],
      fromLocationLabel: inquiry['fromLocationLabel'],
      toLocationCode: inquiry['toLocationCode'],
      toLocationLabel: inquiry['toLocationLabel'],
      finalDestination: inquiry['finalDestination'],
      validity: util.TimeUtil.javaCompactDateTimeFormat(new Date()),
      refCurrency: 'USD',
      currency: 'USD',
      note: '',
      priceGroup: {}
    }
    this.vgridContext.model.addRecord(quote);
    this.nextEditorViewId();
    this.onModify();
    this.forceUpdate()
  }

  onClearPrice = () => {
    const { appContext } = this.props;
    let selectedIds = this.vgridContext.model.getSelectedRecordIds().filter(sel => !!sel);
    this.vgridContext.model.removeSelectedDisplayRecords();
    if (selectedIds) {
      appContext.createHttpBackendCall('CustomerChargeService', 'deleteQuotationChargeByIds', { quoteChargeIds: selectedIds })
        .withSuccessData((_result: any) => {
        })
        .call();
    }
    this.onModify();
    this.forceUpdate()
  }

  onMatchPrice = () => {
    const { appContext, pageContext, observer } = this.props;
    let quotationId: number = observer.getBeanProperty('id', null);
    if (!quotationId) {
      let message = (<div className="ms-1 text-info py-3 border-bottom">Save Quotation First!!!</div>);
      bs.dialogShow('Message', message, { backdrop: 'static', size: 'md' });
      return;
    }

    let inquiry: any = observer.getComplexBeanProperty('inquiry', {});
    const onMultiSelect = (_appCtx: app.AppContext, _pageCtx: app.PageContext, prices: Array<any>) => {
      _pageCtx.back();
      let ids: Array<any> = prices
        .map(record => record['id'])
        .filter(id => id !== undefined);
      let uniqueIds = [...new Set(ids)];
      let params: any = { sQuotationId: quotationId, priceReferenceIds: uniqueIds }

      appContext.createHttpBackendCall('QuotationService', 'matchPriceSpecificQuotation', params)
        .withSuccessData((updateQuotation: any) => {
          observer.replaceWithUpdate(updateQuotation)
          // let quoteList: any[] = updateQuotation['quoteList'] || [];
          // this.vgridContext.model.update(quoteList);
          this.nextEditorViewId();
          this.onModify();
          this.forceUpdate()
        })
        .withFail((_response: server.BackendResponse) => {
          bs.notificationShow('danger', T('Match Prices Failed!'));
          this.vgridContext.getVGrid().forceUpdateView();
        })
        .call();
    }

    let searchParam: SearchParams = new SearchParams(inquiry['purpose'], inquiry['mode'] || TransportationMode.SEA_FCL);
    searchParam.fromLocationCode = inquiry['fromLocationCode'];
    searchParam.toLocationCode = inquiry['toLocationCode'];
    let plugin: PricePlugin = searchParam.pluginBuilder();

    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <div className='flex-vbox'>
          <UITransportPriceList plugin={plugin} appContext={appCtx} pageContext={pageCtx}
            onMultiSelect={onMultiSelect}
            onSelect={(appContext: app.AppContext, pageContext: app.PageContext, entity: any) => {
              onMultiSelect(appContext, pageContext, [entity])
            }} />
        </div>
      )
    }
    pageContext.createPopupPage('match-prices', T('Match Prices'), createAppPage, { size: 'xl', backdrop: 'static' });
  }

  onApplyMargin() {
    const { pageContext, observer } = this.props;
    const quotation: any = observer.getMutableBean();

    let chargeModel: any = observer.getComplexBeanProperty('customerChargeModel', {});
    if (!chargeModel || Object.keys(chargeModel).length === 0) {
      chargeModel = CustomerEntityUtil.createDefaultCustomerChargeModel();
    }

    let chargeMargin: any = chargeModel['transportChargeMargin'] || {};
    let marginConfig: any = { addMethod: AddMethod.Amount, margin: 0 }

    if (chargeMargin && TransportationTool.isSea(this.mode)) {
      marginConfig = chargeMargin['seaTransportChargeMargin'];
    } else if (chargeMargin && TransportationTool.isAir(this.mode)) {
      marginConfig = chargeMargin['airTransportChargeMargin'];
    }

    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {

      const onApplyMargin = (bean: any, targetContainerTypes: ContainerType[], reset: boolean = false) => {
        pageCtx.back();

        appCtx.addOSNotification("success", T('Apply Margin Success'));

        const calculatorContext = (this.context as CalculatorContextType)?.context || {}
        calculatorContext.typeOfContainers = targetContainerTypes;

        let selectedRecords: Array<any> = this.vgridContext.model.getSelectedRecords();
        if (selectedRecords.length === 0) {
          selectedRecords = this.vgridContext.model.getFilterRecords();
        }

        if (TransportationTool.isSeaLCL(this.mode)) {
          chargeMargin['seaTransportChargeMargin'] = bean;
          calculatorContext.chargeMargin = chargeMargin;
          for (let rec of selectedRecords) {
            calculator.seaLCL.calculatorQuote(rec, calculatorContext.chargeableVolume, reset);
            if (!reset) {
              calculator.seaLCL.applyMarginDep(calculatorContext, rec, true);
            }
          }
        } else if (TransportationTool.isSeaFCL(this.mode)) {
          chargeMargin['seaTransportChargeMargin'] = bean;
          calculatorContext.chargeMargin = chargeMargin;
          for (let rec of selectedRecords) {
            if (reset) {
              calculator.seaFCL.resetQuoteCosting(calculatorContext, rec);
            } else {
              calculator.seaFCL.applyMargin(calculatorContext, rec, true);
            }
          }

        } else if (TransportationTool.isAir(this.mode)) {
          chargeMargin['airTransportChargeMargin'] = bean;
          calculatorContext.chargeMargin = chargeMargin;
          for (let rec of selectedRecords) {
            calculator.air.calculatorQuote(rec, calculatorContext.chargeableWeight, reset);
            if (!reset) {
              calculator.air.applyMargin(calculatorContext, rec, true);
            }
          }
        }

        this.onModify()
        this.forceUpdate()
      }

      return (
        <div className='flex-vbox'>
          <UISimpleMarginConfig appContext={appCtx} pageContext={pageCtx}
            observer={new entity.ComplexBeanObserver(marginConfig)} quotation={quotation}
            onApplyMargin={onApplyMargin} />
        </div>
      );
    }
    pageContext.createPopupPage('margin-config', T('Margin Config'), createAppPage, { size: 'md', backdrop: 'static' });
  }

  render() {
    const { width } = this.getScrollContainerDimensions();
    let title = TransportationTool.isSea(this.mode) ? `Ocean Freight` : `Air Freight`;
    const lightColor = '#D6EFD8';
    const borderColor = '#80AF81';

    const hoverStyle: any = {
      boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
      transform: 'translateY(-2px)',
      backgroundColor: lightColor
    };

    return (
      <div className='flex-vbox h-100 pt-1'>

        <div className="bg-white border rounded-3 flex-vbox justify-content-center align-items-center" ref={this.scrollContainerRef}
          style={{
            minWidth: width - 30,
            borderColor: borderColor,
            transition: 'all 0.3s ease',
            marginBottom: '10px'
          }}
          onMouseEnter={(e) => Object.assign(e.currentTarget.style, hoverStyle)}
          onMouseLeave={(e) => {
            e.currentTarget.style.boxShadow = 'none';
            e.currentTarget.style.transform = 'translateY(0)';
            e.currentTarget.style.backgroundColor = '#fff';
          }}>
          <div className="flex-vbox bg-white rounded-md w-100 h-100">

            <div className="flex-hbox flex-grow-0 align-items-center justify-content-between px-2 py-1">

              <div className="flex-hbox flex-grow-0 justify-content-start align-items-center">
                <h5 style={{ color: '#6c757d' }}><FeatherIcon.Anchor className="me-2" size={16} />{title}</h5>
              </div>

              <div className="flex-hbox justify-content-end align-items-center gap-1">

                <bs.Button laf='info' className="border-0 border-end py-1 px-1 rounded-0" outline
                  onClick={() => this.onApplyMargin()}>
                  <FeatherIcon.Activity size={12} /> Apply Margin
                </bs.Button>

                <bs.Button laf='info' className="border-0 border-end py-1 px-1 rounded-0" outline
                  onClick={this.onMatchPrice}>
                  <FeatherIcon.Search size={12} /> Match Prices
                </bs.Button>

                <bs.Button laf='info' className="border-0 border-end py-1 px-1 rounded-0" outline
                  onClick={this.onAddQuote}>
                  <FeatherIcon.Plus size={12} /> Add Quote
                </bs.Button>

                <bs.Button laf='info' className="border-0 py-1 px-1 rounded-0" outline
                  onClick={this.onClearPrice}>
                  <FeatherIcon.Trash2 size={12} /> Clear Prices
                </bs.Button>

              </div>
            </div>

            <div className="flex-vbox" key={this.editorViewId}>
              <grid.VGrid context={this.vgridContext} />
            </div>

          </div>
        </div>
      </div>
    )
  }
}


