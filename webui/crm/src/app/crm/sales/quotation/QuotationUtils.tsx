import React from 'react';
import { app, bs, server, entity, util } from '@datatp-ui/lib';

import { T } from '../backend';

import { UISpecificQuotation } from "./specific/UISpecificQuotation";
import { QuoteExportRequest, } from "./specific/xlsx/UIQuotationExportUtil";
import { SQuotationExportProcessor } from "./specific/xlsx/UIQuotationExportUtil";
import { LOCAL_CHARGES } from './QuotationConfigDialog';

import { ImportExportPurpose, PurposeTool, TransportationMode, TransportationTool } from 'app/crm/common';
import { ContainerTypeUnit } from 'app/crm/common/ContainerTypeUtil';
export interface SQuotationCreation {
  priceReferenceIds: Array<number>;
  inquiryRequestId: number | undefined;
  isAutoSaved: boolean;
  inquiry: any;
}

export class UIQuotationUtils {
  static showUISQuotationById(ui: app.AppComponent, quotationId: number, popup: boolean = false) {
    const { appContext } = ui.props;
    appContext.createHttpBackendCall("QuotationService", "getSpecificQuotationById", { id: quotationId })
      .withSuccessData((quotation: any) => {
        this.showUISpecificQuotation(ui, quotation, '', popup);
      })
      .call()
  }

  static showUISpecificQuotation(ui: bs.BaseComponent, quotation: any, ref: string = '', popup: boolean = false): void {
    let uiAppComp = (ui as app.AppComponent);
    const { pageContext, readOnly } = uiAppComp.props;
    let observer = new entity.ComplexBeanObserver(quotation);
    if (!ref) {
      ref = (quotation['inquiry'] || {})['referenceCode'] || 'NEW QUOTATION';
    }

    let createPageContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (<UISpecificQuotation appContext={appCtx} pageContext={pageCtx} readOnly={readOnly} observer={observer} />);
    }
    let pageId = `squotation-detail-${util.IDTracker.next()}`;
    if (popup) {
      pageContext.createPopupPage(pageId, T(`SQuotation : ${ref}`), createPageContent, { size: 'xl', backdrop: 'static' });
    } else {
      pageContext.addPageContent(pageId, `${ref}`, createPageContent);
    }
  }

  static doExportQuoteAsXlsx(ui: bs.BaseComponent, request: QuoteExportRequest) {
    const { appContext } = (ui as app.AppComponent).props;
    appContext.createHttpBackendCall("QuotationDocumentService", "exportQuotationQuoteAsXlsx", { req: request })
      .withSuccessData((storeInfo: any) => {
        if (storeInfo.privateMode) {
          entity.StoreInfo.privateDownload(storeInfo);
        } else {
          entity.StoreInfo.download(storeInfo.url);
        }
      })
      .withFail((response?: server.BackendResponse) => {
        bs.notificationShow("danger", T("Export Quotation Failed!!"), response?.error.message);
        return;
      })
      .call();
  }

  static createExportRequest(uiQuotation: entity.AppDbComplexEntityEditor): QuoteExportRequest {
    const { observer } = uiQuotation.props;
    let quotation = observer.getMutableBean();
    let processor = new SQuotationExportProcessor(quotation);
    return processor.request;
  }

  static createNewSpecificQuotation(ui: app.AppComponent, template: SQuotationCreation, popup: boolean = false, successCb?: (quotation: any) => void): void {
    const { appContext } = ui.props;
    appContext.createHttpBackendCall('QuotationService', 'newSpecificQuotation', { template: template })
      .withSuccessData((quotation: any) => {
        //TODO: Dan - hardcode to fix bugs
        appContext.addOSNotification('success', "Create quotation success!!!");
        if (successCb) {
          successCb(quotation);
        } else {
          let inquiry: any = quotation['inquiry'];
          let mode: TransportationMode = inquiry['mode'];
          let purpose: 'IMPORT' | 'EXPORT' = inquiry['purpose'];

          if (TransportationTool.isSea(mode)) {
            const key = mode + '_' + purpose;
            const localCharges: any[] = LOCAL_CHARGES[key]
              .map(charge => ({
                ...charge,
                mode,
                currency: 'USD',
                quantity: '',
                unitPrice: '',
                totalAmount: '',
                note: '',
                quoteRate: {}
              }));
            let existingLocalCharges: any[] = quotation['localHandlingCharges'] || [];
            const existingCodes = new Set(existingLocalCharges.map(charge => charge.code));
            const newLocalCharges = localCharges.filter(charge => !existingCodes.has(charge.code));
            quotation['localHandlingCharges'] = [...existingLocalCharges, ...newLocalCharges];
          }
          UIQuotationUtils.showUISpecificQuotation(ui, quotation, '', popup);
        }
      })
      .withFail((response: server.BackendResponse) => {
        let title = T('Create Quotation Failed!');
        appContext.addOSNotification('danger', title, response.error, response);
        let message = response.error.message;
        if (message) {
          bs.notificationShow("danger", title, <div className='text-danger'>{message}</div>);
        }
        return;
      })
      .call();
  }

  static doExportQuotationTemplate = (sQuotation: any): entity.DataListExportModel => {
    const quoteList: any[] = sQuotation['quoteList'] || [];
    let mode: TransportationMode = quoteList[0]['mode'];
    let purpose: ImportExportPurpose = quoteList[0]['purpose'];

    const priceFields = {
      NONE_US: [
        { label: '20DC', name: 'dry20Price', dataType: 'currency' },
        { label: '40DC', name: 'dry40Price', dataType: 'currency' },
        { label: '40HC', name: 'highCube40Price', dataType: 'currency' },
      ],
      US_ROUTE: [
        { label: '20DC', name: 'dry20Price', dataType: 'currency' },
        { label: '40DC', name: 'dry40Price', dataType: 'currency' },
        { label: '40HC', name: 'highCube40Price', dataType: 'currency' },
        { label: '45HC', name: 'highCube45Price', dataType: 'currency' },
        { label: '40NOR', name: 'nor40Price', dataType: 'currency' },
      ],
      SPECIAL: [
        { label: '20RF', name: 'reefer20Price', dataType: 'currency' },
        { label: '40RF', name: 'reefer40Price', dataType: 'currency' },
      ],
      LCL: [
        { label: 'Minimum (< 1 CBM)', name: 'refFreightChargeLCL', dataType: 'currency' },
        { label: '< 2 CBM', name: 'refLess2CbmPrice', dataType: 'currency' },
        { label: '< 3 CBM', name: 'refLess3CbmPrice', dataType: 'currency' },
        { label: '< 5 CBM', name: 'refLess5CbmPrice', dataType: 'currency' },
        { label: '< 7 CBM', name: 'refLess7CbmPrice', dataType: 'currency' },
        { label: '< 10 CBM', name: 'refLess10CbmPrice', dataType: 'currency' },
        { label: '< 15 CBM', name: 'refGeq10CbmPrice', dataType: 'currency' },
      ],
      AIR: [
        { label: 'Min', name: 'refMinPrice', dataType: 'currency' },
        { label: '-45(KG)', name: 'refLevel1Price', dataType: 'currency' },
        { label: '+45(KG)', name: 'refLevel2Price', dataType: 'currency' },
        { label: '+100(KG)', name: 'refLevel3Price', dataType: 'currency' },
        { label: '+300(KG)', name: 'refLevel4Price', dataType: 'currency' },
        { label: '+500(KG)', name: 'refLevel5Price', dataType: 'currency' },
        { label: '+1000(KG)', name: 'refLevel6Price', dataType: 'currency' },
      ]
    };

    let selectedPriceFields: entity.Field[] = priceFields.NONE_US;

    if (TransportationTool.isAir(mode)) {
      selectedPriceFields = priceFields.AIR;
    } else if (TransportationTool.isSeaLCL(mode)) {
      selectedPriceFields = priceFields.LCL;
    } else if (TransportationTool.isSeaFCL(mode)) {
      if (PurposeTool.isImport(purpose)) {
        selectedPriceFields = priceFields.NONE_US;
      } else if (PurposeTool.isExport(purpose)) {
        let record = quoteList[0] || {};
        if (record.priceGroup?.reefer20Price != 0 || record.priceGroup?.reefer40Price != 0) {
          selectedPriceFields = priceFields.SPECIAL;
        } else if (
          record.priceGroup?.highCube45Price != 0 ||
          (record.priceGroup?.nor40Price && record.priceGroup?.nor40Price != 0)
        ) {
          selectedPriceFields = priceFields.US_ROUTE;
        } else {
          selectedPriceFields = priceFields.NONE_US;
        }
      }
    }

    let selectedGroupField: entity.GroupField = {
      label: "_blank_",
      fields: [
        { label: 'STT.', name: 'stt', dataType: 'number' },
        { label: 'POL', name: 'fromLocationLabel', dataType: 'string' },
        { label: 'POD', name: 'toLocationLabel', dataType: 'string' },
        { label: 'Carrier', name: 'carrierLabel', dataType: 'string' },
        { label: 'Curr', name: 'currency', dataType: 'string' },
        ...selectedPriceFields,
        { label: 'Frequency', name: 'frequency', dataType: 'string' },
        { label: 'T/T (days)', name: 'transitTime', dataType: 'string' },
        { label: 'Via', name: 'transitPort', dataType: 'string' },
        { label: 'Free Time', name: 'freeTime', dataType: 'string' },
        { label: 'Effective Date', name: 'effectiveDate', dataType: 'date' },
        { label: 'Valid date', name: 'validity', dataType: 'date' },
        { label: 'Cost Ref', name: 'referenceCode', dataType: 'string' },
        { label: 'Remark', name: 'note', dataType: 'string' },
      ],
    };

    let records: Array<any> = [];
    for (let i = 0; i < quoteList.length; i++) {
      let record = quoteList[i];
      let newRecord: any = {
        stt: i + 1,
        fromLocationLabel: record['fromLocationLabel'],
        toLocationLabel: record['toLocationLabel'],
        carrierLabel: record['carrierLabel'],
        currency: record['currency'],
        frequency: record['frequency'],
        freeTime: record['freeTime'],
        transitTime: record['transitTime'],
        effectiveDate: record['effectiveDate'],
        validity: record['validity'],
        note: record['note'],
        referenceCode: record['referenceCode'],
      };

      if (TransportationTool.isAir(mode)) {
        newRecord.refMinPrice = record.priceGroup?.refMinPrice || '';
        newRecord.refLevel1Price = record.priceGroup?.refLevel1Price || '';
        newRecord.refLevel2Price = record.priceGroup?.refLevel2Price || '';
        newRecord.refLevel3Price = record.priceGroup?.refLevel3Price || '';
        newRecord.refLevel4Price = record.priceGroup?.refLevel4Price || '';
        newRecord.refLevel5Price = record.priceGroup?.refLevel5Price || '';
        newRecord.refLevel6Price = record.priceGroup?.refLevel6Price || '';
      } else if (TransportationTool.isSeaLCL(mode)) {
        newRecord.refFreightChargeLCL = record.priceGroup?.refFreightChargeLCL || '';
        newRecord.refLess2CbmPrice = record.priceGroup?.refLess2CbmPrice || '';
        newRecord.refLess3CbmPrice = record.priceGroup?.refLess3CbmPrice || '';
        newRecord.refLess5CbmPrice = record.priceGroup?.refLess5CbmPrice || '';
        newRecord.refLess7CbmPrice = record.priceGroup?.refLess7CbmPrice || '';
        newRecord.refLess10CbmPrice = record.priceGroup?.refLess10CbmPrice || '';
        newRecord.refGeq10CbmPrice = record.priceGroup?.refGeq10CbmPrice || '';
      } else if (TransportationTool.isSeaFCL(mode)) {
        if (selectedPriceFields === priceFields.SPECIAL) {
          newRecord.reefer20Price = record.priceGroup?.reefer20Price || '';
          newRecord.reefer40Price = record.priceGroup?.reefer40Price || '';
        } else if (selectedPriceFields === priceFields.US_ROUTE) {
          newRecord.dry20Price = record.priceGroup?.dry20Price || '';
          newRecord.dry40Price = record.priceGroup?.dry40Price || '';
          newRecord.highCube40Price = record.priceGroup?.highCube40Price || '';
          newRecord.highCube45Price = record.priceGroup?.highCube45Price || '';
          newRecord.nor40Price = record.priceGroup?.nor40Price || '';
        } else {
          newRecord.dry20Price = record.priceGroup?.dry20Price || '';
          newRecord.dry40Price = record.priceGroup?.dry40Price || '';
          newRecord.highCube40Price = record.priceGroup?.highCube40Price || '';
        }
      }

      records.push(newRecord);
    }

    let exportModel: entity.DataListExportModel = {
      fieldGroups: [selectedGroupField],
      fields: [...selectedGroupField.fields],
      records: records,
      modelName: 'Price_Data',
      fileName: `Price_Data_${util.TimeUtil.toCompactDateFormat(new Date())}.xlsx`,
    };

    return exportModel;
  }


}
