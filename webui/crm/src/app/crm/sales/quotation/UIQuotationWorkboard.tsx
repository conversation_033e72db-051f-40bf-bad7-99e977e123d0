import React from 'react';
import * as FeatherIcon from 'react-feather';
import { bs, app, util, entity, input } from '@datatp-ui/lib';
import { WQuickTimeRangeSelector } from '../../common/UIDashboardUtility';
import { UISpecificQuotationList, UISpecificQuotationPlugin } from './specific/UISpecificQuotationList';
import { UIBookingList, UIBookingListPlugin } from '../booking/UIBookingList';
import { UIMailBulkCargoRequestPricing } from '../../price/request/UIBulkCargoInquiryRequest';

const SESSION = app.host.DATATP_HOST.session;

interface UIInteractivePanelProps extends app.AppComponentProps { }
class UIInterractivePanel extends app.AppComponent<UIInteractivePanelProps> {

  render(): React.ReactNode {
    const { appContext, pageContext } = this.props;
    let plugin: UISpecificQuotationPlugin = new UISpecificQuotationPlugin('User').withPinned();

    return (
      <div className="flex-vbox flex-grow-0 align-items-center justify-content-between border-top" style={{ maxHeight: 300, minHeight: 300 }}>

        <div className='flex-hbox flex-grow-0 border-top justify-content-start align-items-center w-100' >

          <div className='mt-1 py-1 px-2 flex-hbox flex-grow-0 align-items-center justify-content-start'>
            <h5 style={{ color: '#6c757d' }}>
              <FeatherIcon.Slack className="me-2" size={18} />
              Interactive Panel (Not Implemented Yet)
            </h5>
          </div>

          <div className="flex-hbox justify-content-end align-items-center flex-grow-1 gap-1 ms-2 " >

            <div className={'btn-group flex-hbox-grow-0 align-self-end'} role="group" aria-label={'Button Group'}>

              <bs.Button laf={'secondary'} outline className={'p-1'} style={{ minWidth: '100px' }}
                onClick={() => { }} >
                <FeatherIcon.FastForward size={14} className="me-1" /> Quick Acts
              </bs.Button>

              <bs.Button laf={'secondary'} outline className={'p-1'} style={{ minWidth: '100px' }}
                onClick={() => { }} >
                <FeatherIcon.BookOpen size={14} className="me-1" /> Quote History
              </bs.Button>

              <bs.Button laf={'secondary'} outline className={'p-1'} style={{ minWidth: '100px' }} disabled
                onClick={() => { }}>
                <FeatherIcon.Layers size={14} className="me-1" /> Quote Pinned
              </bs.Button>

            </div>

          </div>

        </div>

        <div className='flex-hbox border-top border-dashed w-100 h-100'>
          <UISpecificQuotationList
            appContext={appContext} pageContext={pageContext} space='User' plugin={plugin} />
        </div>
      </div>
    )
  }
}


interface ReportFilter {
  dateFilter: { fromValue: string, toValue: string, label: string };
  enablePanel: boolean;
}

export interface UIQuotationWorkboardProps extends app.AppComponentProps {
  space: 'User' | 'Company' | 'System';
}

interface UIQuotationWorkboardState { }

export class UIQuotationWorkboard extends app.AppComponent<UIQuotationWorkboardProps, UIQuotationWorkboardState> {
  reportFilter: ReportFilter;
  viewId: number = util.IDTracker.next();
  filterPattern: string;
  private quotationListRef: React.RefObject<UISpecificQuotationList>;


  constructor(props: UIQuotationWorkboardProps) {
    super(props);

    this.quotationListRef = React.createRef<UISpecificQuotationList>();
    this.filterPattern = '';

    let dateFilterObj;
    const dateFilterStr = localStorage.getItem('quotationDateFilter');
    if (dateFilterStr) {
      try {
        dateFilterObj = JSON.parse(dateFilterStr);
      } catch (e) {
        dateFilterObj = null;
      }
    }

    if (!(dateFilterObj && dateFilterObj.fromValue && dateFilterObj.toValue)) {
      // Nếu không có trong localStorage thì set mặc định là quý hiện tại
      const today = new Date();
      const currentMonth = today.getMonth();
      const currentQuarter = Math.floor(currentMonth / 3);
      const firstDayOfQuarter = new Date(today.getFullYear(), currentQuarter * 3, 1);
      const lastDayOfQuarter = new Date(today.getFullYear(), currentQuarter * 3 + 3, 0);

      dateFilterObj = {
        fromValue: util.TimeUtil.javaCompactDateTimeFormat(firstDayOfQuarter),
        toValue: util.TimeUtil.javaCompactDateTimeFormat(lastDayOfQuarter),
        label: 'This Quarter'
      };
    }

    this.reportFilter = {
      dateFilter: dateFilterObj,
      enablePanel: localStorage.getItem('enableQuotationPanel') === 'true'
    }

    this.loadData();
  }

  onViewAllBooking = () => {
    let { pageContext, space } = this.props;
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (<UIBookingList appContext={appCtx} pageContext={pageCtx} plugin={new UIBookingListPlugin(space)} />)
    }
    let popupId = `view-all-booking-${util.IDTracker.next()}`;
    let pupupLabel: string = `Booking List`;
    pageContext.createPopupPage(popupId, pupupLabel, createAppPage, { size: 'xl', backdrop: 'static' });
  }

  onShowBulkCargoInquiryRequest = (record: any) => {
    const { pageContext } = this.props;
    let fullName = SESSION.getAccountAcl().getFullName();
    let companyLabel = SESSION.getAccountAcl().getCompanyAcl().companyLabel;
    let title = `Bulk Cargo Request (${fullName} - ${companyLabel})`;

    const createContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <UIMailBulkCargoRequestPricing appContext={appCtx} pageContext={pageCtx}
          observer={new entity.ComplexBeanObserver(record)} onPostCommit={(entity: any) => {
            pageCtx.back();
            this.forceUpdate();
          }} />
      )
    }
    pageContext.createPopupPage('create-cargo-inquiry-request', (title), createContent, { size: 'flex-lg' })

  }

  onLoadBulkCargoInquiryRequest = (inquiryId?: number) => {
    let { appContext } = this.props;

    if (!inquiryId) {
      let request = {
        cargoType: 'BULK',
        unit: 'TNE'
      }
      appContext.createHttpBackendCall('TransportPriceMiscService', 'initBulkCargoInquiryRequest', { request: request })
        .withSuccessData((data: any) => {
          this.onShowBulkCargoInquiryRequest(data);
        })
        .withFail(() => {
          this.onShowBulkCargoInquiryRequest({});
        })
        .call();
      return;
    }

    appContext
      .createHttpBackendCall('TransportPriceMiscService', 'getBulkCargoInquiryRequest', { 'requestId': inquiryId })
      .withSuccessData((request: any) => {
        let newRequest: any = {
          ...request,
          status: 'IN_PROGRESS',
          id: undefined,
          code: undefined,
          mailSubject: undefined,
          mailMessage: undefined,
          pricingDate: undefined,
          pricingAccountId: undefined,
          pricingLabel: undefined,
          feedback: '',
          pricingNote: undefined,
          shipmentDetail: {
            ...request.shipmentDetail,
            laydaysDate: util.TimeUtil.javaCompactDateTimeFormat(new Date()),
          },
          requestDate: util.TimeUtil.javaCompactDateTimeFormat(new Date())
        }
        this.onShowBulkCargoInquiryRequest(newRequest);
      })
      .call();
  }

  renderHeader() {
    const { appContext, pageContext } = this.props;
    const { dateFilter } = this.reportFilter;
    const writeCap = pageContext.hasUserWriteCapability();

    return (
      <div className="flex-hbox flex-grow-0 align-items-center justify-content-between px-2 py-1">

        <div className="flex-hbox justify-content-start align-items-center flex-grow-0 gap-2" >
          <h5 style={{ color: '#6c757d' }}>
            <FeatherIcon.Trello className="me-2" size={18} />
            Quotation Workboard
          </h5>

          <div className='flex-hbox align-items-center flex-grow-0 border-start' key={this.filterPattern}>

            <input.WStringInput className={'flex-hbox border-0'} style={{ maxWidth: 300, minWidth: 300 }}
              name='search' value={this.filterPattern}
              placeholder={'Filter Expression'} onChange={(oldVal: any, newVal: any) => {
                this.filterPattern = newVal;
                if (this.quotationListRef.current) {
                  let uiList: UISpecificQuotationList = this.quotationListRef.current;
                  uiList.getVGridContext().model.getRecordFilter().withPattern(newVal);
                  uiList.getVGridContext().model.filter();
                  uiList.forceUpdate();
                }
              }} />

          </div>
        </div>

        <div className="flex-hbox justify-content-end align-items-center flex-grow-1 gap-1" >

          <bs.Button laf="warning" outline className="py-1 px-2"
            onClick={() => this.onLoadBulkCargoInquiryRequest()} hidden={bs.ScreenUtil.isMobileScreen() || !writeCap}>
            <FeatherIcon.Plus size={12} /> Bulk Cargo
          </bs.Button>

          <WQuickTimeRangeSelector appContext={appContext} pageContext={pageContext}
            initBean={dateFilter}
            onModify={(bean: any, _field: string, _oldVal: any, _newVal: any) => {
              this.reportFilter.dateFilter = bean;
              localStorage.setItem('quotationDateFilter', JSON.stringify(bean));
              this.viewId = util.IDTracker.next();
              this.loadData();
            }} />

          <div className='flex-hbox align-items-center flex-grow-0 px-2 border border-secondary rounded-md'>
            <div className="form-check form-switch d-flex align-items-center my-0">
              <input className="form-check-input mt-0 me-2" type="checkbox"
                role="switch" id="quotationPanelToggle" checked={this.reportFilter.enablePanel}
                onChange={() => {
                  let _newVal: any = !this.reportFilter.enablePanel;
                  this.reportFilter.enablePanel = _newVal;
                  localStorage.setItem('enableQuotationPanel', _newVal);
                  this.forceUpdate();
                }} />
              <label className="form-check-label mb-0 d-flex align-items-center" htmlFor="quotationPanelToggle" >
                <span>{'Toggle Panel'}</span>
              </label>
            </div>
          </div>

          <bs.Button laf="secondary" outline size="sm" className="p-1" style={{ color: '#6c757d' }}
            onClick={this.onViewAllBooking}>
            Booking List
            <FeatherIcon.ChevronRight size={14} className="ms-2" />
          </bs.Button>

        </div>

      </div>

    )
  }

  render(): React.ReactNode {
    const { appContext, pageContext, space } = this.props;
    const { dateFilter, enablePanel } = this.reportFilter;
    let plugin: UISpecificQuotationPlugin = new UISpecificQuotationPlugin(space);
    plugin.withRequestDate(dateFilter.fromValue, dateFilter.toValue);

    return (
      <div className="flex-vbox mx-1 p-1 bg-white rounded-md w-100 h-100 my-1" >
        {this.renderHeader()}
        <div className="flex-vbox border-top bg-body-highlight" key={this.viewId}>
          <UISpecificQuotationList ref={this.quotationListRef}
            appContext={appContext} pageContext={pageContext} space={space} plugin={plugin} />
        </div>

        {enablePanel && <UIInterractivePanel appContext={appContext} pageContext={pageContext} />}

      </div>
    )
  }
}