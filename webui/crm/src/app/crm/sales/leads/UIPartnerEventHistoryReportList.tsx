import React from "react";
import * as FeatherIcon from 'react-feather';
import { module } from '@datatp-ui/erp';
import { util, grid, bs, entity, app, sql, input } from '@datatp-ui/lib';
import { T } from "../backend";
import { buildTooltipValues } from "../../price";
import { UIPartnerEventHistoryList, UIPartnerEventHistoryListPlugin } from "./UIPartnerEventHistoryList";

const SESSION = app.host.DATATP_HOST.session;
export class UIPartnerEventHistoryReportPlugin extends entity.DbEntityListPlugin {
  constructor() {
    super([]);

    this.backend = {
      context: 'company',
      service: 'CustomerLeadsService',
      searchMethod: 'searchPartnerEventHistoryReports',
    }

    this.searchParams = {
      params: {},
      filters: sql.createSearchFilter(),
      rangeFilters: [
        sql.createDateTimeFilterNew("transactionDate", "Event Date")
      ],
      maxReturn: 1000
    }

  }

  withTransactionDate(fromValue: string, toValue: string) {
    if (this.searchParams) {
      let rangeFilters = this.searchParams.rangeFilters;
      if (rangeFilters) {
        for (let i = 0; i < rangeFilters.length; i++) {
          let filter = rangeFilters[i];
          if (filter.name === 'transactionDate') {
            filter.fromValue = fromValue;
            filter.toValue = toValue;
            break;
          }
        }
      }
      this.searchParams.rangeFilters = rangeFilters;
    }
    return this;
  }

  loadData(uiList: entity.DbEntityList<any>): void {
    this.createBackendSearch(uiList, { params: this.searchParams }).call();
  }

}

export class UIPartnerEventHistoryReportList extends entity.DbEntityList {

  createVGridConfig(): grid.VGridConfig {

    const renderTooltipAdvanced = (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
      const record = dRecord.record;
      const val = record[field.name];

      const tooltipFields = [
        { key: 'label', label: 'Label' },
        { key: 'partnerName', label: 'Partner Name' },
        { key: 'partnerAddress', label: 'Partner Address' },
        { key: 'shippingRoute', label: 'Route' },
        { key: 'volumeNote', label: 'Volume Note' },
        { key: 'companySupport', label: 'Company Support' },
        { key: 'description', label: 'Description' },
      ];

      const { htmlFormat, textFormat } = buildTooltipValues(record, tooltipFields);

      const handleClick = () => {
        navigator.clipboard.writeText(textFormat);
        bs.toastShow('Copied to clipboard!', { type: 'success' });
      };

      return (
        <bs.CssTooltip width={400}>
          <bs.CssTooltipToggle >
            <div className='flex-hbox' onClick={handleClick}>
              {field.fieldDataGetter ? field.fieldDataGetter(record) : field.format ? field.format(val) : val}
            </div>
          </bs.CssTooltipToggle>
          <bs.CssTooltipContent>
            {htmlFormat}
          </bs.CssTooltipContent>
        </bs.CssTooltip>
      );
    }

    let config: grid.VGridConfig = {
      record: {
        dataCellHeight: 40,
        editor: {
          supportViewMode: ['table'],
          enable: false,
        },
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          entity.DbEntityListConfigTool.FIELD_ON_SELECT('partnerCode', T('Partner Code'), 150),
          {
            name: 'partnerName', label: T(`Partner Name`), width: 300,
            customRender: renderTooltipAdvanced,
          },
          {
            name: 'transactionDate', label: T(`Event Date`), width: 100,
            format: util.text.formater.compactDate, filterable: true, filterableType: 'date',
            customRender: renderTooltipAdvanced,
          },
          {
            name: 'salemanLabel', label: T(`Salesman`), width: 200, filterable: true, filterableType: 'string',
            customRender: renderTooltipAdvanced,
          },
          {
            name: 'type', label: T(`Type`), width: 150, filterable: true, filterableType: 'options',
            customRender: renderTooltipAdvanced,
          },
          {
            name: 'label', label: T(`Label`), width: 250,
            customRender: renderTooltipAdvanced,
          },
          {
            name: 'partnerAddress', label: T(`Partner Address`), width: 300, state: { visible: false },
            customRender: renderTooltipAdvanced,
          },
          {
            name: 'shippingRoute', label: T(`Route`), width: 300,
            customRender: renderTooltipAdvanced,
          },
          {
            name: 'volumeNote', label: T(`Volume Note`), width: 300,
            customRender: renderTooltipAdvanced,
          },
          {
            name: 'companySupport', label: T(`Company Support`), width: 300,
            customRender: renderTooltipAdvanced,
          },
          {
            name: 'description', label: T(`Description`), width: 350,
            customRender: renderTooltipAdvanced
          },
        ],
      },
      footer: {
      },
      toolbar: {
        filters: entity.DbEntityListConfigTool.TOOLBAR_FILTERS(true),
        actions: [
          {
            name: "export-xlsx", label: 'Export Xlsx',
            createComponent: function (ctx: grid.VGridContext) {
              let uiRoot = ctx.uiRoot as any;
              const { appContext, pageContext } = uiRoot.props;
              return (<entity.XlsxExportButton appContext={appContext} pageContext={pageContext} context={ctx}
                options={{
                  fileName: `Partner_Event_History_${util.TimeUtil.toCompactDateFormat(new Date())}.xlsx`,
                  modelName: 'Partner_Even_History'
                }}
              />)
            }
          },
        ],
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          }
        }
      },
    }
    return config;
  }

  onDefaultSelect(dRecord: grid.DisplayRecord): void {
    let { pageContext } = this.props;
    let record = dRecord.record;
    let referencePartnerId = record.referencePartnerId;
    let referencePartnerType = record.referencePartnerType;

    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <div className='flex-vbox'>
          <div className="flex-vbox flex-grow-0">
            <bs.Row>
              <bs.Col span={3}>
                <input.BBStringField
                  bean={record} field='partnerCode' label={T("Partner No.")} disable />
              </bs.Col>
              <bs.Col span={3}>
                <input.BBStringField
                  bean={record} field='taxCode' label={T("Tax code.")} disable />
              </bs.Col>
              <bs.Col span={6}>
                <input.BBStringField bean={record} field='partnerName' label={T("Partner Name")} disable />
              </bs.Col>
            </bs.Row>
            <bs.Row>
              <bs.Col span={12}>
                <input.BBTextField bean={record} label={T('Address')} field="partnerAddress" disable style={{ height: '3em' }} />
              </bs.Col>
            </bs.Row>
          </div>

          <UIPartnerEventHistoryList
            appContext={appCtx} pageContext={pageCtx}
            plugin={new UIPartnerEventHistoryListPlugin().withReferencePartner(referencePartnerId, referencePartnerType)}
            referencePartnerId={referencePartnerId} referencePartnerType={referencePartnerType} hideToolbar={true} readOnly />

        </div>
      )
    }
    let pupupLabel: string = `Customer: ${record.partnerName}`;
    pageContext.createPopupPage('partner-event-history', pupupLabel, createAppPage, { size: 'lg', backdrop: 'static' });
  }


}
