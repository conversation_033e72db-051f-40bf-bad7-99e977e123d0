import React from "react";

import * as FeatherIcon from 'react-feather'
import { module } from '@datatp-ui/erp';
import { util, grid, bs, entity, app, input } from '@datatp-ui/lib';

import { T } from "../backend";
import { UICustomerAgentEditor, UICustomerLead, UICustomerLeadSimpleEditor } from "./UICustomerLead";
import { LeadUtils } from "./LeadUtils";
import { buildTooltipValues, WRateFinderGridFilter } from "../../price";
import { UICustomerLeadList, UICustomerLeadPlugin } from "./UICustomerLeadList";
import { WQuickTimeRangeSelector, WTaskGroupBySelector } from "app/crm/common";
import { responsiveGridConfig } from "../common";
import { EventBean, UITaskCalendarUtils } from "../report/UITaskCalendarPage";

export interface UICustomerLeadTreeProps extends entity.DbEntityListProps {
  groupBy: 'SALEMAN' | 'TEAM';
}
export class UICustomerLeadTree extends entity.DbEntityList<UICustomerLeadTreeProps> {

  createVGridConfig(): grid.VGridConfig {
    const { groupBy } = this.props;

    const renderTooltipAdvanced = (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
      const record = dRecord.record;
      const { fieldDataGetter, format } = field;
      let val = field.name === '_index_' ? dRecord.getDisplayRow() : record[field.name];
      if (fieldDataGetter) val = fieldDataGetter(record)
      else if (format) val = format(val)

      const tooltipFields = [
        { key: 'name', label: 'Lead Name' },
        { key: 'routing', label: 'Route' },
        { key: 'volumeNote', label: 'Volume' },
        { key: 'customerOnboardingProgress', label: 'Progress' },
        { key: 'kcnLabel', label: 'Industrial Park' },
        { key: 'address', label: 'Address' },
      ];

      // Build tooltip content from record fields
      const { htmlFormat, textFormat } = buildTooltipValues(record, tooltipFields);

      const handleClick = () => {
        navigator.clipboard.writeText(textFormat);
        bs.toastShow('Copied to clipboard!', { type: 'success' });
      };

      let offsetX = field.width || 120

      return (
        <bs.CssTooltip width={400} offset={{ x: offsetX, y: 0 }}>
          <bs.CssTooltipToggle >
            <div className='flex-hbox' onClick={handleClick}>
              {field.fieldDataGetter ? field.fieldDataGetter(record) : field.format ? field.format(val) : val}
            </div>
          </bs.CssTooltipToggle>
          <bs.CssTooltipContent>
            {htmlFormat}
          </bs.CssTooltipContent>
        </bs.CssTooltip>
      );
    }

    let config: grid.VGridConfig = {
      record: {
        control: {
          width: 40,
          items: [
            {
              name: 'view-detail', hint: 'View Detail', icon: FeatherIcon.ExternalLink,
              onClick: (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
                if (dRecord.record['groupType'] !== 'Lead') return;
                let uiList = ctx.uiRoot as UICustomerLeadTree;
                uiList.onViewLead(dRecord);
              },
            },
          ]
        },
        dataCellHeight: 40,
        editor: {
          supportViewMode: ['table'],
          enable: true
        },
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector()),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          {
            name: 'name', label: T(`Lead Name`), width: 350, filterable: true, container: 'fixed-left',
            fieldDataGetter: (record: any) => {
              return record['salemanLabel'] || 'N/A';
            },
            customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let uiList = ctx.uiRoot as entity.DbEntityList
              const { appContext, pageContext } = uiList.props;
              let type = record['groupType'];

              if (type === 'Saleman') {
                let employeeName: string = record['salemanLabel'] || 'N/A';

                const onCollapseRecord = (dRecord: grid.DisplayRecord) => {
                  dRecord.model['collapse'] = !dRecord.model['collapse'];
                  let displayRecordList = ctx.model.getDisplayRecordList();
                  if (displayRecordList instanceof grid.TreeDisplayModel) {
                    displayRecordList.updateDisplayRecords();
                    ctx.getVGrid().forceUpdateView();
                  }
                }

                return (
                  <div className='flex-hbox justify-content-center align-items-center' onClick={() => onCollapseRecord(dRecord)}>
                    <module.account.WAvatars className='px-2'
                      appContext={appContext} pageContext={pageContext} avatarIds={[record['salemanAccountId']]}
                      avatarIdType='AccountId' width={20} height={20} borderRadius={10} />
                    <div className="flex-hbox" style={{
                      cursor: 'pointer',
                      userSelect: 'text',
                      WebkitUserSelect: 'text',
                      MozUserSelect: 'text',
                      msUserSelect: 'text'
                    }}
                      onContextMenu={(e) => {
                        e.stopPropagation();
                        const range = document.createRange();
                        range.selectNodeContents(e.currentTarget);
                        const selection = window.getSelection();
                        selection?.removeAllRanges();
                        selection?.addRange(range);
                      }} >
                      {employeeName}
                    </div>
                  </div>
                )
              }

              let val = record[_field.name] || 'N/A';
              return (
                <bs.CssTooltip position='bottom-right' width={400} offset={{ x: 200, y: -10 }}>
                  <bs.CssTooltipToggle className='flex-hbox justify-content-start h-100 w-100'>
                    <div className="flex-grow-1" onClick={() => uiList.onSelect(dRecord)}>
                      {util.text.formater.uiTruncate(val, 350, true)}
                    </div>
                  </bs.CssTooltipToggle>
                  <bs.CssTooltipContent className='flex-vbox'>
                    <div className="tooltip-body">
                      {val}
                    </div>
                  </bs.CssTooltipContent>
                </bs.CssTooltip>
              )
            },
          },
          {
            name: 'date', label: T(`Date Created`), width: 130, format: util.text.formater.compactDate,
            filterable: true, filterableType: 'date',
            fieldDataGetter: (record: any) => {
              if (record['date'] === '-') return record['date'];
              return util.text.formater.compactDate(record['date']);
            },
            customRender: renderTooltipAdvanced
          },
          {
            name: 'taxCode', label: T(`Taxcode`), width: 130,
            customRender: renderTooltipAdvanced
          },
          {
            name: 'source', label: T(`Source`), width: 180,
          },
          {
            name: 'industryLabel', label: T(`Field Industry`), width: 180,
          },
          // {
          //   name: 'routing', label: T(`Route`), width: 200,
          //   customRender: renderTooltipAdvanced
          // },
          // {
          //   name: 'volumeNote', label: T(`Volume`), width: 200,
          //   customRender: renderTooltipAdvanced
          // },
          // {
          //   name: 'customerOnboardingProgress', label: T(`Progress`), width: 200,
          //   customRender: renderTooltipAdvanced
          // },
          // {
          //   name: 'kcnLabel', label: T(`Industrial Park`), width: 200,
          //   format(val) {
          //     return util.text.formater.uiTruncate(val, 200, true);
          //   },
          // },
          { name: 'countryLabel', label: T(`Country`), width: 150 },
          { name: 'accountCreatorLabel', label: T(`Creator`), width: 180 },
          {
            name: 'modifiedTime', label: T('Modified Time'), width: 120,
            filterable: true, filterableType: 'date', format: util.text.formater.compactDate,
            fieldDataGetter: (record: any) => {
              if (record['modifiedTime'] === '-') return record['modifiedTime'];
              return util.text.formater.compactDate(record['modifiedTime']);
            },
            customRender: renderTooltipAdvanced
          },
          {
            name: 'starRating', label: T('Rating'), width: 80,
            customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let customerLead = dRecord.record;
              if (customerLead['starRating'] === '-') return customerLead['starRating'];
              let currentStar = LeadUtils.getStarRatingInfo(customerLead['starRating']);
              let StarIcon = currentStar.icon;
              let label = currentStar.label;
              return (
                <div className={`d-flex flex-center px-2 py-1 rounded-2 w-100`}
                  style={{ backgroundColor: currentStar.hexColor, color: '#212529' }}>
                  <StarIcon size={14} className="me-1" />
                  <span>{label}</span>
                </div>
              )
            }
          },
          {
            name: 'status', label: T('Status'), width: 150, filterable: true, container: 'fixed-right',
            customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              if (record['status'] === '-') return record['status'];
              let currentStatus = LeadUtils.getStatusInfo(record['status']);
              let StatusIcon = currentStatus.icon;
              let label = currentStatus.label;
              let color = currentStatus.color;
              return (
                <div className={`flex-hbox flex-center px-2 py-2 rounded-2 bg-${color}-subtle text-${color} w-100 `}>
                  <StatusIcon size={14} className="me-1" />
                  <span>{label}</span>
                </div>
              );
            }
          },
        ],
      },
      toolbar: {
        hide: true,
      },
      view: {
        currentViewName: 'tree',
        availables: {
          tree: {
            viewMode: 'tree',
            label: 'Tree View',
            treeField: 'name',
            plugin: new CustomerLeadTreePlugin(groupBy)
          }
        }
      },
    }
    return responsiveGridConfig(config, 1720);
  }

  onDefaultSelect(dRecord: grid.DisplayRecord) {
    const { appContext } = this.props;
    let task = dRecord.record;
    if (task['groupType'] === 'SALEMAN' || task['groupType'] === 'TEAM') return;
    appContext.createHttpBackendCall('CustomerLeadsService', 'getCustomerLeadById', { id: task['referenceEntityId'] })
      .withSuccessData((bean: any) => {
        let eventBean: EventBean = {
          partnerId: bean.id,
          partnerCode: bean['code'],
          taxCode: bean['taxCode'],
          partnerLabel: bean['name'],
          partnerType: 'forwarder_customer_leads',
          address: bean['address'],
          kcnLabel: bean['kcnLabel']
        }
        UITaskCalendarUtils.showUIEventHistory(this, eventBean);
      })
      .call();
  }

  onViewLead(dRecord: grid.DisplayRecord) {
    const { appContext, pageContext } = this.props;
    let customerLead = dRecord.record;
    if (customerLead['groupType'] === 'SALEMAN' || customerLead['groupType'] === 'TEAM') return;
    let popupId = `customer-lead-${util.IDTracker.next()}`;
    appContext.createHttpBackendCall('CustomerLeadsService', 'getCustomerLeadById', { id: customerLead['referenceEntityId'] })
      .withSuccessData((data: any) => {
        const leadType: 'AGENTS_APPROACHED' | 'CUSTOMER_LEAD' = data['type'];

        let observer = new entity.ComplexBeanObserver(data);
        if (leadType === 'AGENTS_APPROACHED') {
          let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
            return (
              <UICustomerAgentEditor appContext={appCtx} pageContext={pageCtx} observer={observer} />
            );
          }
          let pupupLabel: string = `Agent Approach: ${customerLead.name}`;
          pageContext.createPopupPage(`agent-approach-${util.IDTracker.next()}`, pupupLabel, createAppPage, { size: 'flex-lg', backdrop: 'static' });
        } else {
          let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
            return (
              <UICustomerLeadSimpleEditor appContext={appCtx} pageContext={pageCtx} observer={observer} />
            );
          }
          let pupupLabel: string = `Customer Lead: ${customerLead.name}`;
          pageContext.createPopupPage(`customer-lead-${util.IDTracker.next()}`, pupupLabel, createAppPage, { size: 'flex-lg', backdrop: 'static' });
        }
      })
      .call();
  }

  render() {
    if (this.isLoading()) return this.renderLoading();
    return this.renderUIGrid()
  }

}

class CustomerLeadTreePlugin extends grid.TreeDisplayModelPlugin {
  private groupBy: 'SALEMAN' | 'TEAM';

  constructor(groupBy: 'SALEMAN' | 'TEAM') {
    super();
    this.groupBy = groupBy;
  }

  override setCollapse(record: grid.TreeRecord) {
    record.collapse = true;
  }

  buildTreeRecords(records: Array<any>): Array<grid.TreeRecord> {
    let treeRecords: Array<any> = [];
    let idCounter = 1;

    if (this.groupBy === 'TEAM') {
      const teamGroups = records.reduce((acc, record) => {
        const province = record['departmentLabel'] || 'N/A';
        if (!acc[province]) acc[province] = [];
        acc[province].push(record);
        return acc;
      }, {} as Record<string, Array<any>>);

      const sortedTeamGroups = Object.entries(teamGroups)
        .sort(([a], [b]) => a.localeCompare(b));

      for (let [teamLabel, teamRecords] of sortedTeamGroups as [string, Array<any>][]) {
        let teamNode: any = {
          id: idCounter++,
          parentId: undefined,
          referenceEntityId: undefined,
          groupType: 'TEAM',
          label: teamLabel,
          name: teamLabel,
          type: '-',
          date: '-',
          taxCode: '-',
          personalContact: '-',
          industryLabel: '-',
          routing: '-',
          volumeNote: '-',
          customerOnboardingProgress: '-',
          kcnLabel: '-',
          address: '-',
          starRating: '-',
          status: '-',
          modifiedTime: '-',
          createdTime: '-',
        }
        treeRecords.push(teamNode);

        const salemanGroups = teamRecords.reduce((acc, record) => {
          const saleman = record['salemanLabel'] || 'N/A';
          if (!acc[saleman]) acc[saleman] = [];
          acc[saleman].push(record);
          return acc;
        }, {} as Record<string, Array<any>>);

        const sortedSalemanGroups = Object.entries(salemanGroups)
          .sort(([a], [b]) => a.localeCompare(b));

        for (let [salemanLabel, salemanRecords] of sortedSalemanGroups as [string, Array<any>][]) {
          const salemanNode = {
            id: idCounter++,
            parentId: teamNode.id,
            referenceEntityId: undefined,
            groupType: 'SALEMAN',
            salemanAccountId: salemanRecords[0].salemanAccountId,
            salemanLabel: salemanLabel,
            label: salemanLabel,
            code: `${salemanRecords.length}`,
            name: salemanLabel,
            type: '-',
            date: '-',
            taxCode: '-',
            personalContact: '-',
            industryLabel: '-',
            routing: '-',
            volumeNote: '-',
            customerOnboardingProgress: '-',
            kcnLabel: '-',
            address: '-',
            starRating: '-',
            status: '-',
            modifiedTime: '-',
            createdTime: '-',
          }
          treeRecords.push(salemanNode);

          salemanRecords.forEach(record => {
            const labelNode = {
              id: idCounter++,
              parentId: salemanNode.id,
              referenceEntityId: record.id,
              groupType: 'Lead',
              type: record.type,
              name: record.name,
              code: record.code,
              date: record.date,
              taxCode: record.taxCode,
              source: record.source,
              personalContact: record.personalContact,
              industryLabel: record.industryLabel,
              routing: record.routing,
              volumeNote: record.volumeNote,
              customerOnboardingProgress: record.customerOnboardingProgress,
              kcnLabel: record.kcnLabel,
              countryLabel: record.countryLabel,
              address: record.address,
              starRating: record.starRating,
              accountCreatorLabel: record.accountCreatorLabel,
              status: record.status,
              modifiedTime: record.modifiedTime,
              createdTime: record.createdTime,
            }
            treeRecords.push(labelNode);
          });
        }
      }
    } else {
      let salemanGroups: util.ListRecordMap<string> = new util.ListRecordMap<string>();
      salemanGroups.addAllRecords('salemanAccountId', records);

      const sortedSalemanIds = salemanGroups.getListNames().sort((a, b) => {
        return salemanGroups.getList(b).length - salemanGroups.getList(a).length;
      });

      for (let salemanAccountId of sortedSalemanIds) {
        let leads: Array<any> = salemanGroups.getList(salemanAccountId);

        let salemanNode: any = {
          id: idCounter++,
          parentId: undefined,
          referenceEntityId: undefined,
          groupType: 'Saleman',
          salemanAccountId: salemanAccountId,
          salemanLabel: leads[0].salemanLabel,
          code: `${leads.length}`,
          name: '-',
          type: '-',
          date: '-',
          taxCode: '-',
          personalContact: '-',
          industryLabel: '-',
          routing: '-',
          volumeNote: '-',
          customerOnboardingProgress: '-',
          kcnLabel: '-',
          address: '-',
          starRating: '-',
          status: '-',
          modifiedTime: '-',
          createdTime: '-',
        }
        treeRecords.push(salemanNode);

        leads.forEach((record, _index, _records) => {
          const leadNode = {
            id: idCounter++,
            parentId: salemanNode.id,
            referenceEntityId: record.id,
            groupType: 'Lead',
            type: record.type,
            name: record.name,
            code: record.code,
            source: record.source,
            date: record.date,
            taxCode: record.taxCode,
            personalContact: record.personalContact,
            industryLabel: record.industryLabel,
            routing: record.routing,
            volumeNote: record.volumeNote,
            customerOnboardingProgress: record.customerOnboardingProgress,
            kcnLabel: record.kcnLabel,
            countryLabel: record.countryLabel,
            address: record.address,
            accountCreatorLabel: record.accountCreatorLabel,
            starRating: record.starRating,
            status: record.status,
            modifiedTime: record.modifiedTime,
            createdTime: record.createdTime,
          }
          treeRecords.push(leadNode);
        });
      }
    }
    grid.initRecordStates(treeRecords);

    return super.buildTreeRecords(treeRecords);
  }
}



interface FilterParam {
  dateFilter: { fromValue: string, toValue: string, label: string };
  groupedBy: { label: string, value: string };
  enablePanel: boolean;
}

export interface UICustomerLeadPageProps extends app.AppComponentProps { }
interface UICustomerLeadState { }

export class UICustomerLeadPage extends app.AppComponent<UICustomerLeadPageProps, UICustomerLeadState> {
  reportFilter: FilterParam;
  private saleDailyTaskTreeRef: React.RefObject<UICustomerLeadTree>;
  private saleDailyTaskListRef: React.RefObject<UICustomerLeadList>;

  viewId: number = util.IDTracker.next();

  constructor(props: UICustomerLeadPageProps) {
    super(props);

    this.saleDailyTaskTreeRef = React.createRef<UICustomerLeadTree>();
    this.saleDailyTaskListRef = React.createRef<UICustomerLeadList>();

    const today = new Date();
    const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

    let dateFilter = new util.TimeRange();
    dateFilter.fromSetDate(firstDayOfMonth);
    dateFilter.toSetDate(lastDayOfMonth);

    this.reportFilter = {
      dateFilter: { fromValue: dateFilter.fromFormat(), toValue: dateFilter.toFormat(), label: 'This Month' },
      groupedBy: { label: 'Saleman', value: 'Saleman' },
      enablePanel: localStorage.getItem('enableCustomerLeadPanel') === 'true'
    }

    this.loadData();
  }

  doExport = () => {
    let { appContext } = this.props;
    return;
  }

  renderHeader() {
    const { appContext, pageContext } = this.props;
    const { dateFilter } = this.reportFilter;

    return (
      <div className="flex-hbox flex-grow-0 align-items-center justify-content-between px-2 py-1">

        <div className="flex-hbox justify-content-start align-items-center flex-grow-0 gap-2" >
          <h5 style={{ color: '#6c757d' }}>
            <FeatherIcon.Users className="me-2" size={18} />
            Leads Overview
          </h5>

          <div className='flex-hbox align-items-center flex-grow-0 border-start' >
          </div>
        </div>

        <div className="flex-hbox justify-content-end align-items-center flex-grow-1 gap-1" >

          <WQuickTimeRangeSelector appContext={appContext} pageContext={pageContext}
            initBean={dateFilter}
            onModify={(bean: any, _field: string, _oldVal: any, _newVal: any) => {
              this.reportFilter.dateFilter = bean;
              this.loadData();
            }} />

          <WTaskGroupBySelector appContext={appContext} pageContext={pageContext}
            onModify={(bean: any, _field: string, _oldVal: any, _newVal: any) => {
              this.reportFilter.groupedBy = bean;
              this.forceUpdate();
              // this.loadData();
            }} />

          <bs.Button laf="secondary" outline size="sm" className="p-1" style={{ color: '#6c757d' }}
            onClick={this.doExport}>
            <FeatherIcon.Download size={14} className="me-2" />
            Export
          </bs.Button>
        </div>
      </div>
    )
  }

  render(): React.JSX.Element {
    const { appContext, pageContext } = this.props;
    const { dateFilter, groupedBy } = this.reportFilter;

    let plugin = new UICustomerLeadPlugin('Company');
    plugin.withDateFilter('modifiedTime', dateFilter.fromValue, dateFilter.toValue);

    return (
      <div className="flex-vbox mx-1 p-1 bg-white rounded-md w-100 h-100 my-1" >
        {this.renderHeader()}
        <div className="flex-vbox border-top bg-body-highlight" key={util.IDTracker.next()}>
          {groupedBy.value === 'NONE'
            ? <UICustomerLeadList ref={this.saleDailyTaskListRef}
              appContext={appContext} pageContext={pageContext} plugin={plugin} />
            : <UICustomerLeadTree ref={this.saleDailyTaskTreeRef} groupBy={groupedBy.value as any}
              appContext={appContext} pageContext={pageContext} plugin={plugin} />
          }
        </div>
      </div>
    )
  }

}
