import React from "react";
import * as FeatherIcon from 'react-feather';
import { module } from '@datatp-ui/erp';
import { util, grid, bs, entity, app, sql, input } from '@datatp-ui/lib';
import { T } from "../backend";
import { UIPartnerEventHistoryEditor } from "./UIPartnerEventHistory";
import { buildTooltipValues } from "../../price";

const SESSION = app.host.DATATP_HOST.session;
export class UIPartnerEventHistoryListPlugin extends entity.DbEntityListPlugin {
  constructor() {
    super([]);

    this.backend = {
      context: 'company',
      service: 'CustomerLeadsService',
      searchMethod: 'searchPartnerEventHistories',
    }

    this.searchParams = {
      maxReturn: 1000
    }
  }

  getTransDateFilter(): sql.RangeFilter {
    if (this.searchParams) {
      let filters = this.getSearchParams().rangeFilters;
      if (filters) {
        for (let i = 0; i < filters.length; i++) {
          let filter = filters[i];
          if (filter.name === 'transactionDate') return filter;
        }
      }
      return sql.createDateTimeFilterNew('transactionDate', 'Transaction Date');
    }
    return sql.createDateTimeFilterNew('transactionDate', 'Transaction Date');
  }

  withTransDate(fromValue: string, toValue: string) {
    if (this.searchParams) {
      let rangeFilters = this.searchParams.rangeFilters;
      if (rangeFilters) {
        for (let i = 0; i < rangeFilters.length; i++) {
          let filter = rangeFilters[i];
          if (filter.name === 'transactionDate') {
            filter.fromValue = fromValue;
            filter.toValue = toValue;
            break;
          }
        }
      }
      this.searchParams.rangeFilters = rangeFilters;
    }
    return this;
  }

  withCompanyCode(companyCode: 'beehph' | 'beehan' | 'beehcm' | 'beedad') {
    this.addSearchParam('companyCode', companyCode)
    return this;
  }

  withReferencePartner(referencePartnerId: number, referencePartnerType: 'forwarder_customer_leads' | 'bfsone_partner') {
    this.addSearchParam('referencePartnerId', referencePartnerId)
    this.addSearchParam('referencePartnerType', referencePartnerType)
    return this;
  }

  loadData(uiList: entity.DbEntityList<any>): void {
    this.createBackendSearch(uiList, { params: this.searchParams }).call();
  }

}


export interface UIPartnerEventHistoryListProps extends entity.DbEntityListProps {
  referencePartnerId: number;
  referencePartnerType: string;
  hideToolbar?: boolean;
}
export class UIPartnerEventHistoryList extends entity.DbEntityList<UIPartnerEventHistoryListProps> {

  createVGridConfig(): grid.VGridConfig {
    const { appContext, pageContext, type, readOnly } = this.props;
    let EDITOR_CELL_HEIGHT: number = 40;
    let DEFAULT_CELL_HEIGHT: number = 20;

    let onInputChange = (ctx: grid.FieldContext, oldVal: any, newVal: any) => {
      let record: any = ctx.displayRecord.record;
      let fieldName = ctx.fieldConfig.name;

      if (newVal && newVal !== oldVal) {
        record[fieldName] = newVal;
        appContext.createHttpBackendCall('CustomerLeadsService', 'savePartnerEventHistoryRecords', { requests: [record] })
          .withSuccessData((_data: any) => {
            appContext.addOSNotification("success", T(`Save modified records success!!!`));
            this.reloadData();
          })
          .call();
      }
    }

    const renderTooltipAdvanced = (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
      const record = dRecord.record;
      const val = record[field.name];

      const tooltipFields = [
        { key: 'label', label: 'Label' },
        { key: 'shippingRoute', label: 'Route' },
        { key: 'volumeNote', label: 'Volume Note' },
        { key: 'companySupport', label: 'Company Support' },
        { key: 'customerFeedback', label: 'Customer Feedback' },
        { key: 'managerEvaluation', label: 'Manager Evaluation' },
        { key: 'description', label: 'Description' },
      ];

      const { htmlFormat, textFormat } = buildTooltipValues(record, tooltipFields);

      const handleClick = () => {
        navigator.clipboard.writeText(textFormat);
        bs.toastShow('Copied to clipboard!', { type: 'success' });
      };

      return (
        <bs.CssTooltip width={400}>
          <bs.CssTooltipToggle >
            <div className='flex-hbox' onClick={handleClick}>
              {field.fieldDataGetter ? field.fieldDataGetter(record) : field.format ? field.format(val) : val}
            </div>
          </bs.CssTooltipToggle>
          <bs.CssTooltipContent>
            {htmlFormat}
          </bs.CssTooltipContent>
        </bs.CssTooltip>
      );
    }

    const enableEditor = !readOnly && pageContext.hasUserWriteCapability();
    const CELL_HEIGHT = enableEditor ? EDITOR_CELL_HEIGHT : DEFAULT_CELL_HEIGHT;

    let config: grid.VGridConfig = {
      record: {
        dataCellHeight: 40,
        editor: {
          supportViewMode: ['table'],
          enable: enableEditor
        },
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector()),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          {
            name: 'transactionDate', label: T(`Event Date`), width: 120, container: 'fixed-left',
            format: util.text.formater.compactDate,
            customRender: renderTooltipAdvanced,
            editor: {
              type: 'date', enable: false,
              renderCustom: (ctx: grid.FieldContext, _onInputChange: grid.OnInputChange) => {
                let record = ctx.displayRecord.record;
                return (
                  <input.BBDateTimeField bean={record} style={{ minHeight: record['rowHeight'] || CELL_HEIGHT }}
                    field="transactionDate" dateFormat="DD/MM/YYYY" timeFormat={false}
                    onInputChange={(_bean: any, _field: string, oldVal: any, newVal: any) => onInputChange(ctx, oldVal, newVal)} />
                );
              }
            }
          },
          {
            name: 'salemanLabel', label: T(`Salesman`), width: 180,
            customRender: renderTooltipAdvanced,
            editor: {
              type: 'string', enable: enableEditor,
              renderCustom: (ctx: grid.FieldContext, _onInputChange: grid.OnInputChange) => {
                let record = ctx.displayRecord.record;
                let val = record['salemanLabel'];
                return (
                  <module.company.hr.BBRefEmployee style={{ minHeight: record['rowHeight'] || CELL_HEIGHT }}
                    appContext={appContext} pageContext={pageContext} bean={record}
                    beanIdField='salemanAccountId' beanLabelField='salemanLabel'
                    placeholder='Saleman' selectedId='accountId' hideMoreInfo
                    onPostUpdate={(_inputUI, _bean, selectOpt, _userInput) => {
                      record['salemanAccountId'] = selectOpt.accountId;
                      record['salemanLabel'] = selectOpt.label;
                      onInputChange(ctx, val, selectOpt.label);
                    }} />
                );
              }
            }
          },
          {
            name: 'label', label: T(`Label`), width: 300, style: { height: CELL_HEIGHT },
            customRender: renderTooltipAdvanced,
            editor: {
              type: 'string', enable: enableEditor,
              onInputChange: onInputChange,
            }
          },
          {
            name: 'type', label: T(`Type`), width: 120, style: { height: CELL_HEIGHT },
            customRender: renderTooltipAdvanced,
            editor: {
              type: 'string', enable: enableEditor,
              onInputChange: onInputChange,
            }
          },
          {
            name: 'description', label: T(`Description`), width: 350, style: { height: CELL_HEIGHT },
            customRender: renderTooltipAdvanced,
            editor: {
              type: 'string', enable: enableEditor,
              onInputChange: onInputChange,
            }
          },
          {
            name: 'shippingRoute', label: T(`Route`), width: 300, style: { height: CELL_HEIGHT },
            customRender: renderTooltipAdvanced,
            editor: {
              type: 'string', enable: enableEditor,
              onInputChange: onInputChange,
            }
          },
          {
            name: 'volumeNote', label: T(`Volume Note`), width: 300, style: { height: CELL_HEIGHT },
            customRender: renderTooltipAdvanced,
            editor: {
              type: 'string', enable: enableEditor,
              onInputChange: onInputChange,
            }
          },
          {
            name: 'companySupport', label: T(`Company Support`), width: 300, style: { height: CELL_HEIGHT },
            customRender: renderTooltipAdvanced,
            editor: {
              type: 'string', enable: enableEditor,
              onInputChange: onInputChange,
            }
          },
          {
            name: 'customerFeedback', label: T(`Customer Feedback`), width: 300, style: { height: CELL_HEIGHT },
            customRender: renderTooltipAdvanced,
            editor: {
              type: 'string', enable: enableEditor,
              onInputChange: onInputChange,
            }
          },
          {
            name: 'managerEvaluation', label: T(`Manager Evaluation`), width: 300, style: { height: CELL_HEIGHT },
            customRender: renderTooltipAdvanced,
            editor: {
              type: 'string', enable: enableEditor,
              onInputChange: onInputChange,
            }
          },


        ],
      },
      footer: {
        selector: entity.DbEntityListConfigTool.FOOTER_MULTI_SELECT(T('Select'), type),
      },
      toolbar: {
        hide: true,
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          }
        }
      },
    }
    return config;
  }

  onDefaultSelect(dRecord: grid.DisplayRecord): void {
    let customerLeadTransaction = dRecord.record;
    let { appContext, pageContext } = this.props;
    let writeCap = pageContext.hasUserWriteCapability();
    let param: any = { id: customerLeadTransaction.id };
    appContext.createHttpBackendCall('CustomerLeadsService', 'getPartnerEventHistoryById', param)
      .withSuccessData((data: any) => {
        let observer = new entity.ComplexBeanObserver(data);

        let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return (<UIPartnerEventHistoryEditor appContext={appCtx} pageContext={pageCtx} observer={observer} readOnly={!writeCap}
            onPostCommit={(_bean) => {
              pageCtx.back();
              this.onAddOrModifyDBRecordCallback(_bean);
              this.getVGridContext().getVGrid().forceUpdateView();
            }} />)
        }

        let pupupLabel: string = `Customer Lead: ${customerLeadTransaction.leadName}`;
        pageContext.createPopupPage('customer-lead', pupupLabel, createAppPage, { size: 'lg', backdrop: 'static' });
      })
      .call();
  }

  onNewAction(): void {
    const { plugin, referencePartnerId, referencePartnerType } = this.props;
    let newRecord = {
      id: null,
      referencePartnerType: referencePartnerType,
      referencePartnerId: referencePartnerId,
      salemanAccountId: SESSION.getAccountId(),
      salemanLabel: SESSION.getAccountAcl().getFullName(),
      transactionDate: util.TimeUtil.toCompactDateTimeFormat(new Date()),
    }
    plugin.getListModel().addRecord(newRecord);
    grid.getRecordState(newRecord).markNew();
    this.getVGridContext().getVGrid().forceUpdateView();
  }

  render() {
    if (this.isLoading()) return this.renderLoading();
    let { hideToolbar } = this.props;

    return (
      <div className='flex-vbox'>
        {!hideToolbar && (
          <div className='bg-white flex-hbox flex-grow-0 justify-content-between align-items-center mx-1 px-1 mt-1'>
            <div className="flex-hbox justify-content-end align-items-center flex-grow-1">
              <bs.Button laf='info' className="border-0 p-1 mx-1 border-end rounded-0" outline
                onClick={() => this.onNewAction()}>
                <FeatherIcon.Plus size={12} /> Add
              </bs.Button>
            </div>
          </div>
        )}
        <div key={this.viewId} className='flex-vbox'>
          {this.renderUIGrid()}
        </div>
      </div>
    )
  }

}
