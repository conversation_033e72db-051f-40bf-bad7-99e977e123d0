import React from 'react';
import * as FeatherIcon from 'react-feather';
import { util, grid, sql, entity, bs, input } from '@datatp-ui/lib';
import { T } from '../backend';
import { responsiveGridConfig } from '../common';
import { WQuickTimeRangeSelector } from '../../common/UIDashboardUtility';

class CustomerReportTreePlugin extends grid.TreeDisplayModelPlugin {

  override setCollapse(_record: grid.TreeRecord) {
    const type: string = _record.record['groupType'];
    // _record.collapse = type !== 'Province' && type !== 'KCN';
    _record.collapse = true;
  }

  buildTreeRecords(records: Array<any>): Array<grid.TreeRecord> {
    let treeRecords: Array<any> = [];
    let idCounter = 1;

    // Group by province
    const provinceGroups = records.reduce((acc, record) => {
      const province = record['provinceLabel'] || 'N/A';
      record.provinceValue = province;
      if (!acc[province]) acc[province] = [];
      acc[province].push(record);
      return acc;
    }, {} as Record<string, Array<any>>);

    // Sort provinces by totalJobCount
    const sortedProvinces = Object.entries(provinceGroups)
      .sort(([, recordsA], [, recordsB]) => {
        const totalTeuA = (recordsA as any[]).reduce((sum: number, record: any) => sum + (Number(record.totalJobCount) || 0), 0);
        const totalTeuB = (recordsB as any[]).reduce((sum: number, record: any): number => sum + (Number(record.totalJobCount) || 0), 0);
        return totalTeuB - totalTeuA; // Sort in descending order
      });

    // Build tree structure
    for (const [province, provinceRecords] of sortedProvinces as [string, Array<any>][]) {
      // Tính tổng các giá trị từ provinceRecords
      const provinceTotals = provinceRecords.reduce((acc: {
        gw: number;
        cbm: number;
        totalJobCount: number;
        containerCounts: Record<string, number>;
        teu: number;
        totalCDs: number;
        totalTrip: number;
      }, record: any) => {
        acc.gw += Number(record.totalGw) || 0;
        acc.cbm += Number(record.totalCbm) || 0;
        acc.teu += Number(record.totalTEU) || 0;
        acc.totalJobCount += Number(record.totalJobCount) || 0;
        acc.totalCDs += Number(record.totalCDs) || 0;
        acc.totalTrip += Number(record.totalTrip) || 0;

        // Tính toán container counts
        if (record.totalContainer && typeof record.totalContainer === 'string') {
          const containers = record.totalContainer.split('/').map((x: string) => x.trim());
          containers.forEach((container: string) => {
            const [quantity, type] = container.split('x').map(x => x.trim());
            if (quantity && type) {
              const count = parseInt(quantity);
              if (!acc.containerCounts[type]) {
                acc.containerCounts[type] = 0;
              }
              acc.containerCounts[type] += count;
            }
          });
        }
        return acc;
      }, {
        gw: 0,
        cbm: 0,
        totalJobCount: 0,
        containerCounts: {} as Record<string, number>,
        teu: 0,
        totalCDs: 0,
        totalTrip: 0
      });

      // Format container string
      const containerString = Object.entries(provinceTotals.containerCounts)
        .map(([type, count]) => `${count}x${type}`)
        .join(' / ');

      const provinceNode = {
        id: idCounter++,
        parentId: undefined,
        label: province,
        groupType: 'Province',
        routing: '-',
        investmentOrigin: '-',
        totalGw: provinceTotals.gw || '-',
        totalCbm: provinceTotals.cbm || '-',
        totalContainer: containerString || '-',
        totalTEU: provinceTotals.teu || '-',
        lastTransactionDate: '-',
        lastTransactionID: '-',
        totalJobCount: provinceTotals.totalJobCount || '-',
        totalCDs: provinceTotals.totalCDs || '-',
        totalTrip: provinceTotals.totalTrip || '-'
      };
      treeRecords.push(provinceNode);

      // Group by KCN
      const kcnGroups = provinceRecords.reduce((acc, record) => {
        const kcn = record['kcnLabel'] || 'N/A';
        record.kcnValue = kcn;
        if (!acc[kcn]) acc[kcn] = [];
        acc[kcn].push(record);
        return acc;
      }, {} as Record<string, Array<any>>);

      // Sort KCN by totalJobCount
      const sortedKcnGroups = Object.entries(kcnGroups)
        .sort(([, recordsA], [, recordsB]) => {
          const totalJobCountA = (recordsA as any[]).reduce((sum: number, record: any) => sum + (Number(record.totalJobCount) || 0), 0);
          const totalJobCountB = (recordsB as any[]).reduce((sum: number, record: any): number => sum + (Number(record.totalJobCount) || 0), 0);
          return totalJobCountB - totalJobCountA; // Sort in descending order
        });

      // Use sortedKcnGroups instead of Object.entries(kcnGroups)
      for (const [kcn, kcnRecords] of sortedKcnGroups as [string, Array<any>][]) {
        // Tính tổng các giá trị từ kcnRecords
        const kcnTotals = kcnRecords.reduce((acc, record) => {
          acc.gw += Number(record.totalGw) || 0;
          acc.cbm += Number(record.totalCbm) || 0;
          acc.teu += Number(record.totalTEU) || 0;
          acc.totalJobCount += Number(record.totalJobCount) || 0;
          acc.totalCDs += Number(record.totalCDs) || 0;
          acc.totalTrip += Number(record.totalTrip) || 0;

          // Tính toán container counts
          if (record.totalContainer && typeof record.totalContainer === 'string') {
            const containers = record.totalContainer.split('/').map((x: string) => x.trim());
            containers.forEach((container: string) => {
              const [quantity, type] = container.split('x').map(x => x.trim());
              if (quantity && type) {
                const count = parseInt(quantity);
                if (!acc.containerCounts[type]) {
                  acc.containerCounts[type] = 0;
                }
                acc.containerCounts[type] += count;
              }
            });
          }
          return acc;
        }, {
          gw: 0,
          cbm: 0,
          totalJobCount: 0,
          containerCounts: {} as Record<string, number>,
          teu: 0,
          totalCDs: 0,
          totalTrip: 0
        });

        // Format container string
        const containerString = Object.entries(kcnTotals.containerCounts)
          .map(([type, count]) => `${count}x${type}`)
          .join(' / ');

        const kcnNode = {
          id: idCounter++,
          parentId: provinceNode.id,
          label: kcn,
          groupType: 'KCN',
          routing: '-',
          investmentOrigin: '-',
          totalGw: kcnTotals.gw || '-',
          totalCbm: kcnTotals.cbm || '-',
          totalContainer: containerString || '-',
          totalTEU: kcnTotals.teu || '-',
          lastTransactionDate: '-',
          lastTransactionID: '-',
          totalJobCount: kcnTotals.totalJobCount || '-',
          totalCDs: kcnTotals.totalCDs || '-',
          totalTrip: kcnTotals.totalTrip || '-'
        };
        treeRecords.push(kcnNode);

        // Sort customer records by totalJobCount before creating nodes
        const sortedCustomerRecords = kcnRecords.sort((a, b) => {
          const totalJobCountA = Number(a.totalJobCount) || 0;
          const totalJobCountB = Number(b.totalJobCount) || 0;
          return totalJobCountB - totalJobCountA; // Sort in descending order
        });

        // Create customer nodes
        for (const record of sortedCustomerRecords) {
          record.customerValue = record.partnerName || 'N/A';

          const customerNode = {
            id: idCounter++,
            parentId: kcnNode.id,
            label: record.partnerName || 'N/A',
            groupType: 'Customer',
            routing: record.routing || '-',
            investmentOrigin: record.investmentOrigin || '-',
            industryLabel: record.industryLabel || '-',
            totalGw: record.totalGw || '-',
            totalCbm: record.totalCbm || '-',
            totalContainer: record.totalContainer || '-',
            totalTEU: record.totalTEU || '-',
            lastTransactionDate: record.lastTransactionDate || '-',
            lastTransactionID: record.lastTransactionID || '-',
            totalJobCount: record.totalJobCount || '-',
            totalCDs: record.totalCDs || '-',
            totalTrip: record.totalTrip || '-',
            airServiceRatio: record.airServiceRatio || 0,
            lclServiceRatio: record.lclServiceRatio || 0,
            fclServiceRatio: record.fclServiceRatio || 0,
            customServiceRatio: record.customServiceRatio || 0,
            truckingServiceRatio: record.truckingServiceRatio || 0
          };
          treeRecords.push(customerNode);

        }
      }
    }

    grid.initRecordStates(treeRecords);

    return super.buildTreeRecords(treeRecords);
  }

}

export class UICustomerReportPlugin extends entity.DbEntityListPlugin {
  constructor(space: 'User' | 'Company' | 'System') {
    super([]);

    let dateFilter = new util.TimeRange();
    dateFilter.fromSetDate(new Date()).fromStartOf('year');
    dateFilter.toSetDate(new Date());

    this.backend = {
      context: 'company',
      service: 'PartnerReportService',
      searchMethod: 'searchCustomerReports'
    }
    this.searchParams = {
      params: { "space": space },
      filters: [...sql.createSearchFilter()],
      rangeFilters: [
        sql.createDateTimeFilterNew("transactionDate", "Transaction Date", dateFilter)
      ],
      optionFilters: [sql.createStorageStateFilter([entity.StorageState.ACTIVE, entity.StorageState.ARCHIVED])],
    }
  }

  loadData(uiList: entity.DbEntityList<any>) {
    this.createBackendSearch(uiList, { params: this.searchParams }).call();
  }

}


class WPriceGridFilter extends grid.WGridFilter {

  onChange = (_oldVal: any, newVal: any) => {
    let { context } = this.props;
    let uiList = context.uiRoot as UICustomerMap;
    const { plugin } = uiList.props;
    let pluginImp: UICustomerReportPlugin = plugin as UICustomerReportPlugin;
    pluginImp.getListModel().getRecordFilter().withPattern(newVal);
    pluginImp.getListModel().filter();
    context.model.getRecordFilter().withPattern(newVal);
    context.model.filter();
    uiList.forceUpdate();
  }

}

export interface UICustomerMapProps extends entity.DbEntityListProps {
  space: 'User' | 'Company' | 'System'
}

interface FilterParam {
  dateFilter: { fromValue: string, toValue: string, label: string };
  groupedBy: { label: string, value: string };
  colllapsed: boolean;
  onlyFreehand: boolean;
}

export class UICustomerMap extends entity.DbEntityList<UICustomerMapProps> {
  reportFilter: FilterParam;
  private activeOnly: boolean = false;  // Initialize as boolean, false to show all records initially

  onPostLoadData(_records: Array<any>): void {
    console.log(_records);
  }

  constructor(props: UICustomerMapProps) {
    super(props);

    let dateFilter = new util.TimeRange();
    dateFilter.fromSetDate(new Date()).fromStartOf('year');
    dateFilter.toSetDate(new Date());

    this.reportFilter = {
      dateFilter: { fromValue: dateFilter.fromFormat(), toValue: dateFilter.toFormat(), label: 'Year To Date' },
      groupedBy: { label: 'Saleman', value: 'Saleman' },
      colllapsed: true,
      onlyFreehand: false
    }

    this.loadData();
  }

  createVGridConfig(): grid.VGridConfig {

    const renderTooltipAdvanced = (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {
      const record = dRecord.record;
      const val = record[field.name] || '-';

      // Build tooltip content from record fields
      const tooltipFields = [
        { key: field.name, label: field.label },
      ];

      let displayVal = '';
      let clipboardVal = '';

      // Build display and clipboard values
      tooltipFields.forEach(({ key, label }) => {
        const value = record[key];
        if (value) {
          if (displayVal) {
            displayVal += `\n\n<hr/>\n\n${label}:\n${value}`;
            clipboardVal += `\n\n${label}:\n${value}`;
          } else {
            displayVal = `${label}:\n${value}`;
            clipboardVal = `${label}:\n${value}`;
          }
        }
      });

      // Format display value with styling
      const formattedVal = displayVal.split('\n').map((line, index) => {
        const isHeader = tooltipFields.some(({ label }) => line === `${label}:`);

        if (isHeader) {
          return (
            <React.Fragment key={index}>
              <span className="fw-bold" style={{ color: '#198754', fontSize: '1.1em' }}>{line}</span>
              <br style={{ margin: 0, padding: 0 }} />
            </React.Fragment>
          );
        }

        if (line === '<hr/>') {
          return <hr key={index} style={{ margin: 0, padding: 0 }} />;
        }

        return (
          <React.Fragment key={index}>
            <span style={{ fontSize: '1em' }}>{line}</span>
            <br style={{ margin: 0, padding: 0 }} />
          </React.Fragment>
        );
      });

      const handleClick = () => {
        navigator.clipboard.writeText(clipboardVal);
        bs.toastShow('Copied to clipboard!', { type: 'success' });
      };

      return (
        <bs.CssTooltip width={400}>
          <bs.CssTooltipToggle >
            <div className='flex-hbox' onClick={handleClick}>
              {field.fieldDataGetter ? field.fieldDataGetter(record) : field.format ? field.format(val) : val}
            </div>
          </bs.CssTooltipToggle>
          <bs.CssTooltipContent>
            {formattedVal}
          </bs.CssTooltipContent>
        </bs.CssTooltip>
      );
    }

    let createServices = (record: any) => {
      let services = [
        { type: 'Air', ratio: record.airServiceRatio, color: 'primary' },
        { type: 'LCL', ratio: record.lclServiceRatio, color: 'success' },
        { type: 'FCL', ratio: record.fclServiceRatio, color: 'info' },
        { type: 'Custom', ratio: record.customServiceRatio, color: 'warning' },
        { type: 'Trucking', ratio: record.truckingServiceRatio, color: 'danger' }
      ].filter(service => service.ratio > 0);

      let totalRatio = 0;
      services = services.map((service, index) => {
        const roundedRatio = Math.round(service.ratio);
        totalRatio += roundedRatio;
        return { ...service, ratio: roundedRatio };
      });

      // Adjust the largest ratio to ensure total is 100%
      if (totalRatio !== 100 && services.length > 0) {
        const diff = 100 - totalRatio;
        const largestService = services.reduce((prev, current) =>
          (prev.ratio > current.ratio) ? prev : current
        );
        largestService.ratio += diff;
      }

      // If more than 3 services, combine Custom and Trucking into Other
      if (services.length > 3) {
        const mainServices = services.filter(s => ['Air', 'LCL', 'FCL'].includes(s.type));
        const otherServices = services.filter(s => ['Custom', 'Trucking'].includes(s.type));

        if (otherServices.length > 0) {
          const otherRatio = otherServices.reduce((sum, service) => sum + service.ratio, 0);
          services = [
            ...mainServices,
            { type: 'Other', ratio: otherRatio, color: 'secondary' }
          ];
        }
      }
      return services;
    }

    const toggleSelectAllRow = (ctx: grid.VGridContext, checked: boolean) => {
      let state = grid.VGridConfigUtil.getRecordConfigState(ctx.config);
      state.selectAll = !state.selectAll;

      let displayRecordList = ctx.model.getDisplayRecordList();
      let allDisplayRecords = displayRecordList.getDisplayRecords();

      for (let i = 0; i < allDisplayRecords.length; i++) {
        let dRecord = allDisplayRecords[i];
        let treeNode = dRecord.model;

        if (dRecord.indentLevel === 0 || dRecord.indentLevel === 1) {
          displayRecordList.markSelectDisplayRecord(i, checked);

          if (dRecord.record && dRecord.record._state) {
            dRecord.record._state.selected = checked;
          }

          if (treeNode && treeNode.children && treeNode.children.length > 0) {
            for (let child of treeNode.children) {
              if (child.record && child.record._state) {
                child.record._state.selected = checked;
              }
            }
          }
        }
      }

      ctx.getVGrid().forceUpdateView();
    }

    const onToggle = (ctx: grid.VGridContext, row: number, check: boolean) => {
      let displayRecordList: grid.IDisplayRecordList = ctx.model.getDisplayRecordList();
      let dRecord: grid.DisplayRecord = displayRecordList.getDisplayRecordAt(row);
      displayRecordList.markSelectDisplayRecord(row, check);

      let treeNode = dRecord.model;

      if (treeNode && treeNode.children && treeNode.children.length > 0) {
        for (let child of treeNode.children) {
          if (child.record && child.record._state) {
            child.record._state.selected = check;
          }
        }
      }

      let currentLevel = dRecord.indentLevel;
      let nextRow = row + 1;
      let nextDRecord = displayRecordList.getDisplayRecordAt(nextRow);

      while (nextDRecord && nextDRecord.indentLevel > currentLevel) {
        if (nextDRecord.indentLevel === currentLevel + 1) {
          displayRecordList.markSelectDisplayRecord(nextRow, check);
        }
        nextRow++;
        nextDRecord = displayRecordList.getDisplayRecordAt(nextRow);
      }

      ctx.getVGrid().forceUpdateView();
    }

    let config: grid.VGridConfig = {
      record: {
        dataCellHeight: 45,
        fields: [
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          {
            name: '_selector_', label: 'Sel', width: 30, cssClass: 'cell-text-center',
            container: 'fixed-left', removable: false, resizable: false,
            customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, focus: boolean) => {
              let model = ctx.model;
              let selected = model.getDisplayRecordList().isSelectDisplayRecord(dRecord.row);
              return (
                <input.WCheckboxInput className='d-inline-block m-auto'
                  name='row_selector' checked={selected}
                  onInputChange={(check: boolean) => onToggle(ctx, dRecord.row, check)} focus={focus} />
              )
            },
            customHeaderRender: (ctx: grid.VGridContext, field: grid.FieldConfig, _headerEle: any) => {
              let state = grid.VGridConfigUtil.getRecordConfigState(ctx.config);
              return (<div className='flex-hbox justify-content-center'>
                <input.WCheckboxInput className='m-0 p-0' name='selectAll' checked={state.selectAll}
                  onInputChange={(checked: boolean) => toggleSelectAllRow(ctx, checked)} />
              </div>)
            }
          },
          {
            name: 'label', label: T('Hierarchy'), width: 350, filterable: true, container: 'fixed-left',
            fieldDataGetter(record) {
              return record['kcnLabel'] || 'N/A';
            },
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
              let record: any = dRec.record;

              let label = record[field.name] || '';

              let type = record['groupType'];

              if (type === 'KCN') {
                label = label.replace(/Khu công nghiệp/gi, 'KCN').replace(/Cụm công nghiệp/gi, 'CCN');
              }

              const onCollapseRecord = (dRecord: grid.DisplayRecord) => {
                dRecord.model['collapse'] = !dRecord.model['collapse'];
                let displayRecordList = _ctx.model.getDisplayRecordList();
                if (displayRecordList instanceof grid.TreeDisplayModel) {
                  displayRecordList.updateDisplayRecords();
                  _ctx.getVGrid().forceUpdateView();
                }
              }

              let icon = FeatherIcon.File;
              let color = 'secondary';

              switch (type) {
                case 'Province':
                  icon = FeatherIcon.MapPin;
                  color = 'primary';
                  break;
                case 'KCN':
                  icon = FeatherIcon.Home;
                  color = 'success';
                  break;
                case 'Customer':
                  icon = FeatherIcon.Users;
                  color = 'secondary';
                  break;
              }

              let childrenCount = dRec.model.children.length || '';
              const showChildrenCount = type === 'Province' || type === 'KCN';

              const Icon = icon;
              return (
                <div className="d-flex align-items-center" onClick={() => onCollapseRecord(dRec)}
                  style={{ cursor: 'pointer', userSelect: 'text' }}>
                  <span className={`d-flex align-items-center px-1 py-1 text-${color}`}>
                    <Icon size={14} className="me-1" />
                    <span>
                      {util.text.formater.uiTruncate(label, 300, true)}
                      {showChildrenCount && childrenCount > 0 && <span className="ms-1 text-secondary">({childrenCount})</span>}
                    </span>
                  </span>
                </div>
              );
            }
          },

          {
            name: 'totalGw', label: T('Air (KGS)'), width: 130,
            fieldDataGetter(record) {
              let value = record['totalGw'];
              return value && !isNaN(value) ? util.text.formater.number(value, 2) : '-'
            },
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {
              let record: any = dRecord.record;
              let value = record[field.name];
              return (
                <div className="flex-hbox justify-content-center align-items-center">
                  {value && !isNaN(value) ? util.text.formater.number(value, 2) : '-'}
                </div>
              )
            }
          },
          {
            name: 'totalCbm', label: T('LCL (CBM)'), width: 130,
            fieldDataGetter(record) {
              let value = record['totalCbm'];
              return value && !isNaN(value) ? util.text.formater.number(value, 2) : '-'
            },
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {
              let record: any = dRecord.record;
              let value = record[field.name];
              return (
                <div className="flex-hbox justify-content-center align-items-center">
                  {value && !isNaN(value) ? util.text.formater.number(value, 2) : '-'}
                </div>
              )
            }
          },
          {
            name: 'totalTEU', label: T('FCL (TEUs)'), width: 130,
            fieldDataGetter(record) {
              let value = record['totalTEU'];
              return value && !isNaN(value) ? util.text.formater.number(value, 0) : '-'
            },
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {
              let record: any = dRecord.record;
              let value = record[field.name];
              return (
                <div className="flex-hbox justify-content-center align-items-center">
                  {value && !isNaN(value) ? util.text.formater.number(value, 0) : '-'}
                </div>
              )
            }
          },
          {
            name: 'totalContainer', label: T('Container (only FCL)'), width: 250, state: { visible: false },
            customRender: renderTooltipAdvanced
            // customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {
            //   let record: any = dRecord.record;
            //   let valTruncate = util.text.formater.uiTruncate(record[field.name], 250, true);
            //   return <div className="flex-hbox">{valTruncate}</div>
            // }
          },
          {
            name: 'totalCDs', label: T('CDs (TK)'), width: 130,
            fieldDataGetter(record) {
              let value = record['totalCDs'];
              return value && !isNaN(value) ? util.text.formater.number(value, 2) : '-'
            },
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {
              let record: any = dRecord.record;
              let value = record[field.name];
              return (
                <div className="flex-hbox justify-content-center align-items-center">
                  {value && !isNaN(value) ? util.text.formater.number(value, 2) : '-'}
                </div>
              )
            }
          },
          {
            name: 'totalTrip', label: T('Trucking (Trips)'), width: 130,
            fieldDataGetter(record) {
              let value = record['totalTrip'];
              return value && !isNaN(value) ? util.text.formater.number(value, 0) : '-'
            },
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {
              let record: any = dRecord.record;
              let value = record[field.name];
              return (
                <div className="flex-hbox justify-content-center align-items-center">
                  {value && !isNaN(value) ? util.text.formater.number(value, 0) : '-'}
                </div>
              )
            }
          },

          {
            name: 'routing', label: T('Routing'), width: 140,
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {
              let record: any = dRecord.record;
              let valTruncate = util.text.formater.uiTruncate(record[field.name], 140, true);
              return <div className="flex-hbox">{valTruncate}</div>
            }
          },
          {
            name: 'industryLabel', label: T('Industry Sector'), width: 280, filterable: true,
            customRender: renderTooltipAdvanced
          },
          {
            name: 'investmentOrigin', label: T('Invest. Origin'), width: 150, filterable: true,
            customRender: renderTooltipAdvanced
          },
          {
            name: 'serviceRatio', label: T('Service Type Ratio'), width: 430,
            fieldDataGetter(record) {
              if (record.groupType !== 'Customer' && record.groupType !== 'VolumePeriod') {
                return "-";
              }

              let value = "";
              let services: Array<any> = createServices(record);
              if (services.length === 0) {
                return "-";
              }
              services.forEach((service, index) => {
                if (service.ratio === 0) return;
                value += `${service.type}: ${service.ratio}%` + (index < services.length - 1 ? ' / ' : '');
              })

              return value;

            },
            customRender: (_ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {
              let record: any = dRecord.record;

              if (record.groupType !== 'Customer' && record.groupType !== 'VolumePeriod') {
                return <div className="flex-hbox justify-content-center">-</div>;
              }

              let services: Array<any> = createServices(record);

              if (services.length === 0) {
                return <div className="flex-hbox justify-content-center">-</div>;
              }

              // Add opacity for VolumePeriod rows
              const opacity = record.groupType === 'VolumePeriod' ? 0.7 : 1;

              return (
                <div className="flex-hbox justify-content-between align-items-center w-100">
                  {services.map((service, index) => (
                    <span
                      key={service.type}
                      className={`
                        badge rounded-pill bg-${service.color}-subtle
                        text-${service.color} border border-${service.color}
                        d-inline-flex align-items-center
                        ${index < services.length - 1 ? 'me-1' : ''}
                      `}
                      style={{
                        fontSize: '0.85rem',
                        padding: '4px 6px',
                        flex: 1,
                        justifyContent: 'center',
                        opacity: opacity
                      }}
                    >
                      {`${service.type}: ${service.ratio}%`}
                    </span>
                  ))}
                </div>
              );
            }
          },
          {
            name: 'totalJobCount', label: T('Total Jobs'), width: 100,
            fieldDataGetter(record: any) {
              let value = record['totalJobCount'];
              return value && !isNaN(value) ? util.text.formater.number(value, 0) : '-'
            },
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {
              let record: any = dRecord.record;
              let value = record[field.name];
              return (
                <div className="flex-hbox justify-content-center align-items-center">
                  {value && !isNaN(value) ? util.text.formater.number(value, 0) : '-'}
                </div>
              )
            }
          },
          {
            name: 'lastTransactionDate', label: T('Last Date'), width: 100, filterable: true, filterableType: 'date',
            format: util.text.formater.compactDate,
            fieldDataGetter(record: any) {
              let value = record['lastTransactionDate'];
              if (!value || value === '-') {
                return "-";
              }
              return value;
            },
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {
              let record: any = dRecord.record;
              let value = record[field.name];

              if (!value || value === '-') {
                return <div className="flex-hbox justify-content-center align-items-center">-</div>;
              }
              try {
                const formattedDate = util.text.formater.compactDate(value);
                return (
                  <div className="flex-hbox justify-content-center align-items-center">
                    {formattedDate || '-'}
                  </div>
                );
              } catch (error) {
                console.warn('Invalid date format:', value);
                return <div className="flex-hbox justify-content-center align-items-center">-</div>;
              }
            }
          },
          { name: 'lastTransactionID', label: T('Latest Job ID'), width: 150 },
          {
            name: 'daysSinceLastTransaction', label: T('Inactive (days)'), width: 120, container: 'fixed-right',
            fieldDataGetter(record: any) {
              let lastDate = record.lastTransactionDate;

              if (!lastDate || lastDate === '-') {
                return "-";
              }
              try {
                const lastTransactionDate = util.TimeUtil.parseCompactDateTimeFormat(lastDate);
                const today = new Date();
                const diffTime = Math.abs(today.getTime() - lastTransactionDate.getTime());
                return Math.ceil(diffTime / (1000 * 60 * 60 * 24));

              } catch (error) {
                return "-";
              }
            },
            customRender: (_ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {
              let record: any = dRecord.record;
              let lastDate = record.lastTransactionDate;

              if (!lastDate || lastDate === '-') {
                return <div className="flex-hbox justify-content-center align-items-center">-</div>;
              }

              try {
                const lastTransactionDate = util.TimeUtil.parseCompactDateTimeFormat(lastDate);
                const today = new Date();
                const diffTime = Math.abs(today.getTime() - lastTransactionDate.getTime());
                const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

                return (
                  <div className="flex-hbox justify-content-center align-items-center">
                    {diffDays}
                  </div>
                );
              } catch (error) {
                console.warn('Error calculating days difference:', error);
                return <div className="flex-hbox justify-content-center align-items-center">-</div>;
              }
            }
          },

          { name: 'note', label: T('Note'), width: 250, filterable: true, state: { visible: false } }
        ],
      },
      toolbar: { hide: true },
      view: {
        currentViewName: 'tree',
        availables: {
          table: { viewMode: 'table' },
          tree: {
            viewMode: 'tree',
            label: 'Tree View',
            treeField: 'label',
            plugin: new CustomerReportTreePlugin()
          }
        }
      },
    };
    return responsiveGridConfig(config);
  }

  onDefaultSelect(_dRecord: grid.DisplayRecord) { }

  onModify = (_bean: any, _field: string, _oldVal: any, newVal: any): void => {
    if (_oldVal !== newVal) {
      this.viewId = util.IDTracker.next();
      const { plugin } = this.props;
      if (plugin.searchParams) {
        plugin.searchParams.maxReturn = newVal;
      }
      this.reloadData();
      this.forceUpdate();
    }
  }

  onExpandOrCollapsed = () => {
    let dRecords: grid.DisplayRecord[] = this.vgridContext.model.getDisplayRecordList().getDisplayRecords();
    for (let dRecord of dRecords) {
      let model = dRecord.model;
      model['collapse'] = this.reportFilter.colllapsed;
      if (model['children']) {
        for (let child of model['children']) {
          child['collapse'] = this.reportFilter.colllapsed;
        }
      }
    }

    let displayRecordList = this.vgridContext.model.getDisplayRecordList();
    if (displayRecordList instanceof grid.TreeDisplayModel) {
      displayRecordList.updateDisplayRecords();
    }
    this.vgridContext.getVGrid().forceUpdateView();
  }

  render(): React.JSX.Element {
    if (this.isLoading()) return this.renderLoading();
    const { appContext, pageContext } = this.props;
    const { dateFilter } = this.reportFilter;

    if (this.activeOnly) {
      let records = this.vgridContext.model.getRecords();
      let filteredRecords = records.filter((record: any) => {
        return record.totalJobCount > 0;
      });
      this.vgridContext.model.update(filteredRecords);
    }

    return (
      <div className="flex-vbox mx-1 bg-white rounded-md w-100 h-100 my-1" >

        <div className="flex-hbox flex-grow-0 align-items-center justify-content-between px-2 py-1">

          <div className="flex-hbox justify-content-start align-items-center flex-grow-0 gap-2" >
            <h5 style={{ color: '#6c757d' }}>
              <FeatherIcon.Trello className="me-2" size={18} />
              Customer Map
            </h5>
            <div className='flex-hbox align-items-center flex-grow-0 border-start' >
              <WPriceGridFilter context={this.vgridContext} />
            </div>
          </div>

          <div className="flex-hbox justify-content-end align-items-center flex-grow-1 gap-1" >

            <div className="d-flex align-items-center me-1" style={{ minWidth: '110px' }}>

              <WQuickTimeRangeSelector appContext={appContext} pageContext={pageContext}
                initBean={dateFilter}
                onModify={(bean: any, _field: string, _oldVal: any, _newVal: any) => {
                  this.reportFilter.dateFilter = bean;
                  this.loadData();
                }} />

              {/* <div className="form-check form-switch d-flex align-items-center mx-2 my-0" >
                <input className="form-check-input mt-0 me-2" type="checkbox"
                  role="switch" id="collapsedToggle" checked={this.colllapsed}
                  onChange={() => {
                    this.colllapsed = !this.colllapsed;
                    this.onExpandOrCollapsed();
                    this.forceUpdate();
                  }} />
                <label className="form-check-label fs--1 mb-0 d-flex align-items-center" htmlFor="editableToggle" >
                  <span>{T('Collapsed')}</span>
                </label>
              </div>

              <input className="me-2" type="checkbox" checked={this.activeOnly}
                onChange={() => {
                  this.activeOnly = !this.activeOnly;
                  this.forceUpdate();
                }}
                style={{
                  width: '16px',
                  height: '16px',
                  cursor: 'pointer',
                  backgroundColor: this.activeOnly ? '#4285f4' : '#fff',
                  borderColor: '#4285f4'
                }}
              />
              <label className="form-check-label" style={{ cursor: 'pointer' }}>
                {T('Has Volume')}
              </label>

            </div>

            <div className="d-flex align-items-center me-1" style={{ minWidth: '110px' }}>
              <input className="me-2" type="checkbox" checked={this.onlyFreehand}
                onChange={() => {
                  this.onlyFreehand = !this.onlyFreehand;
                  this.forceUpdate();
                }}
                style={{
                  width: '16px',
                  height: '16px',
                  cursor: 'pointer',
                  backgroundColor: this.onlyFreehand ? '#4285f4' : '#fff',
                  borderColor: '#4285f4'
                }}
              />
              <label className="form-check-label me-2" style={{ cursor: 'pointer' }}>
                {T('Only Freehand')}
              </label> */}

              <XlsxCustomerAnalysisReport appContext={appContext} pageContext={pageContext} context={this.getVGridContext()} className='border-0 p-1'
                outline options={{ fileName: `Partner_Report_${util.TimeUtil.toDateIdFormat(new Date())}.xlsx`, modelName: 'report' }} />

            </div>
          </div>
        </div>
        <div className='flex-vbox' key={this.viewId}>
          {this.renderUIGrid()}
        </div>
      </div>
    )
  }

}

class XlsxCustomerAnalysisReport extends entity.XlsxExportButton {

  override initDataSelectModel = (model: grid.DataSelectModel) => {
    const selectedRecords = this.props.context.model.getSelectedDisplayRecords();

    if (selectedRecords.length > 0) {
      model.selectMode = 'selected';
    } else {
      model.selectMode = 'all';
    }
    model.setFieldSelect(true, 'all');
  }

  onSelect = (model: grid.DataSelectModel) => {
    let { context, options } = this.props;
    bs.dialogHideById(this.dialogId);
    let exportModel: entity.DataListExportModel = this.buildDataHeading(model);
    let selectFields = exportModel.fields;
    let recordConfigModel = context.getVGridConfigModel().getRecordConfigModel();

    let dRecords;
    if (model.selectMode === 'selected') {
      dRecords = context.model.getSelectedDisplayRecords();
    } else {
      dRecords = context.model.getDisplayRecordList().getDisplayRecords();
    }

    let exportRecords = new Array<any>();

    for (let dRec of dRecords) {
      let rec = dRec.record;
      let exportRecord: any = {};
      for (let field of selectFields) {
        let fieldConfig = recordConfigModel.getFieldConfig(field.name);
        if (fieldConfig.name === 'label') {
          let label = rec[fieldConfig.name] || '';

          let type = rec['groupType'];

          if (type === 'KCN') {
            label = label.replace(/Khu công nghiệp/gi, 'KCN').replace(/Cụm công nghiệp/gi, 'CCN');
          }
          exportRecord[fieldConfig.name] = label;
          continue;
        }

        let value = rec[fieldConfig.name];

        if (fieldConfig.fieldDataGetter) {
          value = fieldConfig.fieldDataGetter(rec);
        } else if ((field.dataType === 'date' || field.dataType == 'Date') && fieldConfig.format) {
          value = fieldConfig.format(value);
        }
        if (value === "-") value = '';
        exportRecord[fieldConfig.name] = value;
      }
      exportRecords.push(exportRecord);
    }

    if (options) {
      if (options.fileName) exportModel['fileName'] = options.fileName;
      if (options.modelName) exportModel['modelName'] = options.modelName;
    }
    exportModel['records'] = exportRecords;
    this.doExport(exportModel);
  }

}
