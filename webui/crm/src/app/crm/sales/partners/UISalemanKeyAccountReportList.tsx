import React from "react";

import * as FeatherIcon from 'react-feather'
import { util, grid, bs, entity, input, sql, app } from '@datatp-ui/lib';
import { T } from "../backend";
import { module } from '@datatp-ui/erp';

import { UIKeyAccountFormReport } from "./UISalePerformanceFormReport";

export class UISalemanKeyAccountReportPlugin extends entity.DbEntityListPlugin {
  constructor(space: 'User' | 'Company' | 'System') {
    super([]);

    this.backend = {
      service: 'PartnerReportService',
      searchMethod: 'searchSalemanKeyAccountReports',
      deleteMethod: 'deleteSalemanKeyAccountReports',
    }
    this.searchParams = {
      params: {
        space: space,
      },
      filters: [...sql.createSearchFilter()],
      maxReturn: 1000
    }
  }

  loadData(uiList: entity.DbEntityList<any>): void {
    this.createBackendSearch(uiList, { params: this.searchParams }).call();
  }

  withSalemanAccountId(salemanAccountId: number) {
    this.addSearchParam("salemanAccountId", salemanAccountId)
    return this;
  }

}

export class UISalemanKeyAccountReportList extends entity.DbEntityList {

  onPostLoadData(_records: Array<any>): void {
    console.log(_records);

  }

  createVGridConfig() {
    let config: grid.VGridConfig = {
      record: {
        dataCellHeight: 40,
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector()),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          {
            name: 'salemanLabel', label: T('Salesman'), width: 220, filterable: true, container: 'fixed-left',
            customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let uiList = ctx.uiRoot as entity.DbEntityList
              const { appContext, pageContext } = uiList.props;

              let employeeName: string = record['salemanLabel'] || 'N/A';

              let parts = employeeName.trim().toLowerCase().split(' ');
              parts = parts.map(word => word.charAt(0).toUpperCase() + word.slice(1));
              if (parts.length > 3) {
                let initials = parts[0][0] + '.' + parts[1][0] + '.';
                employeeName = `${initials} ${parts.slice(2).join(' ')}`;
              }

              return (
                <div className='flex-hbox justify-content-center align-items-center' onClick={() => this.onDefaultSelect(dRecord)}>
                  <module.account.WAvatars className='px-2'
                    appContext={appContext} pageContext={pageContext} avatarIds={[record['salemanAccountId']]}
                    avatarIdType='AccountId' width={20} height={20} borderRadius={10} />
                  <div className="flex-hbox">{employeeName}</div>
                </div>
              )
            },
          },
          {
            name: 'submittedDate', label: T('Submitted Date'), width: 130,
            format: util.text.formater.compactDate, filterable: true, filterableType: 'date'
          },
          {
            name: 'reportedDateFrom', label: T('Report From'), width: 130,
            format: util.text.formater.compactDate, filterable: true, filterableType: 'date'
          },
          {
            name: 'reportedDateTo', label: T('Report To'), width: 130,
            format: util.text.formater.compactDate, filterable: true, filterableType: 'date'
          },
          {
            name: 'revenue', label: T('Revenue'), width: 100,
            customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let val = record['revenue'];
              if (val && val > 0) {
                return (<div className="text-wrap position-relative" >{util.text.formater.currency(val, 2)}</div>);
              } else {
                let performanceVal = record['volumePerformance'];
                let performance: any = typeof performanceVal === 'string'
                  ? JSON.parse(performanceVal)
                  : (val || {});
                val = performance['totalRevenue'];
                return (<div className="text-wrap position-relative" >{util.text.formater.currency(val, 2)}</div>);
              }
            }
          },
          {
            name: 'profit', label: T('Profit'), width: 100,
            customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let val = record['profit'];
              if (val && val > 0) {
                return (<div className="text-wrap position-relative" >{util.text.formater.currency(val, 2)}</div>);
              } else {
                let performanceVal = record['volumePerformance'];
                let performance: any = typeof performanceVal === 'string'
                  ? JSON.parse(performanceVal)
                  : (val || {});
                val = performance['totalProfit'];
                return (<div className="text-wrap position-relative" >{util.text.formater.currency(val, 2)}</div>);
              }
            }
          },
          {
            name: 'highlights', label: T('Highlights / Forecast'), width: 300,
            customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;

              // Parse highlights
              let val: any = record['highlights'];
              let highlights: any = typeof val === 'string'
                ? JSON.parse(val)
                : (val || {});
              let marketInformation = highlights['marketInformation'] || '';
              let reportedIssues = highlights['reportedIssues'] || '';

              // Parse forecast
              let forecastVal: any = record['forecast'];
              let forecast: any = typeof forecastVal === 'string'
                ? JSON.parse(forecastVal)
                : (forecastVal || {});
              let displayForecast: any = forecast['forecast'];

              // Suggestions/Requests
              let suggestionOrRequest = record['suggestionOrRequest'] || '';

              if (record['type'] === 'SALES') {
                let htmlLines: any[] = [];

                if (marketInformation) {
                  htmlLines.push(
                    <div className='flex-vbox border-0'>
                      <div className="tooltip-header">
                        <span className="tooltip-title">{'Thông tin thị trường'}:</span>
                      </div>
                      <div className="tooltip-body">
                        {marketInformation}
                      </div>
                    </div>
                  );
                }

                if (reportedIssues) {
                  htmlLines.push(
                    <div className='flex-vbox border-0'>
                      <div className="tooltip-header">
                        <span className="tooltip-title">{'Các vấn đề phát sinh'}:</span>
                      </div>
                      <div className="tooltip-body">
                        {reportedIssues}
                      </div>
                    </div>
                  );
                }

                if (displayForecast) {
                  htmlLines.push(
                    <div className='flex-vbox border-0'>
                      <div className="tooltip-header">
                        <span className="tooltip-title">{'Forecast'}:</span>
                      </div>
                      <div className="tooltip-body">
                        {displayForecast}
                      </div>
                    </div>
                  );
                }

                if (suggestionOrRequest) {
                  htmlLines.push(
                    <div className='flex-vbox border-0'>
                      <div className="tooltip-header">
                        <span className="tooltip-title">{'Suggestions/Requests'}:</span>
                      </div>
                      <div className="tooltip-body">
                        {suggestionOrRequest}
                      </div>
                    </div>
                  );
                }

                return (
                  <bs.CssTooltip width={480} position='auto'>
                    <bs.CssTooltipToggle>
                      <div className="d-flex align-items-center justify-content-start w-100" style={{ cursor: 'pointer', userSelect: 'text' }}>
                        <div className={`d-flex align-items-center px-1 py-1 text-success`}>
                          {util.text.formater.uiTruncate(
                            [marketInformation, reportedIssues, displayForecast, suggestionOrRequest].filter(Boolean).join(' | '),
                            300, true
                          )}
                        </div>
                      </div>
                    </bs.CssTooltipToggle>
                    <bs.CssTooltipContent>
                      <div className='flex-vbox'>
                        {htmlLines}
                      </div>
                    </bs.CssTooltipContent>
                  </bs.CssTooltip>
                );
              } else {
                return (<div className="text-wrap position-relative" >-</div>);
              }
            }
          },
          { name: 'suggestionOrRequest', label: T('Suggestions/Requests'), width: 350 },
        ],
      },
      toolbar: {
        hide: true
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          },
        }
      }
    }
    return config;
  }

  onDefaultSelect(dRecord: grid.DisplayRecord): void {
    const { pageContext, appContext } = this.props;
    let record: any = dRecord.record || {};
    let reportId: number = record['id'];
    appContext.createHttpBackendCall("PartnerReportService", "getSalemanKeyAccountReportById", { id: reportId })
      .withSuccessData((report: any) => {

        let volumePerformance: any = typeof report['volumePerformance'] === 'string'
          ? JSON.parse(report['volumePerformance'])
          : (report['volumePerformance'] || {});
        report['volumePerformance'] = volumePerformance;

        let forecast: any = typeof report['forecast'] === 'string'
          ? JSON.parse(report['forecast'])
          : (report['forecast'] || {});
        report['forecast'] = forecast;

        let highlights: any = typeof report['highlights'] === 'string'
          ? JSON.parse(report['highlights'])
          : (report['highlights'] || {});
        report['highlights'] = highlights;

        console.log(report);


        let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return <UIKeyAccountFormReport appContext={appCtx} pageContext={pageCtx} initReportBean={report} />
        }
        let popupId = `make-key-account-report-${util.IDTracker.next()}`;
        let popupLabel: string = `Performance Report - ${report['salemanLabel'] || 'N/A'}`;
        pageContext.createPopupPage(popupId, popupLabel, createAppPage, { size: 'xl', backdrop: 'static' });
      })
      .call()
  }

  onDeleteAction(): void {
    const { appContext, plugin } = this.props;
    const selectedIds = plugin.getListModel().getSelectedRecordIds();

    if (selectedIds.length === 0) {
      bs.notificationShow("warning", T("warning"), T("No saleman account report selected!"));
      return;
    }

    const callbackConfirm = () => {
      appContext.createHttpBackendCall('PartnerReportService', 'deleteSalemanKeyAccountReports', { targetIds: selectedIds })
        .withSuccessData((_data: any) => {
          appContext.addOSNotification("success", T("Delete saleman account report success"));
          this.reloadData();
        })
        .call();
    }
    let message = (<div className="text-danger">Do you want to delete these partner report?</div>);
    bs.dialogConfirmMessage('Confirm Message', message, callbackConfirm);
  }

  onModify = (_bean: any, _field: string, _oldVal: any, _newVal: any): void => {
    const { plugin } = this.props;
    let searchParam = plugin.getSearchParams();
    if (_field === 'maxReturn') {
      searchParam.maxReturn = _newVal || 5000;
    } else {
      searchParam.params = _bean;
    }
    plugin.searchParams = searchParam;
    this.reloadData();
    this.nextViewId();
    this.vgridContext.getVGrid().forceUpdateView();
  };

  render() {
    if (this.isLoading()) return this.renderLoading();
    const { plugin } = this.props;
    let searchParam = plugin.getSearchParams();

    return (
      <div className='flex-vbox'>
        <div className='bg-white flex-hbox flex-grow-0 justify-content-between align-items-center mx-1 px-1 mt-1'>

          <div className='flex-hbox justify-content-start align-items-center'>
            <div className='me-2'>
              <input.BBSelectField className="fw-bold text-primary border-bottom text-center"
                style={{ width: 150 }} bean={searchParam} field={"maxReturn"}
                options={[1000, 2000, 5000, 10000]}
                optionLabels={['Show 1000 records', 'Show 2000 records', 'Show 5000 records', 'Show 10.000 records']}
                onInputChange={this.onModify} />
            </div>
          </div>

          <div className="flex-hbox justify-content-end align-items-center flex-grow-1">
            <bs.Button laf='warning' className="border-0 p-1 border-end rounded-0" outline
              onClick={() => this.onDeleteAction()}>
              <FeatherIcon.Trash size={12} /> Del
            </bs.Button>
          </div>

        </div>
        <div key={this.viewId} className='flex-vbox'>
          {this.renderUIGrid()}
        </div>
      </div>
    )
  }
}

