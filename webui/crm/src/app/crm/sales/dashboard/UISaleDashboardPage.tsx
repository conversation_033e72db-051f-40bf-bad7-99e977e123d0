import React from "react";
import * as FeatherIcon from 'react-feather';
import { bs, app, util } from '@datatp-ui/lib';
import {
  UIPartnerEventHistoryReportList,
  UIPartnerEventHistoryReportPlugin
} from "../leads/UIPartnerEventHistoryReportList";
import { UIInquiryRequestList, UIInquiryRequestReportPlugin } from "../../price";
import { UIBookingList, UIBookingListPlugin } from "../booking/UIBookingList";
import { UISaleReport, UISaleReportPlugin } from "./UISaleReport";
import { UISalemanSystemPerformance } from "./UISalemanSystemPerformance";
import { UITaskCalendarPage } from "../report/UITaskCalendarPage";

export interface UISaleDashboardProps extends app.AppComponentProps {
  space: 'User' | 'Company' | 'System';
}
interface UISaleDashboardState {
  showButtons: boolean;
}
export class UISaleDashboard extends app.AppComponent<UISaleDashboardProps, UISaleDashboardState> {

  constructor(props: UISaleDashboardProps) {
    super(props);

    this.state = {
      showButtons: false,
    }

  }

  handlePartnerLostClick = () => {
    // Handle partner lost action
    console.log('Partner Lost clicked');
  }

  handleCustomerLeadClick = () => {
    // Handle customer lead due date action
    console.log('Customer Lead Due Date clicked');
  }

  onMouseEnterCard = (e: any) => {
    e.target.style.backgroundColor = '#e9ecef';
  }

  onMouseLeaveCard = (e: any) => {
    e.target.style.backgroundColor = '#f8f9fa';
  }

  onShowPartnerDailyTasks = () => {
    let { pageContext } = this.props;
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <UIPartnerEventHistoryReportList appContext={appCtx} pageContext={pageCtx}
          plugin={new UIPartnerEventHistoryReportPlugin()} />
      );
    }
    let pupupLabel: string = `Leads/ Customers Recent Activities`;
    pageContext.createPopupPage('partner-daily-tasks', pupupLabel, createAppPage, { size: 'xl', backdrop: 'static' });
  }

  onViewAllDailyTasks = () => {
    let { pageContext } = this.props;
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (<UITaskCalendarPage appContext={appCtx} pageContext={pageCtx} />);
    }
    let popupId = `view-all-tasks-${util.IDTracker.next()}`;
    let pupupLabel: string = `Sales Daily Activities`;
    pageContext.createPopupPage(popupId, pupupLabel, createAppPage, { size: 'xl', backdrop: 'static' });
  }

  onViewAllInquiryRequests = () => {
    let { pageContext } = this.props;
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      let plugin = new UIInquiryRequestReportPlugin('Company');
      return (
        <UIInquiryRequestList appContext={appCtx} pageContext={pageCtx} plugin={plugin} space="Company" />
      );
    }
    let popupId = `view-all-inquiry-requests-${util.IDTracker.next()}`;
    let pupupLabel: string = `Inquiry Requests`;
    pageContext.createPopupPage(popupId, pupupLabel, createAppPage, { size: 'xl', backdrop: 'static' });
  }

  onViewAllBookings = () => {
    let { pageContext } = this.props;
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      let plugin = new UIBookingListPlugin('Company');
      return (
        <UIBookingList appContext={appCtx} pageContext={pageCtx} plugin={plugin} type="page" />
      );
    }
    let popupId = `view-all-bookings-${util.IDTracker.next()}`;
    let pupupLabel: string = `Bookings`;
    pageContext.createPopupPage(popupId, pupupLabel, createAppPage, { size: 'xl', backdrop: 'static' });
  }

  onViewAllSaleUserAnalysis = () => {
    let { pageContext, space } = this.props;
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      let plugin = new UIBookingListPlugin('Company');
      return (
        <UISaleReport appContext={appCtx} pageContext={pageCtx} plugin={new UISaleReportPlugin(space)} />
      );
    }
    let popupId = `view-all-sale-user-analysis-${util.IDTracker.next()}`;
    let pupupLabel: string = `User Analysis`;
    pageContext.createPopupPage(popupId, pupupLabel, createAppPage, { size: 'xl', backdrop: 'static' });
  }

  renderDailyTasksActivities() {
    const hoverStyle = {
      boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
      transform: 'translateY(-2px)'
    };

    const textColor = '#1A5319';
    const backgroundColor = '#D6EFD8';
    const gradientBackground = 'linear-gradient(135deg, #D6EFD8, #80AF81)';
    const buttonHoverColor = '#fff';

    return (
      <div className="flex-hbox justify-content-center align-items-center my-2 px-2">
        <div className="flex-hbox justify-content-center align-items-center rounded-md w-100"
          style={{
            color: textColor,
            padding: '18px 12px',
            minHeight: '100px',
            maxHeight: '100px',
            transition: 'all 0.3s ease',
            backgroundColor: backgroundColor,
            position: 'relative',
            border: '1px solid #80AF81',
          }}
          onMouseEnter={(e: any) => {
            Object.assign(e.currentTarget.style, hoverStyle);
            this.setState({ showButtons: true });
          }}
          onMouseLeave={(e: any) => {
            e.currentTarget.style.boxShadow = 'none';
            e.currentTarget.style.transform = 'translateY(0)';
            this.setState({ showButtons: false });
          }} >

          {!this.state.showButtons && (
            <div className="d-flex align-items-center" style={{ color: textColor }}>
              <FeatherIcon.Trello size={24} className="me-3" style={{ color: '#508D4E' }} />
              <h3 className="fw-bold" style={{ color: textColor, margin: 0 }}>Sales Activities</h3>
            </div>
          )}

          {this.state.showButtons && (
            <div className="d-flex w-100 justify-content-between gap-4" >
              <div className="d-flex flex-column align-items-center rounded py-2 flex-fill"
                style={{
                  background: gradientBackground,
                  transition: 'all 0.3s ease',
                  cursor: 'pointer',
                  border: '1px solid #80AF81'
                }}
                onClick={this.onViewAllDailyTasks}
                onMouseEnter={e => (e.currentTarget.style.background = buttonHoverColor)}
                onMouseLeave={e => (e.currentTarget.style.background = gradientBackground)} >
                <FeatherIcon.List size={20} className="mb-2" style={{ color: '#1A5319' }} />
                <h5 className="mb-0 fw-bold" style={{ color: '#1A5319', fontSize: 14 }}>All Tasks</h5>
              </div>

              <div className="d-flex flex-column align-items-center rounded py-2 flex-fill"
                style={{
                  background: gradientBackground,
                  transition: 'all 0.3s ease',
                  cursor: 'pointer',
                  border: '1px solid #80AF81'
                }}
                onClick={this.onShowPartnerDailyTasks}
                onMouseEnter={e => (e.currentTarget.style.background = buttonHoverColor)}
                onMouseLeave={e => (e.currentTarget.style.background = gradientBackground)} >
                <FeatherIcon.Users size={20} className="mb-2" style={{ color: '#1A5319' }} />
                <h5 className="mb-0 fw-bold" style={{ color: '#1A5319', fontSize: 14 }}>Leads/ Customers</h5>
              </div>

              <div className="d-flex flex-column align-items-center rounded py-2 flex-fill"
                style={{
                  background: gradientBackground,
                  transition: 'all 0.3s ease',
                  cursor: 'pointer',
                  border: '1px solid #80AF81'
                }}
                onClick={this.onViewAllSaleUserAnalysis}
                onMouseEnter={e => (e.currentTarget.style.background = buttonHoverColor)}
                onMouseLeave={e => (e.currentTarget.style.background = gradientBackground)} >
                <FeatherIcon.Users size={20} className="mb-2" style={{ color: '#1A5319' }} />
                <h5 className="mb-0 fw-bold" style={{ color: '#1A5319', fontSize: 14 }}>Users Monitoring</h5>
              </div>

              <div className="d-flex flex-column align-items-center rounded py-2 flex-fill"
                style={{
                  background: gradientBackground,
                  transition: 'all 0.3s ease',
                  cursor: 'pointer',
                  border: '1px solid #80AF81'
                }}
                onMouseEnter={e => (e.currentTarget.style.background = buttonHoverColor)}
                onMouseLeave={e => (e.currentTarget.style.background = gradientBackground)} >
                <FeatherIcon.Download size={20} className="mb-2" style={{ color: '#1A5319' }} />
                <h5 className="mb-0 fw-bold" style={{ color: '#1A5319', fontSize: 14 }}>Export Excel</h5>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  }

  // Render method for Partner Lost section
  renderPartnerLostSection() {
    // Color palette
    const primaryColor = '#1A5319';
    const secondaryColor = '#508D4E';
    const lightColor = '#D6EFD8';
    const borderColor = '#80AF81';

    const cardStyle: any = {
      border: `1px solid ${borderColor}`,
      borderRadius: '8px',
      padding: '20px',
      margin: '8px',
      backgroundColor: '#fff',
      cursor: 'pointer',
      transition: 'all 0.3s ease',
      position: 'relative',
      minHeight: '120px',
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center'
    };

    const hoverStyle: any = {
      boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
      transform: 'translateY(-2px)'
    };

    const iconStyle: any = {
      marginBottom: '12px',
      color: secondaryColor
    };

    const titleStyle: any = {
      fontSize: '16px',
      fontWeight: '600',
      color: primaryColor,
      marginBottom: '8px'
    };

    const actionButtonsStyle: any = {
      position: 'absolute',
      top: '12px',
      right: '12px',
      display: 'flex',
      gap: '6px',
      opacity: '0',
      transition: 'opacity 0.3s ease'
    };

    const actionButtonStyle: any = {
      backgroundColor: lightColor,
      border: `1px solid ${borderColor}`,
      borderRadius: '4px',
      padding: '4px 8px',
      fontSize: '12px',
      cursor: 'pointer',
      transition: 'all 0.2s ease',
      color: primaryColor
    };

    return (
      <div style={cardStyle} className="flex-grow-1"
        onClick={this.handlePartnerLostClick}
        onMouseEnter={(e: any) => {
          Object.assign(e.currentTarget.style, hoverStyle);
          e.currentTarget.querySelector('.action-buttons').style.opacity = '1';
        }}
        onMouseLeave={(e: any) => {
          e.currentTarget.style.boxShadow = 'none';
          e.currentTarget.style.transform = 'translateY(0)';
          e.currentTarget.querySelector('.action-buttons').style.opacity = '0';
        }} >
        <div style={iconStyle}>
          <FeatherIcon.UserX size={24} />
        </div>
        <div style={titleStyle}>Partner Lost</div>
        <div style={{ fontSize: '14px', color: secondaryColor }}>
          Track and analyze lost partnerships
        </div>

        {/* Action Buttons */}
        <div className="action-buttons" style={actionButtonsStyle as React.CSSProperties}>
          <button style={actionButtonStyle}
            onClick={(e: any) => {
              e.stopPropagation();
              console.log('View details clicked');
            }}
            onMouseEnter={(e) => { e.currentTarget.style.backgroundColor = '#fff' }}
            onMouseLeave={(e) => { e.currentTarget.style.backgroundColor = lightColor }} >
            <FeatherIcon.Eye size={12} style={{ marginRight: '4px' }} />
            View
          </button>
          <button
            style={actionButtonStyle}
            onClick={(e) => {
              e.stopPropagation();
              console.log('Edit clicked');
            }}
            onMouseEnter={(e) => { e.currentTarget.style.backgroundColor = '#fff' }}
            onMouseLeave={(e) => { e.currentTarget.style.backgroundColor = lightColor }} >
            <FeatherIcon.Edit3 size={12} style={{ marginRight: '4px' }} />
            Edit
          </button>
        </div>
      </div>
    );
  }

  renderCustomerLeadDueDateSection() {
    // Color palette
    const primaryColor = '#1A5319';
    const secondaryColor = '#508D4E';
    const lightColor = '#D6EFD8';
    const borderColor = '#80AF81';

    const cardStyle: any = {
      border: `1px solid ${borderColor}`,
      borderRadius: '8px',
      padding: '20px',
      margin: '8px',
      backgroundColor: '#fff',
      cursor: 'pointer',
      transition: 'all 0.3s ease',
      position: 'relative',
      minHeight: '120px',
      display: 'flex',
      flexDirection: 'column',
      justifyContent: 'center'
    };

    const hoverStyle: any = {
      boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
      transform: 'translateY(-2px)'
    };

    const iconStyle: any = {
      marginBottom: '12px',
      color: secondaryColor
    };

    const titleStyle: any = {
      fontSize: '16px',
      fontWeight: '600',
      color: primaryColor,
      marginBottom: '8px'
    };

    const actionButtonsStyle: any = {
      position: 'absolute',
      top: '12px',
      right: '12px',
      display: 'flex',
      gap: '6px',
      opacity: '0',
      transition: 'opacity 0.3s ease'
    };

    const actionButtonStyle: any = {
      backgroundColor: lightColor,
      border: `1px solid ${borderColor}`,
      borderRadius: '4px',
      padding: '4px 8px',
      fontSize: '12px',
      cursor: 'pointer',
      transition: 'all 0.2s ease',
      color: primaryColor
    };

    return (
      <div style={cardStyle} className="flex-grow-1"
        onClick={this.handleCustomerLeadClick}
        onMouseEnter={(e: any) => {
          Object.assign(e.currentTarget.style, hoverStyle);
          e.currentTarget.querySelector('.action-buttons').style.opacity = '1';
        }}
        onMouseLeave={(e: any) => {
          e.currentTarget.style.boxShadow = 'none';
          e.currentTarget.style.transform = 'translateY(0)';
          e.currentTarget.querySelector('.action-buttons').style.opacity = '0';
        }} >
        <div style={iconStyle}>
          <FeatherIcon.Calendar size={24} />
        </div>
        <div style={titleStyle}>Customer Lead Due Date</div>
        <div style={{ fontSize: '14px', color: secondaryColor }}>
          Monitor upcoming lead deadlines
        </div>

        {/* Action Buttons */}
        <div className="action-buttons" style={actionButtonsStyle as React.CSSProperties}>
          <button style={actionButtonStyle}
            onClick={(e) => {
              e.stopPropagation();
              console.log('View calendar clicked');
            }}
            onMouseEnter={(e) => { e.currentTarget.style.backgroundColor = '#fff' }}
            onMouseLeave={(e) => { e.currentTarget.style.backgroundColor = lightColor }} >
            <FeatherIcon.Calendar size={12} style={{ marginRight: '4px' }} />
            Calendar
          </button>
          <button style={actionButtonStyle}
            onClick={(e) => {
              e.stopPropagation();
              console.log('Set reminder clicked');
            }}
            onMouseEnter={(e) => { e.currentTarget.style.backgroundColor = '#fff' }}
            onMouseLeave={(e) => { e.currentTarget.style.backgroundColor = lightColor }} >
            <FeatherIcon.Bell size={12} style={{ marginRight: '4px' }} />
            Remind
          </button>
        </div>
      </div>
    );
  }

  // Render method for Inquiry, Quotation, Booking section
  renderInquiryQuotationBookingSection() {
    // Color palette
    const primaryColor = '#1A5319';
    const secondaryColor = '#508D4E';
    const lightColor = '#D6EFD8';
    const borderColor = '#80AF81';

    const cardStyle = {
      minHeight: '200px',
      cursor: 'pointer',
      transition: 'all 0.3s ease',
      backgroundColor: '#fff',
      border: `1px solid ${borderColor}`,
      borderRadius: '8px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '20px'
    };

    const hoverStyle = {
      boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
      transform: 'translateY(-2px)',
      backgroundColor: lightColor
    };

    const iconStyle = {
      color: secondaryColor
    };

    const titleStyle = {
      color: primaryColor,
      margin: 0,
      fontWeight: '600'
    };

    return (
      <div className="mb-3 px-2 flex-hbox gap-3">
        <div className="flex-grow-1 rounded-md" style={cardStyle}
          onClick={this.onViewAllInquiryRequests}
          onMouseEnter={(e: any) => {
            Object.assign(e.currentTarget.style, hoverStyle);
          }}
          onMouseLeave={(e: any) => {
            e.currentTarget.style.boxShadow = 'none';
            e.currentTarget.style.transform = 'translateY(0)';
            e.currentTarget.style.backgroundColor = '#fff';
          }}>
          <div className="d-flex flex-column align-items-center">
            <FeatherIcon.HelpCircle size={32} className="mb-3" style={iconStyle} />
            <h3 className="fw-bold" style={titleStyle}>Inquiry Requests</h3>
            <span style={{ fontSize: '14px', color: secondaryColor, marginTop: '8px' }}>
              Manage customer inquiries
            </span>
          </div>
        </div>

        <div className="flex-grow-1 rounded-md"
          style={cardStyle}
          onClick={this.onViewAllBookings}
          onMouseEnter={(e: any) => {
            Object.assign(e.currentTarget.style, hoverStyle);
          }}
          onMouseLeave={(e: any) => {
            e.currentTarget.style.boxShadow = 'none';
            e.currentTarget.style.transform = 'translateY(0)';
            e.currentTarget.style.backgroundColor = '#fff';
          }}>
          <div className="d-flex flex-column align-items-center">
            <FeatherIcon.FileText size={32} className="mb-3" style={iconStyle} />
            <h3 className="fw-bold" style={titleStyle}>Quotations</h3>
            <span style={{ fontSize: '14px', color: secondaryColor, marginTop: '8px' }}>
              View and create quotations
            </span>
          </div>
        </div>

        <div className="flex-grow-1 rounded-md" style={cardStyle}
          onClick={this.onViewAllBookings}
          onMouseEnter={(e: any) => {
            Object.assign(e.currentTarget.style, hoverStyle);
          }}
          onMouseLeave={(e: any) => {
            e.currentTarget.style.boxShadow = 'none';
            e.currentTarget.style.transform = 'translateY(0)';
            e.currentTarget.style.backgroundColor = '#fff';
          }}>
          <div className="d-flex flex-column align-items-center">
            <FeatherIcon.Calendar size={32} className="mb-3" style={iconStyle} />
            <h3 className="fw-bold" style={titleStyle}>Bookings</h3>
            <span style={{ fontSize: '14px', color: secondaryColor, marginTop: '8px' }}>
              Manage internal bookings
            </span>
          </div>
        </div>
      </div>
    );
  }

  renderPipelineStagesSection() {
    // Sample data - replace with API data as needed
    const pipelineStats = {
      totalLeads: 320,
      newLeads: 45,
      lostLeads: 28,
      partnerLost: 12, // Thêm số lượng Partner Lost
      conversionRate: '18%',
      totalCustomers: 612
    };

    return (
      <div className="mb-2 p-2 border rounded mx-2 bg-white shadow-sm">
        <div className="d-flex align-items-center mb-2">
          <div className="rounded-circle p-1 me-2" style={{ backgroundColor: '#e4fff5' }}>
            <FeatherIcon.Activity size={16} style={{ color: '#0abb87' }} />
          </div>
          <h5 className="m-0 fw-bold" style={{ fontSize: '16px', color: '#3F4254' }}>
            Giai đoạn của Leads (Pipeline Stages)
          </h5>
        </div>
        <div className="row text-center g-0">
          <div className="col-md-2 col-6 mb-2">
            <div style={{ color: '#3699ff' }}>
              <FeatherIcon.Users size={20} />
            </div>
            <div className="h6 mb-0 fw-bold" style={{ color: '#3699ff' }}>{pipelineStats.totalLeads}</div>
            <small className="text-muted small">Tổng Leads</small>
          </div>
          <div className="col-md-2 col-6 mb-2">
            <div style={{ color: '#0abb87' }}>
              <FeatherIcon.UserPlus size={20} />
            </div>
            <div className="h6 mb-0 fw-bold" style={{ color: '#0abb87' }}>{pipelineStats.newLeads}</div>
            <small className="text-muted small">Leads mới</small>
          </div>
          <div className="col-md-2 col-6 mb-2">
            <div style={{ color: '#f64e60' }}>
              <FeatherIcon.UserX size={20} />
            </div>
            <div className="h6 mb-0 fw-bold" style={{ color: '#f64e60' }}>{pipelineStats.lostLeads}</div>
            <small className="text-muted small">Leads bị Lost</small>
          </div>
          <div className="col-md-2 col-6 mb-2">
            <div style={{ color: '#ff8c00' }}>
              <FeatherIcon.UserMinus size={20} />
            </div>
            <div className="h6 mb-0 fw-bold" style={{ color: '#ff8c00' }}>{pipelineStats.partnerLost}</div>
            <small className="text-muted small">Partner Lost</small>
          </div>
          <div className="col-md-2 col-6 mb-2">
            <div style={{ color: '#f6aa33' }}>
              <FeatherIcon.TrendingUp size={20} />
            </div>
            <div className="h6 mb-0 fw-bold" style={{ color: '#f6aa33' }}>{pipelineStats.conversionRate}</div>
            <small className="text-muted small">Tỷ lệ chuyển đổi</small>
          </div>
          <div className="col-md-2 col-6 mb-2">
            <div style={{ color: '#5557db' }}>
              <FeatherIcon.Users size={20} />
            </div>
            <div className="h6 mb-0 fw-bold" style={{ color: '#5557db' }}>{pipelineStats.totalCustomers}</div>
            <small className="text-muted small">Tổng khách hàng</small>
          </div>
        </div>
      </div>
    );
  }

  renderCustomerMapSection() {
    const { appContext, pageContext } = this.props;

    // Sample data - trong thực tế sẽ lấy từ API
    const provinceData = [
      { name: 'TP.HCM', customers: 156, region: 'south', growth: '+12%' },
      { name: 'Hà Nội', customers: 143, region: 'north', growth: '+8%' },
      { name: 'Bình Dương', customers: 89, region: 'south', growth: '+15%' },
      { name: 'Đồng Nai', customers: 67, region: 'south', growth: '+10%' },
      { name: 'Đà Nẵng', customers: 54, region: 'central', growth: '+6%' },
      { name: 'Hải Phòng', customers: 43, region: 'north', growth: '+5%' },
      { name: 'Khánh Hòa', customers: 32, region: 'central', growth: '+18%' },
      { name: 'Cần Thơ', customers: 28, region: 'south', growth: '+9%' }
    ];

    const industrialZones = [
      { name: 'KCN Việt Nam - Singapore', location: 'Bình Dương', customers: 45, type: 'Sản xuất' },
      { name: 'KCN Thăng Long', location: 'Hà Nội', customers: 38, type: 'Công nghệ' },
      { name: 'KCN Tân Thuận', location: 'TP.HCM', customers: 42, type: 'Xuất khẩu' },
      { name: 'KCN Đà Nẵng', location: 'Đà Nẵng', customers: 29, type: 'Logistics' },
      { name: 'KCN Long Hậu', location: 'Long An', customers: 31, type: 'Sản xuất' }
    ];

    const regionStats = {
      north: { customers: 186, provinces: 8, growth: '+7%' },
      central: { customers: 86, provinces: 5, growth: '+12%' },
      south: { customers: 340, provinces: 12, growth: '+13%' }
    };

    return (
      <div className="mb-4 p-3 border rounded mx-2 bg-white shadow-sm">
        <div className="d-flex justify-content-between align-items-center mb-2">
          <div className="d-flex align-items-center">
            <div className="rounded-circle p-1 me-2" style={{ backgroundColor: '#eef0ff' }}>
              <FeatherIcon.MapPin size={16} style={{ color: '#5557db' }} />
            </div>
            <h5 className="m-0 fw-bold" style={{ fontSize: '16px', color: '#3F4254' }}>
              Bản đồ khách hàng
            </h5>
          </div>
          <div className="d-flex gap-2">
            <button className="btn btn-sm py-1 px-2" style={{ backgroundColor: '#F3F6F9', color: '#3F4254', fontSize: '12px', border: 'none' }}>
              <FeatherIcon.Filter size={12} className="me-1" /> Lọc
            </button>
            <button
              className="btn btn-sm py-1 px-2"
              style={{ backgroundColor: '#5557db', color: 'white', fontSize: '12px', border: 'none' }}
              onClick={this.onViewCustomerMap}
            >
              <FeatherIcon.Map size={12} className="me-1" /> Xem chi tiết
            </button>
          </div>
        </div>

        <div className="row g-3">

          {/* Tổng quan theo miền */}
          <div className="col-12">
            <div className="row g-2">
              <div className="col-md-4">
                <div className="card border-0 shadow-sm h-100" style={{ background: 'linear-gradient(to right, #ffe2e5, #fff5f7)' }}>
                  <div className="card-body text-center py-3">
                    <div style={{ color: '#f64e60' }} className="mb-1">
                      <FeatherIcon.MapPin size={20} />
                    </div>
                    <h6 style={{ color: '#f64e60', fontWeight: 600, fontSize: '15px' }} className="mb-1">Miền Bắc</h6>
                    <div className="d-flex justify-content-around align-items-center mt-2">
                      <div>
                        <div className="h5 mb-0 fw-bold" style={{ color: '#f64e60' }}>{regionStats.north.customers}</div>
                        <small style={{ fontSize: '11px', color: '#7e8299' }}>khách hàng</small>
                      </div>
                      <div className="mx-1" style={{ width: '1px', height: '30px', background: '#eaeaea' }}></div>
                      <div>
                        <div className="fw-bold" style={{ color: '#0abb87', fontSize: '14px' }}>{regionStats.north.growth}</div>
                        <small style={{ fontSize: '11px', color: '#7e8299' }}>{regionStats.north.provinces} tỉnh</small>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="col-md-4">
                <div className="card border-0 shadow-sm h-100" style={{ background: 'linear-gradient(to right, #eef0ff, #f5f6ff)' }}>
                  <div className="card-body text-center py-3">
                    <div style={{ color: '#5557db' }} className="mb-1">
                      <FeatherIcon.MapPin size={20} />
                    </div>
                    <h6 style={{ color: '#5557db', fontWeight: 600, fontSize: '15px' }} className="mb-1">Miền Trung</h6>
                    <div className="d-flex justify-content-around align-items-center mt-2">
                      <div>
                        <div className="h5 mb-0 fw-bold" style={{ color: '#5557db' }}>{regionStats.central.customers}</div>
                        <small style={{ fontSize: '11px', color: '#7e8299' }}>khách hàng</small>
                      </div>
                      <div className="mx-1" style={{ width: '1px', height: '30px', background: '#eaeaea' }}></div>
                      <div>
                        <div className="fw-bold" style={{ color: '#0abb87', fontSize: '14px' }}>{regionStats.central.growth}</div>
                        <small style={{ fontSize: '11px', color: '#7e8299' }}>{regionStats.central.provinces} tỉnh</small>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="col-md-4">
                <div className="card border-0 shadow-sm h-100" style={{ background: 'linear-gradient(to right, #e4fff5, #f0fff8)' }}>
                  <div className="card-body text-center py-3">
                    <div style={{ color: '#0abb87' }} className="mb-1">
                      <FeatherIcon.MapPin size={20} />
                    </div>
                    <h6 style={{ color: '#0abb87', fontWeight: 600, fontSize: '15px' }} className="mb-1">Miền Nam</h6>
                    <div className="d-flex justify-content-around align-items-center mt-2">
                      <div>
                        <div className="h5 mb-0 fw-bold" style={{ color: '#0abb87' }}>{regionStats.south.customers}</div>
                        <small style={{ fontSize: '11px', color: '#7e8299' }}>khách hàng</small>
                      </div>
                      <div className="mx-1" style={{ width: '1px', height: '30px', background: '#eaeaea' }}></div>
                      <div>
                        <div className="fw-bold" style={{ color: '#0abb87', fontSize: '14px' }}>{regionStats.south.growth}</div>
                        <small style={{ fontSize: '11px', color: '#7e8299' }}>{regionStats.south.provinces} tỉnh</small>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Chi tiết theo tỉnh thành */}
          <div className="col-md-8">
            <div className="card h-100 border-0 shadow-sm mx-1">
              <div className="card-header bg-light py-2 px-3">
                <h6 className="mb-0 d-flex align-items-center">
                  <FeatherIcon.BarChart2 size={15} className="me-2" style={{ color: '#5557db' }} />
                  <span style={{ fontSize: '14px' }}>Top tỉnh thành có nhiều khách hàng</span>
                </h6>
              </div>
              <div className="card-body p-0">
                <div className="table-responsive">
                  <table className="table table-sm table-hover mb-0">
                    <thead className="table-light">
                      <tr>
                        <th className="border-0 py-2" style={{ fontSize: '12px', fontWeight: '600' }}>Tỉnh/Thành phố</th>
                        <th className="border-0 py-2" style={{ fontSize: '12px', fontWeight: '600' }}>Số KH</th>
                        <th className="border-0 py-2" style={{ fontSize: '12px', fontWeight: '600' }}>Tăng trưởng</th>
                        <th className="border-0 py-2" style={{ fontSize: '12px', fontWeight: '600' }}>Phân bố</th>
                      </tr>
                    </thead>
                    <tbody>
                      {provinceData.map((province, index) => {
                        const percentage = (province.customers / 612 * 100).toFixed(1);
                        const regionColorMap = {
                          'north': { color: '#f64e60', light: '#ffe2e5' },
                          'central': { color: '#5557db', light: '#eef0ff' },
                          'south': { color: '#0abb87', light: '#e4fff5' }
                        } as const;
                        const regionStyle = regionColorMap[province.region as keyof typeof regionColorMap];

                        return (
                          <tr key={index} style={{ fontSize: '12px' }}>
                            <td className="py-1">
                              <div className="d-flex align-items-center">
                                <div className="rounded-circle"
                                  style={{ width: '7px', height: '7px', backgroundColor: regionStyle.color }}></div>
                                <span className="ms-2">{province.name}</span>
                              </div>
                            </td>
                            <td className="fw-bold py-1">{province.customers}</td>
                            <td className="py-1">
                              <span className="badge" style={{ backgroundColor: '#e4fff5', color: '#0abb87', fontSize: '10px', padding: '2px 5px' }}>
                                {province.growth}
                              </span>
                            </td>
                            <td className="py-1">
                              <div className="d-flex align-items-center">
                                <div className="progress flex-grow-1 me-2" style={{ height: '5px' }}>
                                  <div className="progress-bar"
                                    style={{ width: `${percentage}%`, backgroundColor: regionStyle.color }}></div>
                                </div>
                                <small style={{ fontSize: '10px', color: '#7e8299' }}>{percentage}%</small>
                              </div>
                            </td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>

          {/* Khu công nghiệp */}
          <div className="col-md-4">
            <div className="card h-100 border-0 shadow-sm">
              <div className="card-header bg-light py-2 px-3">
                <h6 className="mb-0 d-flex align-items-center">
                  <FeatherIcon.Briefcase size={15} className="me-2" style={{ color: '#3699ff' }} />
                  <span style={{ fontSize: '14px' }}>Khu công nghiệp</span>
                </h6>
              </div>
              <div className="card-body p-2">
                <div className="d-flex flex-column gap-2">
                  {industrialZones.map((zone, index) => {
                    // Mô phỏng độ phủ - thực tế sẽ lấy từ API
                    const totalCustomers = zone.customers; // Số lượng khách hàng hiện tại
                    const totalPotential = Math.floor(zone.customers * (100 / (20 + Math.random() * 40))); // Tổng tiềm năng (mô phỏng)
                    const coveragePercentage = Math.round((totalCustomers / totalPotential) * 100);

                    // Màu sắc dựa vào độ phủ
                    let coverageColor = '#3699ff'; // Mặc định
                    if (coveragePercentage < 30) coverageColor = '#f64e60'; // Đỏ - thấp
                    else if (coveragePercentage < 60) coverageColor = '#f6aa33'; // Cam - trung bình
                    else coverageColor = '#0abb87'; // Xanh - cao

                    return (
                      <div key={index} className="p-2 bg-light rounded border">
                        <div className="d-flex justify-content-between align-items-center mb-1">
                          <div className="fw-bold text-truncate" style={{ maxWidth: '300px', fontSize: '13px' }} title={zone.name}>
                            {zone.name}
                          </div>
                          <span className="badge" style={{ backgroundColor: '#e4fff5', color: '#0abb87', fontSize: '10px', padding: '2px 5px' }}>
                            {zone.type}
                          </span>
                        </div>

                        <div className="d-flex justify-content-between align-items-center mb-1">
                          <small className="text-muted" style={{ fontSize: '11px' }}>{zone.location}</small>
                          <div className="d-flex align-items-center">
                            <small className="me-1" style={{ fontSize: '11px', color: '#3F4254' }}>
                              <span className="fw-bold" style={{ color: coverageColor }}>{totalCustomers}</span>/{totalPotential}
                            </small>
                          </div>
                        </div>

                        <div className="d-flex align-items-center">
                          <div className="progress flex-grow-1 me-2" style={{ height: '5px' }}>
                            <div className="progress-bar"
                              style={{ width: `${coveragePercentage}%`, backgroundColor: coverageColor }}></div>
                          </div>
                          <div className="fw-bold" style={{ fontSize: '12px', color: coverageColor }}>
                            {coveragePercentage}%
                          </div>
                        </div>

                        <div className="d-flex justify-content-between align-items-center mt-1">
                          <small className="text-muted" style={{ fontSize: '10px' }}>Độ phủ</small>
                          <small className="text-muted" style={{ fontSize: '10px' }}>
                            {coveragePercentage < 30 ? 'Thấp' : coveragePercentage < 60 ? 'Trung bình' : 'Cao'}
                          </small>
                        </div>
                      </div>
                    );
                  })}
                </div>

                <div className="mt-2 pt-2 border-top">
                  <div className="d-flex justify-content-between align-items-center" style={{ fontSize: '12px' }}>
                    <span className="text-muted">Tổng KCN:</span>
                    <div>
                      <span className="fw-bold" style={{ color: '#3699ff' }}>185</span>
                      <span className="text-muted"> / 412 khách hàng tiềm năng</span>
                    </div>
                  </div>
                  <div className="progress mt-1" style={{ height: '4px' }}>
                    <div className="progress-bar" style={{ width: '45%', backgroundColor: '#3699ff' }}></div>
                  </div>
                  <div className="d-flex justify-content-end mt-1">
                    <span style={{ fontSize: '11px', color: '#3699ff', fontWeight: 'bold' }}>45% độ phủ tổng thể</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

        </div>

        {/* Thống kê tổng quan */}
        <div className="row g-2 mt-1">
          <div className="col-12">
            <div className="card bg-light shadow-sm border-0">
              <div className="card-body py-2">
                <div className="row text-center g-0">
                  <div className="col-md-3">
                    <div style={{ color: '#5557db' }}>
                      <FeatherIcon.Users size={20} />
                    </div>
                    <div className="h6 mb-0 fw-bold" style={{ color: '#5557db' }}>612</div>
                    <small className="text-muted small">Tổng khách hàng</small>
                  </div>
                  <div className="col-md-3">
                    <div style={{ color: '#0abb87' }}>
                      <FeatherIcon.TrendingUp size={20} />
                    </div>
                    <div className="h6 mb-0 fw-bold" style={{ color: '#0abb87' }}>+11%</div>
                    <small className="text-muted small">Tăng trưởng chung</small>
                  </div>
                  <div className="col-md-3">
                    <div style={{ color: '#f6aa33' }}>
                      <FeatherIcon.MapPin size={20} />
                    </div>
                    <div className="h6 mb-0 fw-bold" style={{ color: '#f6aa33' }}>25</div>
                    <small className="text-muted small">Tỉnh/Thành phố</small>
                  </div>
                  <div className="col-md-3">
                    <div style={{ color: '#3699ff' }}>
                      <FeatherIcon.Briefcase size={20} />
                    </div>
                    <div className="h6 mb-0 fw-bold" style={{ color: '#3699ff' }}>12</div>
                    <small className="text-muted small">Khu công nghiệp</small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

      </div>
    );
  }

  onViewCustomerMap = () => {
    const { appContext, pageContext } = this.props;
    // Chuyển đến trang bản đồ khách hàng chi tiết
  }

  render() {
    if (this.isLoading()) return this.renderLoading();
    const { space } = this.props;
    return (
      <div className="flex-vbox mx-1">
        <bs.GreedyScrollable className="my-1">

          {space === 'System' && this.renderDailyTasksActivities()}

          <UISalemanSystemPerformance {...this.props} />

          {/* <div className="flex-hbox mb-3">
            {this.renderPartnerLostSection()}
            {this.renderCustomerLeadDueDateSection()}
          </div> */}

          {this.renderPipelineStagesSection()}
          {this.renderCustomerMapSection()}


          {/* <div className="flex-hbox mb-3 gap-2" style={{ maxHeight: 400, minHeight: 400 }}>
            <UISaleConversationRate {...this.props} />
          </div> */}

          {/* {this.renderInquiryQuotationBookingSection()} */}

        </bs.GreedyScrollable>
      </div>
    );
  }
}