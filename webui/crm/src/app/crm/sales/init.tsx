import React from "react";
import * as icon from 'react-feather';
import { app, bs } from '@datatp-ui/lib';
import { module } from '@datatp-ui/erp';

import { WCRMRateFinder } from "./quotation/request/WCRMRateFinder";
import { PartnerDashboardPage } from "./dashboard/UIPartnerWindow";
import { UISalesDailyTaskCalendar } from "./report/UIDailyTaskCalendarView";
import { SalesDailyTaskCalendarPlugin } from "./report/SalesDailyTaskCalendarPlugin";
import { UISaleDashboard } from "./dashboard/UISaleDashboardPage";
import { UIInquiryDashboard } from "../price/request/UIPricingDashboard";
import { UIQuotationWorkboard } from "./quotation/UIQuotationWorkboard";
import { UITaskCalendarPage } from "./report/UITaskCalendarPage";

import space = app.space;
import { UICrmUserRolesPage } from "../common/template/UICrmUserRolesPage";

class SaleSpacePlugin extends space.SpacePlugin {
  constructor() {
    super('forwarder/sales', 'Sale Navigation');
  }

  override createUserScreens(): space.ScreenConfig[] {
    if (bs.ScreenUtil.isMobileScreen()) return this.createUserMobileSreens();
    else return this.createUserDesktopSreens();
  }

  createUserMobileSreens(): space.ScreenConfig[] {
    const featureName = 'user-logistics-sales';
    return [
      {
        id: "crm", label: "CRM", icon: icon.Globe,
        checkPermission: {
          feature: { module: 'logistics', name: featureName },
          requiredCapability: app.READ,
        },
        renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return <WCRMRateFinder appContext={appCtx} pageContext={pageCtx} />
        },
        screens: [
          {
            id: 'user-quotation-workboard', label: 'Quotation Workboard', icon: icon.FileText,
            checkPermission: {
              feature: { module: 'logistics', name: featureName },
              requiredCapability: app.WRITE
            },
            renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
              return <UIQuotationWorkboard appContext={appCtx} pageContext={pageCtx} space="User" />
            },
          },
        ]
      }
    ]
  }

  createUserDesktopSreens(): space.ScreenConfig[] {
    const featureName = 'user-logistics-sales';
    return [
      {
        id: "crm", label: "CRM", icon: icon.Globe,
        checkPermission: {
          feature: { module: 'logistics', name: featureName },
          requiredCapability: app.READ,
        },
        renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return <WCRMRateFinder appContext={appCtx} pageContext={pageCtx} />
        },
        screens: [
          {
            id: 'user-sale-quick-rate-finder', label: 'Quick Rate Finder', icon: icon.Search,
            checkPermission: {
              feature: { module: 'logistics', name: featureName },
              requiredCapability: app.WRITE,
            },
            renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
              return <WCRMRateFinder appContext={appCtx} pageContext={pageCtx} />
            },
          },
          {
            id: 'user-quotation-workboard', label: 'Quotation Workboard', icon: icon.FileText,
            checkPermission: {
              feature: { module: 'logistics', name: featureName },
              requiredCapability: app.WRITE
            },
            renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
              return <UIQuotationWorkboard appContext={appCtx} pageContext={pageCtx} space="User" />
            },
          },
          {
            id: 'user-crm-partners', label: 'Partners', icon: icon.Users,
            checkPermission: {
              feature: { module: 'logistics', name: featureName },
              requiredCapability: app.WRITE
            },
            renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
              return <PartnerDashboardPage appContext={appCtx} pageContext={pageCtx} space="User" />
            },
          },
          {
            id: 'user-tasks-calendar', label: 'Tasks Calendar', icon: icon.Calendar,
            checkPermission: {
              feature: { module: 'logistics', name: featureName },
              requiredCapability: app.WRITE,
            },
            renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
              return <UISalesDailyTaskCalendar appContext={appCtx} pageContext={pageCtx} space="User" />;
            },
          },

        ]
      },
    ];
  }

  override createCompanyScreens(): space.ScreenConfig[] {
    const featureName = 'company-logistics-sales';
    return [
      {
        id: "company-crm-dashboard", label: "CRM", icon: icon.Globe,
        checkPermission: {
          feature: { module: 'logistics', name: featureName },
          requiredCapability: app.READ,
        },
        renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return <UISaleDashboard appContext={appCtx} pageContext={pageCtx} space="Company" />;
        },
        screens: [
          {
            id: 'company-inquiry-dashboard', label: 'Inquiry Dashboard', icon: icon.BarChart2,
            checkPermission: {
              feature: { module: 'logistics', name: featureName },
              requiredCapability: app.READ,
            },
            renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
              return (
                <UIInquiryDashboard appContext={appCtx} pageContext={pageCtx} space="Company" />
              )
            },
          },
          {
            id: 'company-quotation-workboard', label: 'Quotation Workboard', icon: icon.FileText,
            checkPermission: {
              feature: { module: 'logistics', name: featureName },
              requiredCapability: app.MODERATOR,
            },
            renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
              return (
                <UIQuotationWorkboard appContext={appCtx} pageContext={pageCtx} space="Company" />
              )
            },
          },
          {
            id: 'company-crm-partners', label: 'Partners', icon: icon.Users,
            checkPermission: {
              feature: { module: 'logistics', name: featureName },
              requiredCapability: app.READ
            },
            renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
              return <PartnerDashboardPage appContext={appCtx} pageContext={pageCtx} space="Company" />
            },
          },
          {
            id: 'company-tasks-calendar', label: 'Tasks Calendar', icon: icon.Trello,
            checkPermission: {
              feature: { module: 'logistics', name: featureName },
              requiredCapability: app.READ,
            },
            renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
              return <UITaskCalendarPage appContext={appCtx} pageContext={pageCtx} />;
            },
          },
        ]
      }
    ]
  }

  override createSystemScreens(): app.space.ScreenConfig[] {
    const space = 'System';
    const featureName = 'company-logistics-sales';
    return [
      {
        id: "company-crm", label: "CRM", icon: icon.Activity,
        checkPermission: {
          feature: { module: 'logistics', name: featureName },
          requiredCapability: app.READ,
        },
        renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return <UISaleDashboard appContext={appCtx} pageContext={pageCtx} space={space} />;
        },
        screens: [
          {
            id: 'system-crm-partners', label: 'Partners', icon: icon.UserCheck,
            checkPermission: {
              feature: { module: 'logistics', name: featureName },
              requiredCapability: app.READ
            },
            renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
              return <PartnerDashboardPage appContext={appCtx} pageContext={pageCtx} space="System" />
            },
          },
          {
            id: 'company-inquiry-dashboard', label: 'Inquiry Dashboard', icon: icon.BarChart2,
            checkPermission: {
              feature: { module: 'logistics', name: featureName },
              requiredCapability: app.READ,
            },
            renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
              return (
                <UIInquiryDashboard appContext={appCtx} pageContext={pageCtx} space="System" />
              )
            },
          },
          {
            id: 'system-quotation-workboard', label: 'Quotation Workboard', icon: icon.FileText,
            checkPermission: {
              feature: { module: 'logistics', name: featureName },
              requiredCapability: app.MODERATOR,
            },
            renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
              return (
                <UIQuotationWorkboard appContext={appCtx} pageContext={pageCtx} space="System" />
              )
            },
          },
          {
            id: 'system-tasks-calendar', label: 'Tasks Calendar', icon: icon.Calendar,
            checkPermission: {
              feature: { module: 'logistics', name: featureName },
              requiredCapability: app.READ,
            },
            renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
              return <UITaskCalendarPage appContext={appCtx} pageContext={pageCtx} />;
            },
          },
          {
            id: 'system-user-role', label: 'User Role', icon: icon.Users,
            checkPermission: {
              feature: { module: 'logistics', name: featureName },
              requiredCapability: app.READ,
            },
            renderUI: (appCtx: app.AppContext, pageCtx: app.PageContext) => {
              return <UICrmUserRolesPage appContext={appCtx} pageContext={pageCtx} />;
            },
          },
        ]
      }
    ]
  }

}

export function init() {
  space.SpacePluginManager.register(new SaleSpacePlugin());
}

module.common.DbEntityTaskCalendarPluginManger.register(new SalesDailyTaskCalendarPlugin());

