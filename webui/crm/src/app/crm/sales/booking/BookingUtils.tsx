import React from 'react'

import { app, bs, entity, util } from '@datatp-ui/lib';

import { UIBookingModel } from './UIBookingInfo';

import { BookingModelBuilder } from '.';
import { mapToTypeOfShipment, TransportationMode, TransportationTool } from 'app/crm/common';

export function calculateSellingRate(sellingRate: any): any {
  sellingRate.unitPrice = sellingRate.unitPrice || 0;
  sellingRate.taxRate = sellingRate.taxRate || 0;
  sellingRate.exchangeRate = sellingRate.exchangeRate || 0;
  sellingRate.quantity = sellingRate.quantity || 0;
  const total = sellingRate.unitPrice * sellingRate.quantity;
  const totalTax = total * sellingRate.taxRate;
  sellingRate.totalAmount = total + totalTax;
  sellingRate.domesticUnitPrice = sellingRate.unitPrice * sellingRate.exchangeRate;
  sellingRate.domesticTotalAmount = (total + totalTax) * sellingRate.exchangeRate;
  sellingRate.domesticCurrency = 'VND';
  return sellingRate;
}

export class UIBookingUtils {

  static onNewBooking = (appCtx: app.AppContext, pageCtx: app.PageContext, quotation: any) => {
    let quoteList: any[] = quotation['quoteListSelector'] || [];

    if (quoteList.length === 0) {
      quoteList = quotation['quoteList'] || [];
      appCtx.addOSNotification('info', "The first quote is selected by default");
    }

    if (quoteList.length === 0) {
      let message = (<div className="ms-1 text-info py-3 border-bottom">Must be select one freight rate.!!!</div>);
      bs.dialogShow('Message', message, { backdrop: 'static', size: 'md' });
      return;
    }

    const quote: any = quoteList[0];

    const inquiry: any = quotation['inquiry'] || {};

    // inquiry['fromLocationCode'] = quote['fromLocationCode'];
    // inquiry['fromLocationLabel'] = quote['fromLocationLabel'];
    // inquiry['toLocationCode'] = quote['toLocationCode'];
    // inquiry['toLocationLabel'] = quote['toLocationLabel'];
    // inquiry['finalDestination'] = quote['finalDestination'];

    const referenceCode: any = inquiry['referenceCode'];

    const weight: number = inquiry['chargeableWeight'] || inquiry['grossWeightKg'] || 1;
    const volume: number = inquiry['chargeableVolume'] || inquiry['volumeCbm'] || 1;

    const type = mapToTypeOfShipment(inquiry['purpose'], inquiry['mode'])
    const bookingBuilder = new BookingModelBuilder(inquiry);
    bookingBuilder.processQuote(quote);

    quotation['localHandlingCharges'].map((sel: any) => {

      if (sel['unit'] === 'CBM') {
        sel['quantity'] = volume;
      } else if (sel['unit'] === 'KGS') {
        sel['quantity'] = weight;
      } else {
        sel['quantity'] = 1;
      }

      let mode: TransportationMode = sel['mode'];
      if (TransportationTool.isTruck(mode) || TransportationTool.isUnknown(mode)) {
        bookingBuilder.buildLocalCharge('CUSTOM', sel, inquiry['containers'] || []);
      } else {
        bookingBuilder.buildLocalCharge('LOCAL_CHARGE', sel, inquiry['containers'] || []);
      }
    })
    bookingBuilder.buildShipmentInfo();
    let newBookingModel = bookingBuilder.buildBookingModel();

    appCtx.createHttpBackendCall('BookingService', 'newBookingModel', { model: newBookingModel })
      .withSuccessData((data: any) => {
        let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
          return (
            <UIBookingModel appContext={appCtx} pageContext={pageCtx} observer={new entity.ComplexBeanObserver(data)} />
          );
        }
        pageCtx.createPopupPage(`new-booking-page-${util.IDTracker.next()}`, `IBooking: ${referenceCode || type}`, createAppPage, { size: 'xl', backdrop: "static" });
      })
      .call()
  }
}

