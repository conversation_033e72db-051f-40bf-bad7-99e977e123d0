import React from "react";
import * as FeatherIcon from 'react-feather';
import { bs, app, util, input, entity } from '@datatp-ui/lib';
import { ImportExportPurpose, TransportationMode } from "./model";
import { PricingRequestStatusUtils } from "../price";
import { mapToTypeOfShipment } from "./";
import { SOURCE_OPTIONS } from "../partner";

const SESSION = app.host.DATATP_HOST.session;

export interface WQuickTimeRangeSelectorProps extends app.AppComponentProps {
  onModify?: entity.EntityOnModify;
  initBean?: { fromValue: string, toValue: string, label: string }
  disabled?: boolean
}

interface WQuickTimeRangeSelectorState {
  bean: { fromValue: string, toValue: string, label: string }
}
export class WQuickTimeRangeSelector extends app.AppComponent<WQuickTimeRangeSelectorProps, WQuickTimeRangeSelectorState> {

  constructor(props: WQuickTimeRangeSelectorProps) {
    super(props);

    const { initBean } = this.props;
    if (initBean) {
      this.state = { bean: initBean }
    } else {
      const today = new Date();
      const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
      const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

      let dateFilter = new util.TimeRange();
      dateFilter.fromSetDate(firstDayOfMonth);
      dateFilter.toSetDate(lastDayOfMonth);
      this.state = {
        bean: {
          fromValue: dateFilter.fromFormat(),
          toValue: dateFilter.toFormat(),
          label: 'This Month'
        }
      }
    }
  }

  onSelectTimeRange = (range: string, label: string): void => {
    const { onModify } = this.props;
    const { bean } = this.state;

    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    let fromDate: Date, toDate: Date;

    switch (range) {
      case 'today':
        // Today only
        fromDate = new Date(today);
        toDate = new Date(today);
        break;
      case 'ytd':
        // Year to date - from Jan 1 to today
        fromDate = new Date(today.getFullYear(), 0, 1);
        toDate = new Date(today);
        break;
      case 'week':
        // Start of current week (Sunday)
        fromDate = new Date(today);
        fromDate.setDate(today.getDate() - today.getDay());
        // End of current week (Saturday)
        toDate = new Date(fromDate);
        toDate.setDate(fromDate.getDate() + 6);
        break;
      case 'month':
        // Start of current month
        fromDate = new Date(today.getFullYear(), today.getMonth(), 1);
        // End of current month
        toDate = new Date(today.getFullYear(), today.getMonth() + 1, 0);
        break;
      case 'quarter':
        // Start of current quarter
        const currentQuarter = Math.floor(today.getMonth() / 3);
        fromDate = new Date(today.getFullYear(), currentQuarter * 3, 1);
        // End of current quarter
        toDate = new Date(today.getFullYear(), (currentQuarter + 1) * 3, 0);
        break;
      case 'lastWeek':
        // Start of last week
        fromDate = new Date(today);
        fromDate.setDate(today.getDate() - today.getDay() - 7);
        // End of last week
        toDate = new Date(fromDate);
        toDate.setDate(fromDate.getDate() + 6);
        break;
      case 'lastMonth':
        // Start of last month
        fromDate = new Date(today.getFullYear(), today.getMonth() - 1, 1);
        // End of last month
        toDate = new Date(today.getFullYear(), today.getMonth(), 0);
        break;
      case 'lastQuarter':
        // Start of last quarter
        const lastQuarter = Math.floor(today.getMonth() / 3) - 1;
        const year = lastQuarter < 0 ? today.getFullYear() - 1 : today.getFullYear();
        const quarter = lastQuarter < 0 ? 3 : lastQuarter;
        fromDate = new Date(year, quarter * 3, 1);
        // End of last quarter
        toDate = new Date(year, (quarter + 1) * 3, 0);
        break;
      case 'custom':
        this.setState({
          bean: {
            label: 'Custom',
            fromValue: bean.fromValue,
            toValue: bean.toValue
          }
        })
      default:
        return;
    }

    const updatedBean = {
      label: label,
      fromValue: util.TimeUtil.javaCompactDateTimeFormat(fromDate),
      toValue: util.TimeUtil.javaCompactDateTimeFormat(toDate)
    };

    if (onModify) onModify(updatedBean, 'label', null, label);
    this.setState({ bean: updatedBean });
  }

  onInputChange = (bean: any, _field: string, _oldVal: any, newVal: any) => {
    const { onModify } = this.props;
    bean[_field] = newVal;
    if (onModify) onModify(bean, _field, _oldVal, newVal);
    this.forceUpdate();
  }

  render(): React.ReactNode {
    const { disabled } = this.props;
    const { bean } = this.state;

    return (
      <bs.Popover className="flex-vbox flex-grow-0" title={('Quick Time Range')} placement="bottom-start" offset={[-400, 0]} closeOnTrigger=".btn" >
        <bs.PopoverToggle laf="secondary" outline className="me-1 p-1" style={{ color: '#6c757d' }} disabled={disabled}>
          <FeatherIcon.Calendar size={14} className="me-1" />
          {bean.label}
          <FeatherIcon.ChevronDown size={14} className="ms-1" />
        </bs.PopoverToggle>
        <bs.PopoverContent>
          <div className='flex-vbox' style={{ width: '500px' }}>

            <div className="flex-hbox gap-1">
              <div className="flex-grow-0 flex-vbox justify-content-center align-items-center px-1" style={{ width: 250 }}>

                <input.BBDateTimeField label="From" bean={bean} field={'fromValue'}
                  timeFormat={false} onInputChange={this.onInputChange} />
                <input.BBDateTimeField label="To" bean={bean} field={'toValue'}
                  timeFormat={false} onInputChange={this.onInputChange} />

              </div>

              <div className="flex-grow-1 flex-vbox align-items-center p-1">
                {/* Thêm nút Today */}
                <div className="btn-group w-100 mb-2">
                  <button className="btn btn-sm btn-outline-primary w-100 p-1" onClick={() => this.onSelectTimeRange('today', 'Today')}>
                    <FeatherIcon.Calendar size={12} className="me-1" /> Today
                  </button>
                </div>

                <div className="btn-group w-100">
                  <button className="btn btn-sm btn-outline-secondary p-1 flex-grow-1" onClick={() => this.onSelectTimeRange('week', 'This Week')}>This Week</button>
                  <button className="btn btn-sm btn-outline-secondary p-1 flex-grow-1" onClick={() => this.onSelectTimeRange('month', 'This Month')}>This Month</button>
                  <button className="btn btn-sm btn-outline-secondary p-1 flex-grow-1" onClick={() => this.onSelectTimeRange('quarter', 'This Quarter')}>This Quarter</button>
                </div>
                <div className="btn-group w-100 mt-2">
                  <button className="btn btn-sm btn-outline-secondary p-1 flex-grow-1" onClick={() => this.onSelectTimeRange('lastWeek', 'Last Week')}>Last Week</button>
                  <button className="btn btn-sm btn-outline-secondary p-1 flex-grow-1" onClick={() => this.onSelectTimeRange('lastMonth', 'Last Month')}>Last Month</button>
                  <button className="btn btn-sm btn-outline-secondary p-1 flex-grow-1" onClick={() => this.onSelectTimeRange('lastQuarter', 'Last Quarter')}>Last Quarter</button>
                </div>
              </div>
            </div>

            <div className="flex-hbox flex-grow-0 justify-content-between align-items-center mt-2 py-1 border-top">
              <div className="flex-grow-1 ps-2">
                <button className="btn btn-sm btn-link p-0" onClick={() => this.onSelectTimeRange('ytd', 'Year to Date')}>Year to Date</button>
              </div>
              <bs.Button laf='info' outline className="px-2 py-1 mx-1" style={{ width: 100 }}
                onClick={() => this.onSelectTimeRange('custom', 'Custom')}>
                <FeatherIcon.Check size={12} /> OK
              </bs.Button>
            </div>
          </div>
        </bs.PopoverContent>
      </bs.Popover>
    )

  }

}

export interface WCompanySelectorProps extends app.AppComponentProps {
  onModify?: entity.EntityOnModify;
  initCompany?: { code: string, label: string };
}

interface WCompanySelectorState {
  company: { code: string, label: string };
}
export class WCompanySelector extends app.AppComponent<WCompanySelectorProps, WCompanySelectorState> {

  constructor(props: WCompanySelectorProps) {
    super(props);
    const { initCompany } = this.props;
    if (initCompany) {
      this.state = { company: initCompany }
    } else {
      let companyContext = SESSION.getCurrentCompanyContext();
      this.state = {
        company: {
          code: companyContext['companyCode'],
          label: companyContext['companyLabel']
        }
      }
    }
  }

  onInputChange = (companyCode: string, _companyLabel: string) => {
    const { onModify } = this.props;
    const company: any = {
      code: companyCode,
      label: _companyLabel
    }
    this.setState({ company: company })
    if (onModify) onModify(company, 'companyCode', null, companyCode);
  }

  render(): React.ReactNode {
    const { company } = this.state;

    let allowAccessCompanies: Record<string, any> = SESSION.getAccountAcl().getAllowAccessCompanies();

    const companyLabels: any[] = [];
    const companyCodes: string[] = [];
    let companyButtons: any[] = [];
    for (let companyCode in allowAccessCompanies) {
      let ctx = allowAccessCompanies[companyCode];
      companyCodes.push(ctx.companyCode);
      companyLabels.push(ctx.companyLabel);
      companyButtons.push(
        <bs.Button laf="secondary" outline size="sm" className="me-1 py-2 w-100 text-start"
          onClick={() => this.onInputChange(ctx.companyCode, ctx.companyLabel)}>
          <FeatherIcon.Twitter size={14} className="me-1" />
          {ctx.companyLabel}
        </bs.Button>
      )
    }

    return (
      <bs.Popover className="flex-vbox flex-grow-0" placement="bottom-start" closeOnTrigger=".btn" >
        <bs.PopoverToggle laf="secondary" outline className="me-1 p-1" style={{ color: '#6c757d' }}>
          <FeatherIcon.Twitter size={14} className="me-1" />
          {company.label}
          <FeatherIcon.ChevronDown size={14} className="ms-1" />
        </bs.PopoverToggle>
        <bs.PopoverContent>
          <div className='flex-vbox align-items-center gap-1' style={{ width: '180px' }}>
            {companyButtons}
          </div>
        </bs.PopoverContent>
      </bs.Popover>
    )
  }
}

export interface WKeyAccountReportTypeProps extends app.AppComponentProps {
  onModify?: entity.EntityOnModify;
  reportType?: 'BD' | 'SALES';
}

interface WKeyAccountReportTypeState {
  reportType: 'BD' | 'SALES';
}
export class WKeyAccountReportTypeSelector extends app.AppComponent<WKeyAccountReportTypeProps, WKeyAccountReportTypeState> {

  constructor(props: WKeyAccountReportTypeProps) {
    super(props);
    const { reportType } = this.props;
    if (reportType) {
      this.state = { reportType }
    } else {
      this.state = {
        reportType: 'SALES'
      }
    }
  }

  onInputChange = (type?: 'BD' | 'SALES') => {
    const { onModify } = this.props;
    this.setState({ reportType: type || 'SALES' })
    if (onModify) onModify(type, 'reportType', null, type || 'SALES');
  }

  render(): React.ReactNode {
    return (
      <bs.Popover className="flex-vbox flex-grow-0" placement="bottom-start" closeOnTrigger=".btn" >
        <bs.PopoverToggle laf="secondary" outline className="me-1 p-1" style={{ color: '#6c757d' }}>
          <div className="flex-hbox align-items-center justify-content-between px-1">
            <FeatherIcon.Plus size={14} className="me-1" />
            {`Make Report`}
            {/* <FeatherIcon.ChevronDown size={14} className="ms-1" /> */}
          </div>
        </bs.PopoverToggle>
        <bs.PopoverContent>
          <div className='flex-vbox align-items-center gap-1' style={{ width: '150px' }}>
            <bs.Button laf="secondary" outline size="sm" className="me-1 py-2 w-100 text-start"
              onClick={() => this.onInputChange('SALES')}>
              <FeatherIcon.Users size={14} className="me-1" />
              {`SALES`}
            </bs.Button>
            <bs.Button laf="secondary" outline size="sm" className="me-1 py-2 w-100 text-start"
              onClick={() => this.onInputChange('BD')}>
              <FeatherIcon.Twitter size={14} className="me-1" />
              {`BD`}
            </bs.Button>
          </div>
        </bs.PopoverContent>
      </bs.Popover>
    )
  }
}

export interface WMultiTypeOfServiceSelectorProps extends app.AppComponentProps {
  onModify?: entity.EntityOnModify;
  selectedServices?: Array<{ label: string, purpose?: ImportExportPurpose, mode?: TransportationMode }>;
}

interface WInquiryStatusSelectorState {
  selectedStatus: Array<{ label: string, purpose?: ImportExportPurpose, mode?: TransportationMode }>;
}

export class WMultiTypeOfServiceSelector extends app.AppComponent<WMultiTypeOfServiceSelectorProps, WInquiryStatusSelectorState> {

  constructor(props: WMultiTypeOfServiceSelectorProps) {
    super(props);
    const { selectedServices } = this.props;
    if (selectedServices && selectedServices.length > 0) {
      this.state = { selectedStatus: selectedServices }
    } else {
      this.state = {
        selectedStatus: []
      }
    }
  }

  isServiceSelected = (purpose?: ImportExportPurpose, mode?: TransportationMode): boolean => {
    const { selectedStatus: selectedServices } = this.state;
    return selectedServices.some(service =>
      service.purpose === purpose && service.mode === mode
    );
  }

  toggleService = (purpose?: ImportExportPurpose, mode?: TransportationMode) => {
    const { onModify } = this.props;
    const { selectedStatus: selectedServices } = this.state;

    const serviceLabel = (purpose && mode) ? mapToTypeOfShipment(purpose, mode) : '';
    const serviceExists = this.isServiceSelected(purpose, mode);

    let updatedServices;

    if (serviceExists) {
      updatedServices = selectedServices.filter(service =>
        !(service.purpose === purpose && service.mode === mode)
      );
    } else {
      const newService = {
        purpose,
        mode,
        label: serviceLabel
      };
      updatedServices = [...selectedServices, newService];
    }

    this.setState({ selectedStatus: updatedServices });

    if (onModify) onModify(updatedServices, 'selectedServices', null, updatedServices);
  }

  clearAll = () => {
    const { onModify } = this.props;
    const updatedServices: any[] = [];

    this.setState({ selectedStatus: updatedServices });

    if (onModify) onModify(updatedServices, 'selectedServices', null, updatedServices);
  }

  getDisplayLabel = (): string => {
    const { selectedStatus: selectedServices } = this.state;

    if (selectedServices.length === 0) {
      return 'Type Of Service';
    } else if (selectedServices.length === 1) {
      return selectedServices[0].label;
    } else {
      return `${selectedServices.length} services selected`;
    }
  }

  render(): React.ReactNode {
    const { selectedStatus: selectedServices } = this.state;
    const displayLabel = this.getDisplayLabel();

    return (
      <bs.Popover className="flex-vbox flex-grow-0" placement="bottom-start" closeOnTrigger=".btn-close" >
        <bs.PopoverToggle laf="secondary" outline className="me-1 p-1" style={{ color: '#6c757d' }}>
          <FeatherIcon.Layers size={14} className="me-1" />
          {displayLabel}
          <FeatherIcon.ChevronDown size={14} className="ms-1" />
        </bs.PopoverToggle>
        <bs.PopoverContent>
          <div className="flex-vbox" style={{ width: '220px' }}>
            <div className="flex-hbox justify-content-between align-items-center mb-2 pb-2 border-bottom">
              <span className="fw-bold">Services</span>
              <button className="btn-close" onClick={this.clearAll} aria-label="Clear all"></button>
            </div>

            <div className='flex-vbox align-items-start gap-1' style={{ maxHeight: '300px', overflowY: 'auto' }}>
              {/* Air Import */}
              <div className="form-check">
                <input
                  className="form-check-input"
                  type="checkbox"
                  id="airImport"
                  checked={this.isServiceSelected(ImportExportPurpose.IMPORT, TransportationMode.AIR)}
                  onChange={() => this.toggleService(ImportExportPurpose.IMPORT, TransportationMode.AIR)}
                />
                <label className="form-check-label" htmlFor="airImport">
                  Air Import
                </label>
              </div>

              {/* Sea FCL Import */}
              <div className="form-check">
                <input
                  className="form-check-input"
                  type="checkbox"
                  id="seaFclImport"
                  checked={this.isServiceSelected(ImportExportPurpose.IMPORT, TransportationMode.SEA_FCL)}
                  onChange={() => this.toggleService(ImportExportPurpose.IMPORT, TransportationMode.SEA_FCL)}
                />
                <label className="form-check-label" htmlFor="seaFclImport">
                  Sea FCL Import
                </label>
              </div>

              {/* Sea LCL Import */}
              <div className="form-check">
                <input
                  className="form-check-input"
                  type="checkbox"
                  id="seaLclImport"
                  checked={this.isServiceSelected(ImportExportPurpose.IMPORT, TransportationMode.SEA_LCL)}
                  onChange={() => this.toggleService(ImportExportPurpose.IMPORT, TransportationMode.SEA_LCL)}
                />
                <label className="form-check-label" htmlFor="seaLclImport">
                  Sea LCL Import
                </label>
              </div>

              {/* Air Export */}
              <div className="form-check">
                <input
                  className="form-check-input"
                  type="checkbox"
                  id="airExport"
                  checked={this.isServiceSelected(ImportExportPurpose.EXPORT, TransportationMode.AIR)}
                  onChange={() => this.toggleService(ImportExportPurpose.EXPORT, TransportationMode.AIR)}
                />
                <label className="form-check-label" htmlFor="airExport">
                  Air Export
                </label>
              </div>

              {/* Sea FCL Export */}
              <div className="form-check">
                <input
                  className="form-check-input"
                  type="checkbox"
                  id="seaFclExport"
                  checked={this.isServiceSelected(ImportExportPurpose.EXPORT, TransportationMode.SEA_FCL)}
                  onChange={() => this.toggleService(ImportExportPurpose.EXPORT, TransportationMode.SEA_FCL)}
                />
                <label className="form-check-label" htmlFor="seaFclExport">
                  Sea FCL Export
                </label>
              </div>

              {/* Sea LCL Export */}
              <div className="form-check">
                <input
                  className="form-check-input"
                  type="checkbox"
                  id="seaLclExport"
                  checked={this.isServiceSelected(ImportExportPurpose.EXPORT, TransportationMode.SEA_LCL)}
                  onChange={() => this.toggleService(ImportExportPurpose.EXPORT, TransportationMode.SEA_LCL)}
                />
                <label className="form-check-label" htmlFor="seaLclExport">
                  Sea LCL Export
                </label>
              </div>
            </div>

            {selectedServices.length > 0 && (
              <div className="flex-hbox justify-content-between align-items-center mt-2 pt-2 border-top">
                <span className="small text-muted">{selectedServices.length} services selected</span>
                <bs.Button laf='primary' size="sm" className="py-1"
                  onClick={() => { }}>
                  Apply
                </bs.Button>
              </div>
            )}
          </div>
        </bs.PopoverContent>
      </bs.Popover>
    )
  }
}


export interface WInquiryStatusSelectorProps extends app.AppComponentProps {
  onModify?: entity.EntityOnModify;
  selectedStatus?: Array<{ label: string, purpose?: ImportExportPurpose, mode?: TransportationMode }>;
}

interface WInquiryStatusSelectorState {
  selectedStatus: Array<{ label: string, purpose?: ImportExportPurpose, mode?: TransportationMode }>;
}

export class WInquiryStatusSelector extends app.AppComponent<WMultiTypeOfServiceSelectorProps, WInquiryStatusSelectorState> {

  constructor(props: WMultiTypeOfServiceSelectorProps) {
    super(props);
    const { selectedServices } = this.props;
    if (selectedServices && selectedServices.length > 0) {
      this.state = { selectedStatus: selectedServices }
    } else {
      this.state = {
        selectedStatus: []
      }
    }
  }

  isServiceSelected = (purpose?: ImportExportPurpose, mode?: TransportationMode): boolean => {
    const { selectedStatus: selectedServices } = this.state;
    return selectedServices.some(service =>
      service.purpose === purpose && service.mode === mode
    );
  }

  toggleService = (purpose?: ImportExportPurpose, mode?: TransportationMode) => {
    const { onModify } = this.props;
    const { selectedStatus: selectedServices } = this.state;

    const serviceLabel = (purpose && mode) ? mapToTypeOfShipment(purpose, mode) : '';
    const serviceExists = this.isServiceSelected(purpose, mode);

    let updatedServices;

    if (serviceExists) {
      updatedServices = selectedServices.filter(service =>
        !(service.purpose === purpose && service.mode === mode)
      );
    } else {
      const newService = {
        purpose,
        mode,
        label: serviceLabel
      };
      updatedServices = [...selectedServices, newService];
    }

    this.setState({ selectedStatus: updatedServices });

    if (onModify) onModify(updatedServices, 'selectedServices', null, updatedServices);
  }

  clearAll = () => {
    const { onModify } = this.props;
    const updatedServices: any[] = [];

    this.setState({ selectedStatus: updatedServices });

    if (onModify) onModify(updatedServices, 'selectedServices', null, updatedServices);
  }

  getDisplayLabel = (): string => {
    const { selectedStatus: selectedServices } = this.state;

    if (selectedServices.length === 0) {
      return 'Type Of Service';
    } else if (selectedServices.length === 1) {
      return selectedServices[0].label;
    } else {
      return `${selectedServices.length} services selected`;
    }
  }

  render(): React.ReactNode {
    const { selectedStatus: selectedServices } = this.state;
    const displayLabel = this.getDisplayLabel();

    let currentStatus = PricingRequestStatusUtils.getStatusInfo(displayLabel);
    let StatusIcon = currentStatus.icon;
    let label = currentStatus.label;
    let color = currentStatus.color;

    const statusList = PricingRequestStatusUtils.getPricingRequestStatusList();
    const statusRemaining = statusList.filter(status =>
      status.value !== displayLabel &&
      status.value !== 'IN_PROGRESS' &&
      status.value !== 'DONE'
    );


    return (
      <bs.Popover className="flex-vbox flex-grow-0" placement="bottom-start" closeOnTrigger=".btn-close" >
        <bs.PopoverToggle laf="secondary" outline className="me-1 p-1" style={{ color: '#6c757d' }}>
          <FeatherIcon.Layers size={14} className="me-1" />
          {displayLabel}
          <FeatherIcon.ChevronDown size={14} className="ms-1" />
        </bs.PopoverToggle>
        <bs.PopoverContent>
          <div className="flex-vbox gap-2" style={{ width: '220px' }}>
            <div className="flex-hbox justify-content-between align-items-center mb-2 pb-2 border-bottom">
              <span className="fw-bold">Status</span>
              <button className="btn-close" onClick={this.clearAll} aria-label="Clear all"></button>
            </div>

            <div className='flex-vbox align-items-start gap-1' style={{ maxHeight: '300px', overflowY: 'auto' }}>

              {statusRemaining.map(opt => {
                let OptIcon = opt.icon;
                return (
                  <div className="form-check">
                    <input
                      className="form-check-input"
                      type="checkbox"
                      id="airImport"
                      checked={this.isServiceSelected(ImportExportPurpose.IMPORT, TransportationMode.AIR)}
                      onChange={() => this.toggleService(ImportExportPurpose.IMPORT, TransportationMode.AIR)}
                    />
                    <OptIcon size={14} className="me-1" />
                    <label className="form-check-label" htmlFor={opt.label}>
                      {opt.label}
                    </label>
                  </div>
                );
              })}
            </div>

            {selectedServices.length > 0 && (
              <div className="flex-hbox justify-content-between align-items-center mt-2 pt-2 border-top">
                <span className="small text-muted">{selectedServices.length} services selected</span>
                <bs.Button laf='primary' size="sm" className="py-1"
                  onClick={() => { }}>
                  Apply
                </bs.Button>
              </div>
            )}
          </div>
        </bs.PopoverContent>
      </bs.Popover>
    )
  }
}


export interface WTaskGroupBySelectorProps extends app.AppComponentProps {
  onModify?: entity.EntityOnModify;
  groupBy?: { label: string, value: string };
}

interface WTaskGroupBySelectorState {
  groupBy: { label: string, value: string };
}
export class WTaskGroupBySelector extends app.AppComponent<WTaskGroupBySelectorProps, WTaskGroupBySelectorState> {

  constructor(props: WTaskGroupBySelectorProps) {
    super(props);
    const { groupBy } = this.props;
    if (groupBy) {
      this.state = { groupBy: groupBy }
    } else {
      this.state = {
        groupBy: {
          label: 'Saleman',
          value: 'Saleman'
        }
      }
    }
  }

  onInputChange = (label: string, value: string) => {
    const { onModify } = this.props;
    const groupBy: any = { label, value }
    this.setState({ groupBy: groupBy })
    if (onModify) onModify(groupBy, 'groupBy', null, label);
  }

  render(): React.ReactNode {
    const { groupBy } = this.state;

    return (
      <bs.Popover className="flex-vbox flex-grow-0" placement="bottom-start" closeOnTrigger=".btn" offset={[0, 5]}>
        <bs.PopoverToggle laf="secondary" outline className="me-1 p-1" style={{ color: '#6c757d' }}>
          <FeatherIcon.Layers size={14} className="me-1" />
          {groupBy.label}
          <FeatherIcon.ChevronDown size={14} className="ms-1" />
        </bs.PopoverToggle>
        <bs.PopoverContent>
          <div className='flex-vbox align-items-center gap-1' style={{ width: '180px' }}>

            <bs.Button laf="secondary" outline size="sm" className="me-1 py-2 w-100 text-start"
              onClick={() => this.onInputChange('Group By', 'NONE')}>
              <FeatherIcon.Slash size={14} className="me-1" />
              {`Group By (None)`}
            </bs.Button>

            <bs.Button laf="secondary" outline size="sm" className="me-1 py-2 w-100 text-start"
              onClick={() => this.onInputChange('Team', 'TEAM')}>
              <FeatherIcon.Users size={14} className="me-1" />
              {`Team`}
            </bs.Button>

            <bs.Button laf="secondary" outline size="sm" className="me-1 py-2 w-100 text-start"
              onClick={() => this.onInputChange('Saleman', 'SALEMAN')}>
              <FeatherIcon.User size={14} className="me-1" />
              {`Saleman`}
            </bs.Button>

          </div>
        </bs.PopoverContent>
      </bs.Popover>
    )
  }
}

export type PartnerReportParams = {
  searchPattern: string;
  country: string;
  transactionId: string;
  continent: string;
  source: string;
  fromReportDate: string;
  toReportDate: string;
}

export interface WPartnerReportFilterProps extends app.AppComponentProps {
  onModify?: entity.EntityOnModify;
  params?: PartnerReportParams
}

interface WPartnerReportFilterState {
  params: PartnerReportParams
}
export class WPartnerReportFilter extends app.AppComponent<WPartnerReportFilterProps, WPartnerReportFilterState> {

  constructor(props: WTaskGroupBySelectorProps) {
    super(props);
    const { params } = this.props;
    if (params) {
      this.state = { params: params }
    } else {
      this.state = {
        params: {
          searchPattern: '',
          country: '',
          transactionId: '',
          continent: '',
          source: '',
          fromReportDate: '',
          toReportDate: ''
        }
      }
    }
  }

  onInputChange = (bean: any, field: string, oldVal: any, newVal: any) => {
    const { onModify } = this.props;
    this.setState({ params: bean })
    if (onModify) onModify(bean, field, oldVal, newVal);
  }

  render(): React.ReactNode {
    const { params } = this.state;

    return (
      <bs.Popover className="flex-vbox flex-grow-0" placement="bottom-start" closeOnTrigger=".btn" offset={[-300, 5]}>
        <bs.PopoverToggle laf="secondary" outline className="me-1 p-1" style={{ color: '#6c757d' }}>
          <FeatherIcon.Layers size={14} className="me-1" />
          {'Report Filter'}
          <FeatherIcon.ChevronDown size={14} className="ms-1" />
        </bs.PopoverToggle>
        <bs.PopoverContent>
          <div className='flex-vbox p-1' style={{ width: '500px', minHeight: '400px' }}>
            <div className="flex-vbox">
              <bs.Row>
                <bs.Col span={12}>
                  <input.BBStringField
                    bean={params} field='searchPattern' label={("Partner Name Or Code")} onInputChange={this.onInputChange} />
                </bs.Col>
              </bs.Row>

              <bs.Row>
                <bs.Col span={6}>
                  <input.BBStringField
                    bean={params} field='country' label={("Country")} onInputChange={this.onInputChange} />
                </bs.Col>
                <bs.Col span={6}>
                  <input.BBStringField
                    bean={params} field='continent' label={("Continent")} onInputChange={this.onInputChange} />
                </bs.Col>
              </bs.Row>

              <bs.Row>
                <bs.Col span={12}>
                  <input.BBSelectField bean={params} field="source" label={'Source'} options={SOURCE_OPTIONS} onInputChange={this.onInputChange} />
                </bs.Col>
              </bs.Row>

              <bs.Row className="py-1">
                <bs.Col span={6}>
                  <input.BBDateTimeField label="From" bean={params} field={'fromReportDate'}
                    timeFormat={false} onInputChange={this.onInputChange} />
                </bs.Col>
                <bs.Col span={6}>
                  <input.BBDateTimeField label="To" bean={params} field={'toReportDate'}
                    timeFormat={false} onInputChange={this.onInputChange} />
                </bs.Col>
              </bs.Row>

            </div>
            <div className="flex-hbox flex-grow-0 justify-content-end align-items-center mt-2 py-1 border-top">
              <bs.Button laf='info' outline className="px-2 py-1 mx-1" style={{ width: 100 }}
                onClick={() => console.log('TODO')}>
                <FeatherIcon.Check size={12} /> Filter
              </bs.Button>
            </div>
          </div>
        </bs.PopoverContent>
      </bs.Popover>
    )
  }
}
