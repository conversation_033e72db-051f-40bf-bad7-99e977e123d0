import { GroupType } from "../price";

export type PriceLevel = {
  type: 'fcl' | 'truck'
  level: string
}

export class ContainerType {
  name: string;
  abb: string;
  label: string;
  active: boolean = false;
  priceSelector: PriceLevel[];

  constructor(name: string, label: string, selectors?: PriceLevel[]) {
    this.name = name;
    this.label = label;
    this.abb = label;
    this.priceSelector = selectors ? selectors : []
  }

  withAbb(abb: string): ContainerType {
    this.abb = abb;
    return this;
  }

  withActive(active: boolean = true): ContainerType {
    this.active = active;
    return this;
  }

  toFCLPriceLevel(): string | undefined {
    return this.priceSelector.findLast((sel: PriceLevel) => sel.type === 'fcl')?.level
  }

  toTruckPriceLevel(): string | undefined {
    return this.priceSelector.findLast((sel: PriceLevel) => sel.type === 'truck')?.level
  }
}

export class ContainerTypeUnit {
  private static createContainerType(name: string, label: string, priceLevel: PriceLevel): ContainerType {
    return new ContainerType(name, label, [priceLevel]);
  }

  static readonly _20DC = ContainerTypeUnit.createContainerType('20DC', '20´DC', { type: 'fcl', level: 'dry20Price' });
  static readonly _40DC = ContainerTypeUnit.createContainerType('40DC', '40´DC', { type: 'fcl', level: 'dry40Price' });
  static readonly _45DC = ContainerTypeUnit.createContainerType('45DC', '45´DC', { type: 'fcl', level: 'dry45Price' });
  static readonly _40HC = ContainerTypeUnit.createContainerType('40HC', '40´HC', { type: 'fcl', level: 'highCube40Price' });
  static readonly _45HC = ContainerTypeUnit.createContainerType('45HC', '45´HC', { type: 'fcl', level: 'highCube45Price' });
  static readonly _40NOR = ContainerTypeUnit.createContainerType('40NOR', '40´NOR', { type: 'fcl', level: 'nor40Price' });
  static readonly _20RF = ContainerTypeUnit.createContainerType('20RF', '20´RF', { type: 'fcl', level: 'reefer20Price' });
  static readonly _40RF = ContainerTypeUnit.createContainerType('40RF', '40´RF', { type: 'fcl', level: 'reefer40Price' });
  static readonly _20ISO = ContainerTypeUnit.createContainerType('20ISO', "20´ ISO TANK", { type: 'fcl', level: 'isoTank20Price' });
  static readonly _20OT = ContainerTypeUnit.createContainerType('20OT', "20´OT", { type: 'fcl', level: 'openTop20Price' }).withAbb("20'OT");
  static readonly _40OT = ContainerTypeUnit.createContainerType('40OT', "40´OT", { type: 'fcl', level: 'openTop40Price' }).withAbb("40'OT");
  static readonly _20FE = ContainerTypeUnit.createContainerType('20FR', "20´FR", { type: 'fcl', level: 'flatRack20Price' }).withAbb("20'FR");
  static readonly _40FE = ContainerTypeUnit.createContainerType('40FR', "40´FR", { type: 'fcl', level: 'flatRack40Price' }).withAbb("40'FR");

  static DEFAULT(): ContainerType[] {
    return [this._20DC, this._40DC, this._40HC, this._20RF, this._40RF];
  }

  static FCL_REQUEST(): ContainerType[] {
    return [
      this._20DC.withActive(),
      this._40DC.withActive(),
      this._40HC.withActive(),
      this._45HC,
      this._20RF,
      this._40RF
    ];
  }

  static FCL_NONE_US(): ContainerType[] {
    return [
      this._20DC,
      this._40DC,
      this._40HC,
    ];
  }

  static FCL_US_ROUTE(): ContainerType[] {
    return [
      this._20DC,
      this._40DC,
      this._40HC,
      this._45HC,
      this._40NOR
    ];
  }

  static FCL_REEFER(): ContainerType[] {
    return [
      this._20RF,
      this._40RF,
    ];
  }


  static FCL_GROUP: Record<GroupType, ContainerType[]> = {
    NONE_US: ContainerTypeUnit.FCL_NONE_US(),
    US_ROUTE: ContainerTypeUnit.FCL_US_ROUTE(),
    SPECIAL: ContainerTypeUnit.FCL_REEFER(),
  }

  static ALL(): ContainerType[] {
    return [
      this._20DC.withActive(),
      this._40DC.withActive(),
      this._45DC,
      this._40HC.withActive(),
      this._45HC,
      this._20RF,
      this._40RF,
      this._20ISO,
      this._20OT,
      this._40OT,
      this._20FE,
      this._40FE,
    ];
  }

  static match(container: string): ContainerType | undefined {
    let pattern = container ? container.trim() : '';
    return this.ALL().find(sel => sel.name === pattern || sel.label === pattern);
  }

  static fromFCLPriceLevel(priceLevel: string): ContainerType | undefined {
    const allContainers = this.ALL();

    // Tìm container phù hợp với priceLevel
    return allContainers.find(container => {
      const priceLevels = container.priceSelector || [];
      return priceLevels.some(level =>
        level.type === 'fcl' && level.level === priceLevel
      );
    });
  }

  static getDisplayNameFromFCLLevel(priceLevel: string): string {
    const container = this.fromFCLPriceLevel(priceLevel);
    if (container) {
      return container.label || container.name;
    }

    // Nếu không tìm thấy, trả về tên được định dạng từ priceLevel
    return priceLevel
      .replace(/([A-Z])/g, ' $1') // Thêm khoảng trắng trước chữ hoa
      .replace(/Price$/, '')      // Xóa "Price" ở cuối
      .trim();                    // Xóa khoảng trắng thừa
  }

  static computeFromContainer(containers: any[]): ContainerType[] {
    if (!containers || containers.length === 0) return [];

    return containers.reduce((result: ContainerType[], sel: any) => {
      const type: string = (sel['containerType'] || '').trim();
      const typeUnit: ContainerType | undefined = this.ALL().find(sel => sel.name === type || sel.label === type);
      if (typeUnit) {
        result.push(typeUnit);
      }
      return result;
    }, []);
  }

  static toContainerString(types: ContainerType[]): string {
    if (!types || types.length === 0) return '';
    return types.map(type => `1x${type.name}`).join(' / ');
  }

  static calculateTUE(volumeInfo: string) {
    // Nếu chuỗi chứa dấu /, tính TEU lớn nhất
    if (volumeInfo.includes('/')) {
      const entries = volumeInfo.split('/');
      let maxTUE = 0;

      entries.forEach(entry => {
        let entryTUE = 0;
        const match = entry.trim().match(/(\d+)[*x](20|40|45)[A-Za-z]{2,3}/);
        if (match) {
          const size = match[2]; // 20, 40, or 45
          const quantity = parseInt(match[1], 10);
          if (size === '20') {
            entryTUE = quantity * 1; // 1 TEU for 20
          } else if (size === '40' || size === '45') {
            entryTUE = quantity * 2; // 2 TEU for 40 or 45
          }
        }
        maxTUE = Math.max(maxTUE, entryTUE);
      });
      return maxTUE;
    }

    // Nếu chuỗi chứa dấu +, tính tổng TEU
    if (volumeInfo.includes('+')) {
      const entries = volumeInfo.split('+');
      let totalTUE = 0;

      entries.forEach(entry => {
        const match = entry.trim().match(/(\d+)[*x](20|40|45)[A-Za-z]{2,3}/);
        if (match) {
          const size = match[2]; // 20, 40, or 45
          const quantity = parseInt(match[1], 10);
          if (size === '20') {
            totalTUE += quantity * 1; // 1 TEU for 20
          } else if (size === '40' || size === '45') {
            totalTUE += quantity * 2; // 2 TEU for 40 or 45
          }
        }
      });
      return totalTUE;
    }

    // Trường hợp chỉ có một container (không có separator)
    const match = volumeInfo.trim().match(/(\d+)[*x](20|40|45)[A-Za-z]{2,3}/);
    if (match) {
      const size = match[2]; // 20, 40, or 45
      const quantity = parseInt(match[1], 10);
      if (size === '20') {
        return quantity * 1; // 1 TEU for 20
      } else if (size === '40' || size === '45') {
        return quantity * 2; // 2 TEU for 40 or 45
      }
    }

    return 0;
  }

  static textToContainerList(volumeInfo: string) {
    const containers: Array<{ containerType: string, quantity: number }> = [];
    const entries = volumeInfo.split('/');
    entries.forEach(entry => {
      const match = entry.trim().match(/(\d+)[*x](20|40|45)([A-Za-z]{2,3})/);
      if (match) {
        const [_, quantity, size, type] = match;
        containers.push({
          containerType: `${size}${type}`,
          quantity: parseInt(quantity, 10),
        });
      }
    });
    return containers;
  }

  static parseContainerString(value: string): { containerType: string, quantity: number }[] {
    if (!value) return [];

    const containers: { containerType: string, quantity: number }[] = [];
    // Hỗ trợ cả dấu "+" và "/"
    const parts = value.split(/[\/\+]/).map(p => p.trim()).filter(p => p);

    for (const part of parts) {
      const segments = part.split('x');
      if (segments.length === 2) {
        const [qtyStr, type] = segments.map(p => p.trim());
        const qty = parseInt(qtyStr);
        if (!isNaN(qty) && type) {
          containers.push({
            containerType: type.toUpperCase(),
            quantity: qty
          });
        }
      }
    }

    return containers;
  }

  static containerListToVolumeInfo(containers: Array<{ containerType: string, quantity: number }>) {
    if (!containers || containers.length === 0) return '';
    return containers
      .map(container => {
        const match = container.containerType.match(/(20|40|45)([A-Za-z]{2,3})/);
        if (match) {
          const [_, size, type] = match;
          return `${container.quantity}x${size}${type}`;
        }
        return '';
      })
      .filter(entry => entry !== '')
      .join(' / ');
  };

}
