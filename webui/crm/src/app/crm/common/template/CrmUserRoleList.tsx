import React from 'react';
import { grid, entity, } from '@datatp-ui/lib';
import { module } from '@datatp-ui/erp';
import { T } from 'app/crm/price';

export class CrmUserRolePlugin extends entity.DbEntityListPlugin {
  constructor() {
    super([]);

    this.backend = {
      service: 'CRMUserRoleService',
      searchMethod: 'searchCrmUserRoles',
      deleteMethod: 'deleteCrmUserByIds'
    }

    this.searchParams = {
      maxReturn: 1000,
    };

  }

  loadData(uiList: entity.DbEntityList<any>): void {
    this.createBackendSearch(uiList, { sqlParams: this.searchParams }).call();
  }
}

export class CrmUserRoleList extends entity.DbEntityList {

  createVGridConfig() {
    const CELL_HEIGHT: number = 40;
    let config: grid.VGridConfig = {
      title: 'CrmUserRoleList',
      record: {
        editor: {
          enable: true,
          supportViewMode: ['table']
        },
        dataCellHeight: CELL_HEIGHT,
        fields: [
          ...entity.DbEntityListConfigTool.FIELD_SELECTOR(this.needSelector()),
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          {
            name: "fullName", label: T('Full Name'), width: 250, cssClass: 'pe-1',
            customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let uiList = ctx.uiRoot as CrmUserRoleList
              const { appContext, pageContext } = uiList.props;
              let val = record[_field.name] || 'N/A';
              return (
                <div className='flex-hbox justify-content-center align-items-center' >
                  <module.account.WAvatars className='px-2'
                    appContext={appContext} pageContext={pageContext} avatarIds={[record['accountId']]}
                    avatarIdType='AccountId' width={20} height={20} borderRadius={10} />
                  <div className="flex-hbox">{val}</div>
                </div>
              )
            }
          },
          { name: 'bfsoneCode', label: T('BFSOne Code'), width: 150 },
          { name: 'bfsoneUsername', label: T('BFSOne Username'), width: 180 },
          { name: 'companyBranchName', label: T('Company'), width: 150 },
          { name: 'departmentLabel', label: T('Department'), width: 150 },
          { name: 'team', label: T('Team'), width: 250 },
        ],
      },
      toolbar: {
        hide: true,
      },

      footer: {
      },

      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table',
          },
        },
      },
    };
    return config;
  }

}
