import React from 'react';
import * as FeatherIcon from 'react-feather';
import { bs, app, util } from '@datatp-ui/lib';
import { module } from '@datatp-ui/erp';
import { CrmUserRoleList, CrmUserRolePlugin } from './CrmUserRoleList';

const { UIEmployeeList, UIEmployeeListPlugin } = module.company.hr;


export class UICrmUserRolesPage extends app.AppComponent {

  selectEmployee = () => {
    let { pageContext } = this.props;
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {

      const fetchData = () => {
        console.log("Fetching data...");
      };

      return (
        <div className='flex-vbox'>
          <UIEmployeeList appContext={appCtx} pageContext={pageCtx} plugin={new UIEmployeeListPlugin()} />
          <div className='flex-grow-0 flex-hbox justify-content-end align-items-center mt-1 p-1 border-top'>
            <bs.Button laf="primary" size="sm" className="py-1 px-2" onClick={fetchData}>
              <FeatherIcon.RefreshCw size={14} className="me-2" />
              Fetch Data
            </bs.Button>
          </div>
        </div>
      )
    }
    pageContext.createPopupPage(`select-employee-${util.IDTracker.next()}`, 'Select Employees', createAppPage, { size: "xl", backdrop: "static" })
    return;
  }

  renderHeader() {
    const { appContext, pageContext } = this.props;

    return (
      <div className="flex-hbox flex-grow-0 align-items-center justify-content-between px-2 py-1">

        <div className="flex-hbox justify-content-start align-items-center flex-grow-0 gap-2" >
          <h5 style={{ color: '#6c757d' }}>
            <FeatherIcon.Trello className="me-2" size={18} />
            CRM User Roles
          </h5>

          <div className='flex-hbox align-items-center flex-grow-0 border-start' >
          </div>
        </div>

        <div className="flex-hbox justify-content-end align-items-center flex-grow-1 gap-1" >

          <bs.Button laf="secondary" outline size="sm" className="p-1" style={{ color: '#6c757d' }}
            onClick={this.selectEmployee}>
            <FeatherIcon.UserPlus size={14} className="me-2" />
            Select Employees
          </bs.Button>
        </div>
      </div>
    )
  }

  render(): React.JSX.Element {
    const { appContext, pageContext } = this.props;
    return (
      <div className="flex-vbox mx-1 p-1 bg-white rounded-md w-100 h-100 my-1" >
        {this.renderHeader()}
        <div className="flex-vbox border-top bg-body-highlight" key={util.IDTracker.next()}>
          <CrmUserRoleList appContext={appContext} pageContext={pageContext} plugin={new CrmUserRolePlugin()} />
        </div>
      </div>
    )
  }

}

