import React from 'react';
import * as FeatherIcon from 'react-feather';
import { grid, sql, entity, bs, util, app } from '@datatp-ui/lib';

import { T } from '../price';
import { responsiveGridConfig } from '../sales/common';
import {
  UIVolumeSalemanKeyAccountReportList,
} from '../sales/partners/UIVolumeSalemanKeyAccountReportList';

export type Space = 'User' | 'Company' | 'System'

export class UIAgentTransactionsListPlugin extends entity.DbEntityListPlugin {
  space: Space;

  constructor() {
    super([]);
    this.backend = {
      context: 'company',
      service: 'PartnerReportService',
      searchMethod: 'searchAgentTransactionsReport',
      deleteMethod: '',
      changeStorageStateMethod: '',
      entityLabel: 'Agent'
    }

    this.searchParams = {
      "params": {},
      "filters": [...sql.createSearchFilter()],
      rangeFilters: [
        ...sql.createDateTimeFilter("shipmentDate", T("Shipment Date")),
        ...sql.createDateTimeFilter("modifiedDate", T("Modified Date")),
      ],
    }
  }

  loadData(uiList: entity.DbEntityList<any>) {
    this.createBackendSearch(uiList, { param: this.searchParams }).call();
  }

  withParams(name: string, value: string | undefined) {
    this.addSearchParam(name, value);
    return this;
  }

  withSearchPattern(searchPattern: string) {
    const normalizedInput = searchPattern?.toUpperCase().replace(/\s+/g, '');
    if (this.searchParams) {
      const searchFilter = this.searchParams.filters?.find(sel => sel.name === 'search');
      if (searchFilter) {
        searchFilter.filterValue = normalizedInput;
      } else {
        this.searchParams.filters?.push({ name: 'search', fields: [], filterOp: 'ilike', filterValue: normalizedInput, "required": true });
      }
    }
    return this;
  }

  withDateFilter(filterName: 'shipmentDate' | 'modifiedDate', fromValue: string, toValue: string) {
    if (this.searchParams) {
      let rangeFilters = this.searchParams.rangeFilters || [];
      for (let i = 0; i < rangeFilters.length; i++) {
        let filter = rangeFilters[i];
        if (filter.name === filterName) {
          filter.fromValue = fromValue;
          filter.toValue = toValue;
          break;
        }
      }
      this.searchParams.rangeFilters = rangeFilters;
    }
    return this;
  }

}

interface UIAgentTransactionsProps extends entity.DbEntityListProps {
  rawRecords?: any[];
}
export class UIAgentTransactions extends entity.DbEntityList<UIAgentTransactionsProps> {

  createVGridConfig(): grid.VGridConfig {
    let { } = this.props;

    let config: grid.VGridConfig = {
      record: {
        dataCellHeight: 35,
        fields: [
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          {
            name: 'agentName', label: T('Agent Name'), width: 300, filterable: true, container: 'fixed-left',
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
              let record: any = dRec.record;
              let label = record['agentName'] || '';


              let htmlLines: any[] = []
              if (label === 'Total') {
                return (
                  <div className="d-flex align-items-center justify-content-start w-100 fw-bold"
                    onClick={() => this.onDefaultSelect(dRec)}>
                    {'Total'}
                  </div>
                )
              } else {
                htmlLines.push(
                  <div className='flex-vbox border-0'>
                    <div className="tooltip-header">
                      <span className="tooltip-title">{'Agent Name'}:</span>
                    </div>
                    <div className="tooltip-body">
                      {`${record['agentCode']} - ${record['agentName']}`}
                    </div>
                  </div>
                );

                htmlLines.push(
                  <div className='flex-vbox border-0'>
                    <div className="tooltip-header">
                      <span className="tooltip-title">{'Address'}:</span>
                    </div>
                    <div className="tooltip-body">
                      {`${record['address']}`}
                    </div>
                  </div>
                );

                htmlLines.push(
                  <div className='flex-vbox border-0'>
                    <div className="tooltip-header">
                      <span className="tooltip-title">{'Source'}:</span>
                    </div>
                    <div className="tooltip-body">
                      {`${record['source']}`}
                    </div>
                  </div>
                );
              }


              return (
                <bs.CssTooltip width={480} position='bottom-right' offset={{ x: 130, y: -10 }}>
                  <bs.CssTooltipToggle>
                    <div className="d-flex align-items-center justify-content-start w-100"
                      onClick={() => this.onDefaultSelect(dRec)}
                      style={{ cursor: 'pointer', userSelect: 'text' }}>
                      <div className={`d-flex align-items-center px-1 py-1 text-success`}>
                        {util.text.formater.uiTruncate(label, 300, true)}
                      </div>
                    </div>
                  </bs.CssTooltipToggle>
                  <bs.CssTooltipContent>
                    <div className='flex-vbox'>
                      {htmlLines}
                    </div>
                  </bs.CssTooltipContent>
                </bs.CssTooltip>
              );
            }
          },
          {
            name: 'totalJobCountFH', label: T('FH (times)'), width: 100,
            fieldDataGetter(record) {
              return record['totalJobCountFH'] || '-';
            },
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
              let value = dRec.record['totalJobCountFH'] || '-';
              return (
                <div className={`d-flex align-items-center justify-content-end w-100`} onClick={() => this.onToggleShipmentType(dRec, 'FREE-HAND')} >
                  {value}
                </div>
              );
            }
          },
          {
            name: 'totalJobCountNM', label: T('NM (times)'), width: 100,
            fieldDataGetter(record) {
              return record['totalJobCountNM'] || '-';
            },
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRec: grid.DisplayRecord) => {
              let value = dRec.record['totalJobCountNM'] || '-';
              return (
                <div className={`d-flex align-items-center justify-content-end w-100`} onClick={() => this.onToggleShipmentType(dRec, 'NOMINATED')} >
                  {value}
                </div>
              );
            }
          },
          {
            name: 'totalTeuFH', label: T('FCL TEUs(F/H)'), width: 140, sortable: true,
            fieldDataGetter(record) {
              return record['totalTeuFH'] || '-';
            },
          },
          {
            name: 'cont20FH', label: T(`Cont 20'(F/H)`), width: 130, sortable: true,
            fieldDataGetter(record) {
              return record['cont20FH'] || '-';
            },
          },
          {
            name: 'cont40FH', label: T(`Cont 40'(F/H)`), width: 130, sortable: true,
            fieldDataGetter(record) {
              return record['cont40FH'] || '-';
            },
          },
          {
            name: 'cont45FH', label: T(`Cont 45'(F/H)`), width: 130, sortable: true,
            fieldDataGetter(record) {
              return record['cont45FH'] || '-';
            },
          },
          {
            name: 'totalHawbCbmFH', label: T(`CBM(F/H)`), width: 130, sortable: true,
            fieldDataGetter(record) {
              let value = record['totalHawbCbmFH'];
              return value && !isNaN(value) ? util.text.formater.number(value, 2) : '-'
            },
          },
          {
            name: 'totalHawbGwFH', label: T(`KGS(F/H)`), width: 130, sortable: true,
            fieldDataGetter(record) {
              let value = record['totalHawbGwFH'];
              return value && !isNaN(value) ? util.text.formater.number(value, 2) : '-'
            },
          },

          {
            name: 'totalTeuNM', label: T('FCL TEUs(N/M)'), width: 140, sortable: true,
            fieldDataGetter(record) {
              return record['totalTeuNM'] || '-';
            },
          },
          {
            name: 'cont20NM', label: T(`Cont 20'(N/M)`), width: 130, sortable: true,
            fieldDataGetter(record) {
              return record['cont20NM'] || '-';
            },
          },
          {
            name: 'cont40NM', label: T(`Cont 40'(N/M)`), width: 130, sortable: true,
            fieldDataGetter(record) {
              return record['cont40NM'] || '-';
            },
          },
          {
            name: 'cont45NM', label: T(`Cont 45'(N/M)`), width: 130, sortable: true,
            fieldDataGetter(record) {
              return record['cont45NM'] || '-';
            },
          },
          {
            name: 'totalHawbCbmNM', label: T(`CBM(N/M)`), width: 130, sortable: true,
            fieldDataGetter(record) {
              let value = record['totalHawbCbmNM'];
              return value && !isNaN(value) ? util.text.formater.number(value, 2) : '-'
            },
          },
          {
            name: 'totalHawbGwNM', label: T(`KGS(N/M)`), width: 130, sortable: true,
            fieldDataGetter(record) {
              let value = record['totalHawbGwNM'];
              return value && !isNaN(value) ? util.text.formater.number(value, 2) : '-'
            },
          },
          {
            name: 'lastTransactionDate', label: T('Last contact'), width: 120,
            format: util.text.formater.compactDate,
            fieldDataGetter(record: any) {
              let value = record['lastTransactionDate'];
              if (!value || value === '-') {
                return "-";
              }
              return value;
            },
            customRender: (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {
              let record: any = dRecord.record;
              let value = record[field.name];

              if (!value || value === '-') {
                return <div className="flex-hbox justify-content-center align-items-center">-</div>;
              }
              try {
                const formattedDate = util.text.formater.compactDate(value);
                return (
                  <div className="flex-hbox justify-content-center align-items-center">
                    {formattedDate || '-'}
                  </div>
                );
              } catch (error) {
                console.warn('Invalid date format:', value);
                return <div className="flex-hbox justify-content-center align-items-center">-</div>;
              }
            }
          },
          { name: 'lastTransactionID', label: T('Last Job ID'), width: 150 },
          { name: 'source', label: T('Source'), width: 150, filterable: true, filterableType: 'options' },
          { name: 'continent', label: T('Continent'), width: 150, filterable: true, filterableType: 'options' },
          { name: 'countryLabel', label: T('Country'), width: 150, filterable: true, filterableType: 'options' },
        ],
      },
      toolbar: { hide: true },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table',
            header: {
              createRecords: (_ctx: grid.VGridContext) => {
                let records = _ctx.model.getFilterRecords();

                let total = {
                  agentName: 'Total',
                  totalJobCountFH: 0,
                  totalHawbCbmFH: 0,
                  totalHawbGwFH: 0,
                  cont20FH: 0,
                  cont40FH: 0,
                  cont45FH: 0,
                  totalTeuFH: 0,
                  totalJobCountNM: 0,
                  totalHawbCbmNM: 0,
                  totalHawbGwNM: 0,
                  cont20NM: 0,
                  cont40NM: 0,
                  cont45NM: 0,
                  totalTeuNM: 0,
                };

                for (let rec of records) {
                  total.totalJobCountFH += Number(rec.totalJobCountFH) || 0;
                  total.totalHawbCbmFH += Number(rec.totalHawbCbmFH) || 0;
                  total.totalHawbGwFH += Number(rec.totalHawbGwFH) || 0;
                  total.cont20FH += Number(rec.cont20FH) || 0;
                  total.cont40FH += Number(rec.cont40FH) || 0;
                  total.cont45FH += Number(rec.cont45FH) || 0;
                  total.totalTeuFH += Number(rec.totalTeuFH) || 0;

                  total.totalJobCountNM += Number(rec.totalJobCountNM) || 0;
                  total.totalHawbCbmNM += Number(rec.totalHawbCbmNM) || 0;
                  total.totalHawbGwNM += Number(rec.totalHawbGwNM) || 0;
                  total.cont20NM += Number(rec.cont20NM) || 0;
                  total.cont40NM += Number(rec.cont40NM) || 0;
                  total.cont45NM += Number(rec.cont45NM) || 0;
                  total.totalTeuNM += Number(rec.totalTeuNM) || 0;
                }

                return [total];
              }
            }

          }
        },
      },
    };
    return responsiveGridConfig(config);
  }

  onToggleShipmentType(dRecord: grid.DisplayRecord, shipmentType: 'FREE-HAND' | 'NOMINATED') {
    let record: any = dRecord.record;
    const { pageContext, rawRecords } = this.props;
    const agentCode = record['agentCode'];
    const filtered = (rawRecords || []).filter((rec: any) => rec.agentCode === agentCode && rec.shipmentType === shipmentType);
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <UIVolumeSalemanKeyAccountReportList appContext={appCtx} pageContext={pageCtx}
          plugin={new entity.DbEntityListPlugin(filtered)} hideSubtotalColumns={true} />
      );
    }
    pageContext.createPopupPage(`view-all-${util.IDTracker.next()}`, "Shipment Details", createAppPage, { size: 'xl', backdrop: 'static' });
  }

  onDefaultSelect(dRecord: grid.DisplayRecord) {
    const { appContext, pageContext, rawRecords } = this.props;
    let record: any = dRecord.record;
    const agentCode = record['agentCode'];
    const filteredRecords = (rawRecords || []).filter((rec: any) => rec.agentCode === agentCode);

    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <UIVolumeSalemanKeyAccountReportList appContext={appCtx} pageContext={pageCtx}
          plugin={new entity.DbEntityListPlugin(filteredRecords)} hideSubtotalColumns={true} />
      );
    }
    pageContext.createPopupPage(`view-all-${util.IDTracker.next()}`, "Shipment Details", createAppPage, { size: 'xl', backdrop: 'static' });
  }

}