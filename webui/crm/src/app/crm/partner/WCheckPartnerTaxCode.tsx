import React from "react";
import { util, server, grid, bs, entity, app, input } from '@datatp-ui/lib';
import * as FeatherIcon from 'react-feather';
import { buildTooltipValues, T, WRateFinderGridFilter } from "../price";
import { responsiveGridConfig } from "../sales/common";

export class UICheckBFSOnePartnerPlugin extends entity.DbEntityListPlugin {
  searchPattern: string;

  constructor(searchPattern: string) {
    super([]);
    this.searchPattern = searchPattern;

    this.backend = {
      context: 'company',
      service: 'BFSOneCRMService',
      searchMethod: 'checkPartnerExistInSystem',
    }
  }

  loadData(uiList: entity.DbEntityList<any>): void {
    this.createBackendSearch(uiList, { searchPattern: this.searchPattern }).call();
  }
}

export interface UICheckBFSOnePartnerListProps extends entity.DbEntityListProps { }

export class UICheckBFSOnePartnerList extends entity.DbEntityList<UICheckBFSOnePartnerListProps> {

  handleOnChange = (record: any) => {
    const { pageContext } = this.props;
    const createPageContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => (
      <UITransactionInformation appContext={appCtx} pageContext={pageCtx} transactionSummary={record.transactionSummary} />
    );
    pageContext.createPopupPage('transaction-information', T('Transaction Information'), createPageContent, { size: 'md', backdrop: 'static' });
  }

  createVGridConfig(): grid.VGridConfig {
    const { plugin } = this.props;
    const pluginImp: UICheckBFSOnePartnerPlugin = plugin as UICheckBFSOnePartnerPlugin;

    const searchPattern: string = pluginImp.searchPattern || "";

    const renderTooltipAdvanced = (_ctx: grid.VGridContext, field: grid.FieldConfig, dRecord: grid.DisplayRecord) => {
      const record = dRecord.record;
      const val = record[field.name] || 'N/A';

      record.lastTransDate = util.text.formater.compactDate(record.lastTransDate);

      const tooltipFields = [
        { key: 'salemanLabel', label: 'Saleman' },
        { key: 'authorizeUsers', label: 'Authorized Users' },
        { key: 'address', label: 'Address' },
        { key: 'inputPeopleName', label: 'Input People' },
        { key: 'transactionDate', label: 'Last Job Date' },
        { key: 'transactionId', label: 'Last Job ID' },
      ];

      const { htmlFormat, textFormat } = buildTooltipValues(record, tooltipFields)

      const handleClick = () => {
        navigator.clipboard.writeText(textFormat);
        bs.toastShow('Copied to clipboard!', { type: 'success' });
      };

      const highlightText = (text: string) => {
        if (!searchPattern || !text || typeof text !== 'string') return text;
        try {
          const escapedPattern = searchPattern.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
          const regex = new RegExp(`(${escapedPattern})`, 'gi');
          const parts = text.split(regex);

          return (
            <span style={{ display: 'inline', wordWrap: 'normal' }}>
              {parts.map((part, i) => {
                if (part.toLowerCase() === searchPattern.toLowerCase()) {
                  return <span key={i} style={{ backgroundColor: '#fff9c4', display: 'inline' }}>{part}</span>;
                }
                return <span key={i} style={{ display: 'inline' }}>{part}</span>;
              })}
            </span>
          );
        } catch (error) {
          console.error("Regex error:", error);
          return text;
        }
      };

      return (
        <bs.CssTooltip width={350} position='bottom-left' offset={{ x: 20, y: 10 }}>
          <bs.CssTooltipToggle className="w-100 h-100">
            <div className='w-100' onClick={handleClick}
              style={{
                cursor: 'pointer',
                wordWrap: 'break-word',
                wordBreak: 'normal',
                whiteSpace: 'normal',
                maxWidth: '100%',
                lineHeight: '1.2'
              }}>
              {field.format ? field.format(val) : highlightText(val)}
            </div>
          </bs.CssTooltipToggle>
          <bs.CssTooltipContent className="rounded shadow-sm">
            <div className="p-2" style={{ maxHeight: '250px', overflowY: 'auto' }}>
              {htmlFormat}
            </div>
          </bs.CssTooltipContent>
        </bs.CssTooltip>
      );
    }

    let config: grid.VGridConfig = {
      record: {
        dataCellHeight: 90,
        computeDataRowHeight: (ctx: grid.VGridContext, dRecord: grid.DisplayRecord) => {
          const record = dRecord.record;
          const name = record['partnerName'] || '';
          const address = record['address'] || '';

          const fontSize = 0.875 * 16;
          const nameWidth = 250;
          const addressWidth = 300;
          const lineHeight = fontSize * 1.5;

          const nameLines = Math.ceil(name.length * fontSize / (nameWidth * 2));
          const addressLines = Math.ceil(address.length * fontSize / (addressWidth * 2));

          const totalLines = Math.max(nameLines, addressLines);
          const calculatedHeight = totalLines * lineHeight + 10;

          return Math.max(90, calculatedHeight);
        },
        fields: [
          entity.DbEntityListConfigTool.FIELD_INDEX(),
          {
            name: 'partnerId', label: T(`Code`), width: 100, container: 'fixed-left', sortable: true,
            customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let val: string = record[_field.name] || 'N/A';
              let cssCustom: string = 'text-info'
              if (record['partnerType'] === 'LEAD') cssCustom = 'text-warning';
              return (
                <div className={`w-100 text-wrap text-center fw-bold ${cssCustom}`}>
                  {val}
                </div>
              )
            }
          },
          {
            name: 'partnerName', label: T(`Name`), width: 250, container: 'fixed-left', filterable: true, sortable: true,
            customRender: renderTooltipAdvanced
          },
          {
            name: 'dateCreated', label: T(`Created Date`), width: 150, sortable: true, filterable: true, filterableType: 'date',
            format: util.text.formater.compactDate,
          },
          {
            name: "address", label: T('Address'), width: 300,
            customRender: renderTooltipAdvanced
          },
          {
            name: 'salemanLabel', label: T(`Saleman`), width: 200, filterable: true, sortable: true,
            customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let val: string = record[_field.name] || 'N/A';
              return (
                <div className='w-100 text-wrap' style={{ wordBreak: 'break-word' }}>
                  {val}
                </div>
              )
            }
          },
          {
            name: 'authorizeUsers', label: T(`Authorize Users`), width: 300,
            customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let val: string = record[_field.name] || '-';
              return (
                <div className='w-100 text-wrap' style={{ wordBreak: 'break-word' }}>
                  <div className="">{val}</div>
                </div>
              )
            }
          },
          {
            name: 'inputPeopleName', label: T(`Input People`), width: 200, filterable: true, sortable: true,
            customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let val: any = record[_field.name] || 'N/A';
              return (
                <div className='w-100 text-wrap' style={{ wordBreak: 'break-word' }}>
                  {val}
                </div>
              )
            }
          },
          {
            name: 'transactionDate', label: T(`Latest Job`), width: 120, format: util.text.formater.compactDate, filterable: true, filterableType: 'date', sortable: true,
            fieldDataGetter(record) {
              let val: any = record['transactionDate'];
              if (val) return util.text.formater.compactDate(record.transactionDate);
              else return '-'
            },
          },
          {
            name: 'transactionId', label: T(`Transactions`), width: 150, container: 'fixed-right',
            customRender: (ctx: grid.VGridContext, _field: grid.FieldConfig, dRecord: grid.DisplayRecord, _focus: boolean) => {
              let record = dRecord.record;
              let transactionId = record.transactionId;
              if (!transactionId) return <div className="w-100">{'-'}</div>
              let shipmentType = record.shipmentType || '';
              return (
                <div className='flex-vbox justify-content-center align-items-center text-wrap'>
                  <div className="">{transactionId || 'N/A'}</div>
                  {shipmentType && <div className="">{shipmentType}</div>}

                  <div className="flex-vbox flex-grow-0 justify-content-end align-items-center">
                    <bs.Button laf='link' onClick={() => this.handleOnChange(record)}>
                      View more
                    </bs.Button>
                  </div>

                </div>
              );
            }
          },
        ],
      },
      toolbar: {
        hide: true,
      },
      view: {
        currentViewName: 'table',
        availables: {
          table: {
            viewMode: 'table'
          }
        }
      },
    }
    return responsiveGridConfig(config);
  }

  render() {
    if (this.isLoading()) return this.renderLoading();
    if (this.vgridContext.model.getRecords().length === 0) return this.renderNoDataAvailable();
    return (
      <div className='flex-vbox'>
        <div className="bg-white flex-hbox flex-grow-0 align-items-center justify-content-between px-2 py-1">
          <div className="flex-hbox justify-content-start align-items-center" >
            <WRateFinderGridFilter context={this.vgridContext} />
          </div>
          <div className="flex-hbox justify-content-end align-items-center gap-1" >
          </div>
        </div>
        <div key={this.viewId} className='flex-vbox'>
          {this.renderUIGrid()}
        </div>
      </div>
    )
  }

}

export interface UITransactionInformationProps extends app.AppComponentProps {
  transactionSummary: Array<any>;
}

export class UITransactionInformation extends app.AppComponent<UITransactionInformationProps> {
  config: bs.GridConfig = {
    showHeader: true,
    showBorder: true,
    columns: [
      { field: 'transactionId', label: T('Job No.'), width: 200, cssClass: 'text-body fs-9 text-start' },
      { field: 'reportDate', label: T('Job date'), width: 150, cssClass: 'text-body fs-9 text-start' },
      { field: 'shipmentType', label: T('Shipment type'), width: 150, cssClass: 'text-body fs-9 text-start' },
    ]
  }
  render(): React.ReactNode {
    let { transactionSummary } = this.props;
    if (!transactionSummary) transactionSummary = []
    return (
      <div className='flex-vbox p-3'>
        <bs.Grid config={this.config} beans={transactionSummary} />
      </div>
    )
  }
}

export class WCheckPartnerTaxCode extends app.AppComponent {

  onCheckTaxCode = (searchPattern: any) => {
    if (!searchPattern) {
      bs.dialogShow('Information',
        <div className="px-3 py-3 text-center border-bottom rounded" style={{ backgroundColor: '#d1ecf1', color: '#0c5460', fontSize: '1rem', fontWeight: 'bold' }}>
          <i className="bi bi-info-circle-fill" style={{ fontSize: '1.5rem', marginRight: '0.5rem' }}></i>
          Enter Partner Tax Code
        </div>,
        { backdrop: 'static', size: 'sm' }
      );
      return;
    }
    const { pageContext } = this.props;
    const createPageContent = (appCtx: app.AppContext, pageCtx: app.PageContext) => (
      <div className='flex-vbox'>
        <UICheckBFSOnePartnerList
          appContext={appCtx} pageContext={pageCtx} plugin={new UICheckBFSOnePartnerPlugin(searchPattern)} />
      </div>
    );
    pageContext.createPopupPage(`partner-list-${util.IDTracker.next()}`, T(`Existing Partners Found: ${searchPattern.toUpperCase()}`), createPageContent, { size: 'xl', backdrop: 'static' });
  }

  render(): React.ReactNode {
    const { className, style } = this.props;
    let model = { taxCode: '' };

    return (
      <div className={`flex-hbox flex-grow-0 shadow-sm bg-white ${className}`} style={style}>
        <div className="fw-bold text-warning py-1 h-100 px-2 border-end text-nowrap" style={{ fontSize: '0.875rem' }}>Check Partner Name/ Tax Code</div>
        <input.BBStringField style={{ width: 300 }} className="py-1 px-1 h-100 border-0"
          placeholder="Enter Partner Name/ Tax Code..."
          bean={model} field="taxCode" />
        <bs.Button laf="link" className="text-warning d-inline-flex align-items-center px-2 py-1 border-start rounded-0"
          onClick={() => this.onCheckTaxCode(model.taxCode)} >
          <FeatherIcon.Search size={12} className="me-1" /> Check
        </bs.Button>
      </div>
    )
  }
}