import React from 'react';
import * as FeatherIcon from 'react-feather';
import { bs, grid, app, util, entity, sql } from '@datatp-ui/lib';

import {
  PartnerReportParams,
  WPartnerReportFilter,
  WQuickTimeRangeSelector
} from '../common/UIDashboardUtility';
import {
  UIAgentTransactionsListPlugin,
  UIAgentTransactions
} from './UIAgentTransactionsList';

export type Space = 'User' | 'Company' | 'System'

export interface AgentTransactionsReportFilter {
  collapsed: boolean;
  groupedBy: { label: string, value: string };
  dateFilter: { fromValue: string, toValue: string, label: string };
  partnerParams: PartnerReportParams;
}

interface UIAgentTransactionsPageState { }
interface UAgentTransactionsPageProps extends app.AppComponentProps { }

export class UIBFSOneAgentTransactionsPage extends app.AppComponent<UAgentTransactionsPageProps, UIAgentTransactionsPageState> {
  viewId: number = util.IDTracker.next();
  reportFilter: AgentTransactionsReportFilter;
  agentTransactionsTreeRef: React.RefObject<UIAgentTransactions>;
  rawRecords: any[] = [];

  constructor(props: app.AppComponentProps) {
    super(props);
    this.agentTransactionsTreeRef = React.createRef<UIAgentTransactions>();

    const now = new Date();
    let fromDate = new Date(now.getFullYear(), 0, 1, 0, 0, 0);
    let toDate = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59);
    let dateFilter = new util.TimeRange();
    dateFilter.fromSetDate(fromDate);
    dateFilter.toSetDate(toDate);
    this.reportFilter = {
      collapsed: false,
      groupedBy: { label: "Country", value: "COUNTRY" },
      dateFilter: { fromValue: dateFilter.fromFormat(), toValue: dateFilter.toFormat(), label: 'Year to Date' },
      partnerParams: {
        searchPattern: '',
        country: '',
        transactionId: '',
        continent: 'EUROPE',
        source: '',
        fromReportDate: '',
        toReportDate: ''
      }
    }
    this.markLoading(true);
  }

  componentDidMount(): void {
    this.loadData();
  }

  loadData(): void {
    const { dateFilter, partnerParams } = this.reportFilter;
    const { appContext } = this.props;
    let plugin = new UIAgentTransactionsListPlugin()
      .withDateFilter('shipmentDate', dateFilter.fromValue, dateFilter.toValue)
      .withDateFilter('modifiedDate', partnerParams.fromReportDate, partnerParams.toReportDate)
      .withSearchPattern(partnerParams.searchPattern)
      .withParams('continent', partnerParams.continent)
      .withParams('source', partnerParams.source)
      .withParams('country', partnerParams.country)
      .withParams('transactionId', partnerParams.transactionId);

    appContext.createHttpBackendCall('PartnerReportService', 'searchAgentTransactionsReport', { params: plugin.getSearchParams() })
      .withSuccessData((records: Array<any>) => {
        this.rawRecords = records;
        this.markLoading(false);
        this.forceUpdate();
      })
      .call()
  }

  doExport = () => {
    let { appContext } = this.props;
    let targetRecords = this.groupedByAgent();

    let agentGroupField: entity.GroupField = {
      label: "_blank_",
      fields: [
        { label: "STT.", name: "stt", dataType: 'number' },
        { label: "Agent Name", name: "agentName", dataType: 'string' },
        { label: "FH (times)", name: "totalJobCountFH", dataType: 'number' },
        { label: "NM (times)", name: "totalJobCountNM", dataType: 'number' },
        { label: "FCL TEUs(F/H)", name: "totalTeuFH", dataType: 'number' },
        { label: `Cont 20'(F/H)`, name: "cont20FH", dataType: 'number' },
        { label: `Cont 40'(F/H)`, name: "cont40FH", dataType: 'number' },
        { label: `Cont 45'(F/H)`, name: "cont45FH", dataType: 'number' },
        { label: `CBM(F/H)`, name: "totalHawbCbmFH", dataType: 'number' },
        { label: `KGS(F/H)`, name: "totalHawbGwFH", dataType: 'number' },
        { label: "FCL TEUs(N/M)", name: "totalTeuNM", dataType: 'number' },
        { label: `Cont 20'(N/M)`, name: "cont20NM", dataType: 'number' },
        { label: `Cont 40'(N/M)`, name: "cont40NM", dataType: 'number' },
        { label: `Cont 45'(N/M)`, name: "cont45NM", dataType: 'number' },
        { label: `CBM(N/M)`, name: "totalHawbCbmNM", dataType: 'number' },
        { label: `KGS(N/M)`, name: "totalHawbGwNM", dataType: 'number' },
        { label: "Last contact", name: "lastTransactionDate", dataType: 'date' },
        { label: "Last Job ID", name: "lastTransactionID", dataType: 'string' },
        { label: "Source", name: "source", dataType: 'string' },
        { label: "Continent", name: "continent", dataType: 'string' },
        { label: "Country", name: "countryLabel", dataType: 'string', },
      ],
    }

    let records: Array<any> = [];
    for (let i = 0; i < targetRecords.length; i++) {
      let record = targetRecords[i];
      let newRecord: any = {
        stt: i + 1,
        agentName: record.agentName,
        totalJobCountFH: record.totalJobCountFH || 0,
        totalJobCountNM: record.totalJobCountNM || 0,
        totalTeuFH: record.totalTeuFH || 0,
        cont20FH: record.cont20FH || 0,
        cont40FH: record.cont40FH || 0,
        cont45FH: record.cont45FH || 0,
        totalHawbCbmFH: record.totalHawbCbmFH || 0,
        totalHawbGwFH: record.totalHawbGwFH || 0,
        totalTeuNM: record.totalTeuNM || 0,
        cont20NM: record.cont20NM || 0,
        cont40NM: record.cont40NM || 0,
        cont45NM: record.cont45NM || 0,
        totalHawbCbmNM: record.totalHawbCbmNM || 0,
        totalHawbGwNM: record.totalHawbGwNM || 0,
        lastTransactionDate: record.lastTransactionDate || '',
        lastTransactionID: record.lastTransactionID || '',
        source: record.source || '',
        continent: record.continent || '',
        countryLabel: record.countryLabel || '',
      };
      records.push(newRecord);
    }

    let exportModel: entity.DataListExportModel = {
      fieldGroups: [],
      fields: [...agentGroupField.fields],
      records: records,
      modelName: 'Agent_Transactions',
      fileName: `Agent_Transactions_${util.TimeUtil.toCompactDateFormat(new Date())}.xlsx`,
    }

    appContext.createHttpBackendCall('DataMappingService', 'xlsxPrivateExport', { model: exportModel })
      .withSuccessData((data: any) => {
        let storeInfo = data;
        if (storeInfo.type == 'ir.actions.act_url') {
          entity.StoreInfo.download(storeInfo.url);
        } else if (storeInfo.privateMode) {
          entity.StoreInfo.privateDownload(storeInfo);
        } else {
          entity.StoreInfo.download(storeInfo.url);
        }
      })
      .call()
  }

  renderHeader() {
    const { appContext, pageContext } = this.props;
    const { dateFilter } = this.reportFilter;
    return (
      <div className="bg-white flex-hbox flex-grow-0 align-items-center justify-content-between px-2 py-1">

        <div className="flex-hbox justify-content-start align-items-center" >
          <h5 style={{ color: '#6c757d' }}>
            <FeatherIcon.Users className="me-2" size={18} />
            Agent Transactions
          </h5>

          <div className='flex-hbox align-items-center flex-grow-0 border-start' >
          </div>
        </div>

        <div className="flex-hbox justify-content-end align-items-center gap-1" >

          <WPartnerReportFilter appContext={appContext} pageContext={pageContext}
            params={this.reportFilter.partnerParams}
            onModify={(bean: any, _field: string, _oldVal: any, _newVal: any) => {
              this.reportFilter.partnerParams = bean;
              this.markLoading(true);
              this.forceUpdate();
              this.loadData();
            }} />

          <WQuickTimeRangeSelector appContext={appContext} pageContext={pageContext}
            initBean={dateFilter}
            onModify={(bean: any, _field: string, _oldVal: any, _newVal: any) => {
              this.reportFilter.dateFilter = bean;
              this.markLoading(true);
              this.forceUpdate();
              this.loadData();
            }} />

          <bs.Button laf="secondary" outline size="sm" className="p-1" style={{ color: '#6c757d' }}
            onClick={this.doExport}>
            <FeatherIcon.Download size={14} className="me-2" />
            Export
          </bs.Button>

        </div>
      </div>
    );
  }

  calculateShipmentStats = (reportRecordList: any[], shipmentType: string) => {
    const groupByShipmentTypeRecords = reportRecordList.reduce((acc, rec) => {
      const type = rec.shipmentType || '';
      acc[type] = acc[type] || [];
      acc[type].push(rec);
      return acc;
    }, {});

    const records = groupByShipmentTypeRecords[shipmentType] || [];
    let totalHawbCbm = 0;
    let totalHawbGw = 0;
    let cont20 = 0;
    let cont40 = 0;
    let cont45 = 0;
    const totalJobCount = records.length;

    for (const rec of records) {
      totalHawbCbm += Number(rec.hawbCbm || 0);
      totalHawbGw += Number(rec.hawbGw || 0);
      const service = rec.typeOfService || '';
      if (service !== 'SeaExpTransactions_FCL' && service !== 'SeaImpTransactions_FCL') continue;
      const containerSize = rec.containerSize || '';
      if (containerSize) {
        const containers = containerSize.split('&');
        for (let cont of containers) {
          cont = cont.trim();
          const idx = cont.indexOf('X');
          if (idx > 0) {
            const quantityStr = cont.substring(0, idx).replace(/[^0-9]/g, '');
            const qty = quantityStr ? parseInt(quantityStr, 10) : 0;
            if (cont.includes('20')) cont20 += qty;
            else if (cont.includes('40')) cont40 += qty;
            else if (cont.includes('45')) cont45 += qty;
          }
        }
      }
    }
    const totalTeu = cont20 + cont40 * 2 + cont45 * 2;
    return {
      totalJobCount,
      totalHawbCbm,
      totalHawbGw,
      cont20,
      cont40,
      cont45,
      totalTeu,
    };
  };

  groupedByAgent = (): any[] => {
    let holder: any[] = [];
    const groupedRecords = this.rawRecords.reduce((acc, record) => {
      const agentCode = record.agentCode || '';
      acc[agentCode] = acc[agentCode] || [];
      acc[agentCode].push(record);
      return acc;
    }, {});

    if (Object.keys(groupedRecords).length === 0) {
      return [...this.rawRecords];
    } else {
      for (let agentCode in groupedRecords) {
        const reportRecordList = groupedRecords[agentCode];
        const record = { ...reportRecordList[0] };

        let countryLabel = record.countryLabel;
        if (countryLabel) record.countryLabel = countryLabel.toUpperCase();

        const sortedList = [...reportRecordList].sort((a, b) => {
          const dateStrA = a.reportDate || '';
          const dateStrB = b.reportDate || '';
          const dateA = util.TimeUtil.parseCompactDateTimeFormat(dateStrA);
          const dateB = util.TimeUtil.parseCompactDateTimeFormat(dateStrB);
          return dateB.getTime() - dateA.getTime();
        });

        const latestRecord = sortedList[0];
        record.lastTransactionDate = (latestRecord?.reportDate || '').split('@')[0];
        record.lastTransactionID = latestRecord?.transactionId || '';

        const freehandStats = this.calculateShipmentStats(reportRecordList, 'FREE-HAND');
        record.totalJobCountFH = freehandStats.totalJobCount;
        record.totalHawbCbmFH = freehandStats.totalHawbCbm;
        record.totalHawbGwFH = freehandStats.totalHawbGw;
        record.cont20FH = freehandStats.cont20;
        record.cont40FH = freehandStats.cont40;
        record.cont45FH = freehandStats.cont45;
        record.totalTeuFH = freehandStats.totalTeu;

        const nominatedStats = this.calculateShipmentStats(reportRecordList, 'NOMINATED');
        record.totalJobCountNM = nominatedStats.totalJobCount;
        record.totalHawbCbmNM = nominatedStats.totalHawbCbm;
        record.totalHawbGwNM = nominatedStats.totalHawbGw;
        record.cont20NM = nominatedStats.cont20;
        record.cont40NM = nominatedStats.cont40;
        record.cont45NM = nominatedStats.cont45;
        record.totalTeuNM = nominatedStats.totalTeu;

        holder.push(record);
      }
      return holder;
    }
  };

  render(): React.JSX.Element {
    const { appContext, pageContext } = this.props;
    const plugin = new entity.DbEntityListPlugin(this.groupedByAgent());

    return (
      <div className="flex-vbox mx-1 p-1 bg-white rounded-md w-100 h-100 my-1" >
        {this.renderHeader()}
        <div className="flex-vbox border-top bg-body-highlight h-100" key={util.IDTracker.next()}>
          {this.isLoading()
            ? this.renderLoading()
            : <UIAgentTransactions ref={this.agentTransactionsTreeRef} rawRecords={this.rawRecords}
              appContext={appContext} pageContext={pageContext} plugin={plugin} />
          }
        </div>
      </div>
    )
  }
}

