import React from "react";
import * as FeatherIcon from 'react-feather';
import { bs, app, util, entity } from '@datatp-ui/lib';
import { UIInquiryRequestList, UIInquiryRequestReportPlugin } from "..";
import { GridConfig, ResponsiveGrid, GridColumn } from "../../common/ResponsiveGrid";
import { <PERSON><PERSON><PERSON>, Pie, Tooltip, Cell, Legend } from "recharts";
import {
  WCompanySelector, WMultiTypeOfServiceSelector, WQuickTimeRangeSelector
} from "../../common/UIDashboardUtility";

import { ImportExportPurpose, TransportationMode } from "../../common/model";
import { mapToTypeOfShipment, TransportationTool } from "app/crm/common";

const SESSION = app.host.DATATP_HOST.session;

const NUMBER_FORMAT = (val: any) => util.text.formater.number(val, 2);

const routeGroupedGridConfig: GridConfig = {
  header: {
    height: 20,
  },
  row: {
    height: 30,
  },
  showHeader: true,
  showBorder: true,
  columns: [
    {
      field: 'fromLocationLabel', label: 'From Location',
      cssClass: 'border-start border-light text-start'
    },
    {
      field: 'toLocationLabel', label: 'To Location',
      cssClass: 'border-start border-light text-start'
    },
    { field: 'totalInProgress', label: 'Checking', cssClass: 'border-start border-light text-start' },
    { field: 'totalMismatch', label: 'Mismatch', cssClass: 'border-start border-light text-start' },
    { field: 'totalNoResponse', label: 'No Response', cssClass: 'border-start border-light text-start' },
    { field: 'totalResponse', label: 'Responded', cssClass: 'border-start border-light text-start' },
    { field: 'totalWin', label: 'Win', cssClass: 'border-start border-light text-start' },
    { field: 'total', label: 'Total', cssClass: 'border-start border-light text-start' },
    { field: 'winrate', label: 'Win Rate', cssClass: 'border-start border-light text-start' },
  ],
  widthConfig: {
    totalWidth: 1350,
    minColumnWidth: 100,
    ratios: [3, 3, 1, 1, 1, 1, 1, 1, 1]
  }
}

const routeVolumeGroupedGridConfig: GridConfig = {
  header: {
    height: 20,
  },
  row: {
    height: 30,
  },
  showHeader: true,
  showBorder: true,
  columns: [
    { field: 'fromLocationLabel', label: 'From Location', cssClass: 'border-start border-light text-start' },
    { field: 'toLocationLabel', label: 'To Location', cssClass: 'border-start border-light text-start' },
    { field: 'totalVolume', label: 'Total Volume', cssClass: 'border-start border-light text-start' },
    { field: 'totalWin', label: 'Total Win', cssClass: 'border-start border-light text-start' },
    { field: 'totalRequest', label: 'Total Request', cssClass: 'border-start border-light text-start' },
    { field: 'winRate', label: 'Win Rate', cssClass: 'border-start border-light text-start fw-bold' },
    { field: 'comparison', label: 'Comparison', cssClass: 'border-start border-light text-start' }
  ],
  widthConfig: {
    totalWidth: 1350,
    minColumnWidth: 100,
    ratios: [3, 3, 1, 1, 1, 1, 1]
  }
}

interface ReportFilter {
  dateFilter: { fromValue: string, toValue: string, label: string };
  previousPeriod?: { fromValue: string, toValue: string };
  company: { code: string, label: string };
  typeOfServices?: { label: string, purpose?: ImportExportPurpose, mode?: TransportationMode }[]
  pricingOnly: boolean;
}

export interface UIInquiryDashboardProps extends app.AppComponentProps {
  space: 'User' | 'Company' | 'System';
}
export class UIInquiryDashboard extends app.AppComponent<UIInquiryDashboardProps> {
  reportFilter: ReportFilter;
  records: Array<any> = [];
  previousRecords: Array<any> = [];
  routeTypeOfService: { label: string, purpose?: ImportExportPurpose, mode?: TransportationMode } = { label: 'Type Of Service' };
  pricingRecords: Array<any> = []

  constructor(props: UIInquiryDashboardProps) {
    super(props);

    const today = new Date();
    const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const lastDayOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

    let dateFilter = new util.TimeRange();
    dateFilter.fromSetDate(firstDayOfMonth);
    dateFilter.toSetDate(lastDayOfMonth);

    // Previous month
    let previousFromDate: Date = new Date(today.getFullYear(), today.getMonth() - 1, 1);
    let previousToDate = new Date(today.getFullYear(), today.getMonth(), 0);

    let companyContext = SESSION.getCurrentCompanyContext();
    let companyCode: string = companyContext['companyCode'];
    let companyLabel: string = companyContext['companyLabel'];

    this.reportFilter = {
      company: { code: companyCode, label: companyLabel },
      dateFilter: { fromValue: dateFilter.fromFormat(), toValue: dateFilter.toFormat(), label: 'This Month' },
      previousPeriod: { fromValue: util.TimeUtil.javaCompactDateTimeFormat(previousFromDate), toValue: util.TimeUtil.javaCompactDateTimeFormat(previousToDate) },
      typeOfServices: [{ label: 'Type Of Service' }],
      pricingOnly: false
    }
    this.loadData();
  }

  componentDidMount(): void {
    this.loadData();
  }

  loadData(): void {
    const { appContext, space } = this.props;
    const { company, dateFilter, previousPeriod, typeOfServices, pricingOnly } = this.reportFilter;
    let plugin = new UIInquiryRequestReportPlugin(space);
    plugin.withCompanyCode(company.code);
    plugin.withDateFilter('requestDate', dateFilter.fromValue, dateFilter.toValue);
    plugin.updateStatusOptions(['SUCCESS', 'PRICE_MISMATCH', 'NO_RESPONSE', 'RESPONDED', 'IN_PROGRESS', 'DONE']);
    this.markLoading(true);

    appContext.createHttpBackendCall('TransportPriceMiscService', 'inquiryRequestReport', { params: plugin.getSearchParams() })
      .withSuccessData((records: Array<any>) => {

        let includeModes: TransportationMode[] = [];
        let includePurposes: string[] = [];

        if (typeOfServices && typeOfServices.length > 0) {
          for (let type of typeOfServices) {
            if (!type.mode || !type.purpose) continue;
            includeModes.push(type.mode);
            includePurposes.push(type.purpose);
          }
        }

        this.records = [];
        if (includeModes.length > 0 || includeModes.length > 0) {
          for (let rec of records) {
            let mode = rec['mode'];
            let purpose = rec['purpose'];
            if (includeModes.includes(mode) && includePurposes.includes(purpose)) {
              this.records.push(rec);
            }
          }
        } else {
          this.records = records;
        }

        if (pricingOnly) {
          this.records = this.records.filter(rec => rec['pricingDate'])
        }

        // After loading current period data, load previous period data if available
        if (previousPeriod) {
          let previousPlugin = new UIInquiryRequestReportPlugin(space);
          previousPlugin.withCompanyCode(company.code);
          previousPlugin.withDateFilter('requestDate', previousPeriod.fromValue, previousPeriod.toValue);
          previousPlugin.updateStatusOptions(['SUCCESS', 'PRICE_MISMATCH', 'NO_RESPONSE', 'RESPONDED', 'IN_PROGRESS', 'DONE']);

          appContext.createHttpBackendCall('TransportPriceMiscService', 'inquiryRequestReport', { params: previousPlugin.getSearchParams() })
            .withSuccessData((previousRecords: Array<any>) => {
              this.previousRecords = [];
              if (includeModes.length > 0 || includeModes.length > 0) {
                for (let rec of previousRecords) {
                  let mode = rec['mode'];
                  let purpose = rec['purpose'];
                  if (includeModes.includes(mode) && includePurposes.includes(purpose)) {
                    this.previousRecords.push(rec);
                  }
                }
              } else {
                this.previousRecords = previousRecords;
              }

              if (pricingOnly) {
                this.previousRecords = this.previousRecords.filter(rec => rec['pricingDate'])
              }

              this.markLoading(false);
              this.forceUpdate();
            })
            .call();
        } else {
          this.previousRecords = [];
          this.markLoading(false);
          this.forceUpdate();
        }

        this.markLoading(false);
        this.forceUpdate();
      })
      .call()

    plugin.withDateFilter('pricingDate', dateFilter.fromValue, dateFilter.toValue);
    plugin.withDateFilter('requestDate', '', '');
    plugin.updateStatusOptions(['SUCCESS', 'PRICE_MISMATCH', 'NO_RESPONSE', 'RESPONDED', 'IN_PROGRESS', 'DONE']);
    this.markLoading(true);

    appContext.createHttpBackendCall('TransportPriceMiscService', 'inquiryRequestReport', { params: plugin.getSearchParams() })
      .withSuccessData((records: Array<any>) => {
        this.pricingRecords = records;
        this.markLoading(false);
        this.forceUpdate();
      })
      .call()
  }

  onModify = (bean: any, _field: string, _oldVal: any, newVal: any) => {
    // this.viewId = util.IDTracker.next();
    bean[_field] = newVal;
    this.forceUpdate();
    this.loadData();
  }

  calculateVolumeStats(records: Array<any>) {
    // Group by mode/type of shipment for current records
    const modeGroups = records
      .filter(sel => !TransportationTool.isTruck(sel['mode']))
      .reduce((acc, record) => {
        const mode = mapToTypeOfShipment(record['purpose'], record['mode']);
        if (!acc[mode]) acc[mode] = [];
        acc[mode].push(record);
        return acc;
      }, {} as Record<string, Array<any>>);

    // Group by mode/type of shipment for previous records
    const previousModeGroups = this.previousRecords
      .filter(sel => !TransportationTool.isTruck(sel['mode']))
      .reduce((acc, record) => {
        const mode = mapToTypeOfShipment(record['purpose'], record['mode']);
        if (!acc[mode]) acc[mode] = [];
        acc[mode].push(record);
        return acc;
      }, {} as Record<string, Array<any>>);

    // Sort the groups by key
    const sortedModeGroups = Object.keys(modeGroups).sort((a, b) => {
      const aIsImport = a.endsWith('Import') || a.endsWith('Imp');
      const bIsImport = b.endsWith('Import') || b.endsWith('Imp');

      if (aIsImport && !bIsImport) return -1;
      if (!aIsImport && bIsImport) return 1;
      return a.localeCompare(b); // Standard alphabetical sort for same category
    }).reduce((acc, key) => {
      acc[key] = modeGroups[key];
      return acc;
    }, {} as Record<string, Array<any>>);

    // Calculate stats for each group
    const volumeStats = Object.entries(sortedModeGroups).map(([typeOfService, serviceRecords]) => {
      // Get the first record to determine the mode for volume unit
      let firstRec: any = serviceRecords.length > 0 ? serviceRecords[0] : {};
      let mode: string = firstRec['mode'] || '';
      let purpose: string = firstRec['purpose'] || '';

      // Calculate the volume unit based on mode
      let volumeUnit = '-';
      if (mode.includes('AIR')) volumeUnit = 'KGS';
      else if (mode.includes('LCL')) volumeUnit = 'CBM';
      else if (mode.includes('FCL')) volumeUnit = 'TEUs';
      else if (mode.includes('EXPRESS')) volumeUnit = 'KGS';

      // Calculate volume totals for current period
      const totalVolume = serviceRecords.reduce((sum, rec) => sum + (rec['reportVolume'] || 0), 0);
      const totalWin = serviceRecords.filter(rec => rec['status'] === 'SUCCESS').length;
      const totalRequest = serviceRecords.length;
      const winRate = totalRequest > 0 ? Math.round((totalWin / totalRequest) * 100) : 0;

      // Calculate the comparison with previous period
      let comparison = '';
      if (previousModeGroups[typeOfService]) {
        const prevRecords = previousModeGroups[typeOfService];
        const prevTotalWin = prevRecords.filter((rec: any) => rec['status'] === 'SUCCESS').length;
        const prevTotalRequest = prevRecords.length;
        const prevWinRate = prevTotalRequest > 0 ? Math.round((prevTotalWin / prevTotalRequest) * 100) : 0;

        // Calculate the difference in win rate
        const difference = winRate - prevWinRate;

        // Format the comparison string
        if (difference > 0) {
          comparison = `+${difference}%`;
        } else if (difference < 0) {
          comparison = `${difference}%`;
        } else {
          comparison = '0%';
        }
      } else {
        comparison = 'N/A'; // No previous data for comparison
      }

      return {
        serviceType: typeOfService,
        totalVolume: `${NUMBER_FORMAT(totalVolume)} ${volumeUnit}`,
        totalWin: totalWin,
        totalRequest: totalRequest,
        winRate: `${winRate}%`,
        comparison: comparison,
        mode: mode,
        purpose: purpose,
      };
    });
    return volumeStats;
  }

  calculateStatusStats(records: Array<any>) {
    // Group current records by department
    const departmentGroups = records.reduce((acc, record) => {
      const department = record['parentDepartmentLabel'] === 'SALES' ? 'Sales' : 'Back Office';
      if (!acc[department]) acc[department] = [];
      acc[department].push(record);
      return acc;
    }, {} as Record<string, Array<any>>);

    // Group previous records by department
    const previousDepartmentGroups = this.previousRecords.reduce((acc, record) => {
      const department = record['parentDepartmentLabel'] === 'SALES' ? 'Sales' : 'Back Office';
      if (!acc[department]) acc[department] = [];
      acc[department].push(record);
      return acc;
    }, {} as Record<string, Array<any>>);

    // Process current Sales data
    const salesRecords: Array<any> = departmentGroups['Sales'] || [];
    const salesWins = salesRecords.filter(rec => rec['status'] === 'SUCCESS').length;
    const salesTotal = salesRecords.length;
    const salesWinRate = salesTotal > 0 ? parseFloat((salesWins / salesTotal * 100).toFixed(1)) : 0;

    // Calculate comparison for Sales
    let salesComparison = '';
    if (previousDepartmentGroups['Sales'] && previousDepartmentGroups['Sales'].length > 0) {
      const prevSalesRecords = previousDepartmentGroups['Sales'];
      const prevSalesWins = prevSalesRecords.filter((rec: any) => rec['status'] === 'SUCCESS').length;
      const prevSalesTotal = prevSalesRecords.length;
      const prevSalesWinRate = prevSalesTotal > 0 ? parseFloat((prevSalesWins / prevSalesTotal * 100).toFixed(1)) : 0;

      const difference = salesWinRate - prevSalesWinRate;
      if (difference > 0) {
        salesComparison = `+${difference.toFixed(1)}%`;
      } else if (difference < 0) {
        salesComparison = `${difference.toFixed(1)}%`;
      } else {
        salesComparison = '0%';
      }
    } else {
      salesComparison = 'N/A';
    }

    const saleRecord: any = {
      label: 'Sales',
      win: salesWins,
      mismatch: salesRecords.filter(rec => rec['status'] === 'PRICE_MISMATCH').length,
      noResponse: salesRecords.filter(rec => rec['status'] === 'NO_RESPONSE').length +
        salesRecords.filter(rec => rec['status'] === 'DONE').length,
      responded: salesRecords.filter(rec => rec['status'] === 'RESPONDED').length,
      checking: salesRecords.filter(rec => rec['status'] === 'IN_PROGRESS').length,
      total: salesTotal,
      winrate: `${salesWinRate}%`,
      comparision: salesComparison
    }

    // Process current Back Office data
    const backOfficeRecords: Array<any> = departmentGroups['Back Office'] || [];
    const backOfficeWins = backOfficeRecords.filter(rec => rec['status'] === 'SUCCESS').length;
    const backOfficeTotal = backOfficeRecords.length;
    const backOfficeWinRate = backOfficeTotal > 0 ? parseFloat((backOfficeWins / backOfficeTotal * 100).toFixed(1)) : 0;

    // Calculate comparison for Back Office
    let backOfficeComparison = '';
    if (previousDepartmentGroups['Back Office'] && previousDepartmentGroups['Back Office'].length > 0) {
      const prevBackOfficeRecords = previousDepartmentGroups['Back Office'];
      const prevBackOfficeWins = prevBackOfficeRecords.filter((rec: any) => rec['status'] === 'SUCCESS').length;
      const prevBackOfficeTotal = prevBackOfficeRecords.length;
      const prevBackOfficeWinRate = prevBackOfficeTotal > 0 ? parseFloat((prevBackOfficeWins / prevBackOfficeTotal * 100).toFixed(1)) : 0;

      const difference = backOfficeWinRate - prevBackOfficeWinRate;
      if (difference > 0) {
        backOfficeComparison = `+${difference.toFixed(1)}%`;
      } else if (difference < 0) {
        backOfficeComparison = `${difference.toFixed(1)}%`;
      } else {
        backOfficeComparison = '0%';
      }
    } else {
      backOfficeComparison = 'N/A';
    }

    const backOfficeRecord: any = {
      label: 'Back Office',
      win: backOfficeWins,
      mismatch: backOfficeRecords.filter(rec => rec['status'] === 'PRICE_MISMATCH').length,
      noResponse: backOfficeRecords.filter(rec => rec['status'] === 'NO_RESPONSE').length +
        backOfficeRecords.filter(rec => rec['status'] === 'DONE').length,
      responded: backOfficeRecords.filter(rec => rec['status'] === 'RESPONDED').length,
      checking: backOfficeRecords.filter(rec => rec['status'] === 'IN_PROGRESS').length,
      total: backOfficeTotal,
      winrate: `${backOfficeWinRate}%`,
      comparision: backOfficeComparison
    }

    return [saleRecord, backOfficeRecord];
  }

  onViewSalemanStatByDepartment(department: 'Sales' | 'Back Office') {
    // Collect current records by department
    let currentRecords = [];
    if (department === 'Sales') {
      currentRecords = this.records.filter(rec => rec['parentDepartmentLabel'] === 'SALES');
    } else {
      currentRecords = this.records.filter(rec => rec['parentDepartmentLabel'] !== 'SALES');
    }


    // Collect previous records by department
    let previousRecords = [];
    if (department === 'Sales') {
      previousRecords = this.previousRecords.filter(rec => rec['parentDepartmentLabel'] === 'SALES');
    } else {
      previousRecords = this.previousRecords.filter(rec => rec['parentDepartmentLabel'] !== 'SALES');
    }

    const salemanMap: any = currentRecords.reduce((acc, record) => {
      const salemanAccountId = record['salemanAccountId'];
      if (!acc[salemanAccountId]) acc[salemanAccountId] = [];
      acc[salemanAccountId].push(record);
      return acc;
    }, {} as Record<string, Array<any>>);

    // Group previous records by department
    const previousSalemanMap = previousRecords.reduce((acc, record) => {
      const salemanAccountId = record['salemanAccountId'];
      if (!acc[salemanAccountId]) acc[salemanAccountId] = [];
      acc[salemanAccountId].push(record);
      return acc;
    }, {} as Record<string, Array<any>>);

    // Process data
    let records: Array<any> = [];
    for (let key of Object.keys(salemanMap)) {
      let salesRecords: Array<any> = salemanMap[key];
      const salesWins = salesRecords.filter(rec => rec['status'] === 'SUCCESS').length;
      const salesTotal = salesRecords.length;
      const salesWinRate = salesTotal > 0 ? parseFloat((salesWins / salesTotal * 100).toFixed(1)) : 0;

      let salesComparison = '';
      if (previousSalemanMap[key] && previousSalemanMap[key].length > 0) {
        const prevSalesRecords = previousSalemanMap[key];
        const prevSalesWins = prevSalesRecords.filter((rec: any) => rec['status'] === 'SUCCESS').length;
        const prevSalesTotal = prevSalesRecords.length;
        const prevSalesWinRate = prevSalesTotal > 0 ? parseFloat((prevSalesWins / prevSalesTotal * 100).toFixed(1)) : 0;

        const difference = salesWinRate - prevSalesWinRate;
        if (difference > 0) {
          salesComparison = `+${difference.toFixed(1)}%`;
        } else if (difference < 0) {
          salesComparison = `${difference.toFixed(1)}%`;
        } else {
          salesComparison = '0%';
        }
      } else {
        salesComparison = 'N/A';
      }

      const salemanRecord: any = {
        saleman: salesRecords[0]['salemanLabel'],
        win: salesWins,
        mismatch: salesRecords.filter(rec => rec['status'] === 'PRICE_MISMATCH').length,
        noResponse: salesRecords.filter(rec => rec['status'] === 'NO_RESPONSE').length +
          salesRecords.filter(rec => rec['status'] === 'DONE').length,
        responded: salesRecords.filter(rec => rec['status'] === 'RESPONDED').length,
        checking: salesRecords.filter(rec => rec['status'] === 'IN_PROGRESS').length,
        total: salesTotal,
        winrate: `${salesWinRate}%`,
        comparision: salesComparison
      }
      records.push(salemanRecord);
    }

    const gridConfig: GridConfig = {
      header: {
        height: 20,
      },
      row: {
        height: 30,
      },
      showHeader: true,
      showBorder: true,
      columns: [
        { field: 'saleman', label: 'Saleman', cssClass: 'border-start border-light text-start' },
        { field: 'checking', label: 'Checking', cssClass: 'border-start border-light text-start' },
        { field: 'mismatch', label: 'Mismatch', cssClass: 'border-start border-light text-start' },
        { field: 'noResponse', label: 'No Response', cssClass: 'border-start border-light text-start' },
        { field: 'responded', label: 'Responded', cssClass: 'border-start border-light text-start' },
        { field: 'win', label: 'Win', cssClass: 'border-start border-light text-start' },
        { field: 'total', label: 'Total', cssClass: 'border-start border-light text-start' },
        { field: 'winrate', label: 'Win Rate', cssClass: 'border-start border-light text-start' },
        { field: 'comparision', label: 'Comparison', cssClass: 'border-start border-light text-start' },
      ],
      widthConfig: {
        totalWidth: 1020,
        minColumnWidth: 100,
        ratios: [3.5, 1, 1, 1, 1, 1, 1, 1, 1.5]
      }
    }

    let { pageContext } = this.props
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <div className="flex-vbox" >
          <ResponsiveGrid config={gridConfig} data={records} />
          <bs.Toolbar className='border'>
            <bs.Button laf="secondary" outline size="sm" className="me-1 p-1" style={{ color: '#6c757d' }}
              onClick={() => { this.onExport(records, gridConfig, `${department}`) }}>
              <FeatherIcon.Download size={14} className="me-1" />
              Export
            </bs.Button>
          </bs.Toolbar>
        </div>
      )
    }
    let popupId = `view-saleman-${util.IDTracker.next()}`;
    pageContext.createPopupPage(popupId, department, createAppPage, { size: 'lg', backdrop: 'static' });
  }

  calculateRouteStatusStats(records: Array<any>, limit: number = 10) {
    let holder: Array<any> = [];

    const filteredRecords = records.filter(rec => {
      if (!this.routeTypeOfService) return true;

      const purposeMatch = !this.routeTypeOfService.purpose || rec['purpose'] === this.routeTypeOfService.purpose;
      const modeMatch = !this.routeTypeOfService.mode || rec['mode'] === this.routeTypeOfService.mode;

      return purposeMatch && modeMatch;
    });

    const routeGroups = filteredRecords
      .filter(sel => sel['requestDate'])
      .reduce((acc, record) => {
        let fromLocationLabel: string = (record['fromLocationLabel'] || 'N/A').trim();
        let toLocationLabel: string = (record['toLocationLabel'] || 'N/A').trim();
        let route: string = `${fromLocationLabel}-${toLocationLabel}`;
        if (!acc[route]) acc[route] = [];
        acc[route].push(record);
        return acc;
      }, {} as Record<string, Array<any>>);

    for (const [_route, routeRecords] of Object.entries(routeGroups) as [string, Array<any>][]) {
      let firstRec = routeRecords.length > 0 ? routeRecords[0] : {};
      let totalNoResponse = routeRecords.filter(rec => rec['status'] === 'NO_RESPONSE').length;
      let totalDone = routeRecords.filter(rec => rec['status'] === 'DONE').length;
      let totalWin = routeRecords.filter(rec => rec['status'] === 'SUCCESS').length;
      let total = routeRecords.length
      const winRate = total > 0 ? (totalWin / total * 100).toFixed(1) : '0.0';
      const winrate = `${winRate}%`;

      const routeRecord = {
        fromLocationLabel: firstRec['fromLocationLabel'] || 'N/A',
        toLocationLabel: firstRec['toLocationLabel'] || 'N/A',
        totalWin: totalWin,
        totalMismatch: routeRecords.filter(rec => rec['status'] === 'PRICE_MISMATCH').length,
        totalNoResponse: totalNoResponse + totalDone,
        totalResponse: routeRecords.filter(rec => rec['status'] === 'RESPONDED').length,
        totalInProgress: routeRecords.filter(rec => rec['status'] === 'IN_PROGRESS').length,
        total: routeRecords.length,
        winrate: winrate
      };
      holder.push(routeRecord);
    }

    for (let rec of holder) {
      rec['fromLocationLabel'] = util.text.formater.uiTruncate(rec.fromLocationLabel || 'N/A', 280, true);
      rec['toLocationLabel'] = util.text.formater.uiTruncate(rec.toLocationLabel || 'N/A', 280, true);
    }

    const sortedResult = [...holder].sort((a, b) => b.total - a.total);

    return limit > 0 ? sortedResult.slice(0, limit) : sortedResult;
  }

  calculatePricingStatusStats(records: Array<any>) {
    let holder: Array<any> = [];

    let filteredRecords = [...records].filter(sel => sel['pricingDate'])

    let totalNoResponse = filteredRecords.filter(rec => rec['status'] === 'NO_RESPONSE').length;
    let totalDone = filteredRecords.filter(rec => rec['status'] === 'DONE').length;
    let totalWin = filteredRecords.filter(rec => rec['status'] === 'SUCCESS').length;
    let total = filteredRecords.length
    const totalStepCounting = filteredRecords.reduce((sum, rec) => {
      const stepCount = Number(rec['totalStepCounting']) || 0;
      const analysisCount = Number(rec['totalAnalysisPricesCount']) || 0;
      const newPriceCount = Number(rec['totalNewPricesCount']) || 0;
      return sum + stepCount + analysisCount + newPriceCount;
    }, 0);

    const winRate = total > 0 ? (totalWin / total * 100).toFixed(1) : '0.0';
    const winrate = `${winRate}%`;

    const summaryRec: any = {
      label: 'All Count',
      totalUnknown: 0,
      totalWin: totalWin,
      totalMismatch: filteredRecords.filter(rec => rec['status'] === 'PRICE_MISMATCH').length,
      totalNoResponse: totalNoResponse + totalDone,
      totalResponse: filteredRecords.filter(rec => rec['status'] === 'RESPONDED').length,
      totalInProgress: filteredRecords.filter(rec => rec['status'] === 'IN_PROGRESS').length,
      total: filteredRecords.length,
      winrate: winrate,
      totalStepCounting: totalStepCounting,
      pricingAccountId: undefined
    };
    holder.push(summaryRec)

    let pricingEmployeeGroups = filteredRecords.reduce((acc, record) => {
      const pricingEmployee = record['pricingLabel'] || 'N/A';
      if (!acc[pricingEmployee]) acc[pricingEmployee] = [];
      acc[pricingEmployee].push(record);
      return acc;
    }, {} as Record<string, Array<any>>);

    const sortedPricingEmployeeGroups = Object.keys(pricingEmployeeGroups).sort().reverse().reduce((acc, key) => {
      acc[key] = pricingEmployeeGroups[key];
      return acc;
    }, {} as Record<string, Array<any>>);

    for (const [pricingName, pricingRecords] of Object.entries(sortedPricingEmployeeGroups) as [string, Array<any>][]) {

      const totalStepCounting = pricingRecords.reduce((sum, rec) => {
        const stepCount = Number(rec['totalStepCounting']) || 0;
        const analysisCount = Number(rec['totalAnalysisPricesCount']) || 0;
        const newPriceCount = Number(rec['totalNewPricesCount']) || 0;
        return sum + stepCount + analysisCount + newPriceCount;
      }, 0);

      let totalNoResponse = pricingRecords.filter(rec => rec['status'] === 'NO_RESPONSE').length;
      let totalDone = pricingRecords.filter(rec => rec['status'] === 'DONE').length;
      let totalWin = pricingRecords.filter(rec => rec['status'] === 'SUCCESS').length;
      let total = pricingRecords.length
      const winRate = total > 0 ? (totalWin / total * 100).toFixed(1) : '0.0';
      const winrate = `${winRate}%`;
      const pricingAccountId = pricingRecords[0]['pricingAccountId'];
      const employeeRecord = {
        label: pricingName,
        totalUnknown: 0,
        totalWin: totalWin,
        totalMismatch: pricingRecords.filter(rec => rec['status'] === 'PRICE_MISMATCH').length,
        totalNoResponse: totalNoResponse + totalDone,
        totalResponse: pricingRecords.filter(rec => rec['status'] === 'RESPONDED').length,
        totalInProgress: pricingRecords.filter(rec => rec['status'] === 'IN_PROGRESS').length,
        total: pricingRecords.length,
        winrate: winrate,
        totalStepCounting: totalStepCounting,
        pricingAccountId: pricingAccountId
      };
      holder.push(employeeRecord);
    }
    return holder;
  }

  onViewAllRequest = () => {
    const { pageContext, space } = this.props;
    const { dateFilter, company } = this.reportFilter;

    let plugin = new UIInquiryRequestReportPlugin(space);
    plugin.withDateFilter('requestDate', dateFilter['fromValue'], dateFilter['toValue']);
    plugin.withDateFilter('pricingDate', '', '');
    plugin.withCompanyCode(company.code);

    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <UIInquiryRequestList appContext={appCtx} pageContext={pageCtx} plugin={plugin} space={space} />
      )
    }
    let popupId = `view-all-inquiry-${util.IDTracker.next()}`;
    let pupupLabel: string = `Inquiry Requests`;
    pageContext.createPopupPage(popupId, pupupLabel, createAppPage, { size: 'xl', backdrop: 'static' });
  }

  onExport = (records: Array<any>, grid: GridConfig, fileName: string) => {
    const { appContext } = this.props;
    const currentDate = util.TimeUtil.toCompactDateFormat(new Date());
    let fields: entity.Field[] = grid.columns.map(column => ({
      name: column.field,
      label: column.label
    }));
    const exportModel: entity.DataListExportModel = {
      modelName: fileName,
      fileName: `${fileName}_${currentDate}.xlsx`,
      fields: fields,
      fieldGroups: [],
      records: records,
    };

    appContext.createHttpBackendCall('DataMappingService', 'xlsxPrivateExport', { model: exportModel })
      .withSuccessData((response: any) => {
        let storeInfo = response;
        if (storeInfo.type == 'ir.actions.act_url') {
          entity.StoreInfo.download(storeInfo.url);
        } else if (storeInfo.privateMode) {
          entity.StoreInfo.privateDownload(storeInfo);
        } else {
          entity.StoreInfo.download(storeInfo.url);
        }
      })
      .call();
  }

  onViewPricingByInquiries = (pricingAccountId: number, pricingLabel: string) => {
    const { pageContext, space } = this.props;
    const { dateFilter, company } = this.reportFilter;

    let plugin = new UIInquiryRequestReportPlugin(space);
    plugin.withDateFilter('requestDate', dateFilter.fromValue, dateFilter.toValue);
    plugin.withCompanyCode(company.code);
    plugin.withPricingAccountId(pricingAccountId);

    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <UIInquiryRequestList appContext={appCtx} pageContext={pageCtx}
          plugin={plugin} space={space} />
      )
    }

    let popupId = `view-pricing-inquiry-${util.IDTracker.next()}`;
    let popupLabel: string = `Inquiry Requests - ${pricingLabel}`;
    pageContext.createPopupPage(popupId, popupLabel, createAppPage, { size: 'xl', backdrop: 'static' });
  }

  onViewTypeOfServiceByInquiries = (record: any) => {
    const { pageContext, space } = this.props;
    const { dateFilter, company } = this.reportFilter;
    let mode = record['mode'];
    let purpose = record['purpose'];

    let plugin = new UIInquiryRequestReportPlugin(space);
    plugin.withDateFilter('requestDate', dateFilter.fromValue, dateFilter.toValue);
    plugin.withCompanyCode(company.code);
    plugin.withPurpose(purpose);
    plugin.withMode(mode);

    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (
        <UIInquiryRequestList appContext={appCtx} pageContext={pageCtx}
          plugin={plugin} space={space} />
      )
    }

    let popupId = `view-pricing-inquiry-${util.IDTracker.next()}`;
    let popupLabel: string = `Inquiry Requests - ${record['serviceType']}`;
    pageContext.createPopupPage(popupId, popupLabel, createAppPage, { size: 'xl', backdrop: 'static' });
  }


  calculateRouteVolumeStats(records: Array<any>, limit: number = 10) {

    const filteredRecords = records.filter(rec => {
      if (!this.routeTypeOfService) return true;
      const purposeMatch = !this.routeTypeOfService.purpose || rec['purpose'] === this.routeTypeOfService.purpose;
      const modeMatch = !this.routeTypeOfService.mode || rec['mode'] === this.routeTypeOfService.mode;
      return purposeMatch && modeMatch && !TransportationTool.isTruck(rec['mode'])
    });

    const previousFilteredRecords = this.previousRecords.filter(rec => {
      if (!this.routeTypeOfService) return true;
      const purposeMatch = !this.routeTypeOfService.purpose || rec['purpose'] === this.routeTypeOfService.purpose;
      const modeMatch = !this.routeTypeOfService.mode || rec['mode'] === this.routeTypeOfService.mode;
      return purposeMatch && modeMatch && !TransportationTool.isTruck(rec['mode'])
    });

    const routeGroups: any = filteredRecords
      .reduce((acc, record) => {
        const fromLocation = record['fromLocationCode'];
        const toLocation = record['toLocationCode'];
        const routeKey = `${fromLocation}-${toLocation}`;

        if (!acc[routeKey]) acc[routeKey] = [];
        acc[routeKey].push(record);
        return acc;
      }, {} as Record<string, Array<any>>);

    const previousRouteGroups: any = previousFilteredRecords
      .reduce((acc, record) => {
        const fromLocation = record['fromLocationCode'];
        const toLocation = record['toLocationCode'];
        const routeKey = `${fromLocation}-${toLocation}`;

        if (!acc[routeKey]) acc[routeKey] = [];
        acc[routeKey].push(record);
        return acc;
      }, {} as Record<string, Array<any>>);

    const routeStats = Object.entries(routeGroups).map(([routeKey, routeRecords]: [string, Array<any>]) => {
      let firstRec: any = routeRecords.length > 0 ? routeRecords[0] : {};
      let fromLocationLabel = firstRec['fromLocationLabel'];
      let toLocationLabel = firstRec['toLocationLabel'];
      let mode: string = firstRec['mode'] || '';

      let volumeUnit = '-';
      if (mode.includes('AIR')) volumeUnit = 'KGS';
      else if (mode.includes('LCL')) volumeUnit = 'CBM';
      else if (mode.includes('FCL')) volumeUnit = 'TEUs';
      else if (mode.includes('EXPRESS')) volumeUnit = 'KGS';

      const totalVolume = routeRecords.reduce((sum, rec) => sum + (rec['reportVolume'] || 0), 0);
      const totalWin = routeRecords.filter(rec => rec['status'] === 'SUCCESS').length;
      const totalRequest = routeRecords.length;
      const winRate = totalRequest > 0 ? Math.round((totalWin / totalRequest) * 100) : 0;

      let comparison = '';
      if (previousRouteGroups[routeKey]) {
        const prevRecords = previousRouteGroups[routeKey];
        const prevTotalWin = prevRecords.filter((rec: any) => rec['status'] === 'SUCCESS').length;
        const prevTotalRequest = prevRecords.length;
        const prevWinRate = prevTotalRequest > 0 ? Math.round((prevTotalWin / prevTotalRequest) * 100) : 0;

        const difference = winRate - prevWinRate;

        if (difference > 0) {
          comparison = `+${difference}%`;
        } else if (difference < 0) {
          comparison = `${difference}%`;
        } else {
          comparison = '0%';
        }
      } else {
        comparison = 'N/A';
      }

      return {
        fromLocationLabel,
        toLocationLabel,
        volumeNumber: totalVolume || 0,
        totalVolume: `${NUMBER_FORMAT(totalVolume)} ${volumeUnit}`,
        totalWin,
        totalRequest,
        winRate: `${winRate}%`,
        comparison
      };
    }).sort((a, b) => b.volumeNumber - a.volumeNumber);

    const result = limit > 0 ? routeStats.slice(0, limit) : routeStats;

    for (let rec of result) {
      rec['fromLocationLabel'] = util.text.formater.uiTruncate(rec.fromLocationLabel || 'N/A', 280, true);
      rec['toLocationLabel'] = util.text.formater.uiTruncate(rec.toLocationLabel || 'N/A', 280, true);
    }
    return result;
  }

  onExpandRouteVolume = () => {
    let { pageContext } = this.props;
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (<ResponsiveGrid className="w-100 h-100" config={routeVolumeGroupedGridConfig} data={this.calculateRouteVolumeStats(this.records, 0)} />)
    }
    let popupId = `view-expand-top-volume-route-${util.IDTracker.next()}`;
    let pupupLabel: string = `Top Volume Route Performance`;
    pageContext.createPopupPage(popupId, pupupLabel, createAppPage, { size: 'xl', backdrop: 'static' });
  }

  onExpandRoute = () => {
    let { pageContext } = this.props;
    let createAppPage = (appCtx: app.AppContext, pageCtx: app.PageContext) => {
      return (<ResponsiveGrid className="w-100 h-100" config={routeGroupedGridConfig} data={this.calculateRouteStatusStats(this.records, 0)} />)
    }
    let popupId = `view-expand-top-route-${util.IDTracker.next()}`;
    let pupupLabel: string = `Top Route Performance`;
    pageContext.createPopupPage(popupId, pupupLabel, createAppPage, { size: 'xl', backdrop: 'static' });
  }

  renderHeader() {
    const { appContext, pageContext } = this.props;
    const { dateFilter, company, typeOfServices, pricingOnly } = this.reportFilter;

    return (
      <div className="flex-hbox flex-grow-0 align-items-center justify-content-between px-2 py-1">
        <h5 style={{ color: '#6c757d' }}>
          <FeatherIcon.BarChart2 className="me-2" size={18} />
          Inquiry Dashboard Overview
        </h5>

        <div className="flex-hbox justify-content-end align-items-center flex-grow-1">

          <div className="flex-hbox justify-content-start align-items-center flex-grow-0 px-1 border border-secondary border-1 mx-1 rounded"
            style={{ backgroundColor: pricingOnly ? '#fff' : '#f8f9fa' }}
            onClick={() => {
              this.reportFilter.pricingOnly = !this.reportFilter.pricingOnly;
              this.loadData();
            }}>
            <input className="me-2" type="checkbox" checked={pricingOnly}
              // onChange={() => {
              //   this.reportFilter.pricingOnly = !this.reportFilter.pricingOnly;
              //   this.loadData();
              // }}
              style={{
                width: '14px',
                height: '14px',
                cursor: 'pointer',
                backgroundColor: this.reportFilter.pricingOnly ? '#0f9d58' : '#fff',
                borderColor: '#db4437'
              }}
            />
            <label className="form-check-label me-2 p-1" style={{ cursor: 'pointer' }}>
              {'Pricing Only'}
            </label>
          </div>

          <WMultiTypeOfServiceSelector appContext={appContext} pageContext={pageContext}
            selectedServices={typeOfServices}
            onModify={(_bean: any, _field: string, _oldVal: any, newVal: any) => {
              this.reportFilter.typeOfServices = newVal || [];
              this.loadData();
            }} />

          <WCompanySelector appContext={appContext} pageContext={pageContext}
            initCompany={company}
            onModify={(bean: any, _field: string, _oldVal: any, _newVal: any) => {
              this.reportFilter.company = bean;
              this.loadData();
            }} />

          <WQuickTimeRangeSelector appContext={appContext} pageContext={pageContext}
            initBean={dateFilter}
            onModify={(bean: any, _field: string, _oldVal: any, _newVal: any) => {
              this.reportFilter.dateFilter = bean;
              this.loadData();
            }} />

          <bs.Button laf="secondary" outline size="sm" className="p-1" style={{ color: '#6c757d' }}
            onClick={this.onViewAllRequest}>
            View all
            <FeatherIcon.ExternalLink size={12} className="ms-2" />
          </bs.Button>

        </div>

      </div>

    )
  }

  render(): React.ReactNode {

    let thisUI = this;
    const lightColor = '#D6EFD8';
    const borderColor = '#80AF81';

    const hoverStyle: any = {
      boxShadow: '0 4px 12px rgba(0,0,0,0.15)',
      transform: 'translateY(-2px)',
      backgroundColor: lightColor
    };

    const inquiryVolumeGridConfig: GridConfig = {
      header: {
        height: 20,
      },
      row: {
        height: 30,
      },
      showHeader: true,
      showBorder: true,
      columns: [
        {
          field: 'serviceType', label: 'Type Of Service',
          cellRenderer(value: any, record: any, column: GridColumn) {
            return (
              <div className='border-start border-light mx-0 text-start' style={{ width: column.width || 300 }}>
                <bs.Button laf='secondary' outline className='border-0 px-2 py-1 m-0 w-100 m-0 text-start fw-normal rounded-0 fs-9'
                  onClick={() => thisUI.onViewTypeOfServiceByInquiries(record)} >
                  {value}
                </bs.Button>
              </div>
            )
          },
        },
        { field: 'totalVolume', label: 'Total Volume', cssClass: 'border-start border-light text-start' },
        { field: 'totalWin', label: 'Total Win', cssClass: 'border-start border-light text-start' },
        { field: 'totalRequest', label: 'Total Request', cssClass: 'border-start border-light text-start' },
        { field: 'winRate', label: 'Win Rate', cssClass: 'border-start border-light text-start' },
        { field: 'comparison', label: 'Comparison', cssClass: 'border-start border-light text-start' }
      ],
      widthConfig: {
        totalWidth: 750,
        minColumnWidth: 100,
        ratios: [2, 1.5, 1, 1, 1, 1]
      }
    };

    const inquiryStatusGridConfig: GridConfig = {
      header: {
        height: 20,
      },
      row: {
        height: 30,
      },
      showHeader: true,
      showBorder: true,
      columns: [
        {
          field: 'label', label: 'Department', width: 200,

          cellRenderer(value: any, record: any, column: GridColumn) {
            return (
              <div className='border-start border-light mx-0 text-start' style={{ width: column.width || 300 }}>
                <bs.Button laf='secondary' outline className='border-0 px-2 py-1 m-0 w-100 m-0 text-start fw-normal rounded-0 fs-9'
                  onClick={() => thisUI.onViewSalemanStatByDepartment(record['label'])} >
                  {value}
                </bs.Button>
              </div>
            )
          },
        },
        { field: 'checking', label: 'Checking', cssClass: 'border-start border-light text-start' },
        { field: 'mismatch', label: 'Mismatch', cssClass: 'border-start border-light text-start' },
        { field: 'noResponse', label: 'No Response', cssClass: 'border-start border-light text-start' },
        { field: 'responded', label: 'Responded', cssClass: 'border-start border-light text-start' },
        { field: 'win', label: 'Win', cssClass: 'border-start border-light text-start' },
        { field: 'total', label: 'Total', cssClass: 'border-start border-light text-start' },
        { field: 'winrate', label: 'Win Rate', cssClass: 'border-start border-light text-start' },
        { field: 'comparision', label: 'Comparison', cssClass: 'border-start border-light text-start' },
      ],
      widthConfig: {
        totalWidth: 1020,
        minColumnWidth: 100,
        ratios: [2, 1, 1, 1, 1, 1, 1, 1, 1]
      }
    }

    const statusGroupedPricingGridConfig: GridConfig = {
      header: {
        height: 20,
      },
      row: {
        height: 30,
      },
      showHeader: true,
      showBorder: true,
      columns: [
        {
          field: 'label', label: 'Pricing By', width: 300,
          cellRenderer(value: any, record: any, column: GridColumn) {
            let cssCustom: string = 'fw-normal';
            if (value === 'All Count') cssCustom = 'fw-bold'
            return (
              <div className='border-start border-light mx-0 text-start' style={{ minWidth: column.width || 300 }}>
                <bs.Button laf='secondary' outline className={`border-0 px-2 py-1 m-0 w-100 m-0 text-start rounded-0 fs-9 ${cssCustom}`}
                  onClick={() => thisUI.onViewPricingByInquiries(record['pricingAccountId'], record['label'])} >
                  {value}
                </bs.Button>
              </div>
            )
          },
        },
        {
          field: 'totalInProgress', label: 'Checking',
          cssClass: 'border-start border-light text-start'
        },
        {
          field: 'totalMismatch', label: 'Mismatch',
          cssClass: 'border-start border-light text-start'
        },
        {
          field: 'totalNoResponse', label: 'No Response',
          cssClass: 'border-start border-light text-start'
        },
        {
          field: 'totalResponse', label: 'Responded',
          cssClass: 'border-start border-light text-start'
        },
        {
          field: 'totalWin', label: 'Win',
          cssClass: 'border-start border-light text-start'
        },
        {
          field: 'total', label: 'Total',
          cssClass: 'border-start border-light text-start'
        },
        {
          field: 'winrate', label: 'Win Rate',
          cssClass: 'border-start border-light text-start'
        },
        {
          field: 'totalStepCounting', label: 'Step Counting',
          cssClass: 'border-start border-light text-start'
        },
        {
          field: 'comparision', label: 'Comparison',
          cssClass: 'border-start border-light text-start'
        },
      ],
      widthConfig: {
        totalWidth: 1500,
        minColumnWidth: 100,
        ratios: [3, 1, 1, 1, 1, 1, 1, 1, 1, 1]
      }
    }

    return (
      <div className="flex-vbox mx-2 p-2 bg-white rounded-md w-100 h-100 my-2" >
        {this.renderHeader()}
        <div className="flex-vbox border-top bg-body-highlight" key={util.IDTracker.next()}>
          <bs.GreedyScrollable className="my-1">

            <div className="d-flex flex-column flex-md-row gap-2 p-1">

              <div className="bg-white border rounded-3 flex-vbox justify-content-center align-items-center"
                style={{
                  minWidth: 800,
                  maxWidth: 800,
                  borderColor: borderColor,
                  transition: 'all 0.3s ease',
                  marginBottom: '10px'
                }}
                onMouseEnter={(e) => Object.assign(e.currentTarget.style, hoverStyle)}
                onMouseLeave={(e) => {
                  e.currentTarget.style.boxShadow = 'none';
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.backgroundColor = '#fff';
                }}>

                <div className="flex-vbox mx-2 p-2 bg-white rounded-md w-100 h-100">
                  <div className="flex-hbox flex-grow-0 align-items-center justify-content-between px-2 py-2 border-bottom">
                    <h5 style={{ color: '#6c757d' }}><FeatherIcon.TrendingUp className="me-2" size={18} />Volume Performance</h5>
                    <div className="flex-hbox justify-content-end align-items-center flex-grow-1">

                      <bs.Button laf="secondary" outline size="sm" className="me-1 p-1" style={{ color: '#6c757d' }}
                        onClick={() => { this.onExport(this.calculateVolumeStats(this.records), inquiryVolumeGridConfig, 'Volume_Performance'); }}>
                        <FeatherIcon.Download size={14} className="me-1" />
                        Export
                      </bs.Button>

                      <bs.Button laf="secondary" outline size="sm" className="p-1" style={{ color: '#6c757d' }}
                        onClick={() => { }}>
                        View all
                        <FeatherIcon.ExternalLink size={12} className="ms-2" />
                      </bs.Button>

                    </div>
                  </div>

                  <div className="flex-vbox">
                    {this.isLoading()
                      ? this.renderLoading()
                      : <ResponsiveGrid className="w-100 h-100" config={inquiryVolumeGridConfig} data={this.calculateVolumeStats(this.records)} />
                    }
                  </div>
                </div>
              </div>

              <div className="bg-white border rounded-3 flex-vbox justify-content-center align-items-center"
                style={{
                  minWidth: 500,
                  borderColor: borderColor,
                  transition: 'all 0.3s ease',
                  marginBottom: '10px'
                }}
                onMouseEnter={(e) => Object.assign(e.currentTarget.style, hoverStyle)}
                onMouseLeave={(e) => {
                  e.currentTarget.style.boxShadow = 'none';
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.backgroundColor = '#fff';
                }}>
                <div className="flex-vbox">
                  <div className="flex-vbox mx-2 p-2 bg-white rounded-md w-100 h-100">
                    <div className="flex-hbox flex-grow-0 align-items-center justify-content-between px-2 py-2 border-bottom">
                      <h5 style={{ color: '#6c757d' }}><FeatherIcon.PieChart className="me-2" size={18} />Service Distribution</h5>
                    </div>
                    <div className="d-flex justify-content-center align-items-center" style={{ height: '400px' }}>
                      <PieChart width={700} height={400}>
                        <Pie
                          dataKey="totalRequest"
                          nameKey="serviceType"
                          isAnimationActive={false}
                          data={this.calculateVolumeStats(this.records).map(item => {
                            const totalRequests = this.calculateVolumeStats(this.records).reduce((sum, r) => sum + r.totalRequest, 0);
                            const percent = Math.round((item.totalRequest / totalRequests) * 100);
                            return {
                              ...item,
                              percent: percent,
                              tooltipText: `${item.serviceType}: ${item.totalRequest} requests (${percent}%)`
                            };
                          })}
                          cx={350} cy={200} outerRadius={150} innerRadius={60} fill="#8884d8" labelLine={true}
                          label={(entry) => `${entry.percent}%`}>
                          {
                            this.calculateVolumeStats(this.records).map((entry, index) => (
                              <Cell
                                key={`cell-${index}`}
                                fill={[
                                  '#0088FE', '#00C49F', '#FFBB28', '#FF8042',
                                  '#8884d8', '#82ca9d', '#ffc658', '#8dd1e1',
                                  '#a4de6c', '#d0ed57'
                                ][index % 10]}
                              />
                            ))
                          }
                        </Pie>
                        <Tooltip contentStyle={{ backgroundColor: 'white', border: '1px solid #ccc', width: '200px' }} />
                        <Legend layout="vertical" verticalAlign="middle" align="right" />
                      </PieChart>
                    </div>
                  </div>
                </div>
              </div>

            </div>

            <div className="flex-vbox gap-2 p-1">

              <div className="bg-white border rounded-3 flex-vbox justify-content-center align-items-center"
                style={{
                  minWidth: 600,
                  // maxWidth: 1040,
                  borderColor: borderColor,
                  transition: 'all 0.3s ease',
                  marginBottom: '10px'
                }}
                onMouseEnter={(e) => Object.assign(e.currentTarget.style, hoverStyle)}
                onMouseLeave={(e) => {
                  e.currentTarget.style.boxShadow = 'none';
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.backgroundColor = '#fff';
                }}>
                <ResponsiveGrid className="w-100 h-100" config={inquiryStatusGridConfig} data={this.calculateStatusStats(this.records)} />
              </div>

            </div>

            {/* ------------------- Pricing status Report ----------------------- */}

            <div className="flex-vbox gap-2 p-1">
              <div className="bg-white border rounded-3 flex-vbox justify-content-start align-items-center"
                style={{
                  minWidth: 600,
                  borderColor: borderColor,
                  transition: 'all 0.3s ease',
                  marginBottom: '10px',
                }}
                onMouseEnter={(e) => Object.assign(e.currentTarget.style, hoverStyle)}
                onMouseLeave={(e) => {
                  e.currentTarget.style.boxShadow = 'none';
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.backgroundColor = '#fff';
                }}>

                <div className="flex-vbox mx-2 p-1 bg-white rounded-md w-100">

                  <div className="flex-hbox flex-grow-0 align-items-center justify-content-between px-2 py-1">
                    <h5 style={{ color: '#6c757d' }}><FeatherIcon.TrendingUp className="me-2" size={18} />Pricing Performance</h5>

                    <div className="flex-hbox justify-content-end align-items-center flex-grow-1">

                      <bs.Button laf="secondary" outline size="sm" className="me-1 p-1" style={{ color: '#6c757d' }}
                        onClick={() => { this.onExport(this.calculatePricingStatusStats(this.records), statusGroupedPricingGridConfig, 'Pricing_Performance'); }}>
                        <FeatherIcon.Download size={14} className="me-1" />
                        Export
                      </bs.Button>

                      <bs.Button laf="secondary" outline size="sm" className="p-1" style={{ color: '#6c757d' }}
                        onClick={() => { }}>
                        View all
                        <FeatherIcon.ExternalLink size={12} className="ms-2" />
                      </bs.Button>

                    </div>
                  </div>

                  <div className="flex-vbox justify-content-start align-items-center">
                    <ResponsiveGrid className="w-100 h-100" config={statusGroupedPricingGridConfig} data={this.calculatePricingStatusStats(this.records)} />
                  </div>
                </div>

              </div>
            </div>

            {/* ------------------- Route volume Report ----------------------- */}
            <div className="d-flex flex-column flex-md-row gap-2 p-1">
              <div className="bg-white border rounded-3 flex-vbox justify-content-center align-items-center"
                style={{
                  minWidth: 900,
                  borderColor: borderColor,
                  transition: 'all 0.3s ease',
                  marginBottom: '10px'
                }}
                onMouseEnter={(e) => Object.assign(e.currentTarget.style, hoverStyle)}
                onMouseLeave={(e) => {
                  e.currentTarget.style.boxShadow = 'none';
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.backgroundColor = '#fff';
                }}>
                <div className="flex-vbox mx-2 p-2 bg-white rounded-md w-100 h-100">
                  <div className="flex-hbox flex-grow-0 align-items-center justify-content-between px-2 py-1">
                    <h5 style={{ color: '#6c757d' }}><FeatherIcon.TrendingUp className="me-2" size={18} />Top Volume Route Performance</h5>
                    <div className="flex-hbox justify-content-end align-items-center flex-grow-1">

                      <bs.Button laf="secondary" outline size="sm" className="me-1 p-1" style={{ color: '#6c757d' }}
                        onClick={() => { this.onExport(this.calculateRouteVolumeStats(this.records), routeVolumeGroupedGridConfig, 'RouteVolumePerformance'); }}>
                        <FeatherIcon.Download size={14} className="me-1" />
                        Export
                      </bs.Button>

                      <bs.Button laf="secondary" outline size="sm" className="p-1" style={{ color: '#6c757d' }}
                        onClick={this.onExpandRouteVolume}>
                        <FeatherIcon.Maximize2 size={14} className="me-1" />
                        Expand
                      </bs.Button>

                    </div>
                  </div>

                  <div className="flex-vbox">
                    <ResponsiveGrid className="w-100 h-100" config={routeVolumeGroupedGridConfig} data={this.calculateRouteVolumeStats(this.records)} />
                  </div>
                </div>
              </div>
            </div>

            {/* ------------------- Route status Report ----------------------- */}
            <div className="d-flex flex-column flex-md-row gap-2 p-1">

              <div className="bg-white border rounded-3 flex-vbox justify-content-center align-items-center"
                style={{
                  minWidth: 900,
                  borderColor: borderColor,
                  transition: 'all 0.3s ease',
                  marginBottom: '10px'
                }}
                onMouseEnter={(e) => Object.assign(e.currentTarget.style, hoverStyle)}
                onMouseLeave={(e) => {
                  e.currentTarget.style.boxShadow = 'none';
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.backgroundColor = '#fff';
                }}>
                <div className="flex-vbox mx-2 p-2 bg-white rounded-md w-100 h-100">
                  <div className="flex-hbox flex-grow-0 align-items-center justify-content-between px-2 py-1">
                    <h5 style={{ color: '#6c757d' }}><FeatherIcon.TrendingUp className="me-2" size={18} />Top Route Performance</h5>

                    <div className="flex-hbox justify-content-end align-items-center flex-grow-1">

                      <bs.Button laf="secondary" outline size="sm" className="me-1 p-1" style={{ color: '#6c757d' }}
                        onClick={() => { this.onExport(this.calculateRouteStatusStats(this.records), routeGroupedGridConfig, 'Top_Route_Performance'); }}>
                        <FeatherIcon.Download size={14} className="me-1" />
                        Export
                      </bs.Button>

                      <bs.Button laf="secondary" outline size="sm" className="p-1" style={{ color: '#6c757d' }}
                        onClick={this.onExpandRoute}>
                        <FeatherIcon.Maximize2 size={14} className="me-1" />
                        Expand
                      </bs.Button>

                    </div>
                  </div>

                  <div className="flex-vbox">
                    <ResponsiveGrid className="w-100 h-100" config={routeGroupedGridConfig} data={this.calculateRouteStatusStats(this.records)} />
                  </div>
                </div>
              </div>
            </div>

          </bs.GreedyScrollable>
        </div>

      </div>
    )

  }

}

