<!doctype html>
<html lang="vi-VN" dir="ltr" class="docs-wrapper plugin-docs plugin-id-default docs-version-current docs-doc-page docs-doc-id-datatp-keycloak/user/README" data-has-hydrated="false">
<head>
<meta charset="UTF-8">
<meta name="generator" content="Docusaurus v3.5.2">
<title data-rh="true">README | DataTP Cloud Documentation</title><meta data-rh="true" name="viewport" content="width=device-width,initial-scale=1"><meta data-rh="true" name="twitter:card" content="summary_large_image"><meta data-rh="true" property="og:url" content="https://docs.beelogistics.cloud/docs/datatp-keycloak/user/"><meta data-rh="true" property="og:locale" content="vi_VN"><meta data-rh="true" property="og:locale:alternate" content="en_GB"><meta data-rh="true" name="docusaurus_locale" content="vi"><meta data-rh="true" name="docsearch:language" content="vi"><meta data-rh="true" name="docusaurus_version" content="current"><meta data-rh="true" name="docusaurus_tag" content="docs-default-current"><meta data-rh="true" name="docsearch:version" content="current"><meta data-rh="true" name="docsearch:docusaurus_tag" content="docs-default-current"><meta data-rh="true" property="og:title" content="README | DataTP Cloud Documentation"><meta data-rh="true" name="description" content="🚀 Test với Postman"><meta data-rh="true" property="og:description" content="🚀 Test với Postman"><link data-rh="true" rel="icon" href="/img/favicon.ico"><link data-rh="true" rel="canonical" href="https://docs.beelogistics.cloud/docs/datatp-keycloak/user/"><link data-rh="true" rel="alternate" href="https://docs.beelogistics.cloud/docs/datatp-keycloak/user/" hreflang="vi-VN"><link data-rh="true" rel="alternate" href="https://docs.beelogistics.cloud/en/docs/datatp-keycloak/user/" hreflang="en-GB"><link data-rh="true" rel="alternate" href="https://docs.beelogistics.cloud/docs/datatp-keycloak/user/" hreflang="x-default"><link rel="stylesheet" href="/assets/css/styles.2daa48b4.css">
<script src="/assets/js/runtime~main.6a79df19.js" defer="defer"></script>
<script src="/assets/js/main.9805b06d.js" defer="defer"></script>
</head>
<body class="navigation-with-keyboard">
<script>!function(){function t(t){document.documentElement.setAttribute("data-theme",t)}var e=function(){try{return new URLSearchParams(window.location.search).get("docusaurus-theme")}catch(t){}}()||function(){try{return window.localStorage.getItem("theme")}catch(t){}}();t(null!==e?e:"light")}(),function(){try{const n=new URLSearchParams(window.location.search).entries();for(var[t,e]of n)if(t.startsWith("docusaurus-data-")){var a=t.replace("docusaurus-data-","data-");document.documentElement.setAttribute(a,e)}}catch(t){}}()</script><div id="__docusaurus"><div role="region" aria-label="Nhảy tới nội dung"><a class="skipToContent_pkKc" href="#__docusaurus_skipToContent_fallback">Nhảy tới nội dung</a></div><nav aria-label="Thanh điều hướng" class="navbar navbar--fixed-top"><div class="navbar__inner"><div class="navbar__items"><button aria-label="Đóng - mở thanh điều hướng" aria-expanded="false" class="navbar__toggle clean-btn" type="button"><svg width="30" height="30" viewBox="0 0 30 30" aria-hidden="true"><path stroke="currentColor" stroke-linecap="round" stroke-miterlimit="10" stroke-width="2" d="M4 7h22M4 15h22M4 23h22"></path></svg></button><a class="navbar__brand" href="/"><div class="navbar__logo"><img src="/img/logo.png" alt="DataTP Cloud Logo" class="themedComponent_MeEW themedComponent--light_NBvQ"><img src="/img/logo.png" alt="DataTP Cloud Logo" class="themedComponent_MeEW themedComponent--dark_oqmI"></div></a><a class="navbar__item navbar__link" href="/docs/shared/user/system">User Guides<!-- --></a><a class="navbar__item navbar__link" href="/docs/shared/developer/SETUP">Developer<!-- --></a></div><div class="navbar__items navbar__items--right"><div class="navbar__item dropdown dropdown--hoverable dropdown--right"><a href="#" aria-haspopup="true" aria-expanded="false" role="button" class="navbar__link"><svg viewBox="0 0 24 24" width="20" height="20" aria-hidden="true" class="iconLanguage_e5gE"><path fill="currentColor" d="M12.87 15.07l-2.54-2.51.03-.03c1.74-1.94 2.98-4.17 3.71-6.53H17V4h-7V2H8v2H1v1.99h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z"></path></svg>Tiếng Việt<!-- --></a><ul class="dropdown__menu"><li><a href="/docs/datatp-keycloak/user/" target="_self" rel="noopener noreferrer" class="dropdown__link dropdown__link--active" lang="vi-VN">Tiếng Việt<!-- --></a></li><li><a href="/en/docs/datatp-keycloak/user/" target="_self" rel="noopener noreferrer" class="dropdown__link" lang="en-GB">English<!-- --></a></li></ul></div><div class="toggle_VLGw colorModeToggle_FIch"><button class="clean-btn toggleButton_ZTlQ toggleButtonDisabled_NvtF" type="button" disabled="" title="Chuyển đổi chế độ sáng và tối (hiện tại chế độ sáng)" aria-label="Chuyển đổi chế độ sáng và tối (hiện tại chế độ sáng)" aria-live="polite"><svg viewBox="0 0 24 24" width="24" height="24" class="lightToggleIcon_GYGu"><path fill="currentColor" d="M12,9c1.65,0,3,1.35,3,3s-1.35,3-3,3s-3-1.35-3-3S10.35,9,12,9 M12,7c-2.76,0-5,2.24-5,5s2.24,5,5,5s5-2.24,5-5 S14.76,7,12,7L12,7z M2,13l2,0c0.55,0,1-0.45,1-1s-0.45-1-1-1l-2,0c-0.55,0-1,0.45-1,1S1.45,13,2,13z M20,13l2,0c0.55,0,1-0.45,1-1 s-0.45-1-1-1l-2,0c-0.55,0-1,0.45-1,1S19.45,13,20,13z M11,2v2c0,0.55,0.45,1,1,1s1-0.45,1-1V2c0-0.55-0.45-1-1-1S11,1.45,11,2z M11,20v2c0,0.55,0.45,1,1,1s1-0.45,1-1v-2c0-0.55-0.45-1-1-1C11.45,19,11,19.45,11,20z M5.99,4.58c-0.39-0.39-1.03-0.39-1.41,0 c-0.39,0.39-0.39,1.03,0,1.41l1.06,1.06c0.39,0.39,1.03,0.39,1.41,0s0.39-1.03,0-1.41L5.99,4.58z M18.36,16.95 c-0.39-0.39-1.03-0.39-1.41,0c-0.39,0.39-0.39,1.03,0,1.41l1.06,1.06c0.39,0.39,1.03,0.39,1.41,0c0.39-0.39,0.39-1.03,0-1.41 L18.36,16.95z M19.42,5.99c0.39-0.39,0.39-1.03,0-1.41c-0.39-0.39-1.03-0.39-1.41,0l-1.06,1.06c-0.39,0.39-0.39,1.03,0,1.41 s1.03,0.39,1.41,0L19.42,5.99z M7.05,18.36c0.39-0.39,0.39-1.03,0-1.41c-0.39-0.39-1.03-0.39-1.41,0l-1.06,1.06 c-0.39,0.39-0.39,1.03,0,1.41s1.03,0.39,1.41,0L7.05,18.36z"></path></svg><svg viewBox="0 0 24 24" width="24" height="24" class="darkToggleIcon_nQ2C"><path fill="currentColor" d="M9.37,5.51C9.19,6.15,9.1,6.82,9.1,7.5c0,4.08,3.32,7.4,7.4,7.4c0.68,0,1.35-0.09,1.99-0.27C17.45,17.19,14.93,19,12,19 c-3.86,0-7-3.14-7-7C5,9.07,6.81,6.55,9.37,5.51z M12,3c-4.97,0-9,4.03-9,9s4.03,9,9,9s9-4.03,9-9c0-0.46-0.04-0.92-0.1-1.36 c-0.98,1.37-2.58,2.26-4.4,2.26c-2.98,0-5.4-2.42-5.4-5.4c0-1.81,0.89-3.42,2.26-4.4C12.92,3.04,12.46,3,12,3L12,3z"></path></svg></button></div><div class="navbarSearchContainer_qR4N"></div></div></div><div role="presentation" class="navbar-sidebar__backdrop"></div></nav><div id="__docusaurus_skipToContent_fallback" class="main-wrapper mainWrapper__olq"><div class="docsWrapper_fuhk"><button aria-label="Trở lại đầu trang" class="clean-btn theme-back-to-top-button backToTopButton_wMuW" type="button"></button><div class="docRoot_h7H2"><main class="docMainContainer_hm5G docMainContainerEnhanced_DgUX"><div class="container padding-top--md padding-bottom--lg"><div class="row"><div class="col docItemCol_RFeR"><div class="docItemContainer_oFeY"><article><div class="tocCollapsible_Ak7j theme-doc-toc-mobile tocMobile_SryH"><button type="button" class="clean-btn tocCollapsibleButton_TCXD">Trên trang này<!-- --></button></div><div class="theme-doc-markdown markdown"><header><h1>README</h1></header><h2 class="anchor anchorWithStickyNavbar_V4cN" id="-test-với-postman">🚀 Test với Postman<!-- --><a href="#-test-với-postman" class="hash-link" aria-label="Đường dẫn trực tiếp tới 🚀 Test với Postman" title="Đường dẫn trực tiếp tới 🚀 Test với Postman">​</a></h2>
<!-- --><hr>
<!-- --><h3 class="anchor anchorWithStickyNavbar_V4cN" id="1-lấy-access-token-bằng-usernamepassword-người-dùng-đăng-nhập">1. Lấy Access Token bằng Username/Password (Người dùng đăng nhập)<!-- --><a href="#1-lấy-access-token-bằng-usernamepassword-người-dùng-đăng-nhập" class="hash-link" aria-label="Đường dẫn trực tiếp tới 1. Lấy Access Token bằng Username/Password (Người dùng đăng nhập)" title="Đường dẫn trực tiếp tới 1. Lấy Access Token bằng Username/Password (Người dùng đăng nhập)">​</a></h3>
<!-- --><blockquote>
<!-- --><p>Áp dụng cho ứng dụng frontend hoặc user đăng nhập bằng giao diện.</p>
<!-- --></blockquote>
<!-- --><h4 class="anchor anchorWithStickyNavbar_V4cN" id="url">URL:<!-- --><a href="#url" class="hash-link" aria-label="Đường dẫn trực tiếp tới URL:" title="Đường dẫn trực tiếp tới URL:">​</a></h4>
<!-- --><div class="codeBlockContainer_APB6 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_LDm6"><pre tabindex="0" class="prism-code language-text codeBlock_sZcE thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_IULG"><span class="token-line" style="color:#393A34"><span class="token plain">POST http://localhost:8080/realms/myrealm/protocol/openid-connect/token</span><br></span></code></pre><div class="buttonGroup_ZaKO"><button type="button" aria-label="Sao chép code vào bộ nhớ tạm" title="Sao chép" class="clean-btn"><span class="copyButtonIcons_VYo2" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_UQtN"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_fAgG"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<!-- --><h4 class="anchor anchorWithStickyNavbar_V4cN" id="headers">Headers:<!-- --><a href="#headers" class="hash-link" aria-label="Đường dẫn trực tiếp tới Headers:" title="Đường dẫn trực tiếp tới Headers:">​</a></h4>
<!-- --><div class="codeBlockContainer_APB6 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_LDm6"><pre tabindex="0" class="prism-code language-text codeBlock_sZcE thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_IULG"><span class="token-line" style="color:#393A34"><span class="token plain">Content-Type: application/x-www-form-urlencoded</span><br></span></code></pre><div class="buttonGroup_ZaKO"><button type="button" aria-label="Sao chép code vào bộ nhớ tạm" title="Sao chép" class="clean-btn"><span class="copyButtonIcons_VYo2" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_UQtN"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_fAgG"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<!-- --><h4 class="anchor anchorWithStickyNavbar_V4cN" id="body">Body:<!-- --><a href="#body" class="hash-link" aria-label="Đường dẫn trực tiếp tới Body:" title="Đường dẫn trực tiếp tới Body:">​</a></h4>
<!-- --><div class="codeBlockContainer_APB6 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_LDm6"><pre tabindex="0" class="prism-code language-text codeBlock_sZcE thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_IULG"><span class="token-line" style="color:#393A34"><span class="token plain">grant_type=password</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">client_id=my-client</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">username=testuser</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">password=123456</span><br></span></code></pre><div class="buttonGroup_ZaKO"><button type="button" aria-label="Sao chép code vào bộ nhớ tạm" title="Sao chép" class="clean-btn"><span class="copyButtonIcons_VYo2" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_UQtN"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_fAgG"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<!-- --><h4 class="anchor anchorWithStickyNavbar_V4cN" id="kết-quả-trả-về">Kết quả trả về:<!-- --><a href="#kết-quả-trả-về" class="hash-link" aria-label="Đường dẫn trực tiếp tới Kết quả trả về:" title="Đường dẫn trực tiếp tới Kết quả trả về:">​</a></h4>
<!-- --><div class="language-json codeBlockContainer_APB6 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_LDm6"><pre tabindex="0" class="prism-code language-json codeBlock_sZcE thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_IULG"><span class="token-line" style="color:#393A34"><span class="token plain">{</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  &quot;access_token&quot;: &quot;eyJhbGciOiJSUzI1NiIsInR5...&quot;,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  &quot;expires_in&quot;: 300,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  ...</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">}</span><br></span></code></pre><div class="buttonGroup_ZaKO"><button type="button" aria-label="Sao chép code vào bộ nhớ tạm" title="Sao chép" class="clean-btn"><span class="copyButtonIcons_VYo2" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_UQtN"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_fAgG"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<!-- --><hr>
<!-- --><h3 class="anchor anchorWithStickyNavbar_V4cN" id="2-lấy-access-token-bằng-client-secret-service-to-service">2. Lấy Access Token bằng Client Secret (Service-to-Service)<!-- --><a href="#2-lấy-access-token-bằng-client-secret-service-to-service" class="hash-link" aria-label="Đường dẫn trực tiếp tới 2. Lấy Access Token bằng Client Secret (Service-to-Service)" title="Đường dẫn trực tiếp tới 2. Lấy Access Token bằng Client Secret (Service-to-Service)">​</a></h3>
<!-- --><blockquote>
<!-- --><p>Áp dụng cho ứng dụng backend hoặc microservice không có người dùng đăng nhập.</p>
<!-- --></blockquote>
<!-- --><h4 class="anchor anchorWithStickyNavbar_V4cN" id="url-1">URL:<!-- --><a href="#url-1" class="hash-link" aria-label="Đường dẫn trực tiếp tới URL:" title="Đường dẫn trực tiếp tới URL:">​</a></h4>
<!-- --><div class="codeBlockContainer_APB6 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_LDm6"><pre tabindex="0" class="prism-code language-text codeBlock_sZcE thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_IULG"><span class="token-line" style="color:#393A34"><span class="token plain">POST http://localhost:8080/realms/myrealm/protocol/openid-connect/token</span><br></span></code></pre><div class="buttonGroup_ZaKO"><button type="button" aria-label="Sao chép code vào bộ nhớ tạm" title="Sao chép" class="clean-btn"><span class="copyButtonIcons_VYo2" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_UQtN"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_fAgG"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<!-- --><h4 class="anchor anchorWithStickyNavbar_V4cN" id="headers-1">Headers:<!-- --><a href="#headers-1" class="hash-link" aria-label="Đường dẫn trực tiếp tới Headers:" title="Đường dẫn trực tiếp tới Headers:">​</a></h4>
<!-- --><div class="codeBlockContainer_APB6 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_LDm6"><pre tabindex="0" class="prism-code language-text codeBlock_sZcE thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_IULG"><span class="token-line" style="color:#393A34"><span class="token plain">Content-Type: application/x-www-form-urlencoded</span><br></span></code></pre><div class="buttonGroup_ZaKO"><button type="button" aria-label="Sao chép code vào bộ nhớ tạm" title="Sao chép" class="clean-btn"><span class="copyButtonIcons_VYo2" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_UQtN"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_fAgG"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<!-- --><h4 class="anchor anchorWithStickyNavbar_V4cN" id="body-1">Body:<!-- --><a href="#body-1" class="hash-link" aria-label="Đường dẫn trực tiếp tới Body:" title="Đường dẫn trực tiếp tới Body:">​</a></h4>
<!-- --><div class="codeBlockContainer_APB6 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_LDm6"><pre tabindex="0" class="prism-code language-text codeBlock_sZcE thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_IULG"><span class="token-line" style="color:#393A34"><span class="token plain">grant_type=client_credentials</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">client_id=my-service</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">client_secret=&lt;giá-trị-secret-đã-copy&gt;</span><br></span></code></pre><div class="buttonGroup_ZaKO"><button type="button" aria-label="Sao chép code vào bộ nhớ tạm" title="Sao chép" class="clean-btn"><span class="copyButtonIcons_VYo2" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_UQtN"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_fAgG"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<!-- --><h4 class="anchor anchorWithStickyNavbar_V4cN" id="kết-quả-trả-về-1">Kết quả trả về:<!-- --><a href="#kết-quả-trả-về-1" class="hash-link" aria-label="Đường dẫn trực tiếp tới Kết quả trả về:" title="Đường dẫn trực tiếp tới Kết quả trả về:">​</a></h4>
<!-- --><div class="language-json codeBlockContainer_APB6 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_LDm6"><pre tabindex="0" class="prism-code language-json codeBlock_sZcE thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_IULG"><span class="token-line" style="color:#393A34"><span class="token plain">{</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  &quot;access_token&quot;: &quot;eyJhbGciOiJSUzI1NiIsInR5...&quot;,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  &quot;expires_in&quot;: 300,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  ...</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">}</span><br></span></code></pre><div class="buttonGroup_ZaKO"><button type="button" aria-label="Sao chép code vào bộ nhớ tạm" title="Sao chép" class="clean-btn"><span class="copyButtonIcons_VYo2" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_UQtN"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_fAgG"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<!-- --><hr>
<!-- --><h3 class="anchor anchorWithStickyNavbar_V4cN" id="3-gửi-request-kèm-token-đến-api">3. Gửi request kèm token đến API<!-- --><a href="#3-gửi-request-kèm-token-đến-api" class="hash-link" aria-label="Đường dẫn trực tiếp tới 3. Gửi request kèm token đến API" title="Đường dẫn trực tiếp tới 3. Gửi request kèm token đến API">​</a></h3>
<!-- --><p>Giả sử có một API ở:</p>
<!-- --><div class="codeBlockContainer_APB6 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_LDm6"><pre tabindex="0" class="prism-code language-text codeBlock_sZcE thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_IULG"><span class="token-line" style="color:#393A34"><span class="token plain">GET http://localhost:8081/api/secure</span><br></span></code></pre><div class="buttonGroup_ZaKO"><button type="button" aria-label="Sao chép code vào bộ nhớ tạm" title="Sao chép" class="clean-btn"><span class="copyButtonIcons_VYo2" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_UQtN"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_fAgG"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<!-- --><p>Thêm header:</p>
<!-- --><div class="codeBlockContainer_APB6 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_LDm6"><pre tabindex="0" class="prism-code language-text codeBlock_sZcE thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_IULG"><span class="token-line" style="color:#393A34"><span class="token plain">Authorization: Bearer &lt;access_token&gt;</span><br></span></code></pre><div class="buttonGroup_ZaKO"><button type="button" aria-label="Sao chép code vào bộ nhớ tạm" title="Sao chép" class="clean-btn"><span class="copyButtonIcons_VYo2" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_UQtN"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_fAgG"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<!-- --><hr>
<!-- --><h3 class="anchor anchorWithStickyNavbar_V4cN" id="4--kiểm-tra-token-có-gì-decode">4. 🧪 Kiểm tra token có gì (decode)<!-- --><a href="#4--kiểm-tra-token-có-gì-decode" class="hash-link" aria-label="Đường dẫn trực tiếp tới 4. 🧪 Kiểm tra token có gì (decode)" title="Đường dẫn trực tiếp tới 4. 🧪 Kiểm tra token có gì (decode)">​</a></h3>
<!-- --><p>Dán access token vào: <!-- --><a href="https://jwt.io" target="_blank" rel="noopener noreferrer">https://jwt.io</a></p>
<!-- --><p>Ví dụ:</p>
<!-- --><div class="language-json codeBlockContainer_APB6 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_LDm6"><pre tabindex="0" class="prism-code language-json codeBlock_sZcE thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_IULG"><span class="token-line" style="color:#393A34"><span class="token plain">{</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  &quot;preferred_username&quot;: &quot;testuser&quot;,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  &quot;client_id&quot;: &quot;my-service&quot;,</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  &quot;realm_access&quot;: {</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    &quot;roles&quot;: [&quot;user&quot;, &quot;admin&quot;]</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  },</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  ...</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">}</span><br></span></code></pre><div class="buttonGroup_ZaKO"><button type="button" aria-label="Sao chép code vào bộ nhớ tạm" title="Sao chép" class="clean-btn"><span class="copyButtonIcons_VYo2" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_UQtN"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_fAgG"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<!-- --><hr>
<!-- --><h2 class="anchor anchorWithStickyNavbar_V4cN" id="️-backup--restore-realm">🗃️ Backup &amp; Restore Realm<!-- --><a href="#️-backup--restore-realm" class="hash-link" aria-label="Đường dẫn trực tiếp tới 🗃️ Backup &amp; Restore Realm" title="Đường dẫn trực tiếp tới 🗃️ Backup &amp; Restore Realm">​</a></h2>
<!-- --><h3 class="anchor anchorWithStickyNavbar_V4cN" id="export">Export:<!-- --><a href="#export" class="hash-link" aria-label="Đường dẫn trực tiếp tới Export:" title="Đường dẫn trực tiếp tới Export:">​</a></h3>
<!-- --><blockquote>
<!-- --><p>Sử dụng lệnh của keycloak</p>
<!-- --></blockquote>
<!-- --><div class="codeBlockContainer_APB6 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_LDm6"><pre tabindex="0" class="prism-code language-text codeBlock_sZcE thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_IULG"><span class="token-line" style="color:#393A34"><span class="token plain">./kc.sh export --dir=data-export --realm=BeeCorp --users different_files --users-per-file 100</span><br></span></code></pre><div class="buttonGroup_ZaKO"><button type="button" aria-label="Sao chép code vào bộ nhớ tạm" title="Sao chép" class="clean-btn"><span class="copyButtonIcons_VYo2" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_UQtN"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_fAgG"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<!-- --><blockquote>
<!-- --><p>Đối với keycloak chạy với postgres SQL</p>
<!-- --></blockquote>
<!-- --><div class="codeBlockContainer_APB6 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_LDm6"><pre tabindex="0" class="prism-code language-text codeBlock_sZcE thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_IULG"><span class="token-line" style="color:#393A34"><span class="token plain">./db.sh keycloak_db dump --file={$BACKUP_DIR}/dbbackup/keycloak_db_backup.tar</span><br></span></code></pre><div class="buttonGroup_ZaKO"><button type="button" aria-label="Sao chép code vào bộ nhớ tạm" title="Sao chép" class="clean-btn"><span class="copyButtonIcons_VYo2" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_UQtN"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_fAgG"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<!-- --><h3 class="anchor anchorWithStickyNavbar_V4cN" id="import">Import:<!-- --><a href="#import" class="hash-link" aria-label="Đường dẫn trực tiếp tới Import:" title="Đường dẫn trực tiếp tới Import:">​</a></h3>
<!-- --><blockquote>
<!-- --><p>Sử dụng lệnh của keycloak</p>
<!-- --></blockquote>
<!-- --><div class="codeBlockContainer_APB6 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_LDm6"><pre tabindex="0" class="prism-code language-text codeBlock_sZcE thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_IULG"><span class="token-line" style="color:#393A34"><span class="token plain">./kc.sh import --dir backup-dir</span><br></span></code></pre><div class="buttonGroup_ZaKO"><button type="button" aria-label="Sao chép code vào bộ nhớ tạm" title="Sao chép" class="clean-btn"><span class="copyButtonIcons_VYo2" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_UQtN"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_fAgG"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<!-- --><blockquote>
<!-- --><p>Đối với keycloak chạy với postgres SQL</p>
<!-- --></blockquote>
<!-- --><div class="codeBlockContainer_APB6 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_LDm6"><pre tabindex="0" class="prism-code language-text codeBlock_sZcE thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_IULG"><span class="token-line" style="color:#393A34"><span class="token plain">./db.sh keycloak_db restore --db-name=keycloak_db --file=keycloak_db_backup.tar</span><br></span></code></pre><div class="buttonGroup_ZaKO"><button type="button" aria-label="Sao chép code vào bộ nhớ tạm" title="Sao chép" class="clean-btn"><span class="copyButtonIcons_VYo2" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_UQtN"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_fAgG"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<!-- --><hr>
<!-- --><h2 class="anchor anchorWithStickyNavbar_V4cN" id="-một-số-lệnh-hữu-ích">📌 Một số lệnh hữu ích<!-- --><a href="#-một-số-lệnh-hữu-ích" class="hash-link" aria-label="Đường dẫn trực tiếp tới 📌 Một số lệnh hữu ích" title="Đường dẫn trực tiếp tới 📌 Một số lệnh hữu ích">​</a></h2>
<!-- --><table><thead><tr><th>Lệnh</th><th>Mô tả</th></tr></thead><tbody><tr><td><code>kc.sh start-dev</code></td><td>Khởi động Keycloak dev mode</td></tr><tr><td><code>kc.sh export</code></td><td>Xuất dữ liệu realm</td></tr><tr><td><code>kc.sh import</code></td><td>Nhập dữ liệu realm</td></tr><tr><td><code>kc.sh show-config</code></td><td>Xem cấu hình đang chạy hiện tại</td></tr></tbody></table></div></article><nav class="pagination-nav docusaurus-mt-lg" aria-label="Trang tài liệu"></nav></div></div><div class="col col--3"><div class="tableOfContents_M5tG thin-scrollbar theme-doc-toc-desktop"><ul class="table-of-contents table-of-contents__left-border"><li><a href="#-test-với-postman" class="table-of-contents__link toc-highlight">🚀 Test với Postman</a><ul><li><a href="#1-lấy-access-token-bằng-usernamepassword-người-dùng-đăng-nhập" class="table-of-contents__link toc-highlight">1. Lấy Access Token bằng Username/Password (Người dùng đăng nhập)</a><ul><li><a href="#url" class="table-of-contents__link toc-highlight">URL:</a></li><li><a href="#headers" class="table-of-contents__link toc-highlight">Headers:</a></li><li><a href="#body" class="table-of-contents__link toc-highlight">Body:</a></li><li><a href="#kết-quả-trả-về" class="table-of-contents__link toc-highlight">Kết quả trả về:</a></li></ul></li><li><a href="#2-lấy-access-token-bằng-client-secret-service-to-service" class="table-of-contents__link toc-highlight">2. Lấy Access Token bằng Client Secret (Service-to-Service)</a><ul><li><a href="#url-1" class="table-of-contents__link toc-highlight">URL:</a></li><li><a href="#headers-1" class="table-of-contents__link toc-highlight">Headers:</a></li><li><a href="#body-1" class="table-of-contents__link toc-highlight">Body:</a></li><li><a href="#kết-quả-trả-về-1" class="table-of-contents__link toc-highlight">Kết quả trả về:</a></li></ul></li><li><a href="#3-gửi-request-kèm-token-đến-api" class="table-of-contents__link toc-highlight">3. Gửi request kèm token đến API</a></li><li><a href="#4--kiểm-tra-token-có-gì-decode" class="table-of-contents__link toc-highlight">4. 🧪 Kiểm tra token có gì (decode)</a></li></ul></li><li><a href="#️-backup--restore-realm" class="table-of-contents__link toc-highlight">🗃️ Backup &amp; Restore Realm</a><ul><li><a href="#export" class="table-of-contents__link toc-highlight">Export:</a></li><li><a href="#import" class="table-of-contents__link toc-highlight">Import:</a></li></ul></li><li><a href="#-một-số-lệnh-hữu-ích" class="table-of-contents__link toc-highlight">📌 Một số lệnh hữu ích</a></li></ul></div></div></div></div></main></div></div></div><footer class="footer footer--dark"><div class="container container-fluid"><div class="footer__bottom text--center"><div class="footer__copyright">Copyright © 2025 DataTP Cloud.</div></div></div></footer></div>
</body>
</html>