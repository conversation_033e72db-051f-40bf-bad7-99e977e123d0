<!doctype html>
<html lang="en-GB" dir="ltr" class="docs-wrapper plugin-docs plugin-id-default docs-version-current docs-doc-page docs-doc-id-datatp-keycloak/developer/SETUP" data-has-hydrated="false">
<head>
<meta charset="UTF-8">
<meta name="generator" content="Docusaurus v3.5.2">
<title data-rh="true">Hướng dẫn cài đặt và sử dụng Keycloak (Windows &amp; macOS) | DataTP Cloud Documentation</title><meta data-rh="true" name="viewport" content="width=device-width,initial-scale=1"><meta data-rh="true" name="twitter:card" content="summary_large_image"><meta data-rh="true" property="og:url" content="https://docs.beelogistics.cloud/en/docs/datatp-keycloak/developer/SETUP"><meta data-rh="true" property="og:locale" content="en_GB"><meta data-rh="true" property="og:locale:alternate" content="vi_VN"><meta data-rh="true" name="docusaurus_locale" content="en"><meta data-rh="true" name="docsearch:language" content="en"><meta data-rh="true" name="docusaurus_version" content="current"><meta data-rh="true" name="docusaurus_tag" content="docs-default-current"><meta data-rh="true" name="docsearch:version" content="current"><meta data-rh="true" name="docsearch:docusaurus_tag" content="docs-default-current"><meta data-rh="true" property="og:title" content="Hướng dẫn cài đặt và sử dụng Keycloak (Windows &amp; macOS) | DataTP Cloud Documentation"><meta data-rh="true" name="description" content="Yêu cầu hệ thống"><meta data-rh="true" property="og:description" content="Yêu cầu hệ thống"><link data-rh="true" rel="icon" href="/en/img/favicon.ico"><link data-rh="true" rel="canonical" href="https://docs.beelogistics.cloud/en/docs/datatp-keycloak/developer/SETUP"><link data-rh="true" rel="alternate" href="https://docs.beelogistics.cloud/docs/datatp-keycloak/developer/SETUP" hreflang="vi-VN"><link data-rh="true" rel="alternate" href="https://docs.beelogistics.cloud/en/docs/datatp-keycloak/developer/SETUP" hreflang="en-GB"><link data-rh="true" rel="alternate" href="https://docs.beelogistics.cloud/docs/datatp-keycloak/developer/SETUP" hreflang="x-default"><link rel="stylesheet" href="/en/assets/css/styles.2daa48b4.css">
<script src="/en/assets/js/runtime~main.f06c122c.js" defer="defer"></script>
<script src="/en/assets/js/main.5b3033d9.js" defer="defer"></script>
</head>
<body class="navigation-with-keyboard">
<script>!function(){function t(t){document.documentElement.setAttribute("data-theme",t)}var e=function(){try{return new URLSearchParams(window.location.search).get("docusaurus-theme")}catch(t){}}()||function(){try{return window.localStorage.getItem("theme")}catch(t){}}();t(null!==e?e:"light")}(),function(){try{const n=new URLSearchParams(window.location.search).entries();for(var[t,e]of n)if(t.startsWith("docusaurus-data-")){var a=t.replace("docusaurus-data-","data-");document.documentElement.setAttribute(a,e)}}catch(t){}}()</script><div id="__docusaurus"><div role="region" aria-label="Skip to main content"><a class="skipToContent_pkKc" href="#__docusaurus_skipToContent_fallback">Skip to main content</a></div><nav aria-label="Main" class="navbar navbar--fixed-top"><div class="navbar__inner"><div class="navbar__items"><button aria-label="Toggle navigation bar" aria-expanded="false" class="navbar__toggle clean-btn" type="button"><svg width="30" height="30" viewBox="0 0 30 30" aria-hidden="true"><path stroke="currentColor" stroke-linecap="round" stroke-miterlimit="10" stroke-width="2" d="M4 7h22M4 15h22M4 23h22"></path></svg></button><a class="navbar__brand" href="/en/"><div class="navbar__logo"><img src="/en/img/logo.png" alt="DataTP Cloud Logo" class="themedComponent_MeEW themedComponent--light_NBvQ"><img src="/en/img/logo.png" alt="DataTP Cloud Logo" class="themedComponent_MeEW themedComponent--dark_oqmI"></div></a><a class="navbar__item navbar__link" href="/en/docs/shared/user/system">User Guides<!-- --></a><a class="navbar__item navbar__link" href="/en/docs/shared/developer/SETUP">Developer<!-- --></a></div><div class="navbar__items navbar__items--right"><div class="navbar__item dropdown dropdown--hoverable dropdown--right"><a href="#" aria-haspopup="true" aria-expanded="false" role="button" class="navbar__link"><svg viewBox="0 0 24 24" width="20" height="20" aria-hidden="true" class="iconLanguage_e5gE"><path fill="currentColor" d="M12.87 15.07l-2.54-2.51.03-.03c1.74-1.94 2.98-4.17 3.71-6.53H17V4h-7V2H8v2H1v1.99h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z"></path></svg>English<!-- --></a><ul class="dropdown__menu"><li><a href="/docs/datatp-keycloak/developer/SETUP" target="_self" rel="noopener noreferrer" class="dropdown__link" lang="vi-VN">Tiếng Việt<!-- --></a></li><li><a href="/en/docs/datatp-keycloak/developer/SETUP" target="_self" rel="noopener noreferrer" class="dropdown__link dropdown__link--active" lang="en-GB">English<!-- --></a></li></ul></div><div class="toggle_VLGw colorModeToggle_FIch"><button class="clean-btn toggleButton_ZTlQ toggleButtonDisabled_NvtF" type="button" disabled="" title="Switch between dark and light mode (currently light mode)" aria-label="Switch between dark and light mode (currently light mode)" aria-live="polite"><svg viewBox="0 0 24 24" width="24" height="24" class="lightToggleIcon_GYGu"><path fill="currentColor" d="M12,9c1.65,0,3,1.35,3,3s-1.35,3-3,3s-3-1.35-3-3S10.35,9,12,9 M12,7c-2.76,0-5,2.24-5,5s2.24,5,5,5s5-2.24,5-5 S14.76,7,12,7L12,7z M2,13l2,0c0.55,0,1-0.45,1-1s-0.45-1-1-1l-2,0c-0.55,0-1,0.45-1,1S1.45,13,2,13z M20,13l2,0c0.55,0,1-0.45,1-1 s-0.45-1-1-1l-2,0c-0.55,0-1,0.45-1,1S19.45,13,20,13z M11,2v2c0,0.55,0.45,1,1,1s1-0.45,1-1V2c0-0.55-0.45-1-1-1S11,1.45,11,2z M11,20v2c0,0.55,0.45,1,1,1s1-0.45,1-1v-2c0-0.55-0.45-1-1-1C11.45,19,11,19.45,11,20z M5.99,4.58c-0.39-0.39-1.03-0.39-1.41,0 c-0.39,0.39-0.39,1.03,0,1.41l1.06,1.06c0.39,0.39,1.03,0.39,1.41,0s0.39-1.03,0-1.41L5.99,4.58z M18.36,16.95 c-0.39-0.39-1.03-0.39-1.41,0c-0.39,0.39-0.39,1.03,0,1.41l1.06,1.06c0.39,0.39,1.03,0.39,1.41,0c0.39-0.39,0.39-1.03,0-1.41 L18.36,16.95z M19.42,5.99c0.39-0.39,0.39-1.03,0-1.41c-0.39-0.39-1.03-0.39-1.41,0l-1.06,1.06c-0.39,0.39-0.39,1.03,0,1.41 s1.03,0.39,1.41,0L19.42,5.99z M7.05,18.36c0.39-0.39,0.39-1.03,0-1.41c-0.39-0.39-1.03-0.39-1.41,0l-1.06,1.06 c-0.39,0.39-0.39,1.03,0,1.41s1.03,0.39,1.41,0L7.05,18.36z"></path></svg><svg viewBox="0 0 24 24" width="24" height="24" class="darkToggleIcon_nQ2C"><path fill="currentColor" d="M9.37,5.51C9.19,6.15,9.1,6.82,9.1,7.5c0,4.08,3.32,7.4,7.4,7.4c0.68,0,1.35-0.09,1.99-0.27C17.45,17.19,14.93,19,12,19 c-3.86,0-7-3.14-7-7C5,9.07,6.81,6.55,9.37,5.51z M12,3c-4.97,0-9,4.03-9,9s4.03,9,9,9s9-4.03,9-9c0-0.46-0.04-0.92-0.1-1.36 c-0.98,1.37-2.58,2.26-4.4,2.26c-2.98,0-5.4-2.42-5.4-5.4c0-1.81,0.89-3.42,2.26-4.4C12.92,3.04,12.46,3,12,3L12,3z"></path></svg></button></div><div class="navbarSearchContainer_qR4N"></div></div></div><div role="presentation" class="navbar-sidebar__backdrop"></div></nav><div id="__docusaurus_skipToContent_fallback" class="main-wrapper mainWrapper__olq"><div class="docsWrapper_fuhk"><button aria-label="Scroll back to top" class="clean-btn theme-back-to-top-button backToTopButton_wMuW" type="button"></button><div class="docRoot_h7H2"><main class="docMainContainer_hm5G docMainContainerEnhanced_DgUX"><div class="container padding-top--md padding-bottom--lg"><div class="row"><div class="col docItemCol_RFeR"><div class="docItemContainer_oFeY"><article><div class="tocCollapsible_Ak7j theme-doc-toc-mobile tocMobile_SryH"><button type="button" class="clean-btn tocCollapsibleButton_TCXD">On this page<!-- --></button></div><div class="theme-doc-markdown markdown"><header><h1>Hướng dẫn cài đặt và sử dụng Keycloak (Windows &amp; macOS)</h1></header>
<!-- --><h2 class="anchor anchorWithStickyNavbar_V4cN" id="yêu-cầu-hệ-thống">Yêu cầu hệ thống<!-- --><a href="#yêu-cầu-hệ-thống" class="hash-link" aria-label="Direct link to Yêu cầu hệ thống" title="Direct link to Yêu cầu hệ thống">​</a></h2>
<!-- --><ul>
<!-- --><li>Java 17 (JDK)</li>
<!-- --><li>PostgresSql (CSDL)</li>
<!-- --><li>DBeaver</li>
<!-- --><li>Máy có internet</li>
<!-- --><li>Postman để test API</li>
<!-- --></ul>
<!-- --><hr>
<!-- --><h2 class="anchor anchorWithStickyNavbar_V4cN" id="bước-1-tải-keycloak">Bước 1: Tải Keycloak<!-- --><a href="#bước-1-tải-keycloak" class="hash-link" aria-label="Direct link to Bước 1: Tải Keycloak" title="Direct link to Bước 1: Tải Keycloak">​</a></h2>
<!-- --><p>Truy cập: <!-- --><a href="https://www.keycloak.org/downloads" target="_blank" rel="noopener noreferrer">https://www.keycloak.org/downloads</a></p>
<!-- --><p>Tải bản <!-- --><code>keycloak-24.x.x.zip</code> (Keycloak.X)<!-- --></p>
<!-- --><hr>
<!-- --><h2 class="anchor anchorWithStickyNavbar_V4cN" id="cài-đặt-trên-windows">Cài đặt trên Windows<!-- --><a href="#cài-đặt-trên-windows" class="hash-link" aria-label="Direct link to Cài đặt trên Windows" title="Direct link to Cài đặt trên Windows">​</a></h2>
<!-- --><ol>
<!-- --><li><strong>Giải nén:</strong></li>
<!-- --></ol>
<!-- --><div class="codeBlockContainer_APB6 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_LDm6"><pre tabindex="0" class="prism-code language-text codeBlock_sZcE thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_IULG"><span class="token-line" style="color:#393A34"><span class="token plain">C:\ahaysoft\code\keycloak</span><br></span></code></pre><div class="buttonGroup_ZaKO"><button type="button" aria-label="Copy code to clipboard" title="Copy" class="clean-btn"><span class="copyButtonIcons_VYo2" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_UQtN"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_fAgG"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<!-- --><ol>
<!-- --><li><strong>Chạy keycloak:</strong></li>
<!-- --></ol>
<!-- --><div class="codeBlockContainer_APB6 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_LDm6"><pre tabindex="0" class="prism-code language-text codeBlock_sZcE thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_IULG"><span class="token-line" style="color:#393A34"><span class="token plain">cd C:\ahaysoft\code\keycloak\bin</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">./kc.sh start-dev</span><br></span></code></pre><div class="buttonGroup_ZaKO"><button type="button" aria-label="Copy code to clipboard" title="Copy" class="clean-btn"><span class="copyButtonIcons_VYo2" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_UQtN"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_fAgG"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<!-- --><h2 class="anchor anchorWithStickyNavbar_V4cN" id="cài-đặt-trên-macos">Cài đặt trên MacOs<!-- --><a href="#cài-đặt-trên-macos" class="hash-link" aria-label="Direct link to Cài đặt trên MacOs" title="Direct link to Cài đặt trên MacOs">​</a></h2>
<!-- --><ol>
<!-- --><li><strong>Giải nén:</strong></li>
<!-- --></ol>
<!-- --><div class="codeBlockContainer_APB6 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_LDm6"><pre tabindex="0" class="prism-code language-text codeBlock_sZcE thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_IULG"><span class="token-line" style="color:#393A34"><span class="token plain">unzip keycloak-24.x.x.zip</span><br></span></code></pre><div class="buttonGroup_ZaKO"><button type="button" aria-label="Copy code to clipboard" title="Copy" class="clean-btn"><span class="copyButtonIcons_VYo2" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_UQtN"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_fAgG"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<!-- --><ol>
<!-- --><li><strong>Chạy keycloak:</strong></li>
<!-- --></ol>
<!-- --><div class="codeBlockContainer_APB6 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_LDm6"><pre tabindex="0" class="prism-code language-text codeBlock_sZcE thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_IULG"><span class="token-line" style="color:#393A34"><span class="token plain">cd keycloak-24.x.x/bin</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">./kc.sh start-dev</span><br></span></code></pre><div class="buttonGroup_ZaKO"><button type="button" aria-label="Copy code to clipboard" title="Copy" class="clean-btn"><span class="copyButtonIcons_VYo2" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_UQtN"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_fAgG"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<!-- --><h2 class="anchor anchorWithStickyNavbar_V4cN" id="cài-đặt-database">Cài đặt Database<!-- --><a href="#cài-đặt-database" class="hash-link" aria-label="Direct link to Cài đặt Database" title="Direct link to Cài đặt Database">​</a></h2>
<!-- --><div class="codeBlockContainer_APB6 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_LDm6"><pre tabindex="0" class="prism-code language-text codeBlock_sZcE thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_IULG"><span class="token-line" style="color:#393A34"><span class="token plain">./db.sh keycloak_db create-admin</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">./db.sh keycloak_db create-user</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">./db.sh keycloak_db new</span><br></span></code></pre><div class="buttonGroup_ZaKO"><button type="button" aria-label="Copy code to clipboard" title="Copy" class="clean-btn"><span class="copyButtonIcons_VYo2" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_UQtN"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_fAgG"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<!-- --><h2 class="anchor anchorWithStickyNavbar_V4cN" id="cài-đặt-môi-trường-chạy">Cài đặt môi trường chạy<!-- --><a href="#cài-đặt-môi-trường-chạy" class="hash-link" aria-label="Direct link to Cài đặt môi trường chạy" title="Direct link to Cài đặt môi trường chạy">​</a></h2>
<!-- --><h3 class="anchor anchorWithStickyNavbar_V4cN" id="1-cập-nhật-file-cấu-hình-keycloakconf">1. Cập nhật file cấu hình <!-- --><code>keycloak.conf</code><a href="#1-cập-nhật-file-cấu-hình-keycloakconf" class="hash-link" aria-label="Direct link to 1-cập-nhật-file-cấu-hình-keycloakconf" title="Direct link to 1-cập-nhật-file-cấu-hình-keycloakconf">​</a></h3>
<!-- --><p>Di chuyển vào thư mục chứa file cấu hình:</p>
<!-- --><div class="codeBlockContainer_APB6 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_LDm6"><pre tabindex="0" class="prism-code language-text codeBlock_sZcE thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_IULG"><span class="token-line" style="color:#393A34"><span class="token plain">cd keycloak-24.x.x/conf</span><br></span></code></pre><div class="buttonGroup_ZaKO"><button type="button" aria-label="Copy code to clipboard" title="Copy" class="clean-btn"><span class="copyButtonIcons_VYo2" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_UQtN"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_fAgG"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<!-- --><p>Mở file <!-- --><code>keycloak.conf</code> và cập nhật nội dung như sau:<!-- --></p>
<!-- --><div class="language-properties codeBlockContainer_APB6 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_LDm6"><pre tabindex="0" class="prism-code language-properties codeBlock_sZcE thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_IULG"><span class="token-line" style="color:#393A34"><span class="token plain"># Basic settings for running in production. Change accordingly before deploying the server.</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"># Database</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">db=postgres</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">db-username=keycloak</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">db-password=keycloak</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">db-url=***************************************</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"># Observability</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">health-enabled=true</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">metrics-enabled=true</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"># HTTP</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">http-enabled=true</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"># Proxy và hostname</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">proxy=edge</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">hostname-strict=false</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">hostname-strict-https=false</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">hostname=keycloak</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"># Nếu cần HTTPS:</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"># https-certificate-file=${kc.home.dir}/conf/server.crt.pem</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"># https-certificate-key-file=${kc.home.dir}/conf/server.key.pem</span><br></span></code></pre><div class="buttonGroup_ZaKO"><button type="button" aria-label="Copy code to clipboard" title="Copy" class="clean-btn"><span class="copyButtonIcons_VYo2" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_UQtN"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_fAgG"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<!-- --><blockquote>
<!-- --><p><strong>💡 Ghi chú:</strong></p>
<!-- --><ul>
<!-- --><li><code>db-url</code> cần trỏ đúng đến database PostgresSQL đã khởi tạo sẵn.<!-- --></li>
<!-- --><li><code>proxy=edge</code> được dùng khi Keycloak chạy sau reverse proxy như Nginx, Traefik,...<!-- --></li>
<!-- --><li><code>hostname-strict=false</code> giúp Keycloak hoạt động linh hoạt hơn trong môi trường dev hoặc mạng nội bộ.<!-- --></li>
<!-- --><li>Nếu chưa cấu hình HTTPS, có thể để nguyên phần <!-- --><code>https-certificate-*</code> như đang comment.<!-- --></li>
<!-- --></ul>
<!-- --></blockquote>
<!-- --><h2 class="anchor anchorWithStickyNavbar_V4cN" id="2-cấu-hình-spring-để-kết-nối-với-keycloak">2. Cấu hình Spring để kết nối với Keycloak<!-- --><a href="#2-cấu-hình-spring-để-kết-nối-với-keycloak" class="hash-link" aria-label="Direct link to 2. Cấu hình Spring để kết nối với Keycloak" title="Direct link to 2. Cấu hình Spring để kết nối với Keycloak">​</a></h2>
<!-- --><p>Cập nhập bổ sung file <!-- --><code>application-{profile}.yml</code> thêm cấu hình sau:<!-- --></p>
<!-- --><div class="language-yaml codeBlockContainer_APB6 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_LDm6"><pre tabindex="0" class="prism-code language-yaml codeBlock_sZcE thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_IULG"><span class="token-line" style="color:#393A34"><span class="token key atrule" style="color:#00a4db">spring</span><span class="token punctuation" style="color:#393A34">:</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  </span><span class="token key atrule" style="color:#00a4db">security</span><span class="token punctuation" style="color:#393A34">:</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">    </span><span class="token key atrule" style="color:#00a4db">oauth2</span><span class="token punctuation" style="color:#393A34">:</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">      </span><span class="token key atrule" style="color:#00a4db">resourceserver</span><span class="token punctuation" style="color:#393A34">:</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">        </span><span class="token key atrule" style="color:#00a4db">jwt</span><span class="token punctuation" style="color:#393A34">:</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">          </span><span class="token key atrule" style="color:#00a4db">issuer-uri</span><span class="token punctuation" style="color:#393A34">:</span><span class="token plain"> http</span><span class="token punctuation" style="color:#393A34">:</span><span class="token plain">//keycloak</span><span class="token punctuation" style="color:#393A34">:</span><span class="token plain">8080/realms/</span><span class="token punctuation" style="color:#393A34">{</span><span class="token plain">realm_name</span><span class="token punctuation" style="color:#393A34">}</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain" style="display:inline-block"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain"></span><span class="token key atrule" style="color:#00a4db">keycloak</span><span class="token punctuation" style="color:#393A34">:</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  </span><span class="token key atrule" style="color:#00a4db">server-url</span><span class="token punctuation" style="color:#393A34">:</span><span class="token plain"> http</span><span class="token punctuation" style="color:#393A34">:</span><span class="token plain">//keycloak</span><span class="token punctuation" style="color:#393A34">:</span><span class="token number" style="color:#36acaa">8080</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  </span><span class="token key atrule" style="color:#00a4db">realm</span><span class="token punctuation" style="color:#393A34">:</span><span class="token plain"> </span><span class="token punctuation" style="color:#393A34">{</span><span class="token plain">realm_name</span><span class="token punctuation" style="color:#393A34">}</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  </span><span class="token key atrule" style="color:#00a4db">scope</span><span class="token punctuation" style="color:#393A34">:</span><span class="token plain"> openid profile email</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  </span><span class="token key atrule" style="color:#00a4db">client-id</span><span class="token punctuation" style="color:#393A34">:</span><span class="token plain"> spring</span><span class="token punctuation" style="color:#393A34">-</span><span class="token plain">client</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  </span><span class="token key atrule" style="color:#00a4db">client-secret</span><span class="token punctuation" style="color:#393A34">:</span><span class="token plain"> </span><span class="token punctuation" style="color:#393A34">{</span><span class="token plain">client</span><span class="token punctuation" style="color:#393A34">-</span><span class="token plain">secret</span><span class="token punctuation" style="color:#393A34">}</span><span class="token plain"></span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  </span><span class="token key atrule" style="color:#00a4db">token-uri</span><span class="token punctuation" style="color:#393A34">:</span><span class="token plain"> http</span><span class="token punctuation" style="color:#393A34">:</span><span class="token plain">//keycloak</span><span class="token punctuation" style="color:#393A34">:</span><span class="token plain">8080/realms/</span><span class="token punctuation" style="color:#393A34">{</span><span class="token plain">realm_name</span><span class="token punctuation" style="color:#393A34">}</span><span class="token plain">/protocol/openid</span><span class="token punctuation" style="color:#393A34">-</span><span class="token plain">connect/token</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">  </span><span class="token key atrule" style="color:#00a4db">user-info-uri</span><span class="token punctuation" style="color:#393A34">:</span><span class="token plain"> http</span><span class="token punctuation" style="color:#393A34">:</span><span class="token plain">//keycloak</span><span class="token punctuation" style="color:#393A34">:</span><span class="token plain">8080/realms/</span><span class="token punctuation" style="color:#393A34">{</span><span class="token plain">realm_name</span><span class="token punctuation" style="color:#393A34">}</span><span class="token plain">/protocol/openid</span><span class="token punctuation" style="color:#393A34">-</span><span class="token plain">connect/userinfo</span><br></span></code></pre><div class="buttonGroup_ZaKO"><button type="button" aria-label="Copy code to clipboard" title="Copy" class="clean-btn"><span class="copyButtonIcons_VYo2" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_UQtN"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_fAgG"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<!-- --><blockquote>
<!-- --><p><strong>💡 Lưu ý:</strong></p>
<!-- --><ul>
<!-- --><li><code>issuer-uri</code> là đường dẫn tới realm trong Keycloak, dùng để xác minh JWT.<!-- --></li>
<!-- --><li><code>client-id</code> và <!-- --><code>client-secret</code> phải trùng với client cấu hình trong realm của Keycloak.<!-- --></li>
<!-- --><li>Các endpoint như <!-- --><code>token-uri</code> và <!-- --><code>user-info-uri</code> dùng để lấy token và thông tin người dùng.<!-- --></li>
<!-- --><li><code>keycloak</code> block là custom config, có thể dùng <!-- --><code>@ConfigurationProperties(&quot;keycloak&quot;)</code> để ánh xạ trong code.<!-- --></li>
<!-- --></ul>
<!-- --></blockquote>
<!-- --><h2 class="anchor anchorWithStickyNavbar_V4cN" id="3-cấu-hình-file-hosts">3. Cấu hình file <!-- --><code>hosts</code><a href="#3-cấu-hình-file-hosts" class="hash-link" aria-label="Direct link to 3-cấu-hình-file-hosts" title="Direct link to 3-cấu-hình-file-hosts">​</a></h2>
<!-- --><p>Để Spring Boot có thể gọi đúng địa chỉ <!-- --><code>http://keycloak:8080</code>, cần thêm cấu hình DNS tạm thời vào file <!-- --><code>/etc/hosts</code> (trên Linux/macOS) hoặc <!-- --><code>C:\Windows\System32\drivers\etc\hosts</code> (trên Windows).<!-- --></p>
<!-- --><p>Thêm dòng sau vào cuối file <!-- --><code>hosts</code>:<!-- --></p>
<!-- --><div class="codeBlockContainer_APB6 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_LDm6"><pre tabindex="0" class="prism-code language-text codeBlock_sZcE thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_IULG"><span class="token-line" style="color:#393A34"><span class="token plain">127.0.0.1   keycloak</span><br></span></code></pre><div class="buttonGroup_ZaKO"><button type="button" aria-label="Copy code to clipboard" title="Copy" class="clean-btn"><span class="copyButtonIcons_VYo2" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_UQtN"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_fAgG"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<!-- --><blockquote>
<!-- --><p><strong>💡 Lưu ý:</strong></p>
<!-- --><ul>
<!-- --><li>Dòng trên giúp ánh xạ tên miền <!-- --><code>keycloak</code> về <!-- --><code>localhost</code>.<!-- --></li>
<!-- --><li>Nếu chạy Keycloak trong Docker Compose, có thể để Spring Boot dùng <!-- --><code>keycloak</code> làm hostname, vì Docker Compose tự tạo DNS nội bộ.<!-- --></li>
<!-- --><li>Nếu chạy trên môi trường thật (VD: staging, production), cần thay <!-- --><code>keycloak</code> bằng domain thực tế hoặc IP của Keycloak server.<!-- --></li>
<!-- --></ul>
<!-- --></blockquote>
<!-- --><hr>
<!-- --><h2 class="anchor anchorWithStickyNavbar_V4cN" id="tạo-admin-user">Tạo Admin User<!-- --><a href="#tạo-admin-user" class="hash-link" aria-label="Direct link to Tạo Admin User" title="Direct link to Tạo Admin User">​</a></h2>
<!-- --><p>Lần đầu chạy, Keycloak yêu cầu tạo admin user:</p>
<!-- --><div class="codeBlockContainer_APB6 theme-code-block" style="--prism-color:#393A34;--prism-background-color:#f6f8fa"><div class="codeBlockContent_LDm6"><pre tabindex="0" class="prism-code language-text codeBlock_sZcE thin-scrollbar" style="color:#393A34;background-color:#f6f8fa"><code class="codeBlockLines_IULG"><span class="token-line" style="color:#393A34"><span class="token plain">Username: admin</span><br></span><span class="token-line" style="color:#393A34"><span class="token plain">Password: admin123</span><br></span></code></pre><div class="buttonGroup_ZaKO"><button type="button" aria-label="Copy code to clipboard" title="Copy" class="clean-btn"><span class="copyButtonIcons_VYo2" aria-hidden="true"><svg viewBox="0 0 24 24" class="copyButtonIcon_UQtN"><path fill="currentColor" d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"></path></svg><svg viewBox="0 0 24 24" class="copyButtonSuccessIcon_fAgG"><path fill="currentColor" d="M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z"></path></svg></span></button></div></div></div>
<!-- --><p>Truy cập: <!-- --><a href="http://localhost:8080" target="_blank" rel="noopener noreferrer">http://localhost:8080</a> → bấm <!-- --><strong>&quot;Administration Console&quot;</strong></p>
<!-- --><hr>
<!-- --><h2 class="anchor anchorWithStickyNavbar_V4cN" id="tạo-realm-mới">Tạo Realm mới<!-- --><a href="#tạo-realm-mới" class="hash-link" aria-label="Direct link to Tạo Realm mới" title="Direct link to Tạo Realm mới">​</a></h2>
<!-- --><ol>
<!-- --><li>Vào admin console → menu trái → chọn <!-- --><strong>Realm selector</strong> → <!-- --><strong>Create Realm</strong></li>
<!-- --><li>Nhập:<!-- -->
<!-- --><ul>
<!-- --><li>Realm Name: <!-- --><code>tên-reaml</code></li>
<!-- --><li>Save</li>
<!-- --></ul>
<!-- --></li>
<!-- --></ol>
<!-- --><hr>
<!-- --><h2 class="anchor anchorWithStickyNavbar_V4cN" id="tạo-role">Tạo Role<!-- --><a href="#tạo-role" class="hash-link" aria-label="Direct link to Tạo Role" title="Direct link to Tạo Role">​</a></h2>
<!-- --><ol>
<!-- --><li>Vào <!-- --><strong>Realm Settings &gt; Roles</strong></li>
<!-- --><li>Nhấn <!-- --><strong>Create Role</strong>
<!-- --><ul>
<!-- --><li>Role name: <!-- --><code>tên-role</code></li>
<!-- --><li>Save</li>
<!-- --></ul>
<!-- --></li>
<!-- --></ol>
<!-- --><p>Lặp lại nếu muốn thêm role <!-- --><code>admin</code>, <!-- --><code>manager</code>, v.v.<!-- --></p>
<!-- --><hr>
<!-- --><h2 class="anchor anchorWithStickyNavbar_V4cN" id="tạo-user-và-gán-role">Tạo User và gán Role<!-- --><a href="#tạo-user-và-gán-role" class="hash-link" aria-label="Direct link to Tạo User và gán Role" title="Direct link to Tạo User và gán Role">​</a></h2>
<!-- --><ol>
<!-- --><li>
<!-- --><p>Menu bên trái → <!-- --><strong>Users → Add user</strong></p>
<!-- --><ul>
<!-- --><li>Username: <!-- --><code>testuser</code></li>
<!-- --><li>Email, tên: nhập thông tin nếu có</li>
<!-- --><li>Save</li>
<!-- --></ul>
<!-- --></li>
<!-- --><li>
<!-- --><p>Tab <!-- --><strong>Credentials</strong></p>
<!-- --><ul>
<!-- --><li>Set password: <!-- --><code>123456</code></li>
<!-- --><li>Turn off &quot;Temporary&quot;, rồi Save</li>
<!-- --></ul>
<!-- --></li>
<!-- --><li>
<!-- --><p>Tab <!-- --><strong>Role Mappings</strong></p>
<!-- --><ul>
<!-- --><li>Chọn <!-- --><code>Available Roles</code>: chọn <!-- --><code>user</code> → nhấn <!-- --><strong>Add selected</strong></li>
<!-- --></ul>
<!-- --></li>
<!-- --></ol>
<!-- --><hr>
<!-- --><h2 class="anchor anchorWithStickyNavbar_V4cN" id="tạo-client-để-cấp-token">Tạo Client để cấp token<!-- --><a href="#tạo-client-để-cấp-token" class="hash-link" aria-label="Direct link to Tạo Client để cấp token" title="Direct link to Tạo Client để cấp token">​</a></h2>
<!-- --><ol>
<!-- --><li>
<!-- --><p>Menu trái → <!-- --><strong>Clients → Create client</strong></p>
<!-- --><ul>
<!-- --><li>Client ID: <!-- --><code>my-client</code></li>
<!-- --><li>Client type: <!-- --><code>public</code></li>
<!-- --><li>Root URL: <!-- --><code>http://localhost:3000</code> (hoặc bỏ trống nếu không cần UI)<!-- --></li>
<!-- --></ul>
<!-- --></li>
<!-- --><li>
<!-- --><p>Save → Tab <!-- --><strong>Settings</strong>:<!-- --></p>
<!-- --><ul>
<!-- --><li>Standard Flow Enabled: ✅</li>
<!-- --><li>Direct Access Grants Enabled: ✅ (quan trọng để lấy token qua Postman)</li>
<!-- --><li>Save</li>
<!-- --></ul>
<!-- --></li>
<!-- --><li>
<!-- --><p>Vào <!-- --><strong>Clients → Create client</strong></p>
<!-- --><ul>
<!-- --><li><strong>Client ID</strong>: <!-- --><code>my-client</code></li>
<!-- --><li><strong>Client type</strong>: <!-- --><code>confidential</code></li>
<!-- --><li><strong>Root URL</strong>: để trống nếu không cần giao diện frontend<!-- --></li>
<!-- --></ul>
<!-- --></li>
<!-- --><li>
<!-- --><p>Nhấn <!-- --><strong>Save</strong></p>
<!-- --></li>
<!-- --><li>
<!-- --><p>Sau khi lưu → vào tab <!-- --><strong>Credentials</strong></p>
<!-- --><ul>
<!-- --><li>Mục <!-- --><strong>Client Secret</strong> sẽ xuất hiện<!-- --></li>
<!-- --><li>Nhấn icon con mắt để hiện secret</li>
<!-- --><li>Copy giá trị <!-- --><code>client_secret</code> để sử dụng trong các request<!-- --></li>
<!-- --></ul>
<!-- --></li>
<!-- --></ol>
<!-- --><hr>
<!-- --><h2 class="anchor anchorWithStickyNavbar_V4cN" id="ghi-chú">Ghi chú<!-- --><a href="#ghi-chú" class="hash-link" aria-label="Direct link to Ghi chú" title="Direct link to Ghi chú">​</a></h2>
<!-- --><ul>
<!-- --><li>Realm giống như 1 &quot;namespace&quot;, dùng để tách biệt hệ thống đăng nhập</li>
<!-- --><li>Client dùng để cấp token cho ứng dụng cụ thể</li>
<!-- --><li>Role là phân quyền logic, cần validate ở backend</li>
<!-- --><li>Backend dùng Spring Boot hoặc Express có thể đọc token, giải mã role</li>
<!-- --><li>Nếu dùng Windows mà export/import lỗi, nên dùng WSL hoặc Docker</li>
<!-- --></ul>
<!-- --><hr>
<!-- --><h2 class="anchor anchorWithStickyNavbar_V4cN" id="-tài-liệu-tham-khảo">📚 Tài liệu tham khảo<!-- --><a href="#-tài-liệu-tham-khảo" class="hash-link" aria-label="Direct link to 📚 Tài liệu tham khảo" title="Direct link to 📚 Tài liệu tham khảo">​</a></h2>
<!-- --><ul>
<!-- --><li><a href="https://www.keycloak.org/documentation" target="_blank" rel="noopener noreferrer">https://www.keycloak.org/documentation</a></li>
<!-- --><li><a href="https://www.keycloak.org/getting-started" target="_blank" rel="noopener noreferrer">https://www.keycloak.org/getting-started</a></li>
<!-- --><li><a href="https://www.keycloak.org/docs/latest/securing_apps/" target="_blank" rel="noopener noreferrer">https://www.keycloak.org/docs/latest/securing_apps/</a></li>
<!-- --></ul></div></article><nav class="pagination-nav docusaurus-mt-lg" aria-label="Docs pages"></nav></div></div><div class="col col--3"><div class="tableOfContents_M5tG thin-scrollbar theme-doc-toc-desktop"><ul class="table-of-contents table-of-contents__left-border"><li><a href="#yêu-cầu-hệ-thống" class="table-of-contents__link toc-highlight">Yêu cầu hệ thống</a></li><li><a href="#bước-1-tải-keycloak" class="table-of-contents__link toc-highlight">Bước 1: Tải Keycloak</a></li><li><a href="#cài-đặt-trên-windows" class="table-of-contents__link toc-highlight">Cài đặt trên Windows</a></li><li><a href="#cài-đặt-trên-macos" class="table-of-contents__link toc-highlight">Cài đặt trên MacOs</a></li><li><a href="#cài-đặt-database" class="table-of-contents__link toc-highlight">Cài đặt Database</a></li><li><a href="#cài-đặt-môi-trường-chạy" class="table-of-contents__link toc-highlight">Cài đặt môi trường chạy</a><ul><li><a href="#1-cập-nhật-file-cấu-hình-keycloakconf" class="table-of-contents__link toc-highlight">1. Cập nhật file cấu hình <code>keycloak.conf</code></a></li></ul></li><li><a href="#2-cấu-hình-spring-để-kết-nối-với-keycloak" class="table-of-contents__link toc-highlight">2. Cấu hình Spring để kết nối với Keycloak</a></li><li><a href="#3-cấu-hình-file-hosts" class="table-of-contents__link toc-highlight">3. Cấu hình file <code>hosts</code></a></li><li><a href="#tạo-admin-user" class="table-of-contents__link toc-highlight">Tạo Admin User</a></li><li><a href="#tạo-realm-mới" class="table-of-contents__link toc-highlight">Tạo Realm mới</a></li><li><a href="#tạo-role" class="table-of-contents__link toc-highlight">Tạo Role</a></li><li><a href="#tạo-user-và-gán-role" class="table-of-contents__link toc-highlight">Tạo User và gán Role</a></li><li><a href="#tạo-client-để-cấp-token" class="table-of-contents__link toc-highlight">Tạo Client để cấp token</a></li><li><a href="#ghi-chú" class="table-of-contents__link toc-highlight">Ghi chú</a></li><li><a href="#-tài-liệu-tham-khảo" class="table-of-contents__link toc-highlight">📚 Tài liệu tham khảo</a></li></ul></div></div></div></div></main></div></div></div><footer class="footer footer--dark"><div class="container container-fluid"><div class="footer__bottom text--center"><div class="footer__copyright">Copyright © 2025 DataTP Cloud.</div></div></div></footer></div>
</body>
</html>