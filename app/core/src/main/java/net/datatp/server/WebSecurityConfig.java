package net.datatp.server;

import java.util.List;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;

import net.datatp.util.ds.Arrays;

@Configuration
@EnableWebSecurity
public class WebSecurityConfig {
  
  @Bean
  SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
    
    http
    .headers(headers -> headers
      .frameOptions(frameOptions -> frameOptions.disable()
      )
    );
    
    http
    .authorizeHttpRequests((requests) -> {
      requests
      .requestMatchers("/**", "/get/**", "/rest/**")
      .permitAll();
      
      requests
      .anyRequest()
      .authenticated();
    });
    
    
//    http
//    .formLogin((form) -> {
//      form
//      .loginPage("/login")
//      .permitAll();
//    });
    
//    http
//    .logout((logout) -> logout.permitAll());
    
    http.csrf((csrf) -> {
      csrf.disable(); 
    });
    
    //http.sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.IF_REQUIRED));
    return http.build();
  }


  @Bean
  CorsFilter corsFilter() {
    CorsConfiguration config = new CorsConfiguration();
    //config.setMaxAge(Duration.ofMinutes(60));
    //For chrome browser cors fetch can only send the cookie If the ui host and the server have the same domain
    //Ex: http://ui.erp.datatp.net, http://erp.datatp.net
    List<String> allowOrigins = Arrays.asList(
      "https://beelogistics.cloud",
      "https://www.beelogistics.cloud",
      "https://apps.beelogistics.cloud",
        
      "https://datatp-1.dev.datatp.net",
      "https://datatp-1-proxy.dev.datatp.net",
      "https://datatp-1-uiproxy.dev.datatp.net",
      "wss://datatp-1.dev.datatp.net",
      "wss://datatp-1-proxy.dev.datatp.net",
      "wss://datatp-1-uiproxy.dev.datatp.net",
      
      "https://datatp-2.dev.datatp.net",
      "https://datatp-2-proxy.dev.datatp.net",
      "https://datatp-2-uiproxy.dev.datatp.net",
      
      "https://datatp-3.dev.datatp.net",
      "https://datatp-3-proxy.dev.datatp.net",
      "https://datatp-3-uiproxy.dev.datatp.net",
      
      "http://dev.datatp.local",
      "http://platform.datatp-prod.svc",
      "http://localhost:8069",
      "http://localhost:8169",
      "http://localhost:3000",
      "http://localhost:7080",
      "http://localhost:5005",
      "chrome-extension://bnfagclpmnhplpnhfcfobahknkafbmhb"
      
    );
    config.setAllowedOrigins(allowOrigins);
    config.addAllowedHeader("*");
    config.addAllowedMethod("*");
    config.setAllowCredentials(true);
    UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
    source.registerCorsConfiguration("/**", config);
    return new CorsFilter(source);
  }
}