package migration.server.hr

import org.slf4j.Logger
import org.slf4j.LoggerFactory

import lib.data.RunnableReporter
import lib.data.ServiceRunnable
import lib.data.ServiceRunnableSet
import net.datatp.module.account.AccountService
import net.datatp.module.data.db.entity.ICompany
import net.datatp.module.groovy.ServerScriptContext
import net.datatp.module.hr.EmployeeReadLogic
import net.datatp.module.hr.entity.Employee
import net.datatp.module.kpi.KpiLogic
import net.datatp.module.kpi.KpiService
import net.datatp.module.kpi.KpiTemplateLogic
import net.datatp.module.kpi.entity.Kpi
import net.datatp.module.kpi.entity.KpiCalculateAlgorithm
import net.datatp.module.kpi.entity.KpiItem
import net.datatp.module.kpi.entity.KpiObjectiveType
import net.datatp.module.kpi.entity.KpiTemplate
import net.datatp.module.kpi.entity.KpiTemplateItem
import net.datatp.security.client.ClientContext
import net.datatp.util.text.DateUtil

class InitKpiTemplateDataSet extends ServiceRunnableSet {
  private static String label = "------------Init Kpi Data--------------"
  private static final Logger logger = LoggerFactory.getLogger(InitKpiTemplateDataSet.class);

  public InitKpiTemplateDataSet() {
    super(label);

    ServiceRunnable dataInit = new ServiceRunnable(label) {
      @Override
      public void run(RunnableReporter reporter, ServerScriptContext scriptCtx) {
        ClientContext client     = scriptCtx.getClientCtx();
        ICompany company = scriptCtx.getCompany();
        KpiTemplateLogic templateLogic = scriptCtx.getService(KpiTemplateLogic.class);
        KpiService kpiService = scriptCtx.getService(KpiService.class);
        AccountService accountService = scriptCtx.getService(AccountService.class);
        KpiLogic kpiLogic = scriptCtx.getService(KpiLogic.class);
        KpiService KpiService = scriptCtx.getService(KpiService.class);
        EmployeeReadLogic employeeLogic = scriptCtx.getService(EmployeeReadLogic.class);
        
        List<KpiTemplate> templates = templateLogic.findKpiTemplateByCompanyId(client);
        
        for (KpiTemplate templateQ1 : templates) {
          KpiTemplate templateQ2 = new KpiTemplate();
          templateQ2.setCompanyBranch(templateQ1.getCompanyBranch());
          templateQ2.setDepartment(templateQ1.getDepartment());
          templateQ2.setWorkplace(templateQ1.getWorkplace());
          templateQ2.setJobTitle(templateQ1.getJobTitle());
          templateQ2.setJobCode(templateQ1.getJobCode());
          templateQ2.setMilestone("2025-H01-Q02")
          templateQ2.setMilestoneLabel("Quý 2 2025")
          templateQ2.setFromDate(DateUtil.parseLocalDateTime("01/04/2025@00:00:00"));
          templateQ2.setToDate(DateUtil.parseLocalDateTime("30/06/2025@23:59:59"));
          templateQ2.setLabel("${templateQ1.getJobTitle()} - ${templateQ1.getJobCode()}");
          templateQ2.setManagerAccountId(templateQ1.getManagerAccountId());
          templateQ2.setManagerFullName(templateQ1.getManagerFullName());
          templateQ2.setLeaderAccountId(templateQ1.getLeaderAccountId());
          templateQ2.setLeaderFullName(templateQ1.getLeaderFullName());
          templateQ2.set(client, company);
          println("Create Template 2025-H01-Q02 " + templateQ2.getJobCode());
          templateQ2 = templateLogic.getTmplRepo().save(templateQ2);
          
          List<KpiTemplateItem> templateItemsQ1 = templateLogic.findByKpiTemplateItemByTemplateId(client, company, templateQ1.getId());
          Map<Long, Long> mapTemplateItemQ2Id = new HashMap();
          createTemplateWorkItems(scriptCtx, templateQ2, templateItemsQ1, mapTemplateItemQ2Id);
          
          List<Kpi> q1Kpis = kpiLogic.findKpiByTemplateId(client, company, templateQ1.getId());
          for (Kpi kpiQ1 : q1Kpis) {
            Employee employee = employeeLogic.getByAccount(client, company, kpiQ1.getEmployeeAccountId());
            if (Objects.isNull(employee)) continue;
            println("Create KPI for employee " + kpiQ1.getEmployeeCode() + " with template " + templateQ2.getJobCode());
            Kpi kpiQ2 = new Kpi();
            kpiQ2.setJobCode(kpiQ1.getJobCode());
            kpiQ2.setJobTitle(kpiQ1.getJobTitle());
            kpiQ2.setDepartment(kpiQ1.getDepartment());
            kpiQ2.setWorkplace(kpiQ1.getWorkplace());
            kpiQ2.setCompanyBranch(kpiQ1.getCompanyBranch());
            kpiQ2.setTemplateId(templateQ2.getId());
            kpiQ2.setMilestone(templateQ2.getMilestone());
            kpiQ2.setMilestoneLabel(templateQ2.getMilestoneLabel());
            kpiQ2.setFromDate(templateQ2.getFromDate());
            kpiQ2.setToDate(templateQ2.getToDate());
            kpiQ2.setLabel(kpiQ1.getLabel());
            kpiQ2.setManagerAccountId(kpiQ1.getManagerAccountId());
            kpiQ2.setManagerFullName(kpiQ1.getManagerFullName());
            kpiQ2.setLeaderAccountId(kpiQ1.getLeaderAccountId());
            kpiQ2.setLeaderFullName(kpiQ1.getLeaderFullName());
            kpiQ2.setEmployeeAccountId(kpiQ1.getEmployeeAccountId());
            kpiQ2.setEmployeeFullName(kpiQ1.getEmployeeFullName());
            kpiQ2.setEmployeeCode(kpiQ1.getEmployeeCode());
            kpiQ2 = kpiLogic.saveKpi(client, company, kpiQ2);
            
            List<KpiItem> itemsQ1 = kpiLogic.findKpiItemByKpiId(client, company, kpiQ1.getId());
            for(KpiItem itemQ1 : itemsQ1) {
              KpiItem item = new KpiItem();
              item.setKpiId(kpiQ2.getId());
              item.setKpiTemplateItemId(mapTemplateItemQ2Id.get(itemQ1.getKpiTemplateItemId()));
              item.copyBase(itemQ1);
              item.clearIds();
              item.set(client, company);
              if (item.isNew()) {
                String monthId = DateUtil.asCompactMonthId(item.getCreatedTime());
                item.withStroragePath("kpi/" + monthId + "/" + item.getKpiId() + "/" + item.getId());
              }
              kpiLogic.getKpiItemRepo().save(item);
            }
          }
        }
      }
      
      private void createTemplateWorkItems(ServerScriptContext scriptCtx, KpiTemplate template, List<KpiTemplateItem> rootItems, Map<Long, Long> mapTemplateItemId) {
        ClientContext client = scriptCtx.getClientCtx();
        ICompany company = scriptCtx.getCompany();
        KpiTemplateLogic templateLogic = scriptCtx.getService(KpiTemplateLogic.class);

        String jobCode = template.getJobCode();
        for (KpiTemplateItem root : rootItems) {
          KpiTemplateItem item = new KpiTemplateItem();
          item.setJobCode(jobCode);
          item.setLabel(root.getLabel());
          item.setUnit(root.getUnit());
          item.setObjectiveType(root.getObjectiveType());
          item.setCalculateAlgorithm(root.getCalculateAlgorithm());
          item.setContributionWeight(root.getContributionWeight());
          if (item.getCalculateAlgorithm() == KpiCalculateAlgorithm.RATIO_BASED) {
            item.setMeasurementMethod("(%) Hoàn Thành = Thực hiện/Kế hoạch.\r\nKết quả KPI = (%) hoàn thành x Trọng số.");
          } else {
            item.setMeasurementMethod("(%) Hoàn Thành:\r\n  - Thực hiện <= Kế hoạch: 100%\r\n  - Thực hiện > Kế hoạch:    0%\r\nKết quả KPI = (%) hoàn thành x 100% x Trọng số.");
          }
          
          item.setTargetValue(root.getTargetValue());
          item.setTemplateId(template.getId());
          item = templateLogic.saveKpiTemplateItem(client, company, item);
          mapTemplateItemId.put(root.getId(), item.getId());
        }
      }
    };
    addRunnable(dataInit);
  };
}

ServerScriptContext scriptCtx = (ServerScriptContext) CONTEXT;
RunnableReporter reporter  = new RunnableReporter("service", "latest")

InitKpiTemplateDataSet dataInitializer = new InitKpiTemplateDataSet();
dataInitializer.run(reporter, scriptCtx);

return "DONE!!!"