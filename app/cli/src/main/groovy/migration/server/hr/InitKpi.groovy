package migration.server.hr

import org.slf4j.Logger
import org.slf4j.LoggerFactory

import lib.data.RunnableReporter
import lib.data.ServiceRunnable
import lib.data.ServiceRunnableSet
import net.datatp.module.account.AccountService
import net.datatp.module.data.db.entity.ICompany
import net.datatp.module.groovy.ServerScriptContext
import net.datatp.module.hr.EmployeeReadLogic
import net.datatp.module.hr.entity.Employee
import net.datatp.module.kpi.KpiLogic
import net.datatp.module.kpi.KpiService
import net.datatp.module.kpi.KpiTemplateLogic
import net.datatp.module.kpi.entity.Kpi
import net.datatp.module.kpi.entity.KpiTemplate
import net.datatp.security.client.ClientContext
import net.datatp.util.ds.Objects

class KpiDataInitializer extends ServiceRunnableSet {
  private static String label = "------------Init Kpi Data--------------"
  private static final Logger logger = LoggerFactory.getLogger(InitKpi.class);

  public KpiDataInitializer() {
    super(label);

    ServiceRunnable dataInit = new ServiceRunnable(label) {
      @Override
      public void run(RunnableReporter reporter, ServerScriptContext scriptCtx) {
        ClientContext client = scriptCtx.getClientCtx();
        ICompany company = scriptCtx.getCompany();
        KpiTemplateLogic templateLogic = scriptCtx.getService(KpiTemplateLogic.class);
        KpiLogic kpiLogic = scriptCtx.getService(KpiLogic.class);
        KpiService KpiService = scriptCtx.getService(KpiService.class);
        EmployeeReadLogic employeeLogic = scriptCtx.getService(EmployeeReadLogic.class);
        
        List<KpiTemplate> templatesQ1 = templateLogic.getTmplRepo().findByMilestone(company.getId(), "2025-H01-Q01");
        
        for (KpiTemplate templateQ1 : templatesQ1) {
          KpiTemplate templateQ2 = templateLogic.getKpiTemplateByJobCode(client, company, "2025-H01-Q02", templateQ1.getJobCode());
          List<Kpi> kpisQ1 = kpiLogic.findKpiByTemplateId(client, company, templateQ1.getId());
          for (Kpi kpiQ1 : kpisQ1) {
            Employee employee = employeeLogic.getByAccount(client, company, kpiQ1.getEmployeeAccountId());
            if (Objects.isNull(employee)) continue;
            println("Create KPI for employee " + kpiQ1.getEmployeeCode() + " with template " + templateQ2.getJobCode());
            Kpi kpiQ2 = new Kpi();
            kpiQ2.setEmployeeAccountId(kpiQ1.getEmployeeAccountId());
            kpiQ2.setEmployeeFullName(kpiQ1.getEmployeeFullName());
            kpiQ2.setEmployeeCode(kpiQ1.getEmployeeCode());
            kpiQ2.setManagerAccountId(kpiQ1.getManagerAccountId());
            kpiQ2.setManagerFullName(kpiQ1.getManagerFullName());
            kpiQ2.setLeaderAccountId(kpiQ1.getLeaderAccountId());
            kpiQ2.setLeaderFullName(kpiQ1.getLeaderFullName());
            kpiQ2 = KpiService.createKpiFromTemplate(client, company, templateQ2, kpiQ2);
          }
        }
      }
    };
    addRunnable(dataInit);
  };
}

ServerScriptContext scriptCtx = (ServerScriptContext) CONTEXT;
RunnableReporter reporter  = new RunnableReporter("service", "latest")

KpiDataInitializer dataInitializer = new KpiDataInitializer();
dataInitializer.run(reporter, scriptCtx);

return "DONE!!!"