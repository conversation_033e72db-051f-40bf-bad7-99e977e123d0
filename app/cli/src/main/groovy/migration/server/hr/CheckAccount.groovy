package migration.server.hr

import org.slf4j.Logger
import org.slf4j.LoggerFactory

import lib.data.RunnableReporter
import lib.data.ServiceRunnable
import lib.data.ServiceRunnableSet
import net.datatp.module.account.AccountService
import net.datatp.module.account.NewAccountModel
import net.datatp.module.account.entity.Account
import net.datatp.module.account.entity.AccountType
import net.datatp.module.core.security.SecurityLogic
import net.datatp.module.core.security.entity.App
import net.datatp.module.core.security.entity.AppPermission
import net.datatp.module.data.db.entity.ICompany
import net.datatp.module.data.xlsx.XSLXToMapObjectParser
import net.datatp.module.groovy.ServerScriptContext
import net.datatp.module.hr.HRService
import net.datatp.module.hr.NewEmployeeModel
import net.datatp.module.hr.entity.Employee
import net.datatp.module.hr.entity.HRDepartment
import net.datatp.module.hr.http.Params
import net.datatp.security.client.Capability
import net.datatp.security.client.ClientContext
import net.datatp.security.client.DataScope
import net.datatp.util.ds.Arrays
import net.datatp.util.ds.MapObject
import net.datatp.util.ds.Objects
import net.datatp.util.io.IOUtil
import net.datatp.util.text.StringUtil
import net.datatp.util.text.TabularFormater

class CheckKpiAccountDataSet extends ServiceRunnableSet {
  private static String label = "------------Check Kpi Data--------------"
  private static final Logger logger = LoggerFactory.getLogger(CheckKpiAccountDataSet.class);
  
  public CheckKpiAccountDataSet() {
    super(label);
    
    ServiceRunnable dataUpdate = new ServiceRunnable(label) {
      
      @Override
      void run(RunnableReporter reporter, ServerScriptContext scriptCtx) {
        ICompany company = scriptCtx.getCompany();
        
        final String dataDir = scriptCtx.getDataDir();
        InputStream is = IOUtil.loadResource("file:" + dataDir + "/hr/2025-" + company.getCode() + "-kpi.xlsx");
        final byte[] data = IOUtil.getStreamContentAsBytes(is);
        
        XSLXToMapObjectParser accountParser = new XSLXToMapObjectParser(data);
        
        accountParser.parse("kpi");
        List<MapObject> accountRows = accountParser.getRows();
        processAccount(scriptCtx, accountRows);
      }
      
      private void processAccount(ServerScriptContext scriptCtx, List<MapObject> rows) {
        ClientContext client = scriptCtx.getClientCtx();
        ICompany company = scriptCtx.getCompany();
        
        AccountService accountService = scriptCtx.getService(AccountService.class);
        HRService hrService = scriptCtx.getService(HRService.class);
        
        Map<String, String> updateEmployees = new HashMap<>();
        Map<String, String> createNewEmployees = new HashMap<>();
        for (MapObject row : rows) {
          String username = row.getString("User", "").trim();
          String email = row.getString("Email", "").trim();
          String mobile = row.getString("Điện thoại", "").trim();
          println("========" + email)
          
          String status = row.getString("Hiện trạng", "").trim();
          if (status == "Không tạo User") {
            println("Skip: ${username ? username.toLowerCase() : email.toLowerCase()}")
            continue;
          }
          if (status == "Skip") {
            println("Skip: ${username ? username.toLowerCase() : email.toLowerCase()}")
            continue;
          }
          
          Account account = null;
          if (username) {
            account = accountService.getAccountByLoginId(client, username);
          }
          if (email && account == null) {
            account = accountService.getAccountByEmail(client, email);
          }
          if (mobile && account == null) {
            account = accountService.getAccountByMobile(client, mobile);
          }
          
          Employee employee = null;
          if (account != null) {
            if (account.getLoginId().toLowerCase() != username.toLowerCase() && username != "") {
              println("Check account: ${username ? username.toLowerCase() : email.toLowerCase()}")
            }
            employee = hrService.getEmployeeByAccount(client, company, account.getId());
          } else {
            println("Not found account: ${username ? username : email} => Create New")
            NewAccountModel newAccountModel = createNewAccount(scriptCtx, row);
            account = newAccountModel.getAccount();
            employee = hrService.getEmployeeByBFSOneUsername(client, company, username);
          }
          
          if (employee != null) {
            employee.setDescription("Modified")
            employee = updateEmployee(scriptCtx, row, employee);
            updateEmployees.put(account.getLoginId(), employee.getEmployeeCode());
          } else {
            employee = createEmployee(scriptCtx, account, row);
            employee.setDescription("CreateNew")
            employee = updateEmployee(scriptCtx, row, employee);
            createNewEmployees.put(username ? username.toLowerCase() : email.toLowerCase(), employee.getEmployeeCode());
          }
          
          assignAppPermission(scriptCtx, username, null);
        }
        
        Set<String> headerSet = new LinkedHashSet<>();
        headerSet.add("LoginID");
        headerSet.add("EmployeeCode");
        
        String[] header = headerSet.toArray(new String[0]);
        
        TabularFormater updateEmployeesTable = new TabularFormater(header);
        updateEmployeesTable.setTitle("List Employee Update");
        for (Map.Entry<String, String> entry : updateEmployees.entrySet()) {
          updateEmployeesTable.addRow(List.of(entry.getKey()), entry.getValue());
        }
        updateEmployeesTable.addRow(List.of("Count", updateEmployees.size()));
        
        TabularFormater createNewEmployeesTable = new TabularFormater(header);
        createNewEmployeesTable.setTitle("List Employee Create");
        for (Map.Entry<String, String> entry : createNewEmployees.entrySet()) {
          createNewEmployeesTable.addRow(List.of(entry.getKey()), entry.getValue());
        }
        createNewEmployeesTable.addRow(List.of("Count", createNewEmployees.size()));
        
        println("-------------------List Employee Updated----------------")
        println(updateEmployeesTable.getFormattedText());
        println("-------------------List Employee Created----------------")
        println(createNewEmployeesTable.getFormattedText());
      }
      
      private void assignAppPermission(ServerScriptContext scriptCtx, String loginId, String cap) {
        SecurityLogic securityLogic = scriptCtx.getService(SecurityLogic.class);
        ClientContext client = scriptCtx.getClientCtx();
        ICompany company = scriptCtx.getCompany();
        if (cap.equals("admin")) {
          App companyApp = securityLogic.getApp(client, 'company', 'company');
          AppPermission companyAppPermission = securityLogic.getAppPermission(client, companyApp.getId(), company.getId(), loginId);
          if (companyAppPermission == null) {
            println("Update Company Company-Admin Permission for ${loginId}");
            companyAppPermission = new AppPermission();
            companyAppPermission.setCompanyId(company.getId());
            companyAppPermission.setLoginId(loginId);
            companyAppPermission.setAppId(companyApp.getId());
            companyAppPermission.setCapability(net.datatp.security.client.Capability.Admin);
            companyAppPermission.setDataScope(DataScope.Company);
            securityLogic.saveAppPermission(client, companyAppPermission);
          }
          
          App companyKpiApp = securityLogic.getApp(client, 'hr', 'company-kpi');
          AppPermission companyKpiAppPermission = securityLogic.getAppPermission(client, companyKpiApp.getId(), company.getId(), loginId);
          if (companyKpiAppPermission == null) {
            println("Update Company KPI Company-Admin Permission for ${loginId}");
            companyKpiAppPermission = new AppPermission();
            companyKpiAppPermission.setCompanyId(company.getId());
            companyKpiAppPermission.setLoginId(loginId);
            companyKpiAppPermission.setAppId(companyKpiApp.getId());
          }
          companyKpiAppPermission.setCapability(Capability.Admin);
          companyKpiAppPermission.setDataScope(DataScope.Company);
          securityLogic.saveAppPermission(client, companyKpiAppPermission);
        
        } else if (cap.equals("manager")) {
          App companyApp = securityLogic.getApp(client, 'company', 'company');
          AppPermission companyAppPermission = securityLogic.getAppPermission(client, companyApp.getId(), company.getId(), loginId);
          if (companyAppPermission == null) {
            println("Update Company Company-Read Permission for ${loginId}");
            companyAppPermission = new AppPermission();
            companyAppPermission.setCompanyId(company.getId());
            companyAppPermission.setLoginId(loginId);
            companyAppPermission.setAppId(companyApp.getId());
            companyAppPermission.setCapability(Capability.Read);
            companyAppPermission.setDataScope(DataScope.Company);
            securityLogic.saveAppPermission(client, companyAppPermission);
          }
          
          App companyKpiApp = securityLogic.getApp(client, 'hr', 'company-kpi');
          AppPermission companyKpiAppPermission = securityLogic.getAppPermission(client, companyKpiApp.getId(), company.getId(), loginId);
          if (companyKpiAppPermission == null) {
            println("Update Company KPI Owner-Write Permission for ${loginId}");
            companyKpiAppPermission = new AppPermission();
            companyKpiAppPermission.setCompanyId(company.getId());
            companyKpiAppPermission.setLoginId(loginId);
            companyKpiAppPermission.setAppId(companyKpiApp.getId());
          }
          companyKpiAppPermission.setCapability(Capability.Write);
          companyKpiAppPermission.setDataScope(DataScope.Owner);
          securityLogic.saveAppPermission(client, companyKpiAppPermission);
        } else {
          App userKpiApp = securityLogic.getApp(client, 'hr', 'user-kpi');
          AppPermission userKpiAppPermission = securityLogic.getAppPermission(client, userKpiApp.getId(), company.getId(), loginId);
          if (userKpiAppPermission == null) {
            println("Update User KPI Owner-Write Permission for ${loginId}");
            userKpiAppPermission = new AppPermission();
            userKpiAppPermission.setCompanyId(company.getId());
            userKpiAppPermission.setLoginId(loginId);
            userKpiAppPermission.setAppId(userKpiApp.getId());
          }
          userKpiAppPermission.setCapability(Capability.Write);
          userKpiAppPermission.setDataScope(DataScope.Owner);
          securityLogic.saveAppPermission(client, userKpiAppPermission);
        }
      }
      
      private NewAccountModel createNewAccount(ServerScriptContext scriptCtx, MapObject record) {
        ClientContext client = scriptCtx.getClientCtx();
        AccountService accountService = scriptCtx.getService(AccountService.class);
        
        String username = record.getString("User", "").trim();
        String password = record.getString("Pass", "").trim();
        String email = record.getString("Email", "").trim();
        String fullName = record.getString("Họ tên", "").trim();
        String mobile = record.getString("Điện thoại", "").trim();
        
        Account newAccount = new Account();
        newAccount.setAccountType(AccountType.USER);
        newAccount.setLoginId(username ? username : email.split("@")[0]);
        newAccount.setEmail(email);
        newAccount.setFullName(fullName);
        newAccount.setMobile(mobile);
        if (password) {
          newAccount.setPassword(password ? password : email);
        } else {
          newAccount.setPassword(username ? username : email);
        }
        
        NewAccountModel newAccountModel = new NewAccountModel();
        newAccountModel.setAccount(newAccount);
        
        return accountService.createNewAccount(client, newAccountModel);
      }
      
      private Employee createEmployee(ServerScriptContext scriptCtx, Account account, MapObject record) {
        ClientContext client = scriptCtx.getClientCtx();
        ICompany company = scriptCtx.getCompany();
        HRService hrService = scriptCtx.getService(HRService.class);
        
        NewEmployeeModel newEmployeeModel = new NewEmployeeModel();
        newEmployeeModel.setAccount(account);
        
        return hrService.createEmployee(client, company, newEmployeeModel);
      }
      
      private Employee updateEmployee(ServerScriptContext scriptCtx, MapObject record, Employee employee) {
        ClientContext client = scriptCtx.getClientCtx();
        ICompany company = scriptCtx.getCompany();
        HRService hrService = scriptCtx.getService(HRService.class);
        
        employee.setEmployeeCode(record.getString("Mã NV", "").trim());
        employee.setCompanyBranch(record.getString("Chi nhánh", "").trim());
        
        String managerLoginId = record.getString("Manager User", "").trim();
        if (StringUtil.isNotEmpty(managerLoginId)) {
          Employee manager = hrService.getEmployeeByLoginId(client, company, managerLoginId);
          if (Objects.nonNull(manager)) {
            employee.setManagerEmployeeId(manager.getId());
            employee.setManagerEmployeeLabel(manager.getLabel());
          }
        }
        
        String status = record.getString("Hiện trạng", "").trim();
        if (status == "Không Update Department") {
          String username = record.getString("User", "").trim();
          println("Skip Update Department for : ${username}");
        } else {
          updateDepartment(scriptCtx, record, employee);
        }
   
        return hrService.saveEmployee(client, company, employee);
      }
      
      private void updateDepartment(ServerScriptContext scriptCtx, MapObject record, Employee employee) {
        ClientContext client = scriptCtx.getClientCtx();
        ICompany company = scriptCtx.getCompany();
        HRService hrService = scriptCtx.getService(HRService.class);
        
        String departmentName = record.getString("Tên phòng ban", "");
        HRDepartment department = hrService.getHRDepartmentByName(client, company, departmentName);
        if (department == null) {
          println("Departmnet NOT FOUND: ${employee.getLabel()} - ${departmentName}")
          return;
        } 
        Params.HrMembershipRequest request = new Params.HrMembershipRequest();
        request.setEmployeeIds(Arrays.asList(employee.getId()));
        request.setDepartmentId(department.getId());
        hrService.createHRDepartmentRelations(client, company, request);
        println("Update Departmnet: ${employee.getLabel()} - ${department.getLabel()}")
      }
    }
    addRunnable(dataUpdate);
  }
}

ServerScriptContext scriptCtx = (ServerScriptContext) CONTEXT;
RunnableReporter reporter = new RunnableReporter("service", "latest")

CheckKpiAccountDataSet dataUpdate = new CheckKpiAccountDataSet();
dataUpdate.run(reporter, scriptCtx);


return "DONE!!!"
