package migration.server.crm

import cloud.datatp.fforwarder.core.partner.BFSOnePartnerLogic
import cloud.datatp.fforwarder.core.partner.BFSOnePartnerService
import cloud.datatp.fforwarder.core.partner.PartnerObligationService
import cloud.datatp.fforwarder.core.partner.SalemanPartnerObligationLogic
import cloud.datatp.fforwarder.core.partner.entity.BFSOnePartner
import cloud.datatp.fforwarder.core.partner.entity.SalemanPartnerObligation
import cloud.datatp.fforwarder.core.template.CRMUserRoleService
import cloud.datatp.fforwarder.core.template.entity.CrmUserRole
import lib.data.RunnableReporter
import lib.data.ServiceRunnable
import lib.data.ServiceRunnableSet
import net.datatp.module.company.entity.CompanyConfig
import net.datatp.module.data.db.ExternalDataSourceManager
import net.datatp.module.data.db.SqlMapRecord
import net.datatp.module.data.db.SqlQueryManager
import net.datatp.module.data.db.SqlSelectView
import net.datatp.module.data.db.entity.ICompany
import net.datatp.module.data.db.query.SqlQueryParams
import net.datatp.module.groovy.ServerScriptContext
import net.datatp.module.hr.EmployeeReadLogic
import net.datatp.module.hr.HRService
import net.datatp.module.hr.entity.Employee
import net.datatp.module.hr.entity.HRDepartment
import net.datatp.security.client.ClientContext
import net.datatp.util.ds.Collections
import net.datatp.util.ds.Objects
import net.datatp.util.text.StringUtil
import org.slf4j.Logger
import org.slf4j.LoggerFactory

import javax.sql.DataSource
import java.util.stream.Collectors

class SyncAuthorizePartnerBySaleman extends ServiceRunnableSet {
    private static final Logger logger = LoggerFactory.getLogger(SyncAuthorizePartnerBySaleman.class);
    private static String label = "SYNC BFSONE AUTHORIZED CUSTOMER BY SALEMAN";

    public SyncAuthorizePartnerBySaleman() {
        super(label);

        ServiceRunnable syncService = new ServiceRunnable(label) {
            @Override
            public void run(RunnableReporter reporter, ServerScriptContext scriptCtx) {
                ClientContext client = scriptCtx.getClientCtx();
                ICompany company = scriptCtx.getCompany();
                BFSOnePartnerLogic bfsOnePartnerLogic = scriptCtx.getService(BFSOnePartnerLogic.class);
                BFSOnePartnerService bfsOnePartnerService = scriptCtx.getService(BFSOnePartnerService.class);
                HRService hrService = scriptCtx.getService(HRService.class);
                SalemanPartnerObligationLogic obligationLogic = scriptCtx.getService(SalemanPartnerObligationLogic.class);
                PartnerObligationService obligationService = scriptCtx.getService(PartnerObligationService.class);

                List<String> bfsOneUsernames = List.of('TESSIE.VNSGN', "MASON.VNHPH", "MARIA.VNSGN", "DOMINIC.VNSGN", "SANDY.VNHPH", "JOYE.VNSGN", "TESS.VNSGN", "VINCE.VNSGN", "PENNY.VNHPH");

                for (String bfsOneUsername : bfsOneUsernames) {
                    Employee employee = hrService.getEmployeeByBFSOneUsername(client, company, bfsOneUsername);
                    List<SalemanPartnerObligation> obligations = obligationLogic.findBySalemanAccountId(client, employee.getAccountId());
                    List<Long> obligationIds = obligations.stream().map(SalemanPartnerObligation::getId).collect(Collectors.toList());
                    obligationService.deleteObligation(client, company, obligationIds);
                    if (Objects.nonNull(employee)) {
                        List<Employee> employees = List.of(employee);
                        List<SqlMapRecord> customerRecords = bfsOnePartnerLogic.searchBFSOnePartnersBySaleman(client, company, employees);
                        List<BFSOnePartner> bfsonePartners = customerRecords.stream().map(BFSOnePartner::new).collect(Collectors.toList());
                        bfsOnePartnerService.importPartners(client, employee, bfsonePartners);
                    }
                }

            }
        };
        addRunnable(syncService);
    }
}

class ImportCustomerBySaleman extends ServiceRunnableSet {
    private static final Logger logger = LoggerFactory.getLogger(ImportCustomerBySaleman.class);
    private static String label = "IMPORT BFSONE AUTHORIZED CUSTOMER BY SALEMAN";

    public ImportCustomerBySaleman() {
        super(label);

        ServiceRunnable syncService = new ServiceRunnable(label) {
            @Override
            public void run(RunnableReporter reporter, ServerScriptContext scriptCtx) {
                ClientContext client = scriptCtx.getClientCtx();
                ICompany company = scriptCtx.getCompany();
                CRMUserRoleService crmUserRoleService = scriptCtx.getService(CRMUserRoleService.class);
                HRService hrService = scriptCtx.getService(HRService.class);
                Set<String> bfsOneUsernames = new HashSet<>();
                Map<String, String> deptByUsername = new HashMap<>();
                Map<String, String> bfsOneCodeByUsername = new HashMap<>();
                if (company.getCode() == 'bee') {
                    HRDepartment bdd1 = hrService.getHRDepartmentByName(client, company, "bdd1--beehcm");
                    HRDepartment bdd2 = hrService.getHRDepartmentByName(client, company, " BD BEECORP");
                    List<HRDepartment> departments = List.of(bdd1, bdd2);
                    for (HRDepartment department : departments) {
                        SqlQueryParams params = new SqlQueryParams();
                        params.addParam("companyId", company.getId());
                        params.addParam("departmentId", department.getId());
                        List<SqlMapRecord> employees = hrService.searchEmployees(client, company, params);
                        for (SqlMapRecord record : employees) {
                            String bfsOneUsername = record.getString("bfsoneUsername", null);
                            String bfsOneCode = record.getString("bfsoneCode", null);
                            if (StringUtil.isEmpty(bfsOneUsername)) continue;
                            bfsOneUsernames.add(bfsOneUsername);
                            deptByUsername.put(bfsOneUsername, department.getLabel());
                            bfsOneCodeByUsername.put(bfsOneUsername, bfsOneCode);
                        }
                    }
                } else if (company.getCode() == 'beehph') {
                    HRDepartment sales = hrService.getHRDepartmentByName(client, company, 'SALES');
                    List<HRDepartment> departments = hrService.findHRDepartmentByParentId(client, company, sales.getId());
                    for (HRDepartment department : departments) {
                        SqlQueryParams params = new SqlQueryParams();
                        params.addParam("companyId", company.getId());
                        params.addParam("departmentId", department.getId());
                        List<SqlMapRecord> employees = hrService.searchEmployees(client, company, params);
                        for (SqlMapRecord record : employees) {
                            String bfsOneUsername = record.getString("bfsoneUsername", null);
                            String bfsOneCode = record.getString("bfsoneCode", null);
                            if (StringUtil.isEmpty(bfsOneUsername)) continue;
                            bfsOneUsernames.add(bfsOneUsername);
                            deptByUsername.put(bfsOneUsername, department.getLabel());
                            bfsOneCodeByUsername.put(bfsOneUsername, bfsOneCode);
                        }
                    }
                }
                for (String bfsOneUsername : bfsOneUsernames) {
                    Employee employee = hrService.getEmployeeByBFSOneUsername(client, company, bfsOneUsername);
                    Objects.assertNotNull(employee, "Cannot find employee by BFSOne username: " + bfsOneUsername);
                    CrmUserRole role = crmUserRoleService.getByBfsoneCode(client, bfsOneCodeByUsername.get(bfsOneUsername));
                    if (Objects.nonNull(role)) continue;
                    role = new CrmUserRole();
                    company.getCode() == 'bee' ? role.setType(CrmUserRole.UserType.SALE_AGENT) : role.setType(CrmUserRole.UserType.SALE_FREEHAND);
                    role.setFullName(employee.getLabel());
                    role.setBFSOneUsername(employee.getBfsoneUsername());
                    role.setBFSOneCode(employee.getBfsoneCode());
                    role.setAccountId(employee.getAccountId());
                    role.setTeam(deptByUsername.get(bfsOneUsername));
                    role.setCompanyBranchName(client.getCompanyLabel());
                    role.setCompanyBranchCode(client.getCompanyCode());
                    crmUserRoleService.saveCrmUserRole(client, role);
                }
            }
        };
        addRunnable(syncService);
    }
}

class SyncAllBFSOnePartner extends ServiceRunnableSet {
    private static final Logger logger = LoggerFactory.getLogger(SyncAllBFSOnePartner.class);
    private static String label = "SYNC ALL BFSONE PARTNER";

    public SyncAllBFSOnePartner() {
        super(label);

        ServiceRunnable syncService = new ServiceRunnable(label) {
            @Override
            public void run(RunnableReporter reporter, ServerScriptContext scriptCtx) {
                ClientContext client = scriptCtx.getClientCtx();
                BFSOnePartnerLogic bfsOnePartnerLogic = scriptCtx.getService(BFSOnePartnerLogic.class);
                EmployeeReadLogic employeeLogic = scriptCtx.getService(EmployeeReadLogic.class);
                BFSOnePartnerService bfsOnePartnerService = scriptCtx.getService(BFSOnePartnerService.class);
                SqlQueryManager sqlQueryManager = scriptCtx.getService(SqlQueryManager.class);
                CompanyConfig companyConfig = bfsOnePartnerLogic.getCompanyConfigLogic().getCompanyConfigByCompanyId(client, client.getCompanyId());
                ExternalDataSourceManager.DataSourceParams dsPrams = companyConfig.getSubConfigAs("bfsone.ds",
                        ExternalDataSourceManager.DataSourceParams.class);
                DataSource ds = bfsOnePartnerLogic.getDataSourceManager().getDataSource(client, dsPrams);

                final String dataDir = scriptCtx.getDataDir();
                String SCRIPT_DIR = dataDir + "/crm/sql";
                String fileName = "SyncBFSOnePartner.groovy";
                SqlQueryManager.QueryContext queryContext = sqlQueryManager.create(SCRIPT_DIR, fileName);
                Binding binding = new Binding();
                SqlSelectView view = queryContext.createSqlSelectView(ds, binding);
                List<SqlMapRecord> records = view.getSqlMapRecords();
                for (SqlMapRecord record : records) {
                    String salemanOwner = record.getString("sale_owner_code");
                    if (StringUtil.isEmpty(salemanOwner)) {
                        record.put("sale_owner_code", "NA");
                    }
                    String salemanAllocated = record.getString("saleman_obligation_code");
                    if (StringUtil.isEmpty(salemanAllocated)) {
                        record.put("saleman_obligation_code", "NA");
                    }
                }

                Map<String, List<BFSOnePartner>> salePartnerGroup = BFSOnePartner.groupBySaleObligation(records);
                for (String bfsoneCode : salePartnerGroup.keySet()) {
                    List<BFSOnePartner> partners = salePartnerGroup.get(bfsoneCode);
                    if (bfsoneCode.equals("NA")) {
                        for (BFSOnePartner partner : partners) {
                            BFSOnePartner partnerInDb = bfsOnePartnerLogic.getByCode(client, partner.getBfsonePartnerCode());
                            if (partnerInDb == null) {
                                bfsOnePartnerService.saveBFSOnePartner(client, partner);
                            }
                        }
                    } else {
                        List<Employee> employees = employeeLogic.findEmployeeByBFSOneCode(client, bfsoneCode);
                        if (Collections.isNotEmpty(employees) && employees.size() == 1) {
                            Employee employee = employees.get(0);
                            bfsOnePartnerService.importPartners(client, employee, partners);
                        } else {
                            logger.warn("Owner Saleman not found, code = {}", bfsoneCode);
                            for (BFSOnePartner partner : partners) {
                                BFSOnePartner partnerInDb = bfsOnePartnerLogic.getByCode(client, partner.getBfsonePartnerCode());
                                if (partnerInDb == null) {
                                    bfsOnePartnerService.saveBFSOnePartner(client, partner);
                                }
                            }
                        }
                    }
                }

                Map<String, List<BFSOnePartner>> firstSalePartnerGroup = BFSOnePartner.groupBySaleOwnerObligation(records);
                for (String bfsoneCode : firstSalePartnerGroup.keySet()) {
                    List<BFSOnePartner> partners = firstSalePartnerGroup.get(bfsoneCode);
                    if (bfsoneCode.equals("NA")) {
                        for (BFSOnePartner partner : partners) {
                            BFSOnePartner partnerInDb = bfsOnePartnerLogic.getByCode(client, partner.getBfsonePartnerCode());
                            if (partnerInDb == null) {
                                bfsOnePartnerService.saveBFSOnePartner(client, partner);
                            }
                        }
                    } else {
                        List<Employee> employees = employeeLogic.findEmployeeByBFSOneCode(client, bfsoneCode);
                        if (Collections.isNotEmpty(employees) && employees.size() == 1) {
                            Employee employee = employees.get(0);
                            bfsOnePartnerService.importPartners(client, employee, partners);
                        } else {
                            logger.warn("Owner Saleman not found, code = {}", bfsoneCode);
                            for (BFSOnePartner partner : partners) {
                                BFSOnePartner partnerInDb = bfsOnePartnerLogic.getByCode(client, partner.getBfsonePartnerCode());
                                if (partnerInDb == null) {
                                    bfsOnePartnerService.saveBFSOnePartner(client, partner);
                                }
                            }
                        }
                    }
                }

                logger.info("{}", records.size());
                /*
                for (SqlMapRecord bfsonePartner : leads) {
                    logger.info("{}", bfsonePartner.getString("partner_code"));
                }
                 */
            }
        };
        addRunnable(syncService);
    }
}

ServerScriptContext scriptCtx = (ServerScriptContext) CONTEXT;
RunnableReporter reporter = new RunnableReporter("service", "V1_0_0_2024-06-19")

ImportCustomerBySaleman importAuthorizePartner = new ImportCustomerBySaleman();
importAuthorizePartner.run(reporter, scriptCtx);


return "DONE!!!"