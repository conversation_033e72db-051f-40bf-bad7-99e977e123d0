package migration.db.hr

import java.sql.Connection

import jakarta.persistence.Column
import lib.data.DBMigrationRunnable
import lib.data.DBMigrationRunnableSet
import lib.data.RunnableReporter
import net.datatp.cli.ShellApplicationContext
import net.datatp.module.data.db.util.DBConnectionUtil


public class UpdateDataSet extends DBMigrationRunnableSet {

  public UpdateDataSet() {
    super("""Update Data HR""");

    DBMigrationRunnable crmMigration = new DBMigrationRunnable("""Update Data HR""") {
      @Override
      public void run(RunnableReporter reporter, DBConnectionUtil connUtil) {
        connUtil.execute("ALTER TABLE kpi_kpi RENAME COLUMN reporter_account_id TO manager_account_id;");
        connUtil.execute("ALTER TABLE kpi_kpi RENAME COLUMN reporter_full_name TO manager_full_name;");
        
        connUtil.execute("ALTER TABLE kpi_kpi RENAME COLUMN sub_reporter_account_id TO leader_account_id;");
        connUtil.execute("ALTER TABLE kpi_kpi RENAME COLUMN sub_reporter_full_name TO leader_full_name;");
        
        connUtil.execute("ALTER TABLE kpi_kpi DROP CONSTRAINT kpi_kpi_status_check;");
      }
    };
    addRunnable(crmMigration);
  }
}

ShellApplicationContext shellContext = (ShellApplicationContext) SHELL_CONTEXT;
Connection conn = shellContext.getPrimaryDBConnection();
DBConnectionUtil connUtil = new DBConnectionUtil(conn);
RunnableReporter reporter  = new RunnableReporter("dbmigration", "latest")

UpdateDataSet runnableSet = new UpdateDataSet();
runnableSet.run(reporter, connUtil);

connUtil.close();
return "DONE!!!"