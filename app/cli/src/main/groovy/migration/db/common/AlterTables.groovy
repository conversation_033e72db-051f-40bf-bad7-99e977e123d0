package migration.db.common

import lib.data.DBMigrationRunnable
import lib.data.DBMigrationRunnableSet
import lib.data.RunnableReporter
import net.datatp.cli.ShellApplicationContext
import net.datatp.module.data.db.util.DBConnectionUtil
import net.datatp.module.data.db.util.DBUtil

import javax.sql.DataSource
import java.sql.Connection

public class AlterTableCommonSet extends DBMigrationRunnableSet {
    public AlterTableCommonSet() {
        super("""Alter Common Tables""");

    String label = """Alter Common Tables""";
    DBMigrationRunnable alterTables = new DBMigrationRunnable(label) {
        @Override
        public void run(RunnableReporter reporter, DBConnectionUtil connUtil) {
          connUtil.execute(""""
            UPDATE communication_account comm
              SET account_id = (
                SELECT a.id
                FROM account_account a WHERE comm.login_id = a.login_id
              )
            WHERE EXISTS (
                SELECT 1
                FROM account_account aa WHERE comm.login_id = aa.login_id
              );
          """);
          connUtil.execute("DELETE FROM communication_account WHERE account_id IS NULL;");
          connUtil.execute("ALTER TABLE communication_account DROP COLUMN login_id;");
          

          connUtil.execute("ALTER TABLE message_message ADD sender_id varchar NULL;");
          connUtil.execute(""""
            UPDATE message_message m
            SET sender_id = (
              SELECT a.email
              FROM account_account a WHERE m.sender_login_id = a.login_id
            )
            WHERE EXISTS (
              SELECT 1
              FROM account_account aa WHERE m.sender_login_id = aa.login_id
            );
              """);
          connUtil.execute("DELETE FROM message_message WHERE sender_id IS NULL;");
          connUtil.execute("ALTER TABLE message_message ALTER COLUMN sender_id SET NOT NULL;");
          connUtil.execute("ALTER TABLE message_message DROP COLUMN sender_login_id;");
          
          
          
        }
    };
    addRunnable(alterTables);
  }
}

ShellApplicationContext shellContext = (ShellApplicationContext) SHELL_CONTEXT;
Connection conn = shellContext.getPrimaryDBConnection();

DBConnectionUtil connUtil = new DBConnectionUtil(conn);

RunnableReporter reporter = new RunnableReporter("dbmigration", "latest")

AlterTableCommonSet migration = new AlterTableCommonSet();
migration.run(reporter, connUtil);

connUtil.close();
return "DONE!!!"