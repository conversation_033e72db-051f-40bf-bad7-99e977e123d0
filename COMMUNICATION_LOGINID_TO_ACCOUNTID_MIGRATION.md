# Communication Module: Migration từ LoginId sang AccountId

## Tổng quan
Tài liệu này mô tả việc migration hệ thống communication từ việc sử dụng `loginId` (String) sang `accountId` (Long) để cải thiện hiệu suất và tính nhất quán của dữ liệu.

## Lý do Migration
1. **Hiệu suất**: `accountId` (Long) có hiệu suất truy vấn tốt hơn `loginId` (String)
2. **Tính nhất quán**: Sử dụng primary key thay vì unique string field
3. **Bảo mật**: <PERSON><PERSON><PERSON><PERSON> thiểu việc expose loginId trong các API
4. **Chuẩn hóa**: Thống nhất với các module khác trong hệ thống

## Các thay đổi thực hiện

### 1. Entity Changes

#### Message.java
- **Trước**: `private String senderLoginId`
- **Sau**: `private Long senderAccountId`
- **Database column**: `sender_login_id` → `sender_account_id`

**Thay đổi methods:**
```java
// Trước
public Message(String LoginId) {
    this.senderLoginId = LoginId;
}

public Message withPrivateRecipient(String ... loginId) {
    // Implementation với loginId
}

// Sau  
public Message(Long accountId) {
    this.senderAccountId = accountId;
}

public Message withPrivateRecipient(Long ... accountId) {
    // Implementation với accountId
}
```

#### CommunicationAccount.java
- **Trước**: `private String loginId` (primary field)
- **Sau**: `private Long accountId` (primary field)
- **Database column**: `login_id` → `account_id` (as primary unique field)

**Thay đổi constructor:**
```java
// Trước
public CommunicationAccount(Account account) {
    this.loginId = account.getLoginId();
    // ...
}

// Sau
public CommunicationAccount(Account account) {
    this.accountId = account.getId();
    // ...
}
```

#### RecipientMessage.java
- **Trước**: 
  - `private String senderId`
  - `private String recipientId`
- **Sau**: 
  - `private Long senderAccountId`
  - `private Long recipientAccountId`
- **Database columns**: 
  - `sender_id` → `sender_account_id`
  - `recipient_id` → `recipient_account_id`

#### TargetRecipient.java
- **Trước**: `private String recipientId`
- **Sau**: `private Long recipientAccountId`
- **Database column**: `recipient_id` → `recipient_account_id`

### 2. Repository Changes

#### CommunicationAccountRepository.java
```java
// Trước
@Query("SELECT m FROM CommunicationAccount m WHERE lower(m.loginId) = lower(:loginId)")
public CommunicationAccount getCommunicationAccount(@Param("loginId") String loginId);

// Sau
@Query("SELECT m FROM CommunicationAccount m WHERE m.accountId = :accountId")
public CommunicationAccount getCommunicationAccount(@Param("accountId") Long accountId);
```

### 3. Service Layer Changes

#### CommunicationMessageLogic.java
- Thay đổi method signature từ `String loginId` sang `Long accountId`
- Cập nhật search filter từ `senderLoginId` sang `senderAccountId`

#### CommunicationMessageService.java
- Cập nhật method signatures để sử dụng `accountId`

### 4. Frontend Changes

#### WTargetRecipient.tsx
- Thay đổi `idField` từ `'loginId'` sang `'id'`
- Cập nhật `ExcludeRecordFilter` để sử dụng `recipientAccountId`
- Thay đổi display fields trong grid configuration

### 5. Database Migration

#### Schema Changes
```sql
-- Thêm cột mới
ALTER TABLE message_message ADD COLUMN sender_account_id BIGINT;
ALTER TABLE message_recipient_message ADD COLUMN sender_account_id BIGINT, recipient_account_id BIGINT;
ALTER TABLE message_target_recipient ADD COLUMN recipient_account_id BIGINT;

-- Migrate dữ liệu
UPDATE message_message SET sender_account_id = (SELECT id FROM account_account WHERE login_id = sender_login_id);
-- ... (xem migration script để biết chi tiết)

-- Thêm constraints
ALTER TABLE message_message ALTER COLUMN sender_account_id SET NOT NULL;
-- ... (xem migration script để biết chi tiết)
```

## Các file đã thay đổi

### Backend Files
1. `module/communication/service/src/main/java/net/datatp/module/communication/entity/Message.java`
2. `module/communication/service/src/main/java/net/datatp/module/communication/entity/CommunicationAccount.java`
3. `module/communication/service/src/main/java/net/datatp/module/communication/entity/RecipientMessage.java`
4. `module/communication/service/src/main/java/net/datatp/module/communication/entity/TargetRecipient.java`
5. `module/communication/service/src/main/java/net/datatp/module/communication/repository/CommunicationAccountRepository.java`
6. `module/communication/service/src/main/java/net/datatp/module/communication/CommunicationMessageLogic.java`
7. `module/communication/service/src/main/java/net/datatp/module/communication/CommunicationMessageService.java`
8. `module/communication/service/src/main/java/net/datatp/module/communication/CommunicationAccountServicePlugin.java`
9. `module/communication/service/src/main/java/net/datatp/module/zalo/ZaloLogic.java`

### Frontend Files
1. `webui/erp/src/module/communication/message/WTargetRecipient.tsx`

### Migration Files
1. `migration/communication_loginid_to_accountid_migration.sql`
2. `module/communication/service/src/test/java/net/datatp/module/communication/CommunicationAccountIdMigrationTest.java`

## Quy trình triển khai

### Bước 1: Backup Database
```bash
# Backup toàn bộ database trước khi migration
pg_dump -h localhost -U username -d database_name > backup_before_migration.sql
```

### Bước 2: Deploy Code Changes
1. Deploy các thay đổi backend
2. Deploy các thay đổi frontend

### Bước 3: Run Migration Script
```bash
# Chạy migration script
psql -h localhost -U username -d database_name -f migration/communication_loginid_to_accountid_migration.sql
```

### Bước 4: Validation
1. Chạy test suite: `CommunicationAccountIdMigrationTest`
2. Kiểm tra các chức năng communication trong UI
3. Verify dữ liệu đã được migrate đúng

### Bước 5: Cleanup (Sau khi test thành công)
```sql
-- Drop các cột cũ (chỉ sau khi đã test kỹ)
ALTER TABLE message_message DROP COLUMN sender_login_id;
ALTER TABLE message_recipient_message DROP COLUMN sender_id, DROP COLUMN recipient_id;
ALTER TABLE message_target_recipient DROP COLUMN recipient_id;
```

## Rủi ro và Mitigation

### Rủi ro
1. **Data Loss**: Mất dữ liệu trong quá trình migration
2. **Downtime**: Hệ thống không hoạt động trong lúc migration
3. **Inconsistent Data**: Dữ liệu không nhất quán sau migration

### Mitigation
1. **Full Backup**: Backup toàn bộ database trước khi migration
2. **Staged Deployment**: Deploy từng bước và test kỹ
3. **Rollback Plan**: Chuẩn bị script rollback nếu cần
4. **Validation**: Chạy comprehensive test sau migration

## Testing Checklist

- [ ] Unit tests pass: `CommunicationAccountIdMigrationTest`
- [ ] Tạo message mới với accountId
- [ ] Gửi message giữa các users
- [ ] Hiển thị danh sách messages
- [ ] Search messages theo sender
- [ ] Recipient selection trong UI
- [ ] Zalo integration vẫn hoạt động
- [ ] Communication account management
- [ ] Channel và group messaging

## Rollback Plan

Nếu cần rollback:
1. Restore database từ backup
2. Revert code changes
3. Redeploy previous version

## Liên hệ

Nếu có vấn đề trong quá trình migration, liên hệ:
- Development Team
- Database Administrator
- System Administrator
