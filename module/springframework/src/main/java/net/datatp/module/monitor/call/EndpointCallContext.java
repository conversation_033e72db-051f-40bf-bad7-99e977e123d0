package net.datatp.module.monitor.call;

import com.fasterxml.jackson.databind.JsonNode;
import java.beans.MethodDescriptor;
import java.lang.reflect.Parameter;
import java.lang.reflect.ParameterizedType;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.monitor.SourceType;
import net.datatp.module.monitor.annotation.MonitorCall;
import net.datatp.security.client.ClientContext;
import net.datatp.util.bean.BeanInspector;
import net.datatp.util.dataformat.DataSerializer;
import net.datatp.util.error.RuntimeError;

@Getter
@Slf4j
public class EndpointCallContext {
  @Setter
  protected String module;
  @Setter
  protected String componentName;
  @Setter
  protected String method;
  
  protected SourceType    sourceType = SourceType.User;
  
  protected Class<?>      componentType;
  protected Object        component;
  protected ClientContext clientContext;
  protected RequestInfo   requestInfo;

  protected ICompany              company;
  
  protected Map<String, JsonNode> userParams;

  protected MethodDescriptor      mDescriptor;

  public EndpointCallContext(String module, String componentName, String method) {
    this.module        = module;
    this.componentName = componentName;
    this.method        = method;
  }
  
  public EndpointCallContext(Class<?> componentType, Object component, String method) {
    this.componentType = componentType;
    this.component     = component;
    this.method        = method;
    
    this.module        = computeModule();
    this.componentName = componentType.getSimpleName();
  }
  
  public EndpointCallContext withContext(ClientContext client, ICompany company) {
    clientContext = client;
    if(company != null) {
      this.company = company;
    }
    return this;
  }
  
  public EndpointCallContext withRequestInfo(RequestInfo rInfo) {
    this.requestInfo = rInfo;
    return this;
  }
  
  public EndpointCallContext withSourceType(SourceType sourceType) {
    this.sourceType = sourceType;
    return this;
  }
  
  public EndpointCallContext withUserParams(Map<String, JsonNode> userParams) {
    this.userParams = userParams;
    return this;
  }
  
  private List<Object> computeArguments() {
    List<Object> args = new ArrayList<>();
    BeanInspector<?> inspector = BeanInspector.get(componentType);
    mDescriptor = inspector.getMethodDescriptor(method, 2 + userParams.size());
    if(mDescriptor != null) {
      args.add(clientContext);
      args.add(company);
    } else {
      mDescriptor = inspector.getMethodDescriptor(method, 1 + userParams.size());
      args.add(clientContext);
    }
    
    if(mDescriptor == null) {
      log.error("Cannot find the component = {}, mehthod = {}, userParams = \n {}", componentType, method, DataSerializer.JSON.toString(userParams));
      throw RuntimeError.IllegalArgument("Cannot find the component {0}, mehtod {1}", componentType, method);
    }
    
    if(log.isDebugEnabled()) {
      log.debug("Compute the arguments for component = {}, method = {}, userParams size {}", componentType, method, userParams.size());
    }
    
    Parameter[] parameters = mDescriptor.getMethod().getParameters();
    if(userParams != null) {
      int argIdx = args.size();
      for(JsonNode jsonNode : userParams.values()) {
        Class<?> argType = parameters[argIdx].getType();
        if(List.class.isAssignableFrom(argType)) {
          ParameterizedType pType = (ParameterizedType) parameters[argIdx].getParameterizedType();
          Class<?>  actualType = (Class<?> )pType.getActualTypeArguments()[0];
          List<?> values = DataSerializer.JSON.treeToListObject(jsonNode, actualType);
          args.add(values);
        } else {
          Object argVal = DataSerializer.JSON.treeToObject(jsonNode, argType);
          if(argVal instanceof SourceType) {
            sourceType = (SourceType) argVal;
          }
          args.add(argVal);
        }
        argIdx++ ;
      }
    }
    if(sourceType == null) {
      MonitorCall monitorCall = mDescriptor.getMethod().getAnnotation(MonitorCall.class);
      if(monitorCall != null) {
        sourceType = monitorCall.sourceType();
      } else {
        sourceType = SourceType.User;
      }
    }
    return args;
  }

  protected void preCall() { log.debug("preCall()");
  }
  
  public Object call() {

    try {
      preCall();
      Object result = doCall();
      postCall(result);
      return result;
    } catch (Throwable e) {
      throw RuntimeError.toRuntimeError(log, e);
    }
  }  

  protected Object doCall() throws Throwable {
    List<Object> args = computeArguments();
    try {
      if(log.isDebugEnabled()) {
        String jsonArgs = DataSerializer.JSON.toString(args);
        log.debug("Start call with component {}, method {}, arguments\n{}", componentName, method, jsonArgs);
      }
      Object result = mDescriptor.getMethod().invoke(component, args.toArray());
      if(log.isDebugEnabled()) {
        String jsonResult = DataSerializer.JSON.toString(result);
        log.debug("End call with component {}, method {}, result\n{}", componentName, method, jsonResult);
      }
      return result;
    } catch (Throwable e) {
      String jsonArgs = DataSerializer.JSON.toString(args);
      log.error("Start call with component {}, method {}, arguments\n{}", componentName, method, jsonArgs);
      throw RuntimeError.toRuntimeError(log, e);
    }
  }

  protected void postCall(Object result) {
    log.debug("postCall()");
  }
 
  protected String computeModule() {
    String className = component.getClass().getName();
    int idx = className.lastIndexOf(".");
    String module = className.substring(0, idx);
    final String[] modulePrefix =  {
        "net.datatp.module.", "cloud.datatp.module.",
        "net.datatp.", "cloud.datatp."
    };
    for(String prefix : modulePrefix) {
      if(module.startsWith(prefix)) {
        module = module.substring(prefix.length(), module.length());
      }
    }
    return module;
  }
}