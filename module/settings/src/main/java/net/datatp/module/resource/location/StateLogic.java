package net.datatp.module.resource.location;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.transaction.Transactional;
import net.datatp.security.client.ClientContext;
import net.datatp.module.data.db.DAOService;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.query.ConditionFilter;
import net.datatp.module.data.db.query.EntityTable;
import net.datatp.module.data.db.query.OptionFilter;
import net.datatp.module.data.db.query.RangeFilter;
import net.datatp.module.data.db.query.SearchFilter;
import net.datatp.module.data.db.query.SqlQuery;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.resource.location.entity.Country;
import net.datatp.module.resource.location.entity.State;
import net.datatp.module.resource.location.repo.CountryRepository;
import net.datatp.module.resource.location.repo.StateRepository;
import net.datatp.util.error.ErrorType;
import net.datatp.util.error.RuntimeError;

@Component
public class StateLogic extends DAOService {
  @Autowired
  private StateRepository stateRepo;

  @Autowired
  private CountryRepository countryRepo;

  public State getState(ClientContext clientCtx, String code) {
    return stateRepo.getByCode(code);
  }

  public State getByComputeId(ClientContext clientCtx, String computeId) {
    return stateRepo.getByComputeId(computeId);
  }

  public State getState(ClientContext clientCtx, Long id) {
    return stateRepo.findById(id).get();
  }

  public List<State> findStateByIds(ClientContext clientCtx, List<Long> ids) {
    return stateRepo.findStates(ids);
  }

  public State getStateByCode(ClientContext clientCtx, Long countryId, String code) {
    return stateRepo.getByCode(countryId, code);
  }

  public State getByLabel(ClientContext client, String label) {
    return stateRepo.getByLabel(label);
  }

  public State createState(ClientContext clientCtx, State state) {
    Country country = countryRepo.getByCode(state.getCountryCode());
    if(country == null) {
      String msg = "Country does not exist, country " + state.getCountryCode() + ", state = " + state.getLabel();
      throw new RuntimeError(ErrorType.ConstraintViolation, msg);
    }
    state.set(clientCtx);
    return stateRepo.save(state);
  }

  public State saveState(ClientContext clientCtx, State state) {
    state.set(clientCtx);
    return stateRepo.save(state);
  }

  @Transactional
  public State createOrUpdateState(ClientContext clientCtx, State state) {
    if(state.isNew()) {
      State exists = stateRepo.getByCode(state.getCode());
      if(exists != null) {
        state = exists.merge(state);
      }
    }
    state.set(clientCtx);
    return stateRepo.save(state);
  }

  public boolean changeStorageState(ClientContext clientCtx, ChangeStorageStateRequest req) {
    List<State> states = stateRepo.findStates(req.getEntityIds());
    for(State state : states) {
      stateRepo.setStorageState(req.getNewStorageState(), state.getCode());
    }
    return true;
  }

  public List<State> findStatesInCountry(ClientContext clientCtx, String countryCode) {
    return stateRepo.findStatesInCountry(countryCode);
  }

  //TODO: An : turning to Groovy Framework
  List<State> searchStates(ClientContext clientCtx, SqlQueryParams params) {
    String[] SEARCH_FIELDS = new String[] { "code", "label", "countryCode", "countryLabel", "variants" };
    SqlQuery query = new SqlQuery()
      .ADD_TABLE(new EntityTable(State.class).selectAllFields())
      .FILTER(SearchFilter.isearch(State.class, SEARCH_FIELDS))
      .FILTER(
        new ConditionFilter(State.class, "countryId", "= :countryId")
        .hasVariableCondition("countryId"))
      .FILTER(
        OptionFilter.storageState(State.class),
        RangeFilter.createdTime(State.class),
        RangeFilter.modifiedTime(State.class)).
        ORDERBY(new String[] {"code", "modifiedTime"}, "modifiedTime", "ASC");
    query.mergeValue(params);
    return query(clientCtx, query, State.class);
  }
}