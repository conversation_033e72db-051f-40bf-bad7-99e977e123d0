package net.datatp.module.resource.location;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.stereotype.Component;

import groovy.lang.Binding;
import net.datatp.security.client.ClientContext;
import net.datatp.module.data.db.DAOService;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.SqlQueryManager;
import net.datatp.module.data.db.SqlSelectView;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.query.OptionFilter;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.resource.location.entity.Coordinates;
import net.datatp.module.resource.location.entity.Country;
import net.datatp.module.resource.location.entity.Location;
import net.datatp.module.resource.location.entity.LocationType;
import net.datatp.module.resource.location.entity.State;
import net.datatp.module.resource.location.repo.LocationRepository;
import net.datatp.util.ds.Arrays;
import net.datatp.util.ds.Objects;

@Component
public class LocationLogic extends DAOService {

  @Autowired
  private LocationRepository locationRepo;

  public List<Location> findLocationByLabel(ClientContext client, String label) {
    return locationRepo.findByLabel(label);
  }

  public List<SqlMapRecord> findNearestLocationByCoordinates(ClientContext client, LocationType type, Coordinates coordinates) {
    if ("test".equals(env)) return new ArrayList<>();
    String QUERY_SCRIPT_DIR = appEnv.addonPath("core", "groovy/settings/");
    SqlQueryManager.QueryContext queryContext = sqlQueryManager.create(QUERY_SCRIPT_DIR, "FindNearestLocationQuery.groovy");
    MapSqlParameterSource params = new MapSqlParameterSource();
    params.addValue("location_type", LocationType.Port);
    params.addValue("latitude", coordinates.getLatitude());
    params.addValue("longitude", coordinates.getLongitude());
    final SqlSelectView view = queryContext.createSqlSelectView(new Binding(), params);
    return view.renameColumWithJavaConvention().getSqlMapRecords();
  }

  public Location getLocation(ClientContext client, String code) {
    return locationRepo.getByCode(code);
  }

  public Location getAirPortLocation(ClientContext client, String code) {
    return locationRepo.getAirPortByCode(code);
  }

  public Location getLocation(ClientContext clientCtx, Long id) {
    return locationRepo.findById(id).get();
  }

  public Location createLocation(ClientContext client, State state, Location location) {
    location.withState(state);
    location.set(client);
    return locationRepo.save(location);
  }

  public Location createLocation(ClientContext client, Country country, Location location) {
    location.withCountry(country);
    location.set(client);
    return locationRepo.save(location);
  }

  public Location saveLocation(ClientContext clientCtx, Location location) {
    location.set(clientCtx);
    return locationRepo.save(location);
  }

  public List<Location> saveLocations(ClientContext clientCtx, List<Location> locations) {
    for(Location location : locations) {
      location.set(clientCtx);
    }
    return locationRepo.saveAll(locations);
  }

  public List<SqlMapRecord> searchLocations(ClientContext client, SqlQueryParams sqlQueryParams) {
    if ("test".equals(env)) return new ArrayList<>();
    if(!sqlQueryParams.hasParam("countryId")) sqlQueryParams.addParam("countryId", null);
    if(!sqlQueryParams.hasParam("tags")) sqlQueryParams.addParam("tags", null);

    String QUERY_SCRIPT_DIR = appEnv.addonPath("core", "groovy/settings/");
    SqlQueryManager.QueryContext queryContext = sqlQueryManager.create(QUERY_SCRIPT_DIR, "LocationQuery.groovy");
    MapSqlParameterSource params = sqlQueryParams.toSqlParameter();
    final OptionFilter typeFilter = sqlQueryParams.getOptionFilter("locationTypes");
    if(typeFilter != null) {
      final String[] typeOptions = typeFilter.getSelectOptions();
      final boolean match = Arrays.asList(typeOptions).stream().allMatch(String::isEmpty);
      if(Arrays.isEmpty(typeOptions) || match) {
        params.addValue("all_type", true);
      } else {
        params.addValue("all_type", false);
      }
    }
    final SqlSelectView view = queryContext.createSqlSelectView(new Binding(), params);
    return view.renameColumWithJavaConvention().getSqlMapRecords();
  }

  @Deprecated
  public List<SqlMapRecord> filterLocationAutoComplete(ClientContext client, SqlQueryParams sqlQueryParams) {
    if ("test".equals(env)) return new ArrayList<>();
    if(!sqlQueryParams.hasParam("code")) sqlQueryParams.addParam("code", null);
    if(!sqlQueryParams.hasParam("tags")) sqlQueryParams.addParam("tags", null);

    String QUERY_SCRIPT_DIR = appEnv.addonPath("core", "groovy/settings/");
    SqlQueryManager.QueryContext queryContext = sqlQueryManager.create(QUERY_SCRIPT_DIR, "LocationAutoCompleteQuery.groovy");
    MapSqlParameterSource params = sqlQueryParams.toSqlParameter();
    final OptionFilter typeFilter = sqlQueryParams.getOptionFilter("locationTypes");
    if(typeFilter != null) {
      final String[] typeOptions = typeFilter.getSelectOptions();
      final boolean match = Arrays.asList(typeOptions).stream().allMatch(String::isEmpty);
      if(Arrays.isEmpty(typeOptions) || match) {
        params.addValue("all_type", true);
      } else {
        params.addValue("all_type", false);
      }
    }
    final SqlSelectView view = queryContext.createSqlSelectView(new Binding(), params);
    return view.renameColumWithJavaConvention().getSqlMapRecords();
  }

  public List<Location> findActiveLocations(ClientContext client) {
    return locationRepo.findActives();
  }

  public boolean changeStorageState(ClientContext client, ChangeStorageStateRequest req) {
    final int updateCount = locationRepo.setLocationState(req.getNewStorageState(), req.getEntityIds());
    Objects.assertTrue(updateCount == req.getEntityIds().size());
    return true;
  }

  public boolean deleteByIds(ClientContext client, List<Long> ids) {
    locationRepo.deleteAllByIdInBatch(ids);
    return true;
  }

  public List<Location> findLocationsByType(ClientContext client, LocationType type) {
    return locationRepo.findAllByType(type);
  }

  public List<Location> findLocations(ClientContext client, List<Long> locationIds) {
    return locationRepo.findLocations(locationIds);
  }

  public Map<Long, Location> findMapLocationByIds(ClientContext client, List<Long> ids) {
    List<Location> locations = this.findLocations(client, ids);
    Map<Long, Location> map = new HashMap<>();
    for(Location location: locations) {
      map.put(location.getId(), location);
    }
    return map;
  }



}