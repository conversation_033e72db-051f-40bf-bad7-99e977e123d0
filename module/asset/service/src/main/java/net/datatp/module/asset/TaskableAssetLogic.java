package net.datatp.module.asset;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import net.datatp.module.account.AccountLogic;
import net.datatp.module.account.entity.Account;
import net.datatp.module.asset.api.AssetTaskConfirmationPlugin;
import net.datatp.module.asset.entity.Asset;
import net.datatp.module.asset.entity.TaskableAsset;
import net.datatp.module.asset.entity.TaskableAsset.AssetTaskStatus;
import net.datatp.module.asset.entity.TaskableAsset.AssetTaskType;
import net.datatp.module.asset.repository.AssetRepository;
import net.datatp.module.asset.repository.TaskableAssetRepository;
import net.datatp.module.communication.CommunicationMessageLogic;
import net.datatp.module.communication.CommunicationMessageService;
import net.datatp.module.communication.entity.CommunicationAccount;
import net.datatp.module.communication.entity.Message;
import net.datatp.module.communication.entity.Message.Status;
import net.datatp.module.communication.entity.MessageDeliverType;
import net.datatp.module.communication.entity.TargetRecipient;
import net.datatp.module.company.CompanyConfigLogic;
import net.datatp.module.company.CompanyLogic;
import net.datatp.module.company.entity.Company;
import net.datatp.module.company.entity.CompanyConfig;
import net.datatp.module.core.security.AuthorizedToken;
import net.datatp.module.core.security.api.ApiAuthorizationService;
import net.datatp.module.data.db.DAOService;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.wfms.entity.EntityTask;
import net.datatp.module.wfms.entity.EntityTaskRequest;
import net.datatp.module.wfms.entity.EntityTaskStatus;
import net.datatp.security.client.ClientContext;
import net.datatp.util.ds.Collections;
import net.datatp.util.ds.Objects;
import net.datatp.util.text.StringUtil;

@Slf4j
@Component
public class TaskableAssetLogic extends DAOService {

  @Autowired
  private AssetRepository assetRepo;

  @Autowired @Getter
  private TaskableAssetRepository repo;

  @Autowired
  private AccountLogic accountLogic;

  @Autowired
  private CommunicationMessageLogic commMessageLogic;

  @Autowired
  private CompanyConfigLogic companyConfigLogic;

  @Autowired
  private CommunicationMessageService messageService;

  @Autowired
  private ApiAuthorizationService apiAuthorizationService;

  @Autowired
  private AssetTaskPlugin taskPlugin;

  @Autowired
  private CompanyLogic companyLogic;

  private String URL;

  @PostConstruct
  public void onInit() {
    if(appEnv.isProdEnv()) {
      URL = "https://beelogistics.cloud/";
    } else {
      URL = "http://localhost:7080/";
    }
  }

  public TaskableAsset getTaskableAsset(ClientContext client, ICompany company, Long id) {
    return repo.getById(id);
  }

  public TaskableAsset saveTaskableAsset(ClientContext client, ICompany company, TaskableAsset tAsset) {
    tAsset.set(client, company);
    if (tAsset.isNew() && tAsset.getTaskType() != AssetTaskType.Car) tAsset.setStatus(AssetTaskStatus.Approved);
    tAsset.setFromTime(formatTimeWithoutSeconds(tAsset.getFromTime()));
    tAsset.setToTime(formatTimeWithoutSeconds(tAsset.getToTime()));
    if (tAsset.getAssetCompanyId() == null) tAsset.setAssetCompanyId(client.getCompanyId());
    TaskableAsset saved = repo.save(tAsset);
    if (saved.isSendEmail()) sendEmailAssetTask(client, company, saved);
    return saved;
  }

  private void sendEmailAssetTask(ClientContext client, ICompany company, TaskableAsset tAsset) {
    if (tAsset.getTaskType() == AssetTaskType.Car) sendConfirmationEmailCarRequest(client, company, tAsset);
    else if (tAsset.getTaskType() == AssetTaskType.MeetingRoom) sendAlertEmailMeeting(client, company, tAsset);
  }

  private void sendAlertEmailMeeting(ClientContext client, ICompany company, TaskableAsset tAsset) {
    Account senderAccount = accountLogic.getAccountByEmail(client, "<EMAIL>");
    Message message = new Message(senderAccount.getLoginId());
    message.setSubject("You have new message from CRM system !");
    String content =
        "<div>You have new Meeting/Traing alert as follows:</div>" +
            tAsset.createMessageContent();
    message.setContent(content);

    for (String email : tAsset.getSendToEmails()) {
      Account receiverAccount = accountLogic.getAccountByEmail(client, email);
      CommunicationAccount receiver = messageService.getCommunicationAccount(client, receiverAccount.getId());
      TargetRecipient targetRecipient = new TargetRecipient(MessageDeliverType.Email, receiver.getEmail(), receiver.getFullName());
      targetRecipient.setForwardEmail(receiver.isAutoForward());
      message.withRecipient(targetRecipient);
    }

    Account creatorAccount = accountLogic.getAccountById(client, tAsset.getCreatedByAccountId());
    CommunicationAccount creatorCommAccount = messageService.getCommunicationAccount(client, creatorAccount.getId());
    TargetRecipient creatorTargetRecipient = new TargetRecipient(MessageDeliverType.Email, creatorCommAccount.getEmail(), creatorCommAccount.getFullName());
    creatorTargetRecipient.setForwardEmail(creatorCommAccount.isAutoForward());
    message.withRecipient(creatorTargetRecipient);

    try {
      Message result = messageService.sendImmediatelyMessage(client, company, message);
      if (result.getStatus() == Status.DeliveredWithError) {
        throw new RuntimeException("Failed to send message first try: " + result.getError());
      }
    } catch (Exception e) {
      log.error("❌ Failed to send message Asset: {} {}", message.getSubject(), tAsset.getLabel(), e.getMessage(), e);
      try {
        Message result = messageService.sendImmediatelyMessage(client, company, message);
        if (result.getStatus() == Status.DeliveredWithError) {
          throw new RuntimeException("Failed to send message second try: " + result.getError());
        }
      } catch (Exception e2) {
        log.error("❌ Failed to send error message Asset: {} {}", message.getSubject(), tAsset.getLabel(), e2.getMessage(), e2);
        throw new RuntimeException("Failed to send message: " + e.getMessage(), e);
      }
    }
  }

  private void sendConfirmationEmailCarRequest(ClientContext client, ICompany company, TaskableAsset tAsset) {
    Account senderAccount = accountLogic.getAccountByEmail(client, "<EMAIL>");

    for (String email : tAsset.getSendToEmails()) {
      Message message = new Message(senderAccount.getLoginId());
      message.setSubject("[" + tAsset.getTaskType() + " Request]" + tAsset.getPicLabel() + ": " + tAsset.getLabel());
      Account receiverAccount = accountLogic.getAccountByEmail(client, email);
      String confirmURL = URL + "api/ui/" + createAccessToken(client, company, tAsset.getId(), receiverAccount.getId(), tAsset.getStatus().toString());
      String content =
          "<div>You have new " + tAsset.getTaskType().toString() + " requirement alert as follows:</div>" +
              tAsset.createMessageContent() +
              TaskableAsset.appendButton("Click Here to Approve", confirmURL, "#0d6efd") +
              "<div><i>(Please do not reply to this email, because it was sent automatically by the system).</i></div>";
      message.setContent(content);

      CommunicationAccount receiver = messageService.getCommunicationAccount(client, receiverAccount.getLoginId());
      TargetRecipient targetRecipient = new TargetRecipient(MessageDeliverType.Email, receiver.getEmail(), receiver.getFullName());
      targetRecipient.setForwardEmail(receiver.isAutoForward());
      message.withRecipient(targetRecipient);

      try {
        Message result = messageService.sendImmediatelyMessage(client, company, message);
        if (result.getStatus() == Status.DeliveredWithError) {
          throw new RuntimeException("Failed to send message first try: " + result.getError());
        }
      } catch (Exception e) {
        log.error("❌ Failed to send message Asset: {} {}", message.getSubject(), tAsset.getLabel(), e.getMessage(), e);
        try {
          Message result = messageService.sendImmediatelyMessage(client, company, message);
          if (result.getStatus() == Status.DeliveredWithError) {
            throw new RuntimeException("Failed to send message second try: " + result.getError());
          }
        } catch (Exception e2) {
          log.error("❌ Failed to send error message Asset: {} {}", message.getSubject(), tAsset.getLabel(), e2.getMessage(), e2);
          throw new RuntimeException("Failed to send message: " + e.getMessage(), e);
        }
      }

    }
  }

  public void sendConfirmationResultEmailAssetTask(ClientContext client, ICompany company, TaskableAsset tAsset, Long approverAccountId) {
    Account senderAccount = accountLogic.getAccountByEmail(client, "<EMAIL>");
    Account approverAccount = accountLogic.getAccountById(client, approverAccountId);
    Account creatorAccount = accountLogic.getAccountById(client, tAsset.getCreatedByAccountId());
    Message message = new Message(senderAccount.getLoginId());

    message.setSubject("[" + tAsset.getTaskType() + " Request " + tAsset.getStatus() + " ]: " + tAsset.getLabel());

    String content =
        "<div>Your " + tAsset.getTaskType().toString() + " requirement has " + tAsset.getStatus() + "!</div>" +
        tAsset.createMessageContent() +
        TaskableAsset.appendContent("- Approved by", approverAccount.getFullName() + " (" + approverAccount.getEmail() + ")") +
        "<div><i>(Please do not reply to this email, because it was sent automatically by the system).</i></div>";
    message.setContent(content);

    CommunicationAccount creatorCommAccount = messageService.getCommunicationAccount(client, creatorAccount.getLoginId());
    TargetRecipient creatorTargetRecipient = new TargetRecipient(MessageDeliverType.Email, creatorCommAccount.getEmail(), creatorCommAccount.getFullName());
    creatorTargetRecipient.setForwardEmail(creatorCommAccount.isAutoForward());
    message.withRecipient(creatorTargetRecipient);

    for (String email : tAsset.getSendToEmails()) {
      Account receiverAccount = accountLogic.getAccountByEmail(client, email);
      CommunicationAccount receiver = messageService.getCommunicationAccount(client, receiverAccount.getLoginId());
      TargetRecipient targetRecipient = new TargetRecipient(MessageDeliverType.Email, receiver.getEmail(), receiver.getFullName());
      targetRecipient.setForwardEmail(receiver.isAutoForward());
      message.withRecipient(targetRecipient);
    }

    try {
      Message result = messageService.sendImmediatelyMessage(client, company, message);
      if (result.getStatus() == Status.DeliveredWithError) {
        throw new RuntimeException("Failed to send message first try: " + result.getError());
      }
    } catch (Exception e) {
      log.error("❌ Failed to send message Asset: {} {}", message.getSubject(), tAsset.getLabel(), e.getMessage(), e);
      try {
        Message result = messageService.sendImmediatelyMessage(client, company, message);
        if (result.getStatus() == Status.DeliveredWithError) {
          throw new RuntimeException("Failed to send message second try: " + result.getError());
        }
      } catch (Exception e2) {
        log.error("❌ Failed to send error message Asset: {} {}", message.getSubject(), tAsset.getLabel(), e2.getMessage(), e2);
        throw new RuntimeException("Failed to send message: " + e.getMessage(), e);
      }
    }
  }

  private String createAccessToken(ClientContext client, ICompany company, Long taskableAssetId, Long receiverAccountId, String status) {
    CompanyConfig companyConfig = companyConfigLogic.getCompanyConfigByCompanyId(client, client.getCompanyId());
    Long tokenId = companyConfig.getAttributeAsLong(TaskableAsset.TASKABLE_ASSET_TOKEN_ID, 55438L);

    AuthorizedToken token = new AuthorizedToken();
    token.setResourceHandler(AssetTaskConfirmationPlugin.NAME);
    token.withAllowedResourceId("taskableAssetId", taskableAssetId);
    token.withAllowedResourceId("receiverAccountId", receiverAccountId);
    return apiAuthorizationService.createAuthorizedToken(client, company, tokenId, token);
  }

  public TaskableAsset handleTaskableAsset(ClientContext client, ICompany company, TaskableAsset tAsset) {
    EntityTask entityTask = new EntityTask();
    if (!tAsset.isNew()) {
      List<EntityTask> entityTasks = taskPlugin.findByEntityId(client, TaskableAsset.TABLE_NAME, tAsset.getId());
      if (Collections.isNotEmpty(entityTasks)) entityTask = entityTasks.get(0);
    } else {
      tAsset.setEntityTaskRequest(new EntityTaskRequest());
    }
    if(tAsset.getAssetCompanyId() == null) tAsset.setAssetCompanyId(tAsset.getCompanyId());
    tAsset.computeEntityTaskData(entityTask);
    taskPlugin.handle(client, company, tAsset, entityTask, EntityTaskStatus.Submitted);
    return tAsset;
  }

  public TaskableAsset updateAssetTaskStatus(ClientContext client, ICompany company, Long taskableAssetId, Long assetId, AssetTaskStatus status) {
    TaskableAsset taskableAsset = getTaskableAsset(client, company, taskableAssetId);
    taskableAsset.setStatus(status);

    if (assetId != null) {
      Asset asset = assetRepo.getById(assetId);
      Objects.assertNotNull(asset, "Asset is not found by id = {}", assetId);
      taskableAsset.setAssetId(assetId);
      taskableAsset.setAssetCompanyId(asset.getCompanyId());
      taskableAsset.setAssetLabel(asset.getLabel());
    }
    handleTaskableAsset(client, company, taskableAsset);
    sendConfirmationResultEmailAssetTask(client, company, taskableAsset, client.getAccountId());
    return taskableAsset;
  }

  public List<CommunicationAccount> computeSendToEmails(ClientContext client, ICompany company, TaskableAsset taskableAsset) {
    //TODO: Dan - fix this code.
    Long companyId = taskableAsset.getAssetCompanyId();
    if (companyId == null) {
      companyId = client.getCompanyId();
    }

    CompanyConfig companyConfig = companyConfigLogic.getCompanyConfigByCompanyId(client, companyId);
    String emailsToken = null;
    if (taskableAsset.getTaskType() == AssetTaskType.Car) {
      emailsToken = companyConfig.getAttributeAsString(TaskableAsset.TASKABLE_ASSET_CAR_NOTI_EMAILS, null);
    } else if (taskableAsset.getTaskType() == AssetTaskType.MeetingRoom) {
      emailsToken = companyConfig.getAttributeAsString(TaskableAsset.TASKABLE_ASSET_MEETING_ROOM_NOTI_EMAILS, null);
    } else {
      emailsToken = companyConfig.getAttributeAsString(TaskableAsset.TASKABLE_ASSET_OTHER_NOTI_EMAILS, null);
    }

    List<CommunicationAccount> commAccounts = new ArrayList<>();

    if (StringUtil.isNotEmpty(emailsToken)) {
      String[] emails = emailsToken.split(";");
      for (String email : emails) {
        Account account = accountLogic.getAccountByEmail(client, email.trim());
        Objects.assertNotNull(account, "Account is not found by Email: {}", email.trim());
        CommunicationAccount commAccount = commMessageLogic.getCommunicationAccount(client, account.getId());
        if (Objects.nonNull(commAccount)) commAccounts.add(commAccount);
      }
    }

    return commAccounts;
  }

  public TaskableAsset initTaskableAsset(ClientContext client, ICompany company, TaskableAsset tAsset) {
    tAsset = Objects.ensureNotNull(tAsset, TaskableAsset::new);
    Account clientAccount = accountLogic.getAccountById(client, client.getAccountId());
    tAsset.setCreatedByAccountId(clientAccount.getId());
    tAsset.setCreatedByAccountFullName(clientAccount.getFullName());
    tAsset.setPicLabel(clientAccount.getFullName());
    tAsset.setTaskType(AssetTaskType.MeetingRoom);
    tAsset.setSendEmail(true);
    if (tAsset.getUsingDate() == null) {
      LocalDateTime now = LocalDateTime.now();
      tAsset.setUsingDate(Date.from(now.atZone(ZoneId.systemDefault()).toInstant()));
      tAsset.setFromTime(Date.from(now.atZone(ZoneId.systemDefault()).toInstant()));
      tAsset.setToTime(Date.from(now.plusHours(1).atZone(ZoneId.systemDefault()).toInstant()));
    }
    return tAsset;
  }

  private Date formatTimeWithoutSeconds(Date date) {
    if (date == null) return null;

    Calendar cal = Calendar.getInstance();
    cal.setTime(date);
    cal.set(Calendar.SECOND, 0);
    cal.set(Calendar.MILLISECOND, 0);
    return cal.getTime();
  }

  public List<TaskableAsset> findUsedAssets(ClientContext client, ICompany company, FindAssetTimeConflictParams params) {
    Date fromTime = formatTimeWithoutSeconds(params.getFromTime());
    Date toTime = formatTimeWithoutSeconds(params.getToTime());
    return repo.findUsedAssets(params.getAssetId(), fromTime, toTime);
  }

  public List<TaskableAsset> findTasksByUsingDate(ClientContext client, AssetTaskType taskType, Date fromTime, Date toTime) {
    return repo.findTasksByUsingDate(taskType, fromTime, toTime);
  }

  public List<SqlMapRecord> searchTaskableAssets(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    String scriptDir  = appEnv.addonPath("core", "groovy");
    String scriptFile = "net/datatp/module/asset/groovy/AssetSql.groovy";
    if(!sqlParams.hasParam("companyId")) sqlParams.addParam("companyId", company.getId());
    return searchDbRecords(client, scriptDir, scriptFile, "SearchTaskableAssets", sqlParams);
  }

  public List<SqlMapRecord> searchCompanyTaskableAssets(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    String scriptDir  = appEnv.addonPath("core", "groovy");
    String scriptFile = "net/datatp/module/asset/groovy/AssetSql.groovy";
    if(!sqlParams.hasParam("companyId")) sqlParams.addParam("companyId", company.getId());
    if(!sqlParams.hasParam("assetCompanyId")) sqlParams.addParam("assetCompanyId", company.getId());
    return searchDbRecords(client, scriptDir, scriptFile, "SearchCompanyTaskableAssets", sqlParams);
  }

  public List<SqlMapRecord> searchAssetTasks(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    Long companyId = null;
    if (sqlParams.hasParam("companyCode") && StringUtil.isNotEmpty(sqlParams.getString("companyCode"))) {
      String companyCode = sqlParams.getString("companyCode");
      Company targetCompany = companyLogic.getCompany(client, companyCode);
      Objects.assertNotNull(targetCompany, "Company not found!!!, code : " + companyCode);
      companyId = targetCompany.getId();
    } else {
      companyId = client.getCompanyId();
    }
    sqlParams.addParam("assetCompanyId", companyId);
    sqlParams.addParam("companyId", client.getCompanyId());
    List<SqlMapRecord> taskableAsset = searchCompanyTaskableAssets(client, company, sqlParams);
    List<Long> ids = taskableAsset.stream().map(record -> record.getLong("id")).collect(Collectors.toList());
    if (Collections.isEmpty(taskableAsset)) return new ArrayList<>();
    else sqlParams.addParam("entityRefIds", ids);
    String scriptDir  = appEnv.addonPath("core", "groovy");
    String scriptFile = "net/datatp/module/wfms/groovy/EntityTaskSql.groovy";
    sqlParams.addParam("companyId", null);
    return searchDbRecords(client, scriptDir, scriptFile, "SearchEntityTask", sqlParams);
  }

  public boolean deleteTaskableAssetsDailyTaskByIds(ClientContext client, ICompany company, List<Long> ids) {
    for (Long id : ids) {
      TaskableAsset taskableAsset = getTaskableAsset(client, company, id);
      Objects.assertNotNull(taskableAsset, "TaskableAsset is not found by id = {}", id);
      taskPlugin.deleteEntityTaskByEntityRef(client, company, TaskableAsset.TABLE_NAME, taskableAsset.getId());
      repo.delete(taskableAsset);
    }
    return true;
  }

  public boolean changeTaskableAssetStorageState(ClientContext client, ICompany company, ChangeStorageStateRequest req) {
    for (Long id : req.getEntityIds()) {
      TaskableAsset taskableAsset = getTaskableAsset(client, company, id);
      Objects.assertNotNull(taskableAsset, "TaskableAsset is not found by id = {}", id);
      taskPlugin.updateEntityTaskStorageStateByEntityRef(client, company, TaskableAsset.TABLE_NAME, taskableAsset.getId(), req.getNewStorageState());
    }
    repo.updateStorageState(req.getNewStorageState(), req.getEntityIds());
    return true;
  }
}