package net.datatp.module.kpi.repo;

import java.io.Serializable;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import net.datatp.module.kpi.entity.KpiItemRule;

public interface KpiItemRuleRepository extends JpaRepository<KpiItemRule, Serializable> {
  @Query("SELECT r FROM KpiItemRule r WHERE r.companyId = :companyId AND r.id = :id")
  KpiItemRule getById(@Param("companyId") Long companyId, @Param("id") Long id);
}
