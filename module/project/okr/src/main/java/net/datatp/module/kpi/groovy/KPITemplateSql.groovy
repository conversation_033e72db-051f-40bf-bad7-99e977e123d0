package net.datatp.module.kpi.groovy

import org.springframework.context.ApplicationContext

import net.datatp.lib.executable.ExecutableContext
import net.datatp.lib.executable.Executor;
import net.datatp.module.data.db.ExecutableSqlBuilder
import net.datatp.util.ds.MapObject

public class KPITemplateSql extends Executor {
  public class SearchKpiItemRule extends ExecutableSqlBuilder {
    public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
      MapObject sqlParams = ctx.getParam("sqlParams");
      String query = """
        SELECT
          r.*
        FROM kpi_item_rule r
        WHERE
            ${FILTER_BY_STORAGE_STATE('r', sqlParams)}
            ${AND_FILTER_BY_PARAM('r.company_id', 'companyId', sqlParams)}
            ${ORDER_BY(sqlParams)}
            ${MAX_RETURN(sqlParams)}
      """;
      return query;
    }
  }
  
  public class SearchKpiTemplate extends ExecutableSqlBuilder {
    public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
      MapObject sqlParams = ctx.getParam("sqlParams");
      String query = """
        SELECT
          t.*,
          COUNT(k.id) as number_apply_for
        FROM kpi_template t
        LEFT JOIN kpi_kpi k ON k.template_id = t.id 
        WHERE
            ${FILTER_BY_STORAGE_STATE('t', sqlParams)}
            ${AND_FILTER_BY_PARAM('t.company_id', 'companyId', sqlParams)}
            AND (
             ('system' = :space ) OR
             ('company' = :space  AND 'Owner' = :scope AND (t.manager_account_id = :accessAccountId OR t.leader_account_id = :accessAccountId)) OR
             ('company' = :space  AND 'Owner' != :scope)
            )
        GROUP BY t.id
        ${ORDER_BY(sqlParams)}
        ${MAX_RETURN(sqlParams)}
      """;
      return query;
    }
  }
  
  public class SearchKpiTemplateItem extends ExecutableSqlBuilder {
    public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
      MapObject sqlParams = ctx.getParam("sqlParams");
      String query = """
        SELECT
          i.*
        FROM kpi_template_item i
        WHERE
            ${FILTER_BY_STORAGE_STATE('i', sqlParams)}
            ${AND_FILTER_BY_PARAM('i.template_id', 'kpiTemplateId', sqlParams)}
            ${AND_FILTER_BY_PARAM('i.company_id',  'companyId', sqlParams)}
            ${AND_FILTER_BY_PARAM('i.objective_type',  'objectiveType', sqlParams)}
        ${ORDER_BY(sqlParams)}
        ${MAX_RETURN(sqlParams)}
      """;
      println(query)
      return query;
    }
  }
  
  public KPITemplateSql() {
    register(new SearchKpiItemRule());
    register(new SearchKpiTemplate());
    register(new SearchKpiTemplateItem());
  }
}