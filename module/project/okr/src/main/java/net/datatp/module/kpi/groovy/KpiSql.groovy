package net.datatp.module.kpi.groovy;

import org.springframework.context.ApplicationContext

import net.datatp.lib.executable.ExecutableContext
import net.datatp.lib.executable.Executor;
import net.datatp.module.data.db.ExecutableSqlBuilder
import net.datatp.util.ds.MapObject

public class KpiSql extends Executor {
  public class SearchKpi extends ExecutableSqlBuilder {
    public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
      MapObject sqlParams = ctx.getParam("sqlParams");
      String query = """
        SELECT
          kpi.*
        FROM kpi_kpi kpi
        WHERE
            ${FILTER_BY_STORAGE_STATE('kpi', sqlParams)}
            ${AND_FILTER_BY_PARAM('kpi.template_id', 'kpiTemplateId', sqlParams)}
            ${AND_FILTER_BY_PARAM('kpi.status', 'status', sqlParams)}
            ${AND_FILTER_BY_PARAM('kpi.company_id', 'companyId', sqlParams)}
            AND (
             ('system' = :space ) OR
             ('company' = :space  AND 'Owner' = :scope AND (kpi.manager_account_id = :accessAccountId OR kpi.leader_account_id = :accessAccountId)) OR
             ('company' = :space  AND 'Owner' != :scope) OR
             ('user' = :space AND kpi.employee_account_id = :accessAccountId)
            )
            ORDER BY 
              CASE kpi.status 
                  WHEN 'PlanReviewing' THEN 1
                  WHEN 'ResultReviewing' THEN 2
                  WHEN 'Plan' THEN 3
                  WHEN 'Processing' THEN 4
                  WHEN 'ManagerApproved' THEN 5
                  WHEN 'DirectorApproved' THEN 6
                  ELSE 7
              END
            ${MAX_RETURN(sqlParams)}
      """;
      return query;
    }
  }
  
  public class SearchKpiRawData extends ExecutableSqlBuilder {
    public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
      MapObject sqlParams = ctx.getParam("sqlParams");
      
      String query = """
        SELECT 
          kpi.id AS kpi_id,
          kpi.employee_account_id,
          kpi.employee_full_name,
          kpi.employee_code,
          kpi.job_title,
          kpi.job_code,
          kpi.department,
          kpi.milestone_label,
          kpi.status,
          kpi.workplace,
          kpi.company_branch,
          kpi.from_date,
          kpi.to_date,
          kpi.manager_account_id,
          kpi.manager_full_name,
          kpi.leader_account_id,
          kpi.leader_full_name,
          kpi.kpi_result,
          kpi.grade,
          kpi.final_grade,

          item.id AS item_id,
          item.objective_type,
          item.label AS item_label,
          item.contribution_weight,
          item.unit,
          item.target_value,
          item.actual_value,
          item.employee_adjust_value,
          item.employee_adjust_explanation,
          item.manager_adjust_value,
          item.manager_adjust_explanation,
          item.calculate_algorithm,
          item.description
        FROM kpi_item item
        LEFT JOIN kpi_kpi kpi ON kpi.id = item.kpi_id
        WHERE 1=1 
          ${AND_FILTER_BY_STORAGE_STATE('item', sqlParams)}
          ${AND_FILTER_BY_STORAGE_STATE('kpi', sqlParams)}
          ${AND_FILTER_BY_PARAM('kpi.company_id', 'companyId', sqlParams)}
          ${AND_FILTER_BY_PARAM('kpi.template_id', 'kpiTemplateId', sqlParams)}
          AND (
           ('system' = :space ) OR
           ('company' = :space  AND 'Owner' = :scope AND (kpi.manager_account_id = :accessAccountId OR kpi.leader_account_id = :accessAccountId)) OR
           ('company' = :space  AND 'Owner' != :scope) OR
           ('user' = :space AND kpi.employee_account_id = :accessAccountId)
          )
        ORDER BY kpi.employee_account_id, item.objective_type 
      """;
      return query;
    }
  }
  
  public KpiSql() {
    register(new SearchKpi());
    register(new SearchKpiRawData());
  }
}