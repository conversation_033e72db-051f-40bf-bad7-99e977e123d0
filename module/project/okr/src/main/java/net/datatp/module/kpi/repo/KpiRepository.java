package net.datatp.module.kpi.repo;

import java.io.Serializable;
import java.util.List;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import net.datatp.module.data.db.entity.StorageState;
import net.datatp.module.data.db.repository.DataTPRepository;
import net.datatp.module.kpi.entity.Kpi;

public interface KpiRepository extends DataTPRepository<Kpi, Serializable> {
  @Query("SELECT k FROM Kpi k WHERE k.id = :id")
  Kpi getById(@Param("id") Long id);

  @Query("SELECT k FROM Kpi k WHERE k.templateId = :templateId")
  List<Kpi> findKpiByTemplateId(@Param("templateId") Long templateId);
  
  @Query("SELECT k FROM Kpi k WHERE k.templateId IN :templateIds")
  List<Kpi> findKpiByTemplateIds(@Param("templateIds") List<Long> templateIds);
  
  @Query("SELECT p FROM Kpi p WHERE p.id IN :ids")
  List<Kpi> findByIds(@Param("ids") List<Long> ids);
  
  @Modifying
  @Query("UPDATE Kpi t SET t.storageState = :storageState WHERE t.companyId = :companyId AND t.id IN (:ids)")
  int updateStorageState(@Param("companyId") Long companyId, @Param("storageState") StorageState state, @Param("ids") List<Long> ids);
}
