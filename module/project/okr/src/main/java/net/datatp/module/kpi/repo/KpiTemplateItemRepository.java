package net.datatp.module.kpi.repo;

import java.io.Serializable;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import net.datatp.module.kpi.entity.KpiTemplateItem;

public interface KpiTemplateItemRepository extends JpaRepository<KpiTemplateItem, Serializable> {
  @Query("SELECT i FROM KpiTemplateItem i WHERE i.id = :id")
  KpiTemplateItem getById(@Param("id") Long id);
  
  @Query("SELECT i FROM KpiTemplateItem i WHERE i.templateId = :templateId")
  List<KpiTemplateItem> findByTemplateId(@Param("templateId") Long templateId);

  @Query("SELECT o.id FROM KpiTemplateItem o WHERE o.templateId IN :templateIds")
  List<Long> findIdByTemplateIds(@Param("templateIds") List<Long> templateIds);
}
