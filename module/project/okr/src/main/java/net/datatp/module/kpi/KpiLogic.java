package net.datatp.module.kpi;

import java.util.ArrayList;
import java.util.List;

import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import net.datatp.lib.executable.ExecutableContext;
import net.datatp.module.account.AccountLogic;
import net.datatp.module.account.entity.Account;
import net.datatp.module.backend.Notification;
import net.datatp.module.backend.Notification.Type;
import net.datatp.security.client.ClientContext;
import net.datatp.security.client.DataScope;
import net.datatp.module.company.CompanyLogic;
import net.datatp.module.company.entity.Company;
import net.datatp.module.core.security.AuthorizationCipherTool;
import net.datatp.module.core.security.SecurityLogic;
import net.datatp.module.core.security.SessionData;
import net.datatp.module.core.security.entity.AppPermission;
import net.datatp.module.data.db.DAOService;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.hr.EmployeeLogic;
import net.datatp.module.hr.entity.Employee;
import net.datatp.module.http.upload.UploadResource;
import net.datatp.module.http.upload.UploadService;
import net.datatp.module.kpi.entity.Kpi;
import net.datatp.module.kpi.entity.KpiGrade;
import net.datatp.module.kpi.entity.KpiItem;
import net.datatp.module.kpi.entity.KpiItemAttachment;
import net.datatp.module.kpi.entity.KpiObjectiveType;
import net.datatp.module.kpi.entity.KpiStatus;
import net.datatp.module.kpi.entity.KpiTemplate;
import net.datatp.module.kpi.repo.KpiItemAttachmentRepository;
import net.datatp.module.kpi.repo.KpiItemRepository;
import net.datatp.module.kpi.repo.KpiRepository;
import net.datatp.module.kpi.repo.KpiTemplateRepository;
import net.datatp.module.service.ExecutableUnitManager;
import net.datatp.module.storage.CompanyStorage;
import net.datatp.module.storage.IStorageService;
import net.datatp.module.storage.StorageResource;
import net.datatp.util.ds.Objects;
import net.datatp.util.text.DateUtil;
import net.datatp.util.text.StringUtil;

@Slf4j
@Component
public class KpiLogic extends DAOService {
  @Autowired @Getter
  private  KpiRepository kpiRepo;

  @Autowired @Getter
  private KpiItemRepository kpiItemRepo;

  @Autowired
  private KpiTemplateRepository kpiTmplRepo;

  @Autowired
  private KpiItemAttachmentRepository attachmentRepo;

  @Autowired
  private EmployeeLogic employeeLogic;

  @Autowired
  private AccountLogic accountLogic;

  @Autowired
  private CompanyLogic companyLogic;
  
  @Autowired
  private SecurityLogic securityLogic;

  @Autowired
  private ExecutableUnitManager executableUnitManager;

  @Autowired
  private IStorageService storageService;

  @Autowired
  private AuthorizationCipherTool cipherTool;

  @Autowired
  private UploadService              uploadService;

  public Kpi getKpi(ClientContext client, ICompany company, Long id) {
    if(log.isDebugEnabled()) {
      log.debug("Get Kpi id = {}", id);
    }
    return kpiRepo.getById(id);
  }

  public List<Kpi> findKpiByTemplateId(ClientContext client, ICompany company, Long templateId) {
    return kpiRepo.findKpiByTemplateId(templateId);
  }

  public Kpi saveKpi(ClientContext client, ICompany company, Kpi kpi) {
    kpi.set(client, company);
    return kpiRepo.save(kpi);
  }

  public Notification deleteKpis(ClientContext client, ICompany company, List<Long> ids) {
    String scriptDir = appEnv.addonPath("core", "groovy");
    ExecutableContext ctx = new ExecutableContext(client, company)
        .withScriptEnv(scriptDir, KpiLogicExecutor.class, KpiLogicExecutor.DeleteKpi.class)
        .withParam("ids", ids);
    return (Notification) executableUnitManager.execute(ctx);
  }

  public boolean updateKpiStorageState(ClientContext clientCtx, ICompany company, ChangeStorageStateRequest req) {
    kpiRepo.updateStorageState(company.getId(), req.getNewStorageState(), req.getEntityIds());
    return true;
  }
  
  public Kpi createKpi(ClientContext client, ICompany company, KpiTemplate kpiTmpl) {
    Kpi kpi = new Kpi();
    return createKpiFromTemplate(client, company, kpiTmpl, kpi);
  }

  public Kpi createKpiFromTemplate(ClientContext client, ICompany company, KpiTemplate kpiTmpl, Kpi initKpi) {
    ExecutableContext ctx =
        new ExecutableContext(client, company)
        .withScriptEnv(getScriptDir(), KpiLogicExecutor.class, KpiLogicExecutor.CreateKpiFromTemplate.class)
        .withParam(kpiTmpl)
        .withParam(initKpi);
    return (Kpi) executableUnitManager.execute(ctx);
  }

  public Notification updateKpiFinalGrade(ClientContext client, ICompany company, List<Long> ids, KpiGrade newGrade) {
    for (Long id : ids) {
      Kpi kpi = getKpi(client, company, id);
      kpi.setFinalGrade(newGrade);
    }
    return null;
  }

  public Notification updateKpiFromTemplate(ClientContext client, ICompany company, KpiTemplate kpiTmpl) {
    ExecutableContext ctx =
        new ExecutableContext(client, company)
        .withScriptEnv(getScriptDir(), KpiLogicExecutor.class, KpiLogicExecutor.UpdateKpiFromTemplate.class)
        .withParam(kpiTmpl);
    return (Notification) executableUnitManager.execute(ctx);
  }

  public Notification createKpiForEmployees(ClientContext client, ICompany company, Long kpiTmplId, List<Long> employeeIds) {
    KpiTemplate kpiTmpl = kpiTmplRepo.getById(kpiTmplId);
    Objects.assertNotNull(kpiTmpl, "KpiTemplate {} is not found", kpiTmplId);

    for (Long employeeId : employeeIds) {
      Employee employee = employeeLogic.getEmployee(client, company, employeeId);
      Objects.assertNotNull(employee, "Employee {} is not found", employeeId);

      Account account = accountLogic.getAccountById(client, employee.getAccountId());
      Objects.assertNotNull(account, "assertNotNull {} is not found", employee.getAccountId());

      Kpi kpi = new Kpi();
      kpi.setEmployeeAccountId(account.getId());
      kpi.setEmployeeFullName(account.getFullName());
      kpi.setEmployeeCode(employee.getEmployeeCode());

      kpi = createKpiFromTemplate(client, company, kpiTmpl, kpi);
    }

    return new Notification(Type.info, "Create Employee Kpis", "")
        .withParam("createCount", employeeIds.size())
        .withMessage("Create {{createCount}} Employee kpis");
  }

  public List<SqlMapRecord> searchKpis(ClientContext client, ICompany company, SqlQueryParams params) {
    params.addParam("accessAccountId", client.getAccountId());
    if (params.getParam("companyCode") != null) {
      String companyCode = params.getString("companyCode");
      Company _company = companyLogic.getCompany(client, companyCode);
      params.addParam("companyId", _company.getId());
    } else {
      params.addParam("companyId", client.getCompanyId());
    }
    
    String space = params.getString("space");
    if ("company".equals(space) || "system".equals(space)) {
      AppPermission permission = securityLogic.getAppPermission(client, client.getCompanyId(), "hr", "company-kpi");
      if (permission == null) return java.util.Collections.emptyList();
      DataScope dataScope = permission.getDataScope();
      params.addParam("scope", dataScope.toString());
    } else {
      params.addParam("scope", DataScope.Owner.toString());
    }
    
    return searchDbRecords(client, getScriptDir(), getScriptFile("KpiSql.groovy"), "SearchKpi", params);
  }

  public List<SqlMapRecord> searchKpiRawData(ClientContext client, ICompany company, SqlQueryParams params) {
    params.addParam("accessAccountId", client.getAccountId());
    if (params.getParam("companyCode") != null) {
      String companyCode = params.getString("companyCode");
      Company _company = companyLogic.getCompany(client, companyCode);
      params.addParam("companyId", _company.getId());
    } else {
      params.addParam("companyId", client.getCompanyId());
    }
    
    String space = params.getString("space");
    if ("company".equals(space)) {
      AppPermission permission = securityLogic.getAppPermission(client, client.getCompanyId(), "hr", "company-kpi");
      if (permission == null) return java.util.Collections.emptyList();
      DataScope dataScope = permission.getDataScope();
      params.addParam("scope", dataScope.toString());
    }else {
      params.addParam("scope", DataScope.Owner.toString());
    }
    
    return searchDbRecords(client, getScriptDir(), getScriptFile("KpiSql.groovy"), "SearchKpiRawData", params);
  }

  public Notification approveKpiByManager(ClientContext client, ICompany company, List<Long> ids) {
    for (Long id : ids) {
      Kpi kpi = getKpi(client, company, id);
      kpi.setStatus(KpiStatus.DirectorApproved);
      if (kpi.getFinalGrade() == null) kpi.setFinalGrade(kpi.getGrade());
      saveKpi(client, company, kpi);
    }
    return new Notification(Type.info, "Approve KPIs", "")
        .withParam("count", ids.size())
        .withMessage("Approved {{count}} KPIs by Director");
  }

  public KpiItem getKpiItem(ClientContext client, ICompany company, Long id) {
    return kpiItemRepo.getById(id);
  }

  public boolean deleteKpiItems(ClientContext client, ICompany company, List<Long> ids) {
    kpiItemRepo.deleteAllById(ids);
    return true;
  }

  public List<KpiItem> findKpiItemByKpiId(ClientContext client, ICompany company, Long kpiId) {
    return kpiItemRepo.findByKpiId(kpiId);
  }

  public KpiItem saveKpiItem(ClientContext client, ICompany company, KpiItem item) {
    item.set(client, company);
    if (item.isNew()) {
      item.withStroragePath(createKpiItemStoragePath(item));
    }
    return kpiItemRepo.save(client, company, item);
  }

  public List<KpiItem> saveKpiItems(ClientContext client, ICompany company, Long kpiId, List<KpiItem> items) {
    Kpi kpi = getKpi(client, company, kpiId);
    kpi.calculate(items);
    saveKpi(client, company, kpi);
    return kpiItemRepo.saveAll(client, company, items);
  }


  String createKpiItemStoragePath(KpiItem item) {
    String monthId = DateUtil.asCompactMonthId(item.getCreatedTime());
    return "kpi/" + monthId + "/" + item.getKpiId() + "/" + item.getId();
  }

  String getKpiItemAttachmentPath(KpiItem kpiItem) {
    return kpiItem.getStoragePath() + "/attachments";
  }

  public List<KpiItemAttachment> findKpiItemAttachments(ClientContext client, ICompany company, Long kpiItemId) {

    List<KpiItemAttachment> atts = attachmentRepo.findTaskAttachments(kpiItemId);
    for (KpiItemAttachment att : atts) {
      String path = att.getResourceUri();
      SessionData sessionData = new SessionData(client.getToken(), path);
      att.setStoreInfo(cipherTool.encryptSessionData(sessionData));
    }

    return atts;
  }

  public List<KpiItemAttachment> saveKpiItemAttachment(
    ClientContext client, ICompany company, Long kpiItemId, List<KpiItemAttachment> attachments) {
    KpiItem kpiItem = getKpiItem(client, company, kpiItemId);
    if (StringUtil.isEmpty(kpiItem.getStoragePath())) {
      kpiItem.setStoragePath(createKpiItemStoragePath(kpiItem));
    }
    kpiItem = saveKpiItem(client, company, kpiItem);

    CompanyStorage storage = storageService.createCompanyStorage(client, company.getCode());
    String storagePath = getKpiItemAttachmentPath(kpiItem);
    for (KpiItemAttachment attachment : attachments) {
      UploadResource attResource = attachment.getUploadResource();
      String storeId = attachment.getUploadResource().getStoreId();
      byte[] contentBytes = uploadService.load(storeId);
      StorageResource resource = new StorageResource(attResource.getName(), contentBytes);
      resource = storage.save(storagePath, resource);
      String uri = storagePath + "/" + attResource.getName();
      attachment.setKpiItemId(kpiItem.getId());
      attachment.setDownloadUri(uri);
      attachment.setPublicDownloadUri(resource.getPublicDownloadUri());
      attachment.setResourceUri(uri);
      attachment.setSize(resource.toBytes().length);
      attachment.set(client, company.getId());
    }
    attachments = attachmentRepo.saveAll(attachments);
    List<Long> idSet = KpiItemAttachment.getIds(attachments);
    attachmentRepo.deleteOrphan(kpiItem.getId(), idSet);
    return attachments;
  }

  public KpiModel getKpiModel(ClientContext client, ICompany company, Long kpiId) {
    KpiModel model = new KpiModel();
    Kpi kpi = getKpi(client, company, kpiId);
    model.setKpi(kpi);
    model.setWorkList(new ArrayList<>());
    model.setBehaviorList(new ArrayList<>());
    List<KpiItem> items = findKpiItemByKpiId(client, company, kpiId);
    for (KpiItem item : items) {
      KpiItemModel itemModel = new KpiItemModel(item);
      if (item.getObjectiveType() == KpiObjectiveType.Work) model.getWorkList().add(itemModel);
      if (item.getObjectiveType() == KpiObjectiveType.Behavior) model.getBehaviorList().add(itemModel);
    }

    model.getWorkList().sort(KpiItemModel.BY_CREATED_TIME_COMPARATOR);
    model.getBehaviorList().sort(KpiItemModel.BY_CREATED_TIME_COMPARATOR);
    return model;
  }

  public KpiModel saveKpiModel(ClientContext client, ICompany company, KpiModel model) {
    Kpi kpi = model.getKpi();
    saveKpiItemModels(client, company, model.getWorkList());
    saveKpiItemModels(client, company, model.getBehaviorList());
    kpiItemRepo.flush();

    List<KpiItem> items = findKpiItemByKpiId(client, company, kpi.getId());
    kpi.calculate(items);
    kpi = saveKpi(client, company, kpi);
    model.setKpi(kpi);
    return model;
  }

  private void saveKpiItemModels(ClientContext client, ICompany company, List<KpiItemModel> itemModels) {
    for (KpiItemModel model : itemModels) {
      KpiItem item = getKpiItem(client, company, model.getId());
      if (Objects.isNull(item)) item = new KpiItem();
      item = model.mapKpiItem(item);
      item = saveKpiItem(client, company, item);
      model.withItem(item);
    }
  }

  String getScriptFile(String name) {
    return "net/datatp/module/kpi/groovy/" + name;
  }

  String getScriptDir() {
    return appEnv.addonPath("core", "groovy");
  }

  public Logger getLogger() { return log; }
}
