package net.datatp.module.kpi;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import net.datatp.lib.executable.ExecutableContext;
import net.datatp.module.account.AccountLogic;
import net.datatp.module.account.entity.Account;
import net.datatp.module.backend.Notification;
import net.datatp.module.core.security.SecurityLogic;
import net.datatp.module.core.security.entity.AppPermission;
import net.datatp.module.data.db.DAOService;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.kpi.entity.KpiCalculateAlgorithm;
import net.datatp.module.kpi.entity.KpiItemRule;
import net.datatp.module.kpi.entity.KpiObjectiveType;
import net.datatp.module.kpi.entity.KpiTemplate;
import net.datatp.module.kpi.entity.KpiTemplateItem;
import net.datatp.module.kpi.repo.KpiItemRuleRepository;
import net.datatp.module.kpi.repo.KpiTemplateItemRepository;
import net.datatp.module.kpi.repo.KpiTemplateRepository;
import net.datatp.module.service.ExecutableUnitManager;
import net.datatp.security.client.ClientContext;
import net.datatp.security.client.DataScope;
import net.datatp.util.dataformat.DataSerializer;
import net.datatp.util.ds.Objects;

@Slf4j
@Component
public class KpiTemplateLogic extends DAOService {
  @Autowired
  private KpiItemRuleRepository itemRuleRepo;

  @Autowired
  @Getter
  private KpiTemplateRepository tmplRepo;

  @Autowired
  @Getter
  private KpiTemplateItemRepository itemTmplRepo;

  @Autowired
  private AccountLogic accountLogic;
  
  @Autowired
  private SecurityLogic securityLogic;

  @Autowired
  private ExecutableUnitManager executableUnitManager;

  public KpiItemRule getKpiItemRule(ClientContext client, ICompany company, Long id) {
    return itemRuleRepo.getById(company.getId(), id);
  }

  public KpiItemRule saveKpiItemRule(ClientContext client, ICompany company, KpiItemRule rule) {
    rule.set(client, company);
    rule = itemRuleRepo.save(rule);
    return rule;
  }

  public List<SqlMapRecord> searchKpiItemRules(ClientContext client, ICompany company, SqlQueryParams params) {
    String scriptDir = appEnv.addonPath("core", "groovy");
    String scriptFile = "net/datatp/module/kpi/groovy/KPITemplateSql.groovy";
    return searchDbRecords(client, scriptDir, scriptFile, "SearchKpiItemRule", params);
  }
  
  public List<KpiTemplate> findKpiTemplateByCompanyId(ClientContext client) {
    return tmplRepo.findByCompanyId(client.getCompanyId());
  }

  public KpiTemplate getKpiTemplate(ClientContext client, ICompany company, Long id) {
    if (log.isDebugEnabled()) {
      log.debug("Get Kpi Template id = {}", id);
    }
    KpiTemplate tmpl = tmplRepo.getById(id);
    if (log.isDebugEnabled()) {
      log.debug("Kpi Template:\n {}", DataSerializer.JSON.toString(tmpl));
    }
    return tmpl;
  }

  public KpiTemplate getKpiTemplateByJobCode(ClientContext client, ICompany company, String milestone, String jobCode) {
    if (log.isDebugEnabled()) {
      log.debug("Get Kpi Template milestone = {}, job code = {}", milestone, jobCode);
    }
    KpiTemplate tmpl = tmplRepo.getByJobCode(company.getId(), milestone, jobCode);
    if (log.isDebugEnabled()) {
      log.debug("Kpi Template:\n {}", DataSerializer.JSON.toString(tmpl));
    }
    return tmpl;
  }

  public KpiTemplate saveKpiTemplate(ClientContext client, ICompany company, KpiTemplate tmpl) {
    if (log.isDebugEnabled()) {
      log.debug("Save Kpi Template");
    }
    tmpl.set(client, company);

    if (tmpl.isNew()) {
      tmpl = tmplRepo.save(tmpl);
      createKpiTemplateItemBehavior(client, company, tmpl);
    } else {
      tmpl = tmplRepo.save(tmpl);
    }

    if (log.isDebugEnabled()) {
      log.debug("Saved Kpi Template:\n {}", DataSerializer.JSON.toString(tmpl));
    }
    return tmpl;
  }

  private void createKpiTemplateItemBehavior(ClientContext client, ICompany company, KpiTemplate template) {
    String tieuchi1 = "Không tuân thủ nội quy, văn hóa công ty: tác phong và trang phục nơi làm việc, thời gian làm việc.";
    String tieuchi2 = "Bị khách hàng nội bộ/bên ngoài phản ảnh, khiếu nại về thái độ phục vụ không hợp tác, chậm trễ, lãng phí thời gian, không phản hồi/chia sẻ thông tin.";
    String tieuchi3 = "Tự ý giải quyết công việc vượt cấp ngoài thẩm quyền cho phép";
    String tieuchi4 = "Thiếu sự chủ động hoặc/và thiếu cẩn trọng, ẩu trong xử lý công việc";
    String tieuchi5 = "Để xảy ra sai sót trong công việc (báo cáo sai, ban hành văn bản sai), hoặc trễ hạn các công việc được cấp quản lý trực tiếp giao.";

    KpiTemplateItem item1 = createTemplateBehaviorItem(template, tieuchi1);
    saveKpiTemplateItem(client, company, item1);
    KpiTemplateItem item2 = createTemplateBehaviorItem(template, tieuchi2);
    saveKpiTemplateItem(client, company, item2);
    KpiTemplateItem item3 = createTemplateBehaviorItem(template, tieuchi3);
    saveKpiTemplateItem(client, company, item3);
    KpiTemplateItem item4 = createTemplateBehaviorItem(template, tieuchi4);
    saveKpiTemplateItem(client, company, item4);
    KpiTemplateItem item5 = createTemplateBehaviorItem(template, tieuchi5);
    saveKpiTemplateItem(client, company, item5);
  }

  private KpiTemplateItem createTemplateBehaviorItem(KpiTemplate template, String label) {
    KpiTemplateItem item = new KpiTemplateItem();
    item.setJobCode(template.getJobCode());
    item.setLabel(label);
    item.setUnit("Số điểm");
    item.setObjectiveType(KpiObjectiveType.Behavior);
    item.setCalculateAlgorithm(KpiCalculateAlgorithm.RATIO_BASED);
    item.setMeasurementMethod("(%) hoàn thành = Thực hiện/Kế hoạch.\r\nKết quả KPI = (%) hoàn thành x Trọng số.");
    item.setTargetValue(4);
    item.setContributionWeight(0.2);
    item.setTemplateId(template.getId());
    return item;
  }
  
  public KpiTemplate copyKpiTemplate(ClientContext client, ICompany company, Long rootId, KpiTemplate template) {
    template = saveKpiTemplate(client, company, template);
    List<KpiTemplateItem> rootItems = findByKpiTemplateItemByTemplateId(client, company, rootId);
    for (KpiTemplateItem rootItem : rootItems) {
      if (rootItem.getObjectiveType() == KpiObjectiveType.Behavior) continue;
      
      KpiTemplateItem newItem = new KpiTemplateItem(rootItem);
      newItem.setTemplateId(template.getId());
      newItem.setJobCode(template.getJobCode());
      saveKpiTemplateItem(client, company, newItem);
    }
    return template;
  }

  public List<SqlMapRecord> searchKpiTemplates(ClientContext client, ICompany company, SqlQueryParams params) {
    String scriptDir = appEnv.addonPath("core", "groovy");
    String scriptFile = "net/datatp/module/kpi/groovy/KPITemplateSql.groovy";
    params.addParam("companyId", company.getId());
    params.addParam("accessAccountId", client.getAccountId());
    
    String space = params.getString("space");
    if ("company".equals(space) || "system".equals(space)) {
      AppPermission permission = securityLogic.getAppPermission(client, client.getCompanyId(), "hr", "company-kpi");
      if (permission == null) return java.util.Collections.emptyList();
      DataScope dataScope = permission.getDataScope();
      params.addParam("scope", dataScope.toString());
    }else {
      params.addParam("scope", DataScope.Owner.toString());
    }
    
    return searchDbRecords(client, scriptDir, scriptFile, "SearchKpiTemplate", params);
  }

  public KpiTemplateItem getKpiTemplateItem(ClientContext client, ICompany company, Long id) {
    return itemTmplRepo.getById(id);
  }

  public List<KpiTemplateItem> findByKpiTemplateItemByTemplateId(ClientContext client, ICompany company, Long templateId) {
    return itemTmplRepo.findByTemplateId(templateId);
  }

  public KpiTemplateItem saveKpiTemplateItem(ClientContext client, ICompany company, KpiTemplateItem item) {
    if (log.isDebugEnabled()) {
      log.debug("Save Kpi Template Item");
    }
    item.set(client, company);
    item = itemTmplRepo.save(item);
    if (log.isDebugEnabled()) {
      log.debug("Saved the kpi template item:\n {}", DataSerializer.JSON.toString(item));
    }
    return item;
  }

  public List<SqlMapRecord> searchKpiTemplateItems(ClientContext client, ICompany company, SqlQueryParams params) {
    String scriptDir = appEnv.addonPath("core", "groovy");
    String scriptFile = "net/datatp/module/kpi/groovy/KPITemplateSql.groovy";
    params.addParam("companyId", company.getId());
    return searchDbRecords(client, scriptDir, scriptFile, "SearchKpiTemplateItem", params);
  }

  public boolean deleteKpiTemplateItems(ClientContext client, ICompany company, List<Long> ids) {
    itemTmplRepo.deleteAllById(ids);
    return true;
  }

  public Notification deleteKpiTemplates(ClientContext client, ICompany company, List<Long> ids) {
    String scriptDir = appEnv.addonPath("core", "groovy");
    ExecutableContext ctx = new ExecutableContext(client, company)
        .withScriptEnv(scriptDir, KpiLogicExecutor.class, KpiLogicExecutor.DeleteKpiTemplate.class)
        .withParam("ids", ids);
    return (Notification) executableUnitManager.execute(ctx);
  }

  public boolean updateKpiTemplateStorageState(ClientContext clientCtx, ICompany company, ChangeStorageStateRequest req) {
    tmplRepo.updateStorageState(company.getId(), req.getNewStorageState(), req.getEntityIds());
    return true;
  }
  
  public KpiTemplateModel getKpiTemplateModel(ClientContext client, ICompany company, Long kpiTemplateId) {
    KpiTemplateModel model = new KpiTemplateModel();
    KpiTemplate template = getKpiTemplate(client, company, kpiTemplateId);
    model.setTemplate(template);
    model.setWorkList(new ArrayList<>());
    model.setBehaviorList(new ArrayList<>());
    List<KpiTemplateItem> items = findByKpiTemplateItemByTemplateId(client, company, kpiTemplateId);
    for (KpiTemplateItem item : items) {
      KpiTemplateItemModel itemModel = new KpiTemplateItemModel(item);
      if (item.getObjectiveType() == KpiObjectiveType.Work) model.getWorkList().add(itemModel);
      if (item.getObjectiveType() == KpiObjectiveType.Behavior) model.getBehaviorList().add(itemModel);
    }

    model.getWorkList().sort(KpiTemplateItemModel.BY_CREATED_TIME_COMPARATOR);
    model.getBehaviorList().sort(KpiTemplateItemModel.BY_CREATED_TIME_COMPARATOR);
    return model;
  }
  
  public KpiTemplateModel saveKpiTemplateModel(ClientContext client, ICompany company, KpiTemplateModel model) {
    KpiTemplate template = model.getTemplate();
    saveKpiTemplateItemModels(client, company, model.getWorkList());
    template = saveKpiTemplate(client, company, template);
    model.setTemplate(template);
    return model;
  }

  private List<KpiTemplateItemModel> saveKpiTemplateItemModels(ClientContext client, ICompany company, List<KpiTemplateItemModel> itemModels) {
    for (KpiTemplateItemModel model : itemModels) {
      KpiTemplateItem kpiTemplateItem = getKpiTemplateItem(client, company, model.getId());
      if (Objects.isNull(kpiTemplateItem)) kpiTemplateItem = new KpiTemplateItem();
      kpiTemplateItem = model.mapKpiTemplateItem(kpiTemplateItem);
      kpiTemplateItem = saveKpiTemplateItem(client, company, kpiTemplateItem);
      model.withItem(kpiTemplateItem);
    }
    return itemModels;
  }

}
