package net.datatp.module.kpi.repo;

import java.io.Serializable;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import net.datatp.module.data.db.entity.StorageState;
import net.datatp.module.kpi.entity.KpiTemplate;

public interface KpiTemplateRepository extends JpaRepository<KpiTemplate, Serializable> {
  @Query("SELECT t FROM KpiTemplate t WHERE t.id = :id")
  KpiTemplate getById(@Param("id") Long id);
  
  @Query(
      """
        SELECT t FROM KpiTemplate t 
        WHERE t.companyId = :companyId AND t.milestone = :milestone AND t.jobCode = :jobCode
      """
  )
  KpiTemplate getByJobCode(
      @Param("companyId") Long companyId, @Param("milestone") String milestone, @Param("jobCode") String jobCode);

  @Query("SELECT p FROM KpiTemplate p WHERE p.id IN :ids")
  List<KpiTemplate> findByIds(@Param("ids") List<Long> ids);
  
  @Query("SELECT p FROM KpiTemplate p WHERE p.companyId = :companyId AND p.milestone = :milestone")
  List<KpiTemplate> findByMilestone(@Param("companyId") Long companyId, @Param("milestone") String milestone);

  @Query("SELECT p FROM KpiTemplate p WHERE p.companyId = :companyId")
  List<KpiTemplate> findByCompanyId(@Param("companyId") Long companyId);
  
  @Modifying
  @Query("UPDATE KpiTemplate t SET t.storageState = :storageState WHERE t.companyId = :companyId AND t.id IN (:ids)")
  int updateStorageState(@Param("companyId") Long companyId, @Param("storageState") StorageState state, @Param("ids") List<Long> ids);
}
