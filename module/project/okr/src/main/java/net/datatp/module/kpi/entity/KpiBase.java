package net.datatp.module.kpi.entity;

import java.io.Serial;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import jakarta.persistence.Column;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.MappedSuperclass;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.company.entity.CompanyEntity;
import net.datatp.module.data.db.entity.EditMode;
import net.datatp.util.text.DateUtil;

@MappedSuperclass
@JsonInclude(Include.NON_NULL)
@NoArgsConstructor @Getter @Setter
abstract public class KpiBase extends CompanyEntity {
  @Serial
  private static final long serialVersionUID = 1L;
  
  @Column(name = "edit_mode")
  @Enumerated(EnumType.STRING)
  protected EditMode editMode = EditMode.DRAFT;
  
  @Column(name="job_code")
  protected String jobCode;
  
  @Column(name="job_title")
  protected String jobTitle;
  
  protected String department;
  
  protected String workplace;
  
  @Column(name="company_branch")
  protected String companyBranch;

  @Column(name = "leader_account_id")
  protected Long leaderAccountId;

  @Column(name = "leader_full_name")
  protected String leaderFullName;
  
  @Column(name = "manager_account_id")
  protected Long managerAccountId;

  @Column(name = "manager_full_name")
  protected String managerFullName;
  
  protected String milestone;

  @Column(name = "milestone_label")
  protected String milestoneLabel;
  
  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name="from_date")
  protected Date fromDate;

  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name="to_date")
  protected Date toDate;
  
  @Column(name="label", length = 512)
  protected String label;

  @Column(name="description", length = 2*1024)
  protected String description;

  public void copyBase(KpiBase kpiBase) {
    this.jobCode = kpiBase.getJobCode();
    this.jobTitle = kpiBase.getJobTitle();
    this.department = kpiBase.getDepartment();
    this.workplace = kpiBase.getWorkplace();
    this.companyBranch = kpiBase.getCompanyBranch();
    this.milestone = kpiBase.getMilestone();
    this.milestoneLabel = kpiBase.getMilestoneLabel();
    this.fromDate = kpiBase.getFromDate();
    this.toDate = kpiBase.getToDate();
    this.label = kpiBase.getLabel();
    this.description = kpiBase.getDescription();
  }

}