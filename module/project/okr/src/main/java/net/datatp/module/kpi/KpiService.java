package net.datatp.module.kpi;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import lombok.Getter;
import net.datatp.module.backend.Notification;
import net.datatp.module.core.security.AuthorizationCipherTool;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.kpi.entity.Kpi;
import net.datatp.module.kpi.entity.KpiGrade;
import net.datatp.module.kpi.entity.KpiItem;
import net.datatp.module.kpi.entity.KpiItemAttachment;
import net.datatp.module.kpi.entity.KpiItemRule;
import net.datatp.module.kpi.entity.KpiTemplate;
import net.datatp.module.kpi.entity.KpiTemplateItem;
import net.datatp.module.project.task.entity.TaskAttachment;
import net.datatp.security.client.ClientContext;

@Service("KpiService")
public class KpiService {
  @Getter
  @Autowired
  private KpiLogic kpiLogic;

  @Getter
  @Autowired
  private KpiTemplateLogic kpiTmplLogic;

  @Autowired
  private AuthorizationCipherTool authCipherTool;

  @Transactional(readOnly = true)
  public KpiItemRule getKpiItemRule(ClientContext client, ICompany company, Long id) {
    return kpiTmplLogic.getKpiItemRule(client, company, id);
  }

  @Transactional
  public KpiItemRule saveKpiItemRule(ClientContext client, ICompany company, KpiItemRule rule) {
    return kpiTmplLogic.saveKpiItemRule(client, company, rule);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchKpiItemRules(ClientContext client, ICompany company, SqlQueryParams params) {
    return kpiTmplLogic.searchKpiItemRules(client, company, params);
  }

  //Kpi Template Entity

  @Transactional(readOnly = true)
  public KpiTemplate getKpiTemplate(ClientContext client, ICompany company, Long id) {
    return kpiTmplLogic.getKpiTemplate(client, company, id);
  }

  @Transactional(readOnly = true)
  public KpiTemplate getKpiTemplateByJobCode(ClientContext client, ICompany company, String milestone, String jobCode) {
    return kpiTmplLogic.getKpiTemplateByJobCode(client, company, milestone, jobCode);
  }

  @Transactional
  public KpiTemplate saveKpiTemplate(ClientContext client, ICompany company, KpiTemplate tmpl) {
    return kpiTmplLogic.saveKpiTemplate(client, company, tmpl);
  }

  @Transactional
  public KpiTemplate copyKpiTemplate(ClientContext client, ICompany company, Long rootId, KpiTemplate template) {
    return kpiTmplLogic.copyKpiTemplate(client, company, rootId, template);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchKpiTemplates(ClientContext client, ICompany company, SqlQueryParams params) {
    return kpiTmplLogic.searchKpiTemplates(client, company, params);
  }

  @Transactional(readOnly = true)
  public KpiTemplateItem getKpiTemplateItem(ClientContext client, ICompany company, Long id) {
    return kpiTmplLogic.getKpiTemplateItem(client, company, id);
  }

  @Transactional
  public KpiTemplateItem saveKpiTemplateItem(ClientContext client, ICompany company, KpiTemplateItem item) {
    return kpiTmplLogic.saveKpiTemplateItem(client, company, item);
  }

  @Transactional
  public boolean deleteKpiTemplateItems(ClientContext client, ICompany company, List<Long> ids) {
    return kpiTmplLogic.deleteKpiTemplateItems(client, company, ids);
  }

  @Transactional
  public Notification deleteKpiTemplates(ClientContext client, ICompany company, List<Long> ids) {
    return kpiTmplLogic.deleteKpiTemplates(client, company, ids);
  }
  
  @Transactional
  public boolean updateKpiTemplateStorageState(ClientContext clientCtx, ICompany company, ChangeStorageStateRequest req) {
    return kpiTmplLogic.updateKpiTemplateStorageState(clientCtx, company, req);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchKpiTemplateItems(ClientContext client, ICompany company, SqlQueryParams params) {
    return kpiTmplLogic.searchKpiTemplateItems(client, company, params);
  }

  @Transactional(readOnly = true)
  public KpiTemplateModel getKpiTemplateModel(ClientContext client, ICompany company, Long kpiTemplateId) {
    return kpiTmplLogic.getKpiTemplateModel(client, company, kpiTemplateId);
  }

  @Transactional
  public KpiTemplateModel saveKpiTemplateModel(ClientContext client, ICompany company, KpiTemplateModel model) {
    return kpiTmplLogic.saveKpiTemplateModel(client, company, model);
  }

  // Kpi Entity

  @Transactional(readOnly = true)
  public Kpi getKpi(ClientContext client, ICompany company, Long id) {
    return kpiLogic.getKpi(client, company, id);
  }

  @Transactional
  public Kpi saveKpi(ClientContext client, ICompany company, Kpi kpi) {
    return kpiLogic.saveKpi(client, company, kpi);
  }

  @Transactional
  public Notification deleteKpis(ClientContext client, ICompany company, List<Long> ids) {
    return kpiLogic.deleteKpis(client, company, ids);
  }
  
  @Transactional
  public boolean updateKpiStorageState(ClientContext clientCtx, ICompany company, ChangeStorageStateRequest req) {
    return kpiLogic.updateKpiStorageState(clientCtx, company, req);
  }
  
  @Transactional
  public Kpi createKpiFromTemplate(ClientContext client, ICompany company, KpiTemplate kpiTmpl, Kpi initKpi) {
    return kpiLogic.createKpiFromTemplate(client, company, kpiTmpl, initKpi);
  }

  @Transactional
  public Notification updateKpiFinalGrade(ClientContext client, ICompany company, List<Long> ids, KpiGrade newGrade) {
    return kpiLogic.updateKpiFinalGrade(client, company, ids, newGrade);
  }

  @Transactional
  public Notification approveKpiByManager(ClientContext client, ICompany company, List<Long> ids) {
    return kpiLogic.approveKpiByManager(client, company, ids);
  }

  @Transactional
  public Notification updateKpiFromTemplate(ClientContext client, ICompany company, KpiTemplate kpiTmpl) {
    return kpiLogic.updateKpiFromTemplate(client, company, kpiTmpl);
  }

  @Transactional
  public Notification createKpiForEmployees(ClientContext client, ICompany company, Long kpiTmplId, List<Long> employeeIds) {
    return kpiLogic.createKpiForEmployees(client, company, kpiTmplId, employeeIds);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchKpis(ClientContext client, ICompany company, SqlQueryParams params) {
    return kpiLogic.searchKpis(client, company, params);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchKpiRawData(ClientContext client, ICompany company, SqlQueryParams params) {
    return kpiLogic.searchKpiRawData(client, company, params);
  }

  public List<KpiItem> findKpiItemByKpiId(ClientContext client, ICompany company, Long kpiId) {
    return kpiLogic.findKpiItemByKpiId(client, company, kpiId);
  }

  @Transactional(readOnly = true)
  public KpiModel getKpiModel(ClientContext client, ICompany company, Long kpiId) {
    return kpiLogic.getKpiModel(client, company, kpiId);
  }

  @Transactional
  public KpiModel saveKpiModel(ClientContext client, ICompany company, KpiModel model) {
    return kpiLogic.saveKpiModel(client, company, model);
  }

  @Transactional
  public boolean deleteKpiItems(ClientContext client, ICompany company, List<Long> ids) {
    return kpiLogic.deleteKpiItems(client, company, ids);
  }

  @Transactional(readOnly = true)
  public List<KpiItemAttachment> findKpiItemAttachments(ClientContext client, ICompany company, Long kpiItemid) {
    List<KpiItemAttachment> atts = kpiLogic.findKpiItemAttachments(client, company, kpiItemid);
    TaskAttachment.setStoreInfo(authCipherTool, client.getToken(), atts);
    return atts;
  }

  @Transactional
  public List<KpiItemAttachment> saveKpiItemAttachment(
      ClientContext client, ICompany company, Long kpiItemid, List<KpiItemAttachment> attachments) {
    List<KpiItemAttachment> atts =  kpiLogic.saveKpiItemAttachment(client, company, kpiItemid, attachments);
    KpiItemAttachment.setStoreInfo(authCipherTool, client.getToken(), atts);
    return atts;
  }
}
