package net.datatp.module.kpi.repo;

import java.io.Serializable;
import java.util.List;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import net.datatp.module.data.db.repository.DataTPRepository;
import net.datatp.module.kpi.entity.KpiItem;

public interface KpiItemRepository extends DataTPRepository<KpiItem, Serializable> {
  @Query("SELECT i FROM KpiItem i WHERE i.id = :id")
  KpiItem getById( @Param("id") Long id);

  @Query("SELECT i FROM KpiItem i WHERE i.kpiId = :kpiId ORDER BY i.label ASC")
  List<KpiItem> findByKpiId(@Param("kpiId") Long kpiId);

  @Query("SELECT o.id FROM KpiItem o WHERE o.kpiId IN :kpiIds")
  List<Long> findIdByKpiIds(@Param("kpiIds") List<Long> kpiIds);
}
