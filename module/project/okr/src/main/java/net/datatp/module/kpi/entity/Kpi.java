package net.datatp.module.kpi.entity;

import java.io.Serial;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.data.db.util.DeleteGraph;
import net.datatp.module.data.db.util.DeleteGraphJoinType;
import net.datatp.module.data.db.util.DeleteGraphs;
import net.datatp.module.wfms.entity.EntityTaskRequest;
import net.datatp.module.wfms.entity.ITaskableEntity;

@Entity
@Table(
  name = Kpi.TABLE_NAME,
  uniqueConstraints = {
    @UniqueConstraint(
      name = Kpi.TABLE_NAME + "_employee_account_id_template_id_milestone",
      columnNames = {"company_id", "employee_account_id", "template_id", "milestone"}
    ),
  },
  indexes = {
      @Index(name = Kpi.TABLE_NAME + "_company_id", columnList = "company_id"),
      @Index(name = Kpi.TABLE_NAME + "_storage_state", columnList = "storage_state")
  }
)
@DeleteGraphs({
  @DeleteGraph(
    target = KpiItem.class,
    joinType = DeleteGraphJoinType.OneToMany, joinField = "kpi_id"
  )
})
@JsonInclude(Include.NON_NULL)
@NoArgsConstructor @Getter @Setter
public class Kpi extends KpiBase implements ITaskableEntity {
  @Serial
  private static final long serialVersionUID = 1L;

  final static public String TABLE_NAME = "kpi_kpi";  
  
  @Column(name = "template_id")
  private Long templateId;
  
  @Column(name="employee_account_id")
  private Long   employeeAccountId;
  
  @Column(name="employee_full_name")
  private String employeeFullName;
  
  @Column(name="employee_code")
  private String employeeCode;
  
  @Enumerated(EnumType.STRING)
  private KpiStatus status = KpiStatus.Plan;

  @Column(name="kpi_result")
  private double kpiResult;

  @Enumerated(EnumType.STRING)
  private KpiGrade grade = KpiGrade.Failed;

  @Enumerated(EnumType.STRING)
  @Column(name="final_grade")
  private KpiGrade finalGrade;
  
  private EntityTaskRequest entityTaskRequest = new EntityTaskRequest();
  
  public void copyTemplate(KpiTemplate template) {
    super.copyBase(template);
    templateId = template.getId();
    managerAccountId = template.getManagerAccountId();
    managerFullName = template.getManagerFullName();
    leaderAccountId = template.getLeaderAccountId();
    leaderFullName = template.getLeaderFullName();
  }

  public EntityTaskRequest getEntityTaskRequest() {
    return entityTaskRequest;
  }
  
  public Kpi calculate(List<KpiItem> items) {
    computeKpiResult(items);
    computeRating();
    return this;
  }
  
  private void computeKpiResult(List<KpiItem> items) {
    double workResult = 0;
    double behaviorResult = 0;
    for (KpiItem item : items) {
      double weight = item.getContributionWeight();
      double complete = item.calculateKpiItemComplete();
      if (item.getObjectiveType() == KpiObjectiveType.Work) workResult += complete * weight;
      if (item.getObjectiveType() == KpiObjectiveType.Behavior) behaviorResult += complete * weight;
    }
    kpiResult = workResult * 0.8 + behaviorResult * 0.2;
  }
  
  private void computeRating() {
    double result = kpiResult * 100;
    if (result <= 70) {
      grade = KpiGrade.Failed;
    } else if (result < 95) {
      grade = KpiGrade.NeedImprovement;
    } else if (result <= 115) {
      grade = KpiGrade.Passed;
    } else if (result < 135) {
      grade = KpiGrade.ExceedExpectations;
    } else if (result >= 135) {
      grade = KpiGrade.Outstanding;
    }
  }
}