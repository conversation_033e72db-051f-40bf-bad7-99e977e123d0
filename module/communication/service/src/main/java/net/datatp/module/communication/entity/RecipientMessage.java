package net.datatp.module.communication.entity;

import java.util.Set;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Index;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.security.client.ClientContext;
import net.datatp.module.data.db.entity.PersistableEntity;
import net.datatp.util.ds.Arrays;

@Entity
@Table(
  name = RecipientMessage.TABLE_NAME,
  indexes = {
    @Index(columnList = "recipient_account_id")
  }
)
@NoArgsConstructor @Getter @Setter
public class RecipientMessage extends PersistableEntity<Long> {
  private static final long serialVersionUID = 1L;

  public static final String TABLE_NAME = "message_recipient_message";

  public static final String TAG_REL_TABLE = "message_recipient_message_tag_rel" ;

  @NotNull
  private String            subject;

  @NotNull
  @Column(name="sender_account_id")
  private Long            senderAccountId;

  @NotNull
  @Column(name="recipient_account_id")
  private Long            recipientAccountId;

  @Column(name="channel_name")
  private String           channelName;

  @NotNull
  @Enumerated(EnumType.STRING)
  @Column(name = "deliver_type")
  private MessageDeliverType deliverType;

  private boolean read ;

  @ManyToOne(optional = false)
  @JoinColumn(name = "message_id")
  private Message message;

  @ManyToMany(cascade = { CascadeType.MERGE })
  @JoinTable(
    name = TAG_REL_TABLE,
    joinColumns = @JoinColumn(name = "recipient_message_id"),
    inverseJoinColumns = @JoinColumn(name = "recipient_message_tag_id")
  )
  private Set<RecipientMessageTag> tags;

  public RecipientMessage(Long recipientAccountId, Message message) {
    this.senderAccountId = message.getSenderAccountId();
    this.recipientAccountId = recipientAccountId;
    this.subject = message.getSubject();
    this.message = message;
  }

  public RecipientMessage withDeliverType(MessageDeliverType deliverType) {
    this.deliverType = deliverType;
    return this;
  }

  public RecipientMessage withChannelName(String channelName) {
    this.deliverType = MessageDeliverType.Channel;
    this.channelName = channelName;
    return this;
  }

  public RecipientMessage withTag(RecipientMessageTag... tag) {
    tags = Arrays.addToSet(tags, tag);
    return this;
  }

  public void withClient(ClientContext client) {
    super.set(client);
    if(tags != null) {
      for(RecipientMessageTag sel : tags) sel.set(client);
    }
  }

}