package net.datatp.module.communication.entity;

import java.io.Serial;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Index;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.account.entity.Account;
import net.datatp.module.chat.entity.ChatChannel;
import net.datatp.security.client.ClientContext;
import net.datatp.module.data.db.entity.PersistableEntity;
import net.datatp.util.ds.Arrays;

@Entity
@Table(
  name = CommunicationAccount.TABLE_NAME,
  uniqueConstraints = {
    @UniqueConstraint(
      name = CommunicationAccount.TABLE_NAME + "_account_id",
      columnNames = {"account_id"}),
  },
  indexes = {
    @Index(columnList = "account_id")
  }
)
@NoArgsConstructor
@Getter
@Setter
public class CommunicationAccount extends PersistableEntity<Long> {
  @Serial
  private static final long serialVersionUID = 1L;

  public static final String TABLE_NAME = "communication_account";

  static public enum SmtpProvider {Smtp, GraphApi}

  @NotNull
  @Column(name = "account_id")
  private Long accountId;

  @NotNull
  @Column(name = "full_name")
  private String fullName;

  @Column(name = "email")
  private String email;

  @Column(name = "forward_email")
  private String forwardEmail;

  @Column(name = "auto_forward")
  private boolean autoForward;

  private String mobile;

  @Column(name = "zalo_app_id")
  private String zaloAppId;

  @Column(name = "zalo_display_name")
  private String zaloDisplayName;

  @Column(name = "zalo_user_id")
  private String zaloUserId;

  @Column(name = "zalo_avatar")
  private String zaloAvatar;

  @Column(name = "smtp_provider")
  @Enumerated(EnumType.STRING)
  private SmtpProvider smtpProvider = SmtpProvider.Smtp;

  @Column(name = "smtp_host")
  private String smtpHost;

  @Column(name = "smtp_port")
  private int smtpPort;

  @Column(name = "smtp_username")
  private String smtpUsername;

  @Column(name = "smtp_password")
  private String smtpPassword;

  @Column(name = "smtp_enable_tls")
  private boolean smtpEnableTLS;

  @Column(name = "graph_api_account_id")
  private String graphApiAccountId;

  @OneToMany(cascade = CascadeType.ALL, orphanRemoval = false)
  @JoinColumn(name = "communication_channel_id")
  private Set<RecipientMessageTag> tags = new HashSet<>();

  @ManyToMany(cascade = {CascadeType.MERGE})
  @JoinTable(
    name = "communication_channel_account_rel",
    joinColumns = @JoinColumn(name = "communication_account_id"),
    inverseJoinColumns = @JoinColumn(name = "communication_channel_id"))
  private List<CommunicationChannel> channels = new ArrayList<>();

  @ManyToMany(cascade = {CascadeType.MERGE})
  @JoinTable(
    name = "chat_channel_account_rel",
    joinColumns = @JoinColumn(name = "chat_account_id"),
    inverseJoinColumns = @JoinColumn(name = "chat_channel_id"))
  private List<ChatChannel> chatChannels = new ArrayList<>();

  public CommunicationAccount(Account account) {
    this.accountId = account.getId();
    this.email = account.getEmail();
    this.fullName = account.getFullName();
  }

  public void setMobile(String mobile) {
    if (mobile == null) {
      this.mobile = null;
      return;
    }
    String normalized = mobile.replaceAll("[^0-9]", "");
    if (normalized.startsWith("0")) {
      normalized = "84" + normalized.substring(1);
    } else if (!normalized.startsWith("84")) {
      normalized = "84" + normalized;
    }
    this.mobile = normalized;
  }

  public String getForwardEmail() {
    if (forwardEmail == null) return email;
    return forwardEmail;
  }

  public CommunicationAccount withRecipientMessageTag(RecipientMessageTag... tag) {
    tags = Arrays.addToSet(tags, tag);
    return this;
  }

  public CommunicationAccount withFullName(String fullName) {
    this.fullName = fullName;
    return this;
  }

  public CommunicationAccount withEmail(String email) {
    this.email = email;
    return this;
  }

  public CommunicationAccount withChannel(CommunicationChannel... channel) {
    channels = Arrays.addToList(channels, channel);
    return this;
  }

  public CommunicationAccount withSmtpHost(String smtpHost) {
    this.smtpHost = smtpHost;
    return this;
  }

  public CommunicationAccount withSmtpPort(int smtpPort) {
    this.smtpPort = smtpPort;
    return this;
  }

  public CommunicationAccount withSmtpUsername(String smtpUsername) {
    this.smtpUsername = smtpUsername;
    return this;
  }

  public CommunicationAccount withSmtpPassword(String smtpPassword) {
    this.smtpPassword = smtpPassword;
    return this;
  }

  public CommunicationAccount withSmtpEnableTLS(boolean smtpEnableTLS) {
    this.smtpEnableTLS = smtpEnableTLS;
    return this;
  }

  public String getGraphApiAccountId() {
    if (graphApiAccountId != null) return graphApiAccountId;
    return email;
  }

  public String identify() { return accountId.toString(); }

  public void set(ClientContext client) {
    super.set(client);
    set(client, tags);
    set(client, channels);
  }
}