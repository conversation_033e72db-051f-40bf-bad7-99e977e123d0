package net.datatp.module.communication;

import java.util.Properties;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.mail.internet.MimeMessage;
import lombok.extern.slf4j.Slf4j;
import net.datatp.security.client.ClientContext;
import net.datatp.module.communication.entity.CommunicationAccount;
import net.datatp.module.communication.entity.Message;
import net.datatp.module.communication.entity.TargetRecipient;
import net.datatp.util.ds.Objects;
import net.datatp.util.text.StringUtil;

@Component
@Slf4j
public class DomainMailSender {
  //private InMemoryCache<JavaMailSender> senders = new InMemoryCache<>();
  
  @Value("${app.mail.smtp.host:smtp.gmail.com}") 
  String smtpHost;
  
  @Value("${app.mail.smtp.port:587}") 
  int port;
  @Value("${app.mail.smtp.auth:true}") 
  String auth;
  @Value("${app.mail.smtp.username:ahaysoft}") 
  String username;
  @Value("${app.mail.smtp.password:byfvcyoaiuuvdzqz}") 
  String password;
  @Value("${app.mail.smtp.starttls.enable:true}") 
  boolean enableTLS;
  @Value("${app.mail.smtp.debug:true}") 
  String debug;
  
  @Value("${app.mail.smtp.from:<EMAIL>}") 
  String fromEmail;
  
  private JavaMailSender defaultEmailSender;
  
  @PostConstruct
  public void onInit() {
    JavaMailSenderImpl emailSender = new JavaMailSenderImpl();
    emailSender.setHost(smtpHost);
    emailSender.setPort(port);
    emailSender.setUsername(username);
    emailSender.setPassword(password);

    Properties props = emailSender.getJavaMailProperties();
    props.put("mail.transport.protocol", "smtp");
    props.put("mail.smtp.auth", auth);
    if(enableTLS) {
      props.put("mail.smtp.starttls.enable", "true");
      props.put("mail.smtp.starttls.required", "true");
      props.put("mail.smtp.ssl.protocols", "TLSv1.2");
    }
    props.put("mail.debug", debug);
    
    this.defaultEmailSender = emailSender;
  }
  
  public JavaMailSender createSenderFor(CommunicationAccount account) {
    if(StringUtil.isEmpty(account.getSmtpHost())) {
      return null;
    }
    JavaMailSenderImpl mailSender = new JavaMailSenderImpl();
    mailSender.setHost(account.getSmtpHost());
    mailSender.setPort(account.getSmtpPort());
    mailSender.setUsername(account.getSmtpUsername());
    mailSender.setPassword(account.getSmtpPassword());

    Properties props = mailSender.getJavaMailProperties();
    props.put("mail.transport.protocol", "smtp");
    props.put("mail.smtp.auth", true);
    props.put("mail.smtp.starttls.enable", "true");
    props.put("mail.smtp.starttls.required", "true");
    props.put("mail.smtp.ssl.protocols", "TLSv1.2");
    props.put("mail.debug", false);
    return mailSender;
  }
  
  public boolean sendEmail(
      ClientContext client, CommunicationAccount fromAccount, Message message, TargetRecipient target, String email) {
    String subject = message.getSubject();
    if(StringUtil.isEmpty(subject)) {
      subject = "Message From " + fromAccount.getFullName();
      log.info("Message subject is null from account ", fromAccount.getFullName());
    }
    log.debug("Send an email {} to {}", subject, email);
    JavaMailSender sender = createSenderFor(fromAccount);
    String         from = fromAccount.getEmail();
    if(sender == null) {
      sender = defaultEmailSender;
      from   = fromEmail;
    }
    MimeMessage mimeMessage = sender.createMimeMessage();
    try {
      MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, false, "UTF-8");
      helper.getEncoding();
      helper.setFrom(from);
      helper.setTo(email);
      helper.setSubject(subject);
      if(Objects.isNull(message.getContent())) {
        message.setContent("");
        log.info("Message Content is null {} {}", subject, email);
      }
      helper.setText(message.getContent(), true);
      sender.send(mimeMessage);
      return true;
    } catch (jakarta.mail.MessagingException e) {
      log.error("Cannot send the email", e);
      return false;
    }
  }
}
