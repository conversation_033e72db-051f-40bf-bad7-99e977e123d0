package net.datatp.module.chat;

import java.util.List;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import net.datatp.module.chat.entity.ChatChannel;
import net.datatp.module.chat.repository.ChatChannelRepository;
import net.datatp.security.client.ClientContext;
import net.datatp.module.communication.CommunicationMessageLogic;
import net.datatp.module.communication.entity.CommunicationAccount;
import net.datatp.module.communication.repository.CommunicationAccountRepository;
import net.datatp.module.data.db.DAOService;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.query.SqlQueryParams;

@Component
public class ChatChannelLogic extends DAOService {
	@Autowired
	private ChatChannelRepository chatChannelRepository;

	@Autowired
	private CommunicationAccountRepository communicationAccountRepo;

	@Autowired
	private CommunicationMessageLogic communicationMessageLogic;

	public ChatChannel getChatChannel(ClientContext client, String name) {
		return chatChannelRepository.getChatChannel(name);
	}

	public ChatChannel saveChatChannel(ClientContext client, ChatChannel channel) {
		channel.set(client);
		return chatChannelRepository.save(channel);
	}

	List<SqlMapRecord> searchChatChannels(ClientContext client, SqlQueryParams params) {
		String scriptDir = appEnv.addonPath("core", "groovy");
		String scriptFile = "net/datatp/module/chat/groovy/ChatChannelSql.groovy";
		return searchDbRecords(client, scriptDir, scriptFile, "SearchChatChannels", params);
	}

	List<SqlMapRecord> SearchSubscribeChatChannels(ClientContext client, SqlQueryParams params) {
		String scriptDir = appEnv.addonPath("core", "groovy");
		String scriptFile = "net/datatp/module/chat/groovy/ChatChannelSql.groovy";
		return searchDbRecords(client, scriptDir, scriptFile, "SearchSubscribeChatChannels", params);
	}

	List<SqlMapRecord> SearchMyChatChannels(ClientContext client, SqlQueryParams params) {
		String scriptDir = appEnv.addonPath("core", "groovy");
		String scriptFile = "net/datatp/module/chat/groovy/ChatChannelSql.groovy";
		return searchDbRecords(client, scriptDir, scriptFile, "SearchMyChatChannels", params);
	}

	public List<CommunicationAccount> findCommunicationAccountInChannel(ClientContext client, String channel) {
		return communicationAccountRepo.findCommunicationAccountInChatChannel(channel);
	}

	public boolean addCommunicationAccountToChannel(ClientContext client, Long messageChannelId, List<CommunicationAccount> accounts) {
		ChatChannel channel = chatChannelRepository.getById(messageChannelId);
		for(CommunicationAccount account : accounts) {
			CommunicationAccount res = communicationMessageLogic.getCommunicationAccount(client, account.getLoginId());
			res.getChatChannels().add(channel);
			saveCommunicationAccount(client, res);
		}
		return true;
	}

	public boolean joinChannel(ClientContext client, Long messageChannelId, Long accountId) {
		ChatChannel channel = chatChannelRepository.getById(messageChannelId);
		CommunicationAccount account = communicationMessageLogic.getCommunicationAccount(client, accountId);
		if (!account.getChatChannels().contains(channel)) {
			account.getChatChannels().add(channel);
			saveCommunicationAccount(client, account);
		}
		return true;
	}

	public boolean leaveChannel(ClientContext client, Long messageChannelId, Long accountId) {
		CommunicationAccount account = communicationMessageLogic.getCommunicationAccount(client, accountId);
		account.getChatChannels().removeIf(c -> Objects.equals(c.getId(), messageChannelId));
		saveCommunicationAccount(client, account);
		return true;
	}

	public boolean removeCommunicationAccountFromChannel(ClientContext client, Long messageChannelId, List<CommunicationAccount> accounts) {
		ChatChannel channel = chatChannelRepository.getById(messageChannelId);
		for(CommunicationAccount account : accounts) {
			CommunicationAccount res = communicationMessageLogic.getCommunicationAccount(client, account.getLoginId());
			res.getChatChannels().remove(channel);
			saveCommunicationAccount(client, res);
		}
		return true;
	}

	public CommunicationAccount saveCommunicationAccount(ClientContext client, CommunicationAccount account) {
		account.set(client);
		return communicationAccountRepo.save(account);
	}

	List<SqlMapRecord> SearchListChatChannelOfAccount(ClientContext client, SqlQueryParams params) {
		String scriptDir = appEnv.addonPath("core", "groovy");
		String scriptFile = "net/datatp/module/chat/groovy/ChatChannelSql.groovy";
		return searchDbRecords(client, scriptDir, scriptFile, "SearchListChatChannelOfAccount", params);
	}
}
