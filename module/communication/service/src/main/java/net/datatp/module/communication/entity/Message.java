package net.datatp.module.communication.entity;

import java.io.Serial;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.atomic.AtomicLong;

import org.eclipse.jetty.util.security.Credential.MD5;

import com.microsoft.graph.models.Attachment;

import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Index;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.JoinTable;
import jakarta.persistence.ManyToMany;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import jakarta.persistence.UniqueConstraint;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.security.client.ClientContext;
import net.datatp.module.data.db.entity.PersistableEntity;
import net.datatp.module.http.upload.UploadResource;
import net.datatp.util.ds.Arrays;

@Entity
@Table(
  name = Message.TABLE_NAME,
  uniqueConstraints = {
    @UniqueConstraint(name = Message.TABLE_NAME + "_code", columnNames = {"code"}),
  },
  indexes = {
    @Index(columnList="code")
  }
)
@NoArgsConstructor @Getter @Setter
public class Message extends PersistableEntity<Long> {
  @Serial
  private static final long serialVersionUID = 1L;

  public static final String TABLE_NAME = "message_message";

  public static enum Status { Draft, Ready, PartiallyDelivered, Delivered, DeliveredWithError }

//  @Column(name="company_id")
//  private Long companyId;

  @NotNull
  private String code;

  @Column(name="parent_code")
  private String parentCode;

  @NotNull
  @Column(name="sender_account_id")
  private Long senderAccountId;

  private String subject;

  @Column(length=1024 * 64)
  private String content;

  @Column(name="mime_type")
  private String mimeType;

  @Column(name="sent_to_zalo")
  private Boolean sentToZalo;

  @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
  @JoinColumn(name = "message_id", referencedColumnName = "id")
  private List<TargetRecipient>  recipients = new ArrayList<>();

  @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
  @JoinColumn(name = "message_id", referencedColumnName = "id")
  private List<MessageLink> links = new ArrayList<>();

  @ManyToMany()
  @JoinTable(
    name = "message_message_flag_rel",
    joinColumns = @JoinColumn(name = "message_id"),
    inverseJoinColumns = @JoinColumn(name = "message_flag_id")
  )
  private Set<MessageFlag> flags = new HashSet<>();

  @Enumerated(EnumType.STRING)
  private Status status = Status.Draft;

  @Transient
  private List<UploadResource>     attachments      = new ArrayList<>();

  @Transient
  private LinkedList<Attachment>   graphAttachments = new LinkedList<>();

  @Transient
  private String eSignature;

  @Column(length = 64 * 1024)
  private String error;

  @Column(name = "error_file_attachment", length = 64 * 1024)
  private String errorFileAttachment;

  public Message(Long accountId) {
    this.senderAccountId = accountId;
  }

  public Message withSubject(String subject) {
    this.subject = subject;
    return this;
  }

  public Message withContent(String content) {
    this.content = content;
    return this;
  }

  public Message withStatus(Status status) {
    this.status = status;
    return this;
  }

  public Message withFlag(MessageFlag... flag) {
    flags = Arrays.addToSet(flags, flag);
    return this;
  }

  public Message withRecipient(TargetRecipient... recipient) {
    recipients = Arrays.addToList(recipients, recipient);
    return this;
  }

  public Message withPrivateRecipient(Long ... accountId) {
    if(recipients == null) recipients = new ArrayList<>();
    for(Long selAccountId : accountId) {
      TargetRecipient target = new TargetRecipient(MessageDeliverType.Private, selAccountId);
      recipients.add(target);
    }
    return this;
  }

  public Message withEmailRecipient(String ... email) {
    if(recipients == null) recipients = new ArrayList<>();
    for(String selEmail : email) {
      TargetRecipient target = new TargetRecipient(MessageDeliverType.Email, selEmail);
      recipients.add(target);
    }
    return this;
  }

  public Message withChannelRecipient(String ... channelName) {
    if(recipients == null) recipients = new ArrayList<>();
    for(String selChannelName : channelName) {
      TargetRecipient target = new TargetRecipient(MessageDeliverType.Channel, selChannelName);
      recipients.add(target);
    }
    return this;
  }

  static AtomicLong ID_TRACKER = new AtomicLong();
  public void genCode() {
    code = MD5.digest(System.currentTimeMillis() + "-" + Long.toString(ID_TRACKER.incrementAndGet()));
  }

  public Message withError(String error) {
    if(this.error != null && this.error.contains(error))  return this;
    String errorMessage = this.error == null ? error + "\n" : this.error + error + "\n";
    if(errorMessage.length() > 64 * 1024) {
      errorMessage = errorMessage.substring(0, 64 * 1020);
    }
    this.error = errorMessage;
    return this;
  }

  public Message clearError() {
    this.error = null;
    return this;
  }

  public Message withErrorFileAttachment(String error) {
    String errorMessage = this.errorFileAttachment == null ? "" : this.errorFileAttachment +"\n" + error;
    if(errorMessage.length() > (64 * 1024) / 2) {
      errorMessage = errorMessage.substring(0, (64 * 1024) / 2);
    }
    this.errorFileAttachment = errorMessage;
    return this;
  }


  public void set(ClientContext client) {
    super.set(client);
    set(client, recipients);
  }
}