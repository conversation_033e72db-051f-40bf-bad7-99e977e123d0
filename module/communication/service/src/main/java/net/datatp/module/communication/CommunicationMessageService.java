package net.datatp.module.communication;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import net.datatp.module.bot.BotEvent.ProcessMode;
import net.datatp.module.bot.BotService;
import net.datatp.security.client.ClientContext;
import net.datatp.module.communication.entity.CommunicationAccount;
import net.datatp.module.communication.entity.CommunicationChannel;
import net.datatp.module.communication.entity.Message;
import net.datatp.module.communication.entity.MessageFlag;
import net.datatp.module.communication.entity.RecipientMessage;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.monitor.SourceType;
import net.datatp.module.monitor.annotation.MonitorCall;
import net.datatp.module.service.BaseComponent;

@Service("CommunicationMessageService")
public class CommunicationMessageService extends BaseComponent {

  @Autowired
  private CommunicationMessageLogic messageLogic;

  @Autowired
  private BotService botService;

  // MessageFlag
  @Transactional(readOnly = true)
  public MessageFlag getMessageFlag(ClientContext client, String name) {
    return messageLogic.getMessageFlag(client, name);
  }

  @Transactional
  public MessageFlag saveMessageFlag(ClientContext client, MessageFlag flag) {
    return messageLogic.saveMessageFlag(client, flag);
  }

  @Transactional(readOnly = true)
  public List<MessageFlag> searchMessageFlags(ClientContext client, SqlQueryParams params) {
    return messageLogic.searchMessageFlags(client, params);
  }

  // MessageAccount
  @Transactional
  public CommunicationAccount linkZaloUserId(ClientContext client) {
    return messageLogic.linkZaloUserId(client);
  }

  @Transactional(readOnly = true)
  public CommunicationAccount getCommunicationAccountByAccountId(ClientContext client, Long accountId) {
    return messageLogic.getCommunicationAccountByAccountId(client, accountId);
  }

  @Transactional(readOnly = true)
  public CommunicationAccount getCommunicationAccount(ClientContext client, Long accountId) {
    return messageLogic.getCommunicationAccount(client, accountId);
  }

  @Transactional
  public CommunicationAccount saveCommunicationAccount(ClientContext client, CommunicationAccount account) {
    return messageLogic.saveCommunicationAccount(client, account);
  }

  @Transactional
  public Boolean deleteCommunicationAccount(ClientContext client, List<Long> ids) {
    return messageLogic.deleteCommunicationAccount(client, ids);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchCommunicationAccount(ClientContext client, SqlQueryParams params) {
    return messageLogic.searchCommunicationAccount(client, params);
  }

  @Transactional(readOnly = true)
  public List<CommunicationAccount> searchChatChannelAccount(ClientContext client, SqlQueryParams params) {
    return messageLogic.searchChatChannelAccount(client, params);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchCommunicationAccounts(ClientContext client, ICompany company, SqlQueryParams params) {
    return messageLogic.searchCommunicationAccounts(client, params);
  }

  //Message Channel
  @Transactional(readOnly = true)
  public CommunicationChannel getCommunicationChannel(ClientContext client, String name) {
    return messageLogic.getCommunicationChannel(client, name);
  }

  @Transactional
  public CommunicationChannel saveCommunicationChannel(ClientContext client, CommunicationChannel channel) {
    return messageLogic.saveCommunicationChannel(client, channel);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchCommunicationChannels(ClientContext client, SqlQueryParams params) {
    return messageLogic.searchCommunicationChannels(client, params);
  }

  @Transactional
  public boolean addCommunicationAccountToChannel(ClientContext client, Long channelId, List<CommunicationAccount> accounts) {
    return messageLogic.addCommunicationAccountToChannel(client, channelId, accounts);
  }

  @Transactional
  public boolean removeCommunicationAccountFromChannel(ClientContext client, Long channelId, List<CommunicationAccount> accounts) {
    return messageLogic.removeCommunicationAccountFromChannel(client, channelId, accounts);
  }

  // Message

  @Transactional(readOnly = true)
  public Message getMessage(ClientContext client, String code) {
    return messageLogic.getMessage(client, code);
  }

  @Transactional(readOnly = true)
  public Message getMessageById(ClientContext client, Long id) {
    return messageLogic.getMessage(client, id);
  }

  @Transactional(readOnly = true)
  public List<Message> findMessageByIds(ClientContext client, Long [] ids) {
    return messageLogic.findMessageByIds(client, ids);
  }

  @Transactional
  public Message saveMessage(ClientContext client, Message message) {
    message = messageLogic.saveMessage(client, message);
    return message;
  }

  public Message sendMessage(ClientContext client, ICompany company, Message message) {
    message = saveMessage(client, message);
    BotSendMessageEvent botEvent = new BotSendMessageEvent(client, company, message);
    botEvent.withProcessMode(ProcessMode.Queueable);
    botService.broadcast(SourceType.UserBot, botEvent);
    return message;
  }

  @Transactional
  public Message sendImmediatelyMessage(ClientContext client, ICompany company, Message message) {
    message = saveMessage(client, message);
    BotSendMessageEvent botEvent = new BotSendMessageEvent(client, company, message);
    botEvent.withProcessMode(ProcessMode.Immediately);
    botService.broadcast(SourceType.User, botEvent);
    return message;
  }

  @MonitorCall(label="Search Messages", tags= {"user", "communication"})
  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchMessages(ClientContext client, SqlQueryParams params) {
    return messageLogic.searchMessages(client, params);
  }

  // RecipientMessage
  @Transactional
  public RecipientMessage readRecipientMessage(ClientContext client, Long id) {
    RecipientMessage message = getRecipientMessage(client, id);
    if(!message.isRead()) {
      message.setRead(true);
      saveRecipientMessage(client, message);
    }
    return message;
  }

  @Transactional(readOnly = true)
  public RecipientMessage getRecipientMessage(ClientContext client, Long id) {
    return messageLogic.getRecipientMessage(client, id);
  }

  @Transactional
  public RecipientMessage saveRecipientMessage(ClientContext client, RecipientMessage message) {
    return messageLogic.saveRecipientMessage(client, message);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchRecipientMessages(ClientContext client, ICompany company, SqlQueryParams params) {
    return messageLogic.searchRecipientMessages(client, params);
  }

  @MonitorCall(label="Search Recipient Messages", tags= {"communication"})
  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchRecipientMessages(
      ClientContext client, ICompany company, SourceType sourceType, SqlQueryParams params) {
    return messageLogic.searchRecipientMessages(client, params);
  }

  @Transactional(readOnly = true)
  public void sendMailResetPassword(ClientContext client, String email, String message) {
    messageLogic.sendMailResetPassword(client, email, message);
  }

}