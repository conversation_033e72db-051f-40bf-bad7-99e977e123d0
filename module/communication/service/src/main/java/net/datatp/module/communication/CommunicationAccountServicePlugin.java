package net.datatp.module.communication;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import net.datatp.module.account.entity.Account;
import net.datatp.module.account.plugin.AccountServicePlugin;
import net.datatp.security.client.ClientContext;
import net.datatp.module.communication.entity.CommunicationAccount;
import net.datatp.module.data.db.entity.StorageState;
import net.datatp.util.ds.Objects;

@Component
public class CommunicationAccountServicePlugin extends AccountServicePlugin {

  @Autowired
  CommunicationMessageService service;

  public CommunicationAccountServicePlugin() {
    super("communication");
  }

  @Override
  public void onPreSave(ClientContext client, Account account, boolean isNew) {
  }

  @Override
  public void onPostSave(ClientContext client, Account account, boolean isNew) {
    CommunicationAccount communicationAccount = service.getCommunicationAccount(client, account.getId());

    if (Objects.isNull(communicationAccount)) {
      communicationAccount = new CommunicationAccount();
      communicationAccount.setAccountId(account.getId());
    }
    communicationAccount.setFullName(account.getFullName());
    communicationAccount.setEmail(account.getEmail());
    communicationAccount.setMobile(account.getMobile());
    communicationAccount.setForwardEmail(account.getEmail());
    service.saveCommunicationAccount(client, communicationAccount);
  }

  public void onPostStateChange(ClientContext client, Account account, StorageState newState) {
    CommunicationAccount communicationAccount = service.getCommunicationAccount(client, account.getId());
    communicationAccount.setStorageState(newState);
    service.saveCommunicationAccount(client, communicationAccount);
  }

}