package net.datatp.module.communication;

import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.microsoft.graph.models.Attachment;
import com.microsoft.graph.models.FileAttachment;

import groovy.lang.Binding;
import lombok.Getter;
import net.datatp.module.chat.entity.ChatChannel;
import net.datatp.security.client.ClientContext;
import net.datatp.module.communication.entity.CommunicationAccount;
import net.datatp.module.communication.entity.CommunicationChannel;
import net.datatp.module.communication.entity.Message;
import net.datatp.module.communication.entity.MessageFlag;
import net.datatp.module.communication.entity.RecipientMessage;
import net.datatp.module.communication.repository.CommunicationAccountRepository;
import net.datatp.module.communication.repository.CommunicationChannelRepository;
import net.datatp.module.communication.repository.MessageFlagRepository;
import net.datatp.module.communication.repository.MessageRepository;
import net.datatp.module.communication.repository.RecipientMessageRepository;
import net.datatp.module.core.security.AuthorizationCipherTool;
import net.datatp.module.core.security.SessionData;
import net.datatp.module.data.db.DAOService;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.SqlQueryManager;
import net.datatp.module.data.db.SqlSelectView;
import net.datatp.module.data.db.entity.StorageState;
import net.datatp.module.data.db.query.ClauseFilter;
import net.datatp.module.data.db.query.EntityTable;
import net.datatp.module.data.db.query.Join;
import net.datatp.module.data.db.query.OptionFilter;
import net.datatp.module.data.db.query.RangeFilter;
import net.datatp.module.data.db.query.SearchFilter;
import net.datatp.module.data.db.query.SqlQuery;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.graphapi.GraphApiService;
import net.datatp.module.http.get.GETContent;
import net.datatp.module.http.get.GETService;
import net.datatp.module.http.upload.UploadResource;
import net.datatp.module.http.upload.UploadService;
import net.datatp.module.service.ExecutableUnitManager;
import net.datatp.module.zalo.ZaloLogic;
import net.datatp.util.ds.Collections;

@Component @Getter
public class CommunicationMessageLogic extends DAOService {

  @Autowired
  protected GraphApiService          graphApiService;

  @Autowired
  private ExecutableUnitManager      executableUnitManager;

  @Autowired
  private ZaloLogic                  zaloLogic;

  @Autowired
  private AuthorizationCipherTool    cipherTool;

  @Autowired
  private UploadService              uploadService;

  @Autowired
  private GETService                 GETService;

  @Autowired
  private MessageFlagRepository      messageFlagRepo;

  @Autowired
  private CommunicationChannelRepository communicationChannelRepo;

  @Autowired
  private CommunicationAccountRepository communicationAccountRepo;

  @Autowired
  private MessageRepository          messageRepo;

  @Autowired
  private RecipientMessageRepository recipientMessageRepo;

  // Message Flag
  public MessageFlag getMessageFlag(ClientContext client, String name) {
    return messageFlagRepo.getMessageFlag(name);
  }

  public MessageFlag saveMessageFlag(ClientContext client, MessageFlag flag) {
    flag.set(client);
    return messageFlagRepo.save(flag);
  }

  public List<MessageFlag> searchMessageFlags(ClientContext client, SqlQueryParams params) {
   String[] SEARCH_FIELDS = new String[] {"name", "label"};
   SqlQuery query =
       new SqlQuery().
       ADD_TABLE(new EntityTable(MessageFlag.class).selectAllFields()).
       FILTER(new SearchFilter(MessageFlag.class, SEARCH_FIELDS, "LIKE", "search")).
       FILTER(new OptionFilter(MessageFlag.class,"storageState", "=", StorageState.ALL, StorageState.ACTIVE),
           new RangeFilter(MessageFlag.class, "createdTime"),
           new RangeFilter(MessageFlag.class, "modifiedTime")).
       ORDERBY(new String[] {"modifiedTime" }, "modifiedTime", "DESC");
   if(params != null) {
     query.mergeValue(params);
   }
   return query(client, query, MessageFlag.class);
  }

  // Message Private
  public CommunicationAccount linkZaloUserId(ClientContext client) {
    return zaloLogic.linkZaloUserId(client);
  }

  public CommunicationAccount getCommunicationAccount(ClientContext client, Long accountId) {
    return communicationAccountRepo.getCommunicationAccount(accountId);
  }

  public CommunicationAccount getCommunicationAccountByAccountId(ClientContext client, Long accountId) {
    return communicationAccountRepo.getCommunicationAccountByAccountId(accountId);
  }

  public List<CommunicationAccount> findCommunicationAccountInChannel(ClientContext client, String channel) {
    return communicationAccountRepo.findCommunicationAccountInChannel(channel);
  }

  public CommunicationAccount saveCommunicationAccount(ClientContext client, CommunicationAccount account) {
    account.set(client);
    return communicationAccountRepo.save(account);
  }

  public Boolean deleteCommunicationAccount(ClientContext client, List<Long> ids) {
    communicationAccountRepo.deleteAllById(ids);
    return true;
  }

  List<SqlMapRecord> searchCommunicationAccount(ClientContext client, SqlQueryParams params) {
    String scriptDir = appEnv.addonPath("core", "groovy");
    String scriptFile = "net/datatp/module/communication/groovy/MessageAccountSql.groovy";
    return searchDbRecords(client, scriptDir, scriptFile, "SearchCommunicationAccount", params);
  }

  List<CommunicationAccount> searchChatChannelAccount(ClientContext client, SqlQueryParams params) {
    String[] SEARCH_FIELDS = new String[] {"accountId", "email", "fullName"};
    SqlQuery query =
        new SqlQuery().
            ADD_TABLE(new EntityTable(CommunicationAccount.class).selectAllFields()).
            FILTER(new SearchFilter(CommunicationAccount.class, SEARCH_FIELDS, "LIKE", "search")).
            FILTER( new OptionFilter(CommunicationAccount.class, "storageState", "=", StorageState.ALL, StorageState.ACTIVE),
            new RangeFilter(CommunicationAccount.class, "createdTime"),
            new RangeFilter(CommunicationAccount.class, "modifiedTime")).
            ORDERBY(new String[] { "modifiedTime" }, "modifiedTime", "DESC");
    if(params.hasParam("channelName")) {
      query
          .JOIN(new Join("INNER JOIN", "chat_channel_account_rel").ON("chat_account_id", CommunicationAccount.class, "id"))
          .JOIN(new Join("INNER JOIN", ChatChannel.class).ON("id", "chat_channel_account_rel", "chat_channel_id"))
          .FILTER(
              new ClauseFilter(ChatChannel.class, "name", "=", ":channelName")
          );
    }
    return query(client, query, params, CommunicationAccount.class);
  }

  public List<SqlMapRecord> searchCommunicationAccounts(ClientContext client, SqlQueryParams sqlParams) {
    String scriptDir = appEnv.addonPath("core", "groovy");
    String scriptFile = "net/datatp/module/communication/groovy/MessageAccountSql.groovy";
    return searchDbRecords(client, scriptDir, scriptFile, "SearchMessageAccount", sqlParams);
  }

  //MessageChannel
  public CommunicationChannel getCommunicationChannel(ClientContext client, String name) {
    return communicationChannelRepo.getCommunicationChannel(name);
  }

  public CommunicationChannel saveCommunicationChannel(ClientContext client, CommunicationChannel channel) {
    channel.set(client);
    return communicationChannelRepo.save(channel);
  }

  List<SqlMapRecord> searchCommunicationChannels(ClientContext client, SqlQueryParams params) {
    String scriptDir = appEnv.addonPath("core", "groovy");
    String scriptFile = "net/datatp/module/communication/groovy/MessageAccountSql.groovy";
    return searchDbRecords(client, scriptDir, scriptFile, "SearchCommunicationChannels", params);
  }

  public boolean addCommunicationAccountToChannel(ClientContext client, Long messageChannelId, List<CommunicationAccount> accounts) {
    CommunicationChannel channel = communicationChannelRepo.getById(messageChannelId);
    for(CommunicationAccount account : accounts) {
      account.getChannels().add(channel);
      saveCommunicationAccount(client, account);
    }
    return true;
  }

  public boolean removeCommunicationAccountFromChannel(ClientContext client, Long messageChannelId, List<CommunicationAccount> accounts) {
    CommunicationChannel channel = communicationChannelRepo.getById(messageChannelId);
    for(CommunicationAccount account : accounts) {
      account.getChannels().remove(channel);
      saveCommunicationAccount(client, account);
    }
    return true;
  }

  //Message
  public Message getMessage(ClientContext client, String code) {
    Message message = messageRepo.getMessage(code);
    return message;
  }

  public Message getMessage(ClientContext client, Long id) {
    Message message = messageRepo.getMessage(id);
    return message;
  }

  public List<Message> findMessageByIds(ClientContext client, Long [] ids) {
    return messageRepo.findMessageByIds(ids);
  }

  public Message saveMessage(ClientContext client, Message message) {
    if(message.isNew() && message.getCode() == null) {
      message.genCode();
    }
    if(message.getParentCode() == null) message.setParentCode(message.getCode());
    message.set(client);
    message = messageRepo.save(message);
    return message;
  }

  List<SqlMapRecord> searchMessages(ClientContext client, SqlQueryParams params) {
    String[] SEARCH_FIELDS = new String[] {"code"};
    SqlQuery query =
        new SqlQuery().
        ADD_TABLE(new EntityTable(Message.class).selectAllFields()).
        FILTER(new SearchFilter(Message.class, SEARCH_FIELDS, "LIKE", "search")).
        FILTER(new OptionFilter(Message.class,"storageState", "=", StorageState.ALL, StorageState.ACTIVE),
            new RangeFilter(Message.class, "createdTime"),
            new RangeFilter(Message.class, "modifiedTime")).
        ORDERBY(new String[] {"modifiedTime" }, "modifiedTime", "ASC");
    if(params.hasParam("accountId")) {
      query
          .FILTER(new ClauseFilter(Message.class, "senderAccountId", "=", ":accountId"));
    }
    query.mergeValue(params);
    return query(client, query).getSqlMapRecords();
  }

  public RecipientMessage getRecipientMessage(ClientContext client, Long id) {
    return recipientMessageRepo.getById(id);
  }

  public RecipientMessage saveRecipientMessage(ClientContext client, RecipientMessage recipient) {
    recipient.set(client);
    return recipientMessageRepo.save(recipient);
  }

  public List<SqlMapRecord> searchRecipientMessages(ClientContext client, SqlQueryParams params) {
    String groovyDir = appEnv.addonPath("core", "groovy");
    SqlQueryManager.QueryContext queryContext =
        sqlQueryManager.create(groovyDir, "communication/sql/RecipientMessageQuery.groovy");
    Binding binding = new Binding();
    binding.setVariable("sqlparams", params);

    SqlSelectView view  = queryContext.createSqlSelectView(getPrimaryDataSource(), binding);
    return view.renameColumWithJavaConvention().getSqlMapRecords();
  }

  public Message buildMessageFileAttachments(Message message) {
    LinkedList<Attachment> attachments = new LinkedList<Attachment>();
    List<UploadResource> attachmentResources = message.getAttachments();
    if(Collections.isEmpty(attachmentResources)) return null;
    StringBuilder error = new StringBuilder("ATT Fail:");

    for(UploadResource att : attachmentResources) {
      String fileName = att.getName();
      FileAttachment attachment = new FileAttachment();
      attachment.setOdataType("#microsoft.graph.fileAttachment");
      attachment.setName(fileName);
      try {
        String      storeId     = att.getStoreId();
        if("store".equals(att.getResourceScheme())) {
          SessionData sessionData = cipherTool.decryptSessionData(storeId);
          GETContent content      = GETService.get("store", sessionData.getData());
          attachment.setContentBytes(content.getData());
        } else {
          byte[] contentBytes = uploadService.load(storeId);
          attachment.setContentBytes(contentBytes);
        }
        attachments.add(attachment);
      } catch (Exception e) {
        error.append("\n File: " + fileName + " Error: " + e.getMessage());
        String errorAtt = error.toString();
        if(errorAtt.length() >  64 * 1024) {
          errorAtt = errorAtt.substring(0, 64 * 1024 - 1);
        }
        message.setErrorFileAttachment(errorAtt);
      }
    }
    message.setGraphAttachments(attachments);
    return message;
  }

  public void sendMailResetPassword(ClientContext client, String email, String message) {
    if(email != null) {
      MailMessage mailMessage = new MailMessage();
      mailMessage.setSubject("Thay đổi mật khẩu đăng nhập");
      mailMessage.setFrom("<EMAIL>");
      mailMessage.setTo(Arrays.asList(email));
      mailMessage.setMessage(message);
      graphApiService.sendEmail(client, null, mailMessage);
    }
  }

}