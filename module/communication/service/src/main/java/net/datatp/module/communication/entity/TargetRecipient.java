package net.datatp.module.communication.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.data.db.entity.PersistableEntity;

@Entity
@Table(
  name = TargetRecipient.TABLE_NAME,
  indexes = {
    @Index(columnList = "recipient_account_id")
  }
)
@NoArgsConstructor @Getter @Setter
public class TargetRecipient extends PersistableEntity<Long> {
  private static final long serialVersionUID = 1L;

  public static final String TABLE_NAME = "message_target_recipient";

  static public enum DeliverStatus {
    Waiting, Delivered, DeliveredWithError, Aborted
  }

  @NotNull
  @Enumerated(EnumType.STRING)
  @Column(name="deliver_type")
  private MessageDeliverType deliverType;

  @Column(name="recipient_display_name")
  private String recipientDisplayName;

  @NotNull
  @Column(name = "recipient_account_id")
  private Long recipientAccountId;

  @NotNull
  @Enumerated(EnumType.STRING)
  @Column(name = "deliver_status")
  private DeliverStatus deliverStatus =  DeliverStatus.Waiting;

  @Column(name = "deliver_count")
  private int deliverCount;

  private boolean delivered;

  private String note;

  @Column(name = "forward_email")
  private boolean forwardEmail ;

  public TargetRecipient(MessageDeliverType type, Long recipientAccountId) {
    this.deliverType = type;
    this.recipientAccountId = recipientAccountId;
  }

  public TargetRecipient(MessageDeliverType type, Long recipientAccountId, String recipientDisplayName ) {
    this.deliverType = type;
    this.recipientAccountId = recipientAccountId;
    this.recipientDisplayName = recipientDisplayName;
  }

  public TargetRecipient withDelivered(boolean delivered) {
    this.delivered = delivered;
    return this;
  }

  public TargetRecipient incDeliverCount(int count) {
    this.deliverCount += count;
    return this;
  }

  public TargetRecipient withForwardEmail(boolean bool) {
    this.forwardEmail = bool;
    return this;
  }
}