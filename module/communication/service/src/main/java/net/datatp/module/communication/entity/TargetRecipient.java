package net.datatp.module.communication.entity;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.data.db.entity.PersistableEntity;

@Entity
@Table(
  name = TargetRecipient.TABLE_NAME,
  indexes = {
    @Index(columnList = "recipient_account_id"),
    @Index(columnList = "recipient_id")
  }
)
@NoArgsConstructor @Getter @Setter
public class TargetRecipient extends PersistableEntity<Long> {
  private static final long serialVersionUID = 1L;

  public static final String TABLE_NAME = "message_target_recipient";

  static public enum DeliverStatus {
    Waiting, Delivered, DeliveredWithError, Aborted
  }

  @NotNull
  @Enumerated(EnumType.STRING)
  @Column(name="deliver_type")
  private MessageDeliverType deliverType;

  @Column(name="recipient_display_name")
  private String recipientDisplayName;

  // For Private deliver type - uses accountId
  @Column(name = "recipient_account_id")
  private Long recipientAccountId;

  // For Email/Channel deliver types - uses email/channel name
  @Column(name = "recipient_id")
  private String recipientId;

  @NotNull
  @Enumerated(EnumType.STRING)
  @Column(name = "deliver_status")
  private DeliverStatus deliverStatus =  DeliverStatus.Waiting;

  @Column(name = "deliver_count")
  private int deliverCount;

  private boolean delivered;

  private String note;

  @Column(name = "forward_email")
  private boolean forwardEmail ;

  // Constructor for Private deliver type (uses accountId)
  public TargetRecipient(MessageDeliverType type, Long recipientAccountId) {
    this.deliverType = type;
    this.recipientAccountId = recipientAccountId;
  }

  public TargetRecipient(MessageDeliverType type, Long recipientAccountId, String recipientDisplayName ) {
    this.deliverType = type;
    this.recipientAccountId = recipientAccountId;
    this.recipientDisplayName = recipientDisplayName;
  }

  // Constructor for Email/Channel deliver types (uses string id)
  public TargetRecipient(MessageDeliverType type, String recipientId) {
    this.deliverType = type;
    this.recipientId = recipientId;
  }

  public TargetRecipient(MessageDeliverType type, String recipientId, String recipientDisplayName) {
    this.deliverType = type;
    this.recipientId = recipientId;
    this.recipientDisplayName = recipientDisplayName;
  }

  public TargetRecipient withDelivered(boolean delivered) {
    this.delivered = delivered;
    return this;
  }

  public TargetRecipient incDeliverCount(int count) {
    this.deliverCount += count;
    return this;
  }

  public TargetRecipient withForwardEmail(boolean bool) {
    this.forwardEmail = bool;
    return this;
  }
}