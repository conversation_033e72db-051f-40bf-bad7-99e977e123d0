package net.datatp.module.zalo;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import net.datatp.security.client.ClientContext;
import net.datatp.module.communication.entity.CommunicationAccount;
import net.datatp.module.communication.repository.CommunicationAccountRepository;
import net.datatp.module.data.db.DAOService;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.zalo.entity.ZaloAuthorization;
import net.datatp.module.zalo.entity.ZaloOfficialAccount;
import net.datatp.module.zalo.entity.ZaloOfficialAccount.ZaloAccountType;
import net.datatp.module.zalo.repository.ZaloAuthorizationRepository;
import net.datatp.module.zalo.repository.ZaloOfficialAccountRepository;
import net.datatp.util.ds.MapObject;
import net.datatp.util.error.RuntimeError;
import net.datatp.util.text.StringUtil;

@Component
public class ZaloLogic extends DAOService {

  @Autowired
  private CommunicationAccountRepository messageAccountRepo;

  @Autowired
  private ZaloOfficialAccountRepository officialAccountRepository;

  @Autowired
  private ZaloAuthorizationRepository authorizationRepository;

  private final String ZALO_URL = "https://openapi.zalo.me/v3.0/oa";
  private final String AUTHENTICATE_URL = "https://oauth.zaloapp.com/v4/oa";

  public ZaloOfficialAccount saveZaloOfficialAccount(ClientContext client, ICompany company, ZaloOfficialAccount account) {
    account.set(client);
    if(account.isNew() && company != null) {
      if(ZaloAccountType.COMPANY.equals(account.getType())) {
        account.setCompanyId(company.getId());
        account.setCompanyCode(company.getCode());
      }
    }
    return officialAccountRepository.save(account);
  }

  public ZaloOfficialAccount getZaloOfficialAccount(ClientContext client, Long id) {
    return officialAccountRepository.getById(id);
  }

  public List<ZaloOfficialAccount> searchZaloOfficialAccount(ClientContext client, SqlQueryParams params) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "net/datatp/module/zalo/groovy/ZaloSql.groovy";
    return searchDbRecords(client, scriptDir, scriptFile, "SearchZaloOfficialAccount", params, ZaloOfficialAccount.class);
  }

  public ZaloAuthorization authenticate(ClientContext client, ZaloAuthenticateParams params) {
    ZaloApi api = new ZaloApi(AUTHENTICATE_URL);
    ZaloAuthorization auth = api.authenticate(params.getSecretKey(), params.getAppId(), params.getCode());
    return saveZaloAuthorization(client, auth);
  }

  public boolean deleteZaloAuthorizations(ClientContext client, List<Long> ids) {
    authorizationRepository.deleteAllById(ids);
    return true;
  }

  public ZaloAuthorization saveZaloAuthorization(ClientContext client, ZaloAuthorization auth) {
    auth.set(client);
    return authorizationRepository.save(auth);
  }

  public List<ZaloAuthorization> searchZaloAuthorization(ClientContext client, SqlQueryParams params) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "net/datatp/module/zalo/groovy/ZaloSql.groovy";
    return searchDbRecords(client, scriptDir, scriptFile, "SearchZaloAuthorization", params, ZaloAuthorization.class);
  }

  public ZaloOfficialAccount getZaloAuthorization(ClientContext client, Long id) {
    return officialAccountRepository.getById(id);
  }

  public CommunicationAccount linkZaloUserId(ClientContext client) {
    CommunicationAccount account = messageAccountRepo.getCommunicationAccount(client.getAccountId());
    ZaloApi api = getZaloApi(null, client, account.getZaloAppId());
    if(account.getMobile() == null) throw RuntimeError.UnknownError("Account {0} need mobile not null!!!", account.getAccountId());
    MapObject userDetail = api.getUserDetailByPhone(account.getMobile());
    account.setZaloAvatar(userDetail.getString("avatar"));
    account.setZaloUserId(userDetail.getString("user_id"));
    account.setZaloAppId(api.getAppId());
    account.setZaloDisplayName(userDetail.getString("display_name"));
    account.set(client);
    messageAccountRepo.save(account);
    return account;
  }

  public boolean sendMessage(ICompany company, ClientContext client, String toLoginId, String message) {
    CommunicationAccount fromCommunicationAccount = messageAccountRepo.getCommunicationAccount(client.getRemoteUser());
    CommunicationAccount toCommunicationAccount = messageAccountRepo.getCommunicationAccount(toLoginId);
    ZaloApi api = getZaloApi(company ,client, fromCommunicationAccount.getZaloAppId());
    if(toCommunicationAccount.getZaloUserId() == null) {
      toCommunicationAccount = linkZaloUserId(client);
    }
    api.sendMessage(toCommunicationAccount.getZaloUserId(), message);
    return true;
  }

  public boolean sendMessage(ICompany company, ClientContext client, CommunicationAccount fromCommunicationAccount, CommunicationAccount toCommunicationAccount, String message) {
    ZaloApi api = getZaloApi(company, client, fromCommunicationAccount.getZaloAppId());
    api.sendMessage(toCommunicationAccount.getZaloUserId(), message);
    return true;
  }

  public boolean sendZaloMessageByPhone(ICompany company, ClientContext client, String zaloAppId, String phone, String message) {
    ZaloApi api = getZaloApi(company, client, null);
    MapObject userDetail = api.getUserDetailByPhone(phone);
    String userId = userDetail.getString("user_id");
    api.sendMessage(userId, message);
    return true;
  }

  public boolean sendZaloMessageByPhone(ICompany company, ClientContext client, String phone, String message) {
    return sendZaloMessageByPhone(company, client, null, phone, message);
  }

  public ZaloApi getZaloApi(ICompany company, ClientContext client, String appId) {
    ZaloOfficialAccount oa = null;
    if(StringUtil.isBlank(appId)) {
      if(company !=  null) {
        oa = officialAccountRepository.getByCompanyPrimary(company.getId());
      }
      if(oa == null) oa = officialAccountRepository.getByDefault();
    } else {
      oa = officialAccountRepository.getByAppId(appId);
    }
    if(oa == null) throw RuntimeError.UnknownError("Zalo Official Account not found!!!");
    ZaloAuthorization auth =  getZaloAuthorization(client, oa, AUTHENTICATE_URL);
    ZaloApi api = new ZaloApi(ZALO_URL);
    api.setToken(auth.getAccessToken());
    api.setAppId(auth.getZaloAppId());
    return api;
  }

  public ZaloAuthorization getZaloAuthorization(ClientContext client, ZaloOfficialAccount oa, String authenticateUrl) {
    String appId = oa.getAppId();
    ZaloAuthorization auth =  authorizationRepository.getLastest(appId);
    if(auth == null) {
      throw RuntimeError.UnknownError("You need authorization zalo with code, follow doc authorization_code of zalo oa");
    } else {
      if(auth.isAccessTokenExpired() && !"test".equals(env)) {
        if(auth.isRefreshExpired()) throw RuntimeError.UnknownError("You need authorization zalo with code, follow doc authorization_code of zalo oa, Refresh token expired!!!");
        ZaloApi api = new ZaloApi(authenticateUrl);
        ZaloAuthorization newAuth = api.refreshToken(oa.getSecretKey(), appId, auth.getRefreshToken());
        auth = saveZaloAuthorization(client, newAuth);
      }
    }
    return auth;
  }
}
