package net.datatp.module.communication.repository;

import java.io.Serializable;
import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import net.datatp.module.communication.entity.CommunicationAccount;

public interface CommunicationAccountRepository extends JpaRepository<CommunicationAccount, Serializable> {

  @Query("SELECT m FROM CommunicationAccount m WHERE m.accountId = :accountId")
  public CommunicationAccount getCommunicationAccount(@Param("accountId") Long accountId);

  @Query("SELECT m FROM CommunicationAccount m LEFT JOIN m.channels ch WHERE ch.name = :name")
  public List<CommunicationAccount> findCommunicationAccountInChannel(@Param("name") String name);

  @Query("SELECT m FROM CommunicationAccount m LEFT JOIN m.chatChannels ch WHERE ch.name = :name")
  public List<CommunicationAccount> findCommunicationAccountInChatChannel(@Param("name") String name);

  @Query("SELECT m FROM CommunicationAccount m WHERE m.accountId = :accountId")
  CommunicationAccount getCommunicationAccountByAccountId(@Param("accountId") Long accountId);
}