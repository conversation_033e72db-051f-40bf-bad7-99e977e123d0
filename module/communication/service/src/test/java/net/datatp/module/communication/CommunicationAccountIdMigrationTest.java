package net.datatp.module.communication;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import net.datatp.module.account.AccountService;
import net.datatp.module.account.entity.Account;
import net.datatp.module.account.entity.AccountType;
import net.datatp.module.communication.entity.CommunicationAccount;
import net.datatp.module.communication.entity.Message;
import net.datatp.module.communication.entity.MessageDeliverType;
import net.datatp.module.communication.entity.RecipientMessage;
import net.datatp.module.communication.entity.TargetRecipient;
import net.datatp.security.client.ClientContext;

/**
 * Test class để validate việc migration từ loginId sang accountId
 * trong communication module
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
public class CommunicationAccountIdMigrationTest {

    @Autowired
    private CommunicationMessageService communicationService;

    @Autowired
    private AccountService accountService;

    private ClientContext testClient;
    private Account testAccount1;
    private Account testAccount2;

    @BeforeEach
    void setUp() {
        testClient = new ClientContext();

        // Tạo test accounts
        testAccount1 = new Account("testuser1", "password123", "<EMAIL>", AccountType.Employee);
        testAccount1 = accountService.saveAccount(testClient, testAccount1);

        testAccount2 = new Account("testuser2", "password123", "<EMAIL>", AccountType.Employee);
        testAccount2 = accountService.saveAccount(testClient, testAccount2);

        testClient.setAccountId(testAccount1.getId());
    }

    @Test
    void testCommunicationAccountCreationWithAccountId() {
        // Test tạo CommunicationAccount với accountId
        CommunicationAccount commAccount = new CommunicationAccount(testAccount1);

        assertNotNull(commAccount.getAccountId());
        assertEquals(testAccount1.getId(), commAccount.getAccountId());
        assertEquals(testAccount1.getEmail(), commAccount.getEmail());
        assertEquals(testAccount1.getFullName(), commAccount.getFullName());

        // Test identify method
        assertEquals(testAccount1.getId().toString(), commAccount.identify());
    }

    @Test
    void testMessageCreationWithSenderAccountId() {
        // Test tạo Message với senderAccountId
        Message message = new Message(testAccount1.getId());

        assertNotNull(message.getSenderAccountId());
        assertEquals(testAccount1.getId(), message.getSenderAccountId());

        // Test withPrivateRecipient method
        message.withPrivateRecipient(testAccount2.getId());

        assertNotNull(message.getRecipients());
        assertEquals(1, message.getRecipients().size());

        TargetRecipient recipient = message.getRecipients().get(0);
        assertEquals(testAccount2.getId(), recipient.getRecipientAccountId());
        assertEquals(MessageDeliverType.Private, recipient.getDeliverType());
    }

    @Test
    void testRecipientMessageCreationWithAccountIds() {
        // Tạo message
        Message message = new Message(testAccount1.getId());
        message.withSubject("Test Subject");

        // Test tạo RecipientMessage với accountId
        RecipientMessage recipientMessage = new RecipientMessage(testAccount2.getId(), message);

        assertNotNull(recipientMessage.getSenderAccountId());
        assertNotNull(recipientMessage.getRecipientAccountId());
        assertEquals(testAccount1.getId(), recipientMessage.getSenderAccountId());
        assertEquals(testAccount2.getId(), recipientMessage.getRecipientAccountId());
        assertEquals("Test Subject", recipientMessage.getSubject());
    }

    @Test
    void testTargetRecipientCreationWithAccountId() {
        // Test tạo TargetRecipient với accountId
        TargetRecipient target1 = new TargetRecipient(MessageDeliverType.Private, testAccount2.getId());

        assertNotNull(target1.getRecipientAccountId());
        assertEquals(testAccount2.getId(), target1.getRecipientAccountId());
        assertEquals(MessageDeliverType.Private, target1.getDeliverType());

        // Test constructor với display name
        TargetRecipient target2 = new TargetRecipient(
            MessageDeliverType.Private,
            testAccount2.getId(),
            testAccount2.getFullName()
        );

        assertEquals(testAccount2.getId(), target2.getRecipientAccountId());
        assertEquals(testAccount2.getFullName(), target2.getRecipientDisplayName());
    }

    @Test
    void testCommunicationAccountRepositoryMethods() {
        // Tạo và lưu CommunicationAccount
        CommunicationAccount commAccount = new CommunicationAccount(testAccount1);
        commAccount = communicationService.saveCommunicationAccount(testClient, commAccount);

        // Test getCommunicationAccount với accountId
        CommunicationAccount retrieved = communicationService.getCommunicationAccount(testClient, testAccount1.getId());

        assertNotNull(retrieved);
        assertEquals(testAccount1.getId(), retrieved.getAccountId());
        assertEquals(testAccount1.getEmail(), retrieved.getEmail());

        // Test getCommunicationAccountByAccountId
        CommunicationAccount retrievedById = communicationService.getCommunicationAccountByAccountId(testClient, testAccount1.getId());

        assertNotNull(retrievedById);
        assertEquals(testAccount1.getId(), retrievedById.getAccountId());
    }

    @Test
    void testMessageWithMultipleRecipients() {
        // Test message với nhiều recipients
        Message message = new Message(testAccount1.getId());
        message.withSubject("Multi Recipient Test")
               .withContent("Test content")
               .withPrivateRecipient(testAccount2.getId())
               .withEmailRecipient("<EMAIL>")
               .withChannelRecipient("general");

        // Thêm recipient thứ hai
        Account testAccount3 = new Account("testuser3", "password123", "<EMAIL>", AccountType.Employee);
        testAccount3 = accountService.saveAccount(testClient, testAccount3);

        message.withPrivateRecipient(testAccount3.getId());

        assertEquals(4, message.getRecipients().size());

        // Kiểm tra từng recipient
        boolean foundAccount2 = false;
        boolean foundAccount3 = false;
        boolean foundEmail = false;
        boolean foundChannel = false;

        for (TargetRecipient recipient : message.getRecipients()) {
            if (MessageDeliverType.isPrivate(recipient.getDeliverType())) {
                if (recipient.getRecipientAccountId().equals(testAccount2.getId())) {
                    foundAccount2 = true;
                } else if (recipient.getRecipientAccountId().equals(testAccount3.getId())) {
                    foundAccount3 = true;
                }
            } else if (MessageDeliverType.isEmail(recipient.getDeliverType())) {
                if ("<EMAIL>".equals(recipient.getRecipientId())) {
                    foundEmail = true;
                }
            } else if (MessageDeliverType.isChannel(recipient.getDeliverType())) {
                if ("general".equals(recipient.getRecipientId())) {
                    foundChannel = true;
                }
            }
        }

        assertTrue(foundAccount2, "Should find testAccount2 in recipients");
        assertTrue(foundAccount3, "Should find testAccount3 in recipients");
        assertTrue(foundEmail, "Should find email recipient");
        assertTrue(foundChannel, "Should find channel recipient");
    }

    @Test
    void testMessageSaveAndRetrieve() {
        // Test lưu và lấy message với accountId
        Message message = new Message(testAccount1.getId());
        message.withSubject("Save Test")
               .withContent("Test save and retrieve")
               .withPrivateRecipient(testAccount2.getId());

        Message savedMessage = communicationService.saveMessage(testClient, message);

        assertNotNull(savedMessage.getId());
        assertEquals(testAccount1.getId(), savedMessage.getSenderAccountId());

        // Retrieve message
        Message retrievedMessage = communicationService.getMessage(testClient, savedMessage.getId());

        assertNotNull(retrievedMessage);
        assertEquals(testAccount1.getId(), retrievedMessage.getSenderAccountId());
        assertEquals("Save Test", retrievedMessage.getSubject());
        assertEquals(1, retrievedMessage.getRecipients().size());
        assertEquals(testAccount2.getId(), retrievedMessage.getRecipients().get(0).getRecipientAccountId());
    }
}
