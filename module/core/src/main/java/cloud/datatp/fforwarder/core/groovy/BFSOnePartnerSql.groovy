package cloud.datatp.fforwarder.core.groovy

import net.datatp.lib.executable.ExecutableContext
import net.datatp.lib.executable.Executor
import net.datatp.module.data.db.ExecutableSqlBuilder
import net.datatp.util.ds.MapObject
import net.datatp.util.text.StringUtil
import org.springframework.context.ApplicationContext

import java.util.stream.Collectors

//TODO: An - move to logistics-sales module
class BFSOnePartnerSql extends Executor {

    public class FetchPublicPartner extends ExecutableSqlBuilder {

        public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
            MapObject sqlParams = ctx.getParam("sqlParams");

            String query = """
                SELECT
                  p.PartnerID                   AS partner_code,
                  p.DateCreate                  AS date_created,
                  p.DateModify                  AS date_modified,
                  p.PartnerName                 AS name,
                  p.PartnerName2                AS label,
                  p.PartnerName3                AS localized_label,
                  p.Address                     AS address,
                  p.Address2                    AS localized_address,
                  p.PersonalContact             AS personal_contact,
                  p.Email                       AS email,
                  p.Fax                         AS fax,
                  p.Cell                        AS cell,
                  p.Homephone                   AS home_phone,
                  p.Workphone                   AS work_phone,
                  p.Taxcode                     AS tax_code,
                  p.Country                     AS country_label,
                  p.VIP                         AS "source",
                  p.Location                    AS "scope",
                  p.Industry                    AS industry_code,
                  i.IndustryName                AS industry_label,
                  p.NotesLess                   AS note_1,
                  p.Notes                       AS note_2,

                  -- first sale employee
                  p.ContactID                   AS sale_owner_code,
                  ui.Username                   AS sale_owner_username,

                  -- input employee
                  p.InputPeople                 AS input_username,

                  p.Category                    AS category,
                  p.[Group]                     AS "group",
                  'TRUE'                        AS "public"
                FROM Partners p
                  LEFT JOIN lst_Industries i ON i.IDKey = p.Industry
                  LEFT JOIN UserInfos ui ON ui.UserID = p.ContactID
                WHERE 1 = 1 and p.[Group] is not null and p.Category is not null and p.Category <> ''
                    AND p.[Public] = 1 
                   ${AND_FILTER_BY_PARAM("p.Group", "groups", sqlParams)} 
              """
            return query;
        }
    }

    public class FetchPartnerBySalemanSql extends ExecutableSqlBuilder {

        public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
            MapObject sqlParams = ctx.getParam("sqlParams");

            String bfsoneCode = sqlParams.getString("bfsonePartnerCode", null);

            String filterByCode = ""
            if (StringUtil.isNotEmpty(bfsoneCode)) filterByCode = " AND (p.PartnerID = '" + bfsoneCode + "' OR p.Taxcode = '" + bfsoneCode + "')"

            String query = """
                SELECT
                  p.PartnerID                   AS partner_code,
                  p.DateCreate                  AS date_created,
                  p.DateModify                  AS date_modified,
                  p.PartnerName                 AS name,
                  p.PartnerName2                AS label,
                  p.PartnerName3                AS localized_label,
                  p.Address                     AS address,
                  p.Address2                    AS localized_address,
                  p.PersonalContact             AS personal_contact,
                  p.Email                       AS email,
                  p.Fax                         AS fax,
                  p.Cell                        AS cell,
                  p.Homephone                   AS home_phone,
                  p.Workphone                   AS work_phone,
                  p.Taxcode                     AS tax_code,
                  p.Country                     AS country_label,
                  p.VIP                         AS "source",
                  p.BankAccsNo                  AS bank_accs_no,
                  p.BankName                    AS bank_name,
                  p.BankAddress                 AS bank_address,
                  p.Location                    AS "scope",
                  p.Industry                    AS industry_code,
                  i.IndustryName                AS industry_label,
                  p.NotesLess                   AS note_1,
                  p.Notes                       AS note_2,

                  -- first sale employee
                  p.ContactID                   AS sale_owner_code,
                  ui.Username                   AS sale_owner_username,
                  o.ContactID                   AS saleman_obligation_code,

                  -- input employee
                  p.InputPeople                 AS input_username,

                  p.Category                    AS category,
                  p.[Group]                     AS "group"
                FROM Partners p
                  LEFT JOIN lst_Industries i ON i.IDKey = p.Industry
                  LEFT JOIN UserInfos ui ON ui.UserID = p.ContactID
                  LEFT JOIN ContactPartnerObligation o ON p.PartnerID = o.PartnerID
                WHERE 1 = 1 and p.[Group] is not null and p.Category is not null and p.Category <> ''
                ${AND_FILTER_BY_PARAM("p.PartnerID", "partnerIds", sqlParams)}
                ${AND_FILTER_BY_PARAM("o.ContactID", "contactIds", sqlParams)}
                ${OR_FILTER_BY_PARAM("p.ContactID", "contactIds", sqlParams)}
                ${filterByCode}
                ORDER BY p.ContactID ASC
              """;
            return query;
        }
    }

    public class FetchSalemanPartnersYesterdaySql extends ExecutableSqlBuilder {

        public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
            MapObject sqlParams = ctx.getParam("sqlParams");

            String query = """
                SELECT
                  p.PartnerID                   AS partner_code,
                  p.DateCreate                  AS date_created,
                  p.DateModify                  AS date_modified,
                  p.PartnerName                 AS name,
                  p.PartnerName2                AS label,
                  p.PartnerName3                AS localized_label,
                  p.Address                     AS address,
                  p.Address2                    AS localized_address,
                  p.PersonalContact             AS personal_contact,
                  p.Email                       AS email,
                  p.Fax                         AS fax,
                  p.Cell                        AS cell,
                  p.Homephone                   AS home_phone,
                  p.Workphone                   AS work_phone,
                  p.Taxcode                     AS tax_code,
                  p.Country                     AS country_label,
                  p.VIP                         AS "source",
                  p.BankAccsNo                  AS bank_accs_no,
                  p.BankName                    AS bank_name,
                  p.BankAddress                 AS bank_address,
                  p.Location                    AS "scope",
                  p.Industry                    AS industry_code,
                  i.IndustryName                AS industry_label,
                  p.NotesLess                   AS note_1,
                  p.Notes                       AS note_2,

                  -- first sale employee
                  p.ContactID                   AS sale_owner_code,
                  ui.Username                   AS sale_owner_username,

                  -- input employee
                  p.InputPeople                 AS input_username,

                  p.Category                    AS category,
                  p.[Group]                     AS "group",
                  
                  -- sale employee
                  ui.UserID                     AS saleman_obligation_code
                FROM Partners p
                  LEFT JOIN lst_Industries i ON i.IDKey = p.Industry
                  LEFT JOIN UserInfos ui ON ui.UserID = p.ContactID
                  LEFT JOIN ContactPartnerObligation o ON p.PartnerID = o.PartnerID
                WHERE 1 = 1 
                  AND p.DateCreate >= DATEADD(DAY, DATEDIFF(DAY, 1, GETDATE()), 0) 
                  AND p.DateCreate <  DATEADD(DAY, DATEDIFF(DAY, 0, GETDATE()), 0)
                ORDER BY p.ContactID ASC
              """;
            return query;
        }
    }

    public class CheckPartnerExistingInfo extends ExecutableSqlBuilder {
        @Override
        public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
            MapObject sqlParams = ctx.getParam("sqlParams");
            String pattern = sqlParams.getString("searchPattern", "");
            System.out.println("--------------CheckPartnerExistingInfo: " + pattern);
            String query = """
                SELECT 
                    p.partner_code, 
                    p.tax_code, 
                    p."name", 
                    p.date_created, 
                    p.address, 
                    p.input_username, 
                    p."group",
                    hb.transaction_id, 
                    hb.report_date,
                    hb.shipment_type,
                    -- Danh sách 10 lô hàng gần nhất (JSON)
                    sd.shipment_detail
                FROM integrated_partner p
                -- Lấy 1 lô hàng gần nhất
                LEFT JOIN LATERAL (
                    SELECT hb.transaction_id, hb.report_date, hb.shipment_type
                    FROM integrated_housebill hb
                    WHERE hb.customer_code = p.partner_code
                    ORDER BY hb.report_date DESC
                    LIMIT 1
                ) hb ON true
                -- Lấy top 10 lô hàng gần nhất
                LEFT JOIN LATERAL (
                    SELECT json_agg(
                        json_build_object(
                            'transaction_id', hb.transaction_id,
                            'report_date', hb.report_date,
                            'shipment_type', hb.shipment_type
                        ) ORDER BY hb.report_date DESC
                    ) AS shipment_detail
                    FROM (
                        SELECT hb.transaction_id, hb.report_date, hb.shipment_type
                        FROM integrated_housebill hb
                        WHERE hb.customer_code = p.partner_code
                        ORDER BY hb.report_date DESC
                        LIMIT 10
                    ) hb
                ) sd ON true
                WHERE (p.tax_code = '${pattern}' OR p."name" ILIKE '%${pattern}%');
            """;
            return query;
        }
    }

    //TODO: An - review, consider to use beelegacy_db
    public class CheckBFSOnePartnerByTaxCode extends ExecutableSqlBuilder {
        @Override
        public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
            MapObject sqlParams = ctx.getParam("sqlParams");
            String searchPattern = sqlParams.getString("searchPattern", "");
            System.out.println("--------------CheckBFSOnePartnerByTaxCode: " + searchPattern);

            String query = """
                -- CTE gom các user theo PartnerID
                WITH ObligationDetails AS (
                    SELECT
                        o.PartnerID,
                        STRING_AGG(u.FullName, CHAR(10)) AS authorize_users
                    FROM ContactPartnerObligation o
                    JOIN UserInfos u ON u.UserID = o.ContactID
                    GROUP BY o.PartnerID
                )
                SELECT
                    p.PartnerID           AS partner_id,
                    p.PartnerName         AS partner_name,
                    p.Address2            AS address,
                    p.Taxcode             AS tax_code,
                    p.DateCreate          AS date_created,
                
                    -- Sales employee
                    ui2.FullName          AS saleman_label,
                    ui2.Username          AS saleman_username,
                
                    -- Concatenated authorize_users
                    od.authorize_users    AS authorize_users,
                
                    -- Input employee
                    p.InputPeople         AS input_people,
                    inputUser.FullName    AS input_people_name
                
                    -- Transaction gần nhất
                    --hb.transaction_id     AS transaction_id, 
                    --hb.transaction_date   AS transaction_date,
                    --hb.shipment_type      AS shipment_type
                FROM Partners p
                -- Saleman owner
                LEFT JOIN UserInfos ui2 ON ui2.UserID = p.ContactID
                -- Input data
                LEFT JOIN UserInfos inputUser ON inputUser.Username = p.InputPeople
                -- Allocated Saleman
                LEFT JOIN ObligationDetails od ON od.PartnerID = p.PartnerID
                -- Lasted Transaction OUTER APPLY (chuẩn SQL Server, thay cho LATERAL)
                --OUTER APPLY (
                --    SELECT TOP 1
                --        t.TransID        AS transaction_id,
                --        t.TransDate      AS transaction_date,
                --        td.ShipperID     AS customer_code,
                --        td.ShipmentType  AS shipment_type
                --    FROM Transactions t
                --    INNER JOIN TransactionDetails td ON td.TransID = t.TransID
                --    WHERE (td.ShipperID = p.PartnerID OR t.AgentID = p.PartnerID)
                --    ORDER BY t.TransDate DESC
                --) hb
                WHERE p.Taxcode = '${searchPattern}' OR p.PartnerName LIKE '%${searchPattern}%'
            """
            return query;
        }
    }

    // -------------- Bee Legacy DB ------------------
    public class FindTop10LatestTransactionByCustomer extends ExecutableSqlBuilder {
        @Override
        public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
            MapObject sqlParams = ctx.getParam("sqlParams");
            List<String> partnerIds = sqlParams.getStringList("partnerIds");
            List<String> quotedPartnerIds = partnerIds.stream()
                    .map(id -> "'" + id + "'")
                    .collect(Collectors.toList());
            String partnerIdsStr = String.join(",", quotedPartnerIds);
            System.out.println("--------------Find Top 10 Latest Transaction By Customer: " + partnerIdsStr + "--------------");

            String query = """
                WITH raw_data AS (
                  SELECT 
                    transaction_id,
                    report_date,
                    shipment_type,
                    saleman_contact_id,
                    CASE 
                      WHEN customer_code IN (${partnerIdsStr}) THEN customer_code
                      ELSE agent_code
                    END AS customer_code
                  FROM integrated_housebill ih
                  WHERE ih.customer_code IN (${partnerIdsStr}) OR ih.agent_code IN (${partnerIdsStr})
                ),
                deduplicated AS (
                  SELECT *,
                         ROW_NUMBER() OVER (
                           PARTITION BY customer_code, transaction_id 
                           ORDER BY report_date DESC
                         ) AS tx_rn
                  FROM raw_data
                ),
                ranked AS (
                  SELECT *,
                         ROW_NUMBER() OVER (
                           PARTITION BY customer_code
                           ORDER BY report_date DESC
                         ) AS rn
                  FROM deduplicated
                  WHERE tx_rn = 1
                )
                SELECT *
                FROM ranked
                WHERE rn <= 10
                ORDER BY customer_code, report_date DESC;
            """
            return query;
        }
    }

    public class BFSOnePartnerVolumeReport extends ExecutableSqlBuilder {
        // -------------- Bee Legacy DB ------------------
        @Override
        public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
            MapObject sqlParams = ctx.getParam("sqlParams");
            String query = """
                SELECT
                h.customer_code             as bfsone_partner_code,
                h.type_of_service           as type_of_service,
                h.transaction_id            as job_no,
                h.transaction_date          as job_date,
                h.container_size            as job_cont,
                h.hawb_gw                   as hawb_gw,
                h.hawb_cbm                  as hawb_cbm
                FROM integrated_housebill h
                WHERE
                ${FILTER_BY_PARAM("h.customer_code", "partnerCodes", sqlParams)}
                AND h.transaction_date >= '2024-01-01'
                AND h.type_of_service IN (
                    'CustomsLogistics',
                    'AirExpTransactions',
                    'AirImpTransactions',
                    'SeaExpTransactions_LCL',
                    'SeaImpTransactions_LCL',
                    'SeaExpTransactions_FCL',
                    'SeaImpTransactions_FCL'
                )
                ORDER BY h.transaction_id ASC, h.transaction_date DESC
            """
            return query;
        }
    }

    public class SearchAgentTransactions extends ExecutableSqlBuilder {
        @Override
        public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
            MapObject sqlParams = ctx.getParam("sqlParams");
            String query = """
                SELECT 
                    p.partner_code AS agent_code,
                    p.name AS agent_name, 
                    h.customer_name,
                    p.country_label, 
                    p.source, 
                    p.address, 
                    p.work_phone, 
                    p.fax,
                    h.transaction_id,
                    h.transaction_date,
                    h.shipment_type, 
                    h.type_of_service,
                    h.container_size,
                    h.hawb_no,
                    h.hawb_cbm,
                    h.hawb_gw,
                    h.report_date
                FROM integrated_partner p
                INNER JOIN integrated_housebill h ON h.agent_code = p.partner_code
                WHERE p.group = 'AGENTS'
                  AND h.agent_code IS NOT NULL 
                  ${addAndClause(sqlParams, "searchPattern", "(UPPER(p.name) LIKE '%' || :searchPattern || '%' OR p.partner_code LIKE :searchPattern)")}
                  ${AND_FILTER_BY_PARAM('p.country_label', 'country', sqlParams)}
                  ${AND_FILTER_BY_PARAM('p.continent', 'continent', sqlParams)}
                  ${AND_FILTER_BY_PARAM('p.source', 'source', sqlParams)}
                  ${AND_FILTER_BY_RANGE('h.report_date', 'shipmentDate', sqlParams)}
            """;
            return query;
        }
    }

    public BFSOnePartnerSql() {
        register(new FetchPartnerBySalemanSql());
        register(new FetchPublicPartner());
        register(new CheckBFSOnePartnerByTaxCode());
        register(new BFSOnePartnerVolumeReport());
        register(new FetchSalemanPartnersYesterdaySql());
        register(new FindTop10LatestTransactionByCustomer());
        register(new CheckPartnerExistingInfo());
        register(new SearchAgentTransactions());
    }
}