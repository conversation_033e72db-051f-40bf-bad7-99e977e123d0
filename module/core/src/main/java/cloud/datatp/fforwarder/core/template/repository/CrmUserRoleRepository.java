package cloud.datatp.fforwarder.core.template.repository;

import cloud.datatp.fforwarder.core.template.entity.CrmUserRole;
import java.io.Serializable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface CrmUserRoleRepository extends JpaRepository<CrmUserRole, Serializable> {

  @Query("SELECT c FROM CrmUserRole c WHERE c.accountId = :accountId")
  CrmUserRole findByAccountId(@Param("accountId") Long accountId);

  @Query("SELECT c FROM CrmUserRole c WHERE c.bfsoneCode = :bfsoneCode")
  CrmUserRole getByBfsoneCode(@Param("bfsoneCode") String bfsoneCode);

  @Query("SELECT c FROM CrmUserRole c WHERE c.bfsoneUsername = :bfsoneUsername")
  CrmUserRole getByBFSOneUsername(@Param("bfsoneUsername") String bfsoneUsername);

}