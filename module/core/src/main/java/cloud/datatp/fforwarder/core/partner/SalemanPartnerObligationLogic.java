package cloud.datatp.fforwarder.core.partner;

import cloud.datatp.fforwarder.core.db.CRMDaoService;
import cloud.datatp.fforwarder.core.partner.entity.SalemanPartnerObligation;
import cloud.datatp.fforwarder.core.partner.repository.SalemanPartnerObligationRepository;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import net.datatp.module.account.AccountLogic;
import net.datatp.module.account.entity.Account;
import net.datatp.module.company.CompanyReadLogic;
import net.datatp.module.company.entity.Company;
import net.datatp.module.core.security.SecurityLogic;
import net.datatp.module.core.security.entity.AppPermission;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.data.db.util.DBConnectionUtil;
import net.datatp.module.data.db.util.DeleteGraphBuilder;
import net.datatp.module.hr.EmployeeLogic;
import net.datatp.security.client.Capability;
import net.datatp.security.client.ClientContext;
import net.datatp.util.ds.Objects;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class SalemanPartnerObligationLogic extends CRMDaoService {

  @Autowired
  private SalemanPartnerObligationRepository obligationRepo;

  @Autowired
  private EmployeeLogic employeeLogic;
  
  @Autowired
  private CompanyReadLogic companyReadLogic;

  @Autowired
  private AccountLogic accountLogic;

  @Autowired
  private SecurityLogic securityLogic;

  public SalemanPartnerObligation getById(ClientContext client, Long id) {
    return obligationRepo.getById(id);
  }

  public List<SalemanPartnerObligation> findByPartnerCode(ClientContext client, String partnerCode) {
    return obligationRepo.findByPartnerCode(partnerCode);
  }

  public List<SalemanPartnerObligation> findBySalemanAccountId(ClientContext client, Long salemanAccountId) {
    return obligationRepo.findBySaleman(salemanAccountId);
  }

  public SalemanPartnerObligation updateObligation(ClientContext client, SalemanPartnerObligation obligation) {
    obligation.computeCode();
    obligation.set(client);
    return obligationRepo.save(obligation);
  }

  public SalemanPartnerObligation createIfNotExists(ClientContext client, SalemanPartnerObligation obligation) {
    final String code = obligation.computeCode();
    SalemanPartnerObligation existing = obligationRepo.getByCode(code);
    if (existing == null) {
      Account approver = accountLogic.getAccountById(client, client.getAccountId());
      Objects.assertNotNull(approver, "Approver Account {} is not found", client.getRemoteUser());
      obligation.withApproverAccount(approver);
      return updateObligation(client, obligation);
    } else return existing;
  }

  public List<SqlMapRecord> searchObligations(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    sqlParams.addParam("companyId", company.getId());
    final AppPermission permission = securityLogic.getAppPermission(client, company.getId(), "logistics", "user-logistics-sales");
    if (permission == null) return Collections.emptyList();
    final boolean accessAll = permission.getCapability().hasCapability(Capability.Moderator) && permission.isAllScope();
    sqlParams.addParam("accessAll", accessAll);
    sqlParams.addParam("accessAccountId", client.getAccountId());
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fforwarder/core/groovy/SearchEmployeePartnerObligationSql.groovy";
    
    
    List<Company> companies = companyReadLogic.findAll(client);
    Map<Long, String> companyMap = companies.stream().collect(Collectors.toMap(c -> c.getId(), c -> c.getLabel()));
    List<SqlMapRecord> searchDbRecords = searchDbRecords(client, scriptDir, scriptFile, "SearchEmployeePartnerObligation", sqlParams);
    return searchDbRecords.stream().map(record -> {
      Long companyId = record.getLong("salemanCompanyId");
      if (companyId != null) {
        String companyLabel = companyMap.get(companyId);
        if (companyLabel != null) record.set("companyLabel", companyLabel);
      }
      return record;
    }).toList();
  }

  public int deleteObligation(ClientContext client, ICompany company, List<Long> targetIds) {
    DBConnectionUtil connectionUtil = getDBConnectionUtil();
    DeleteGraphBuilder deleteGraphBuilder = new DeleteGraphBuilder(connectionUtil, company.getId(), SalemanPartnerObligation.class, targetIds);
    final int count = deleteGraphBuilder.runDelete();
    connectionUtil.commit();
    connectionUtil.close();
    return count;
  }

  public List<SalemanPartnerObligation> findBySalemanCompany(ClientContext client, ICompany company) {
    return obligationRepo.findBySalemanCompanyId(company.getId());
  }

}