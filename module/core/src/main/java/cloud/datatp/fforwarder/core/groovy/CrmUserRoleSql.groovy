package cloud.datatp.fforwarder.core.groovy

import net.datatp.lib.executable.ExecutableContext
import net.datatp.lib.executable.Executor
import net.datatp.module.data.db.ExecutableSqlBuilder
import net.datatp.util.ds.MapObject
import org.springframework.context.ApplicationContext

class CrmUserRoleSql extends Executor {
    public class SearchCrmUserRole extends ExecutableSqlBuilder {
        @Override
        public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
            MapObject sqlParams = ctx.getParam("sqlParams");

            String query = """
                SELECT 
                    u.id                        AS id,
                    u.account_id                AS account_id,
                    u.full_name                 AS full_name,
                    u.bfsone_username           AS bfsone_username,
                    u.bfsone_code               AS bfsone_code,
                    u.company_branch_name       AS company_branch_name,
                    u.company_branch_code       AS company_branch_code,
                    u.department_name           AS department_name,
                    u.department_label          AS department_label,
                    u.team                      AS team,
                    u.type                      AS type
                FROM lgc_forwarder_crm_user_roles u
                WHERE
                  ${FILTER_BY_STORAGE_STATE('u', sqlParams)}
                ${MAX_RETURN(sqlParams)}  
            """;
            return query;
        }
    }

    public CrmUserRoleSql() {
        register(new SearchCrmUserRole());
    }
}