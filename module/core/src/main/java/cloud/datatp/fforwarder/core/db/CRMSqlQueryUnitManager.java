package cloud.datatp.fforwarder.core.db;

import groovy.lang.Binding;
import java.util.List;
import java.util.Map;
import javax.sql.DataSource;
import net.datatp.lib.executable.ExecutableContext;
import net.datatp.module.data.db.ResultSetDataExtractor;
import net.datatp.module.data.db.SqlSelectView;
import net.datatp.module.data.db.entity.PersistableEntity;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.data.db.repository.DAOTemplate;
import net.datatp.module.data.db.util.DBConnectionUtil;
import net.datatp.module.service.ExecutableUnitManager;
import net.datatp.util.ds.MapObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class CRMSqlQueryUnitManager {

  @Autowired
  private ExecutableUnitManager executableUnitManager;

  @Autowired
  private CRMDAOTemplatePrimary daoTemplate;

  public QueryContext create(String scriptDir, String scriptFile, String name) {
    return new QueryContext(scriptDir, scriptFile, name);
  }

  public class QueryContext {
    private ExecutableContext executableContext;

    public QueryContext(String scriptDir, String scriptFile, String name) {
      executableContext = new ExecutableContext().withScriptEnv(scriptDir, scriptFile, name);
    }

    public int update(Map<String, Object> sqlParams) {
      executableContext
        .resetParams()
        .withParam("sqlParams", sqlParams);
      String query = createQuery(executableContext);
      return daoTemplate.update(query, sqlParams);
    }

    public SqlSelectView createSqlSelectView(SqlQueryParams sqlParams) {
      MapObject params = sqlParams.toNamedQueryParams();
      executableContext
        .resetParams()
        .withParam("sqlParams", params);
      String query = createQuery(executableContext);
      return daoTemplate.sqlSelect(query, params);
    }
    
    public <T> List<T> query(SqlQueryParams sqlParams, Class<T> type) {
      MapObject params = sqlParams.toNamedQueryParams();
      executableContext
        .resetParams()
        .withParam("sqlParams", params);
      String query = createQuery(executableContext);
      return daoTemplate.queryForList(query, params, type);
    }

    public <T extends PersistableEntity<?>> List<T> queryForEntities(SqlQueryParams sqlParams, Class<T> type) {
      MapObject params = sqlParams.toNamedQueryParams();
      executableContext
        .resetParams()
        .withParam("sqlParams", params);
      String query = createQuery(executableContext);
      return daoTemplate.queryForEntities(query, params, type);
    }
    
    public <T> T query(SqlQueryParams sqlParams, ResultSetDataExtractor<T> extractor) {
      MapObject params = sqlParams.toNamedQueryParams();
      executableContext
        .resetParams()
        .withParam("sqlParams", params);
      String query = createQuery(executableContext);
      return daoTemplate.query(query, params, extractor);
    }

    // ------------- customize another data source ---------------
    public SqlSelectView createSqlSelectView(DataSource ds, Binding binding) {
      executableContext
        .resetParams()
        .withParam("binding", binding);
      String query = createQuery(executableContext);
      DBConnectionUtil connUtil = new DBConnectionUtil(ds);
      try {
        return connUtil.createSqlSelectView(query);
      } finally {
        connUtil.close();
      }
    }

    public SqlSelectView createSqlSelectView(DataSource ds, SqlQueryParams sqlParams) {
      DAOTemplate daoTemplate = new DAOTemplate(ds);
      MapObject params = sqlParams.toNamedQueryParams();
      executableContext
        .resetParams()
        .withParam("sqlParams", params);
      String query = createQuery(executableContext);
      return daoTemplate.sqlSelect(query, params);
    }

    protected String createQuery(ExecutableContext ctx) {
      return (String) executableUnitManager.execute(ctx);
    }
  }
}