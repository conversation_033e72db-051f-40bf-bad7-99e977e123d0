package cloud.datatp.fforwarder.core.partner.repository;

import cloud.datatp.fforwarder.core.partner.entity.SalemanPartnerObligation;
import java.io.Serializable;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface SalemanPartnerObligationRepository extends JpaRepository<SalemanPartnerObligation, Serializable> {

  @Query("SELECT l FROM SalemanPartnerObligation l WHERE l.bfsonePartnerCode = :partnerCode ORDER BY l.createdTime")
  List<SalemanPartnerObligation> findByPartnerCode(@Param("partnerCode") String partnerCode);

  @Modifying
  @Query("DELETE FROM SalemanPartnerObligation WHERE bfsonePartnerCode = :partnerCode")
  int deleteByBFSOnePartnerCode(@Param("partnerCode") String partnerCode);

  @Query("SELECT l FROM SalemanPartnerObligation l WHERE l.id = :id")
  SalemanPartnerObligation getById(@Param("id") Long id);

  @Query("SELECT l FROM SalemanPartnerObligation l WHERE l.id IN :ids")
  List<SalemanPartnerObligation> findByIds(@Param("ids") List<Long> ids);

  @Query("SELECT l FROM SalemanPartnerObligation l WHERE l.salemanCompanyId = :salemanCompanyId")
  List<SalemanPartnerObligation> findBySalemanCompanyId(@Param("salemanCompanyId") Long saleCompanyId);

  @Query("SELECT l FROM SalemanPartnerObligation l WHERE l.salemanAccountId = :salemanAccountId")
  List<SalemanPartnerObligation> findBySaleman(@Param("salemanAccountId") Long salemanAccountId);

  @Query("SELECT l FROM SalemanPartnerObligation l WHERE l.code = :code")
  SalemanPartnerObligation getByCode(@Param("code") String code);
}