package cloud.datatp.fforwarder.core.partner;

import cloud.datatp.fforwarder.core.message.MessageServicePlugin;
import cloud.datatp.fforwarder.core.message.entity.CRMMessageSystem;
import lombok.extern.slf4j.Slf4j;
import net.datatp.security.client.ClientContext;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class PartnerMonitorMessagePlugin extends MessageServicePlugin {
  public static final String PLUGIN_TYPE = "partner-monitor";

  protected PartnerMonitorMessagePlugin() {
    super(PLUGIN_TYPE);
  }

  @Override
  public CRMMessageSystem onPreSend(ClientContext client, CRMMessageSystem message) {
    return message;
  }

  @Override
  public void onPostSend(ClientContext client, CRMMessageSystem message) {}

  @Override
  public void onSendError(ClientContext client, CRMMessageSystem message, Exception error) {
  }

}