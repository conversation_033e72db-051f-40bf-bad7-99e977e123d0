package cloud.datatp.fforwarder.core.partner;

import cloud.datatp.fforwarder.core.partner.entity.BFSOnePartner;
import java.util.List;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.hr.entity.Employee;
import net.datatp.module.service.BaseComponent;
import net.datatp.security.client.ClientContext;
import net.datatp.util.ds.MapObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service("BFSOnePartnerService")
public class BFSOnePartnerService extends BaseComponent {

  @Autowired
  private BFSOnePartnerLogic bfsonePartnerLogic;

  @Transactional(readOnly = true)
  public BFSOnePartner getBFSOnePartner(ClientContext client, Long id) {
    return bfsonePartnerLogic.getById(client, id);
  }

  @Transactional(readOnly = true)
  public BFSOnePartner getBFSOnePartnerByCode(ClientContext client, ICompany company, String code) {
    return bfsonePartnerLogic.getByCode(client, code);
  }

  @Transactional(readOnly = true)
  public List<BFSOnePartner> findBFSOnePartnerByCodeTemp(ClientContext client, ICompany company, String codeTemp) {
    return bfsonePartnerLogic.findByBFSOneCodeTemp(client, codeTemp);
  }

  @Transactional(readOnly = true)
  public List<BFSOnePartner> findByTaxCode(ClientContext client, ICompany company, String taxCode) {
    return bfsonePartnerLogic.findByTaxCode(client, company, taxCode);
  }

  @Transactional
  public int deleteBFSOnePartners(ClientContext client, ICompany company, List<Long> targetIds) {
    return bfsonePartnerLogic.deleteBFSOnePartner(client, company, targetIds);
  }

  @Transactional(readOnly = true)
  public BFSOnePartner getByPartnerId(ClientContext client, Long partnerId) {
    return bfsonePartnerLogic.getByAccountId(client, partnerId);
  }

  @Transactional
  public BFSOnePartner saveBFSOnePartner(ClientContext client, BFSOnePartner partner) {
    return bfsonePartnerLogic.saveBFSOnePartner(client, partner);
  }

  @Transactional
  public List<BFSOnePartner> importPartners(ClientContext client, Employee saleman, List<BFSOnePartner> partners) {
    return bfsonePartnerLogic.importPartners(client, saleman, partners);
  }

  @Transactional
  public List<BFSOnePartner> importPartners(ClientContext client, List<MapObject> partners) {
    return bfsonePartnerLogic.importPartners(client, partners);
  }

  @Transactional
  public List<BFSOnePartner> syncBFSOnePartnersBySaleman(ClientContext client, Long salemanAccountId) {
    return bfsonePartnerLogic.syncBFSOnePartnersBySaleman(client, salemanAccountId);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchBFSOnePartners(ClientContext client, ICompany company, SqlQueryParams params) {
    return bfsonePartnerLogic.searchBFSOnePartners(client, company, params);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchBFSOnePartnerCustomers(ClientContext client, ICompany company, SqlQueryParams params) {
    return bfsonePartnerLogic.searchBFSOnePartnerCustomers(client, company, params);
  }

  @Transactional(readOnly = true)
  public BFSOnePartner fetchBFSOnePartnersByCode(ClientContext client, ICompany company, String bfsonePartnerCode) {
    return bfsonePartnerLogic.fetchBFSOnePartnersByCode(client, company, bfsonePartnerCode);
  }

  @Transactional
  public BFSOnePartner syncBFSOnePartnersByCode(ClientContext client, ICompany company, String bfsonePartnerCode) {
    return bfsonePartnerLogic.syncBFSOnePartnersByCode(client, company, bfsonePartnerCode);
  }

}