package cloud.datatp.fforwarder.core.template.entity;

import com.fasterxml.jackson.annotation.JsonInclude;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import jakarta.validation.constraints.NotNull;
import java.util.Locale;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.data.db.entity.PersistableEntity;
import net.datatp.util.text.StringUtil;

/**
 * <AUTHOR>
 * <EMAIL>
 * @version 1.0
 * @since 2024
 */

@Entity
@Table(
  name = CrmUserRole.TABLE_NAME,
  uniqueConstraints = {
    @UniqueConstraint(
      name = CrmUserRole.TABLE_NAME + "_bfsone_username",
      columnNames = { "bfsone_username" }
    ),
    @UniqueConstraint(
      name = CrmUserRole.TABLE_NAME + "_bfsone_code",
      columnNames = { "bfsone_code" }
    ),
  },
  indexes = {
    @Index(
      name = CrmUserRole.TABLE_NAME + "_full_name_idx",
      columnList = "full_name"
    ),
    @Index(
      name = CrmUserRole.TABLE_NAME + "_account_id_idx",
      columnList = "account_id"
    ),
    @Index(
      name = CrmUserRole.TABLE_NAME + "_bfsone_username_idx",
      columnList = "bfsone_username"
    ),
    @Index(
      name = CrmUserRole.TABLE_NAME + "_bfsone_code_idx",
      columnList = "bfsone_code"
    )
  }
)
@JsonInclude(JsonInclude.Include.NON_NULL)
@NoArgsConstructor
@AllArgsConstructor
@Setter @Getter
public final class CrmUserRole extends PersistableEntity<Long> {

  final static public String TABLE_NAME = "lgc_forwarder_crm_user_roles";

  public enum UserType {
    SALE_FREEHAND,
    SALE_AGENT,
    OTHER
  }

  @Enumerated(EnumType.STRING)
  @Column(nullable = false)
  private UserType type;

  @NotNull
  @Column(name = "full_name", nullable = false)
  private String fullName;

  @NotNull
  @Column(name = "account_id")
  private Long accountId;

  @Column(name = "bfsone_username")
  private String bfsoneUsername;

  @Column(name = "bfsone_code")
  private String bfsoneCode;

  @Column(name = "company_branch_name")
  private String companyBranchName;

  @Column(name = "company_branch_code")
  private String companyBranchCode;

  @Column(name = "department_name")
  private String departmentName;

  @Column(name = "department_label")
  private String departmentLabel;

  @Column(name = "team")
  private String team;

  public CrmUserRole(Long accountId) {
    this.accountId = accountId;
  }

  public void setFullName(String fullName) {
    if(StringUtil.isEmpty(fullName)) return;
    this.fullName = fullName.toUpperCase(Locale.ROOT);
  }

  public void setBFSOneUsername(String userName) {
    if(StringUtil.isEmpty(userName)) return;
    this.bfsoneUsername = userName.toUpperCase(Locale.ROOT);
  }

  public void setBFSOneCode(String code) {
    if(StringUtil.isEmpty(code)) return;
    this.bfsoneCode = code.toUpperCase(Locale.ROOT);
  }

}