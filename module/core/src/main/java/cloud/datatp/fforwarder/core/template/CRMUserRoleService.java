package cloud.datatp.fforwarder.core.template;

import cloud.datatp.fforwarder.core.template.entity.CrmUserRole;

import java.util.List;

import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.service.BaseComponent;
import net.datatp.security.client.ClientContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service("CRMUserRoleService")
public class CRMUserRoleService extends BaseComponent {

  @Autowired
  private CRMUserRoleLogic crmUserRoleLogic;

  @Transactional(readOnly = true)
  public CrmUserRole loadById(ClientContext client, Long id) {
    return crmUserRoleLogic.getById(client, id);
  }

  @Transactional(readOnly = true)
  public CrmUserRole findByAccountId(ClientContext client, Long accountId) {
    return crmUserRoleLogic.findByAccountId(client, accountId);
  }

  @Transactional(readOnly = true)
  public CrmUserRole getByBfsoneCode(ClientContext client, String bfsoneCode) {
    return crmUserRoleLogic.getByBfsoneCode(client, bfsoneCode);
  }

  @Transactional(readOnly = true)
  public CrmUserRole getByBfsoneUsername(ClientContext client, String bfsoneUsername) {
    return crmUserRoleLogic.getByBfsoneUsername(client, bfsoneUsername);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchCrmUserRoles(ClientContext client, SqlQueryParams sqlParams) {
    return crmUserRoleLogic.searchCrmUserRoles(client, sqlParams);
  }

  @Transactional
  public CrmUserRole saveCrmUserRole(ClientContext client, CrmUserRole crmUserRole) {
    return crmUserRoleLogic.saveCrmUserRole(client, crmUserRole);
  }

  @Transactional
  public boolean deleteCrmUserByIds(ClientContext client, List<Long> ids) {
    return crmUserRoleLogic.deleteByIds(client, ids);
  }

}