package cloud.datatp.fforwarder.core.template;

import cloud.datatp.fforwarder.core.db.CRMDaoService;
import cloud.datatp.fforwarder.core.template.entity.CrmUserRole;
import cloud.datatp.fforwarder.core.template.repository.CrmUserRoleRepository;

import java.util.List;

import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.security.client.ClientContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class CRMUserRoleLogic extends CRMDaoService {

  @Autowired
  private CrmUserRoleRepository repo;

  public CrmUserRole getById(ClientContext client, Long id) {
    return repo.findById(id).get();
  }

  public CrmUserRole findByAccountId(ClientContext client, Long accountId) {
    return repo.findByAccountId(accountId);
  }

  public CrmUserRole getByBfsoneCode(ClientContext client, String bfsoneCode) {
    return repo.getByBfsoneCode(bfsoneCode);
  }

  public CrmUserRole getByBfsoneUsername(ClientContext client, String bfsoneUsername) {
    return repo.getByBFSOneUsername(bfsoneUsername);
  }

  public List<SqlMapRecord> searchCrmUserRoles(ClientContext client, SqlQueryParams sqlParams) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fforwarder/core/groovy/CrmUserRoleSql.groovy";
    return searchDbRecords(client, scriptDir, scriptFile, "SearchCrmUserRole", sqlParams);
  }

  public CrmUserRole saveCrmUserRole(ClientContext client, CrmUserRole customList) {
    customList.set(client);
    return repo.save(customList);
  }

  public boolean deleteByIds(ClientContext client, List<Long> ids) {
    repo.deleteAllById(ids);
    return true;
  }

}