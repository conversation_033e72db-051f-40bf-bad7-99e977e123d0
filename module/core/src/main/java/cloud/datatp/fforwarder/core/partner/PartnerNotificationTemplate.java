package cloud.datatp.fforwarder.core.partner;

import cloud.datatp.fforwarder.core.partner.entity.BFSOnePartner;
import java.text.SimpleDateFormat;
import java.util.Date;
import net.datatp.util.text.StringUtil;

public class PartnerNotificationTemplate {

  public static String buildPartnerCodeSyncZaloMessage(
      BFSOnePartner partner, 
      String oldPartnerCode, 
      String updatedBy,
      String successMessage,
      String errorMessage) {
        
    String currentDate = new SimpleDateFormat("dd/MM/yyyy HH:mm").format(new Date());
    StringBuilder message = new StringBuilder();

    // Start with error message if present
    if (StringUtil.isNotEmpty(errorMessage)) {
      message.append(String.format("""
          ❌ THÔNG BÁO LỖI: Cập nhật Mã Đối tác BFSOne thất bại (%s)
          ━━━━━━━━━━━━━━━━━━━━
          
          ⚠️ Lỗi: %s
          
          """, 
        currentDate,
        errorMessage
      ));
    } else {
      message.append(String.format("""
          ✅ THÔNG BÁO: Cậ<PERSON> nhật Mã Đối tác B<PERSON>One thành công (%s)
          ━━━━━━━━━━━━━━━━━━━━
          
          """, 
        currentDate
      ));
    }

    // Add partner details
    message.append(String.format("""
        🏢 Tên đối tác: %s
        🔢 Mã cũ: %s
        🔢 Mã mới: %s
        """,
      StringUtil.isNotEmpty(partner.getName()) ? partner.getName() : "N/A",
      StringUtil.isNotEmpty(oldPartnerCode) ? oldPartnerCode : "Chưa có mã",
      StringUtil.isNotEmpty(partner.getBfsonePartnerCode()) ? partner.getBfsonePartnerCode() : "N/A"
    ));

    // Add additional partner information if available
    if (StringUtil.isNotEmpty(partner.getTaxCode())) {
      message.append(String.format("""
            
            📋 Mã số thuế: %s
            """, partner.getTaxCode()));
    }
    // Add success message if present and no error
    if (StringUtil.isNotEmpty(successMessage) && StringUtil.isEmpty(errorMessage)) {
      message.append(String.format("""
            
            📝 Thông báo: %s
            """, successMessage));
    }

    message.append(String.format("""
        
        👤 Người thực hiện: %s
        📅 Thời gian: %s
        """,
      updatedBy,
      currentDate
    ));

    // Add system message based on success/error
    if (StringUtil.isNotEmpty(errorMessage)) {
      message.append("""
          
          ⚠️ Vui lòng kiểm tra lại thông tin và thử lại.
          """);
    } else {
      message.append("""
          
          ⚡️ Hệ thống đã cập nhật mã đối tác thành công.
          """);
    }

    message.append("\n━━━━━━━━━━━━━━━━━━━━");
    return message.toString();
  }

  public static String buildPartnerSyncZaloMessage(BFSOnePartner partner, String updatedBy, boolean isNewPartner) {
    String currentDate = new SimpleDateFormat("dd/MM/yyyy HH:mm").format(new Date());
    String action = isNewPartner ? "thêm mới" : "cập nhật";
    String emoji = isNewPartner ? "🆕" : "🔄";
    String actionPast = isNewPartner ? "tạo mới" : "cập nhật";
    
    StringBuilder message = new StringBuilder();

    message.append(String.format("""
        %s ĐỒNG BỘ %s ĐỐI TÁC BFSONE
        ━━━━━━━━━━━━━━━━━━━━
        
        📌 Hệ thống đã %s thông tin đối tác thành công
        
        🏢 Tên đối tác: %s
        🔢 Mã đối tác: %s
        """,
      emoji,
      action.toUpperCase(),
      actionPast,
      StringUtil.isNotEmpty(partner.getName()) ? partner.getName() : "N/A",
      StringUtil.isNotEmpty(partner.getBfsonePartnerCode()) ? partner.getBfsonePartnerCode() : "N/A"
    ));

    // Add additional partner information if available
    if (StringUtil.isNotEmpty(partner.getTaxCode())) {
      message.append(String.format("""
            
            📋 Mã số thuế: %s
            """, partner.getTaxCode()));
    }

    if (StringUtil.isNotEmpty(partner.getEmail())) {
      message.append(String.format("""
            
            📧 Email: %s
            """, partner.getEmail()));
    }

    message.append(String.format("""
        
        👤 Người thực hiện: %s
        📅 Thời gian: %s
        
        ⚡️ Hệ thống đã %s thông tin đối tác thành công.
        ━━━━━━━━━━━━━━━━━━━━
        """,
      updatedBy,
      currentDate,
      isNewPartner ? "tạo mới" : "cập nhật"
    ));

    return message.toString();
  }

}