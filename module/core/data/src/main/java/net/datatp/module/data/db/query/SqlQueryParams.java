package net.datatp.module.data.db.query;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.google.common.base.CaseFormat;
import java.sql.Timestamp;
import java.sql.Types;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import lombok.Getter;
import lombok.Setter;
import net.datatp.util.ds.Arrays;
import net.datatp.util.ds.Collections;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;
import net.datatp.util.text.DateUtil;
import net.datatp.util.text.StringUtil;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;

@JsonInclude(Include.NON_NULL)
public class SqlQueryParams {
  @Getter @Setter
  protected MapObject params;

  protected Map<String, SearchFilter>  filters;
  protected Map<String, OptionFilter>  optionFilters;
  protected Map<String, RangeFilter>   rangeFilters;

  @Getter @Setter
  protected OrderBy           orderBy;

  @Getter @Setter
  protected int               maxReturn = 100;

  public SqlQueryParams() { }

  public SqlQueryParams(String exp) {
    addSearchTerm("search", exp);
  }

  public SqlQueryParams(Map<String, SearchFilter> filters,
                        Map<String, OptionFilter> optionFilters,  
                        Map<String, RangeFilter>  rangeFilters,
                        OrderBy orderBy, int maxReturn) {
    if(filters != null) {
      for(SearchFilter sel : filters.values()) {
        add(sel.paramClone());
      }
    }
    if(optionFilters != null) {
      for(OptionFilter sel : optionFilters.values()) {
        add(sel);
      }
    }

    if(rangeFilters != null) {
      for(RangeFilter sel : rangeFilters.values()) {
        add(sel);
      }
    }
    this.orderBy   = orderBy;
    this.maxReturn = maxReturn;
  }

  public SqlQueryParams addSearchTerm(String name, String exp) {
    FILTER(new SearchFilter(name).value(exp));
    return this;
  }

  public boolean hasParam(String name) {
    if(params == null) return false;
    Object val = params.get(name);
    if(val == null) return false;
    if(val instanceof String) {
      return !StringUtil.isEmpty((String) val);
    }
    return true;
  }

  public Object getParam(String name) {
    if(params == null) return null;
    Object val = params.get(name);
    if(val instanceof String) {
      return val.toString();
    }
    return val;
  }

  public String[] getStringArray(String name) {
    if(params == null) return null;
    return params.getStringArray(name, new String[]{});
  }

  public List<String> getStringList(String name) {
    if(params == null) return new ArrayList<String>();
    return params.getStringList(name);
  }

  public String getString(String name) {
    return params.getString(name);
  }

  public Long getLong(String name) {
    return params.getLong(name, null);
  }

  public SqlQueryParams addParam(String name, Object value) {
    if(params == null) params = new MapObject();
    params.put(name, value);
    return this;
  }


  public SqlQueryParams FILTER(FieldFilter ... filter) {
    for(FieldFilter sel : filter) {
      if(sel instanceof RangeFilter) {
        add((RangeFilter)sel);
      } else if(sel instanceof OptionFilter) {
        add((OptionFilter)sel);
      }
    }
    return this;
  }

  public SqlQueryParams FILTER(SearchFilter  ... filter) {
    for(SearchFilter sel : filter) {
      add(sel);
    }
    return this;
  }


  public SqlQueryParams ORDERBY(String selectedField, String order) {
    this.orderBy = new OrderBy(null, selectedField, order);
    return this;
  }

  public SqlQueryParams ORDERBY(String[] fields, String selectedField, String order) {
    this.orderBy = new OrderBy(fields, selectedField, order);
    return this;
  }

  public List<SearchFilter> getFilters() {
    if(filters == null) return null;
    return new ArrayList<>(filters.values());
  }

  public void setFilters(List<SearchFilter> filters) {
    if(filters == null) {
      this.filters = null;
    } else {
      this.filters = new LinkedHashMap<>();
      for(SearchFilter sel : filters) {
        this.filters.put(sel.getName(), sel);
      }
    }
  }

  public List<OptionFilter> getOptionFilters() {
    if(optionFilters == null) return null;
    return new ArrayList<>(optionFilters.values());
  }

  public void setOptionFilters(List<OptionFilter> filters) {
    if(filters == null) {
      this.optionFilters = null;
    } else {
      this.optionFilters = new LinkedHashMap<>();
      for(OptionFilter sel : filters) {
        this.optionFilters.put(sel.getName(), sel);
      }
    }
  }

  public List<RangeFilter> getRangeFilters() {
    if(rangeFilters == null) return new ArrayList<>();
    return new ArrayList<>(rangeFilters.values());
  }

  public void setRangeFilters(List<RangeFilter> filters) {
    if(filters == null) {
      this.rangeFilters = null;
    } else {
      this.rangeFilters = new LinkedHashMap<>();
      for(RangeFilter sel : filters) {
        this.rangeFilters.put(sel.getName(), sel);
      }
    }
  }

  public SqlQueryParams add(SearchFilter filter) {
    if(filters == null) filters = new LinkedHashMap<>();
    filters.put(filter.getName(), filter);
    return this;
  }

  public SqlQueryParams add(OptionFilter filter) {
    if(optionFilters == null) optionFilters = new LinkedHashMap<>();
    optionFilters.put(filter.getName(), filter);
    return this;
  }

  public SqlQueryParams add(RangeFilter filter) {
    if(rangeFilters == null) rangeFilters = new LinkedHashMap<>();
    rangeFilters.put(filter.getName(), filter);
    return this;
  }

  public SearchFilter getFilter(String name) {
    if(this.filters == null) return null;
    return filters.get(name);
  }

  public OptionFilter getOptionFilter(String name) {
    if(this.optionFilters == null) return null;
    return optionFilters.get(name);
  }

  public boolean hasFilter(String name) {
    if(Objects.isNull(filters)) return false;
    return filters.containsKey(name);
  }

  public boolean hasRangerFilter(String name) {
    if(Objects.isNull(rangeFilters)) return false;
    return rangeFilters.containsKey(name);
  }

  public RangeFilter getRangerFilter(String name) {
    if(Objects.isNull(rangeFilters)) return null;
    return rangeFilters.get(name);
  }

  @JsonIgnore
  public SearchFilter getDefaultFilter() {
    return filters.get("search");
  }

  public MapSqlParameterSource toSqlParameter() {
    MapSqlParameterSource sqlParameter = new MapSqlParameterSource();

    if(getRangeFilters() != null) {
      for(RangeFilter rangeFilter: getRangeFilters()) {
        if (rangeFilter == null) continue;
        final String nameFilter = CaseFormat.LOWER_CAMEL.to(CaseFormat.LOWER_UNDERSCORE, rangeFilter.getName());
        final String fromValue = rangeFilter.getFromValue();

        Timestamp from = null;
        if (fromValue != null && !fromValue.isEmpty()) {
          Date fromDate = DateUtil.parseCompactDateTime(fromValue);
          Objects.assertNotNull(fromDate, "Invalid date format: " + fromValue);
          from = new Timestamp(fromDate.getTime());
        }
        sqlParameter.addValue(nameFilter + "_from", from, Types.TIMESTAMP);

        Timestamp to = null;
        final String toValue = rangeFilter.getToValue();
        if (toValue != null && !toValue.isEmpty()) {
          Date toDate = DateUtil.parseCompactDateTime(toValue);
          Objects.assertNotNull(toDate, "Invalid date format: " + toValue);
          to = new Timestamp(toDate.getTime());
        }
        sqlParameter.addValue(nameFilter + "_to", to, Types.TIMESTAMP);
      }
    }

    if(getOptionFilters() != null) {
      for(OptionFilter optionFilter: getOptionFilters()) {
        if (optionFilter == null) continue;
        final String name = CaseFormat.LOWER_CAMEL.to(CaseFormat.LOWER_UNDERSCORE, optionFilter.getName());

        if(optionFilter.isMultiple()) {
          final String[] selectOptions = optionFilter.getSelectOptions();
          List<String> optionList = Arrays.asList(selectOptions);
          if(Collections.isEmpty(optionList)) {
            sqlParameter.addValue(name, null);
          } else {
            sqlParameter.addValue(name, optionList);
          }
        } else {
          String selectOption = optionFilter.getSelectOption();
          if(selectOption.isEmpty()) selectOption = null;
          sqlParameter.addValue(name, selectOption, Types.VARCHAR);
        }
      }
    }

    final List<SearchFilter> searchFilters= getFilters();
    for (SearchFilter searchFilter : searchFilters) {
      final String name = searchFilter.getName();
      final String filterValue = searchFilter.getFilterValue().replace("*", "");
      if("search".equals(name)) {
        sqlParameter.addValue("org_filter_val", filterValue.trim());
        sqlParameter.addValue("filter_val", "%" + filterValue.trim() + "%");
        sqlParameter.addValue("filter_val_prefix", filterValue.trim() + "%");
        sqlParameter.addValue("filter_val_suffix", "%" + filterValue.trim());
      } else {
        String nameFormat = CaseFormat.LOWER_CAMEL.to(CaseFormat.LOWER_UNDERSCORE, name);
        sqlParameter.addValue(nameFormat + "_val", filterValue + "%");
      }
    }

    sqlParameter.addValue("max_return", getMaxReturn());
    sqlParameter.addValue("order_by", getOrderBy());

    if(params != null) {
      final Set<Entry<String, Object>> paramSet = params.entrySet();
      for(Entry<String, Object> entry: paramSet) {
        final String formatParam = CaseFormat.LOWER_CAMEL.to(CaseFormat.LOWER_UNDERSCORE, entry.getKey());
        final Object val = entry.getValue();
        if(val == null) {
          sqlParameter.addValue(formatParam, null);
          continue;
        }

        if(val instanceof Date date) {
          final Timestamp timestamp = new Timestamp(date.getTime());
          sqlParameter.addValue(formatParam, timestamp, Types.TIMESTAMP);
        } else if(val instanceof String) {
          final String s = val.toString();
          try {
            if(s.length() == DateUtil.LOCAL_DATETIME_FORMAT.length()) {
              final Date date = DateUtil.parseLocalDateTime(s);
              final Timestamp timestamp = new Timestamp(date.getTime());
              sqlParameter.addValue(formatParam, timestamp, Types.TIMESTAMP);
            } else if(s.length() == DateUtil.COMPACT_DATE_FORMAT.length()) {
              final Date date = DateUtil.parseCompactDate(s);
              final Timestamp timestamp = new Timestamp(date.getTime());
              sqlParameter.addValue(formatParam, timestamp, Types.TIMESTAMP);
            } else if(s.length() == DateUtil.COMPACT_DATETIME_FORMAT.length()){
              final Date date = DateUtil.parseCompactDateTime(s);
              final Timestamp timestamp = new Timestamp(date.getTime());
              sqlParameter.addValue(formatParam, timestamp, Types.TIMESTAMP);
            } else {
              sqlParameter.addValue(formatParam, s, Types.VARCHAR);
            }
          } catch (Exception e) {
            sqlParameter.addValue(formatParam, s, Types.VARCHAR);
          }
        } else {
          sqlParameter.addValue(formatParam, val);
        }
      }
    }
    return sqlParameter;
  }
  
  public MapObject toNamedQueryParams() {
    MapObject namedQueryParams = new MapObject();

    if(getRangeFilters() != null) {
      for(RangeFilter rangeFilter: getRangeFilters()) {
        namedQueryParams.set(rangeFilter.getName(), rangeFilter);
      }
    }

    if(getOptionFilters() != null) {
      for(OptionFilter optionFilter: getOptionFilters()) {
        if (optionFilter == null) continue;
        String name = optionFilter.getName();
        namedQueryParams.set(name, optionFilter);
        
        if("storageState".equals(name)) {
          name = name + "Dep";
          if(optionFilter.isMultiple()) {
            List<String> optionList = optionFilter.optionList();
            if(Collections.isEmpty(optionList)) {
              namedQueryParams.remove(name);
            } else {
              namedQueryParams.set(name, optionList);
            }
          } else {
            String selectOption = optionFilter.getSelectOption();
            if(selectOption.isEmpty()) selectOption = null;
            namedQueryParams.set(name, selectOption);
          }
        }
      }
    }

    final List<SearchFilter> searchFilters= getFilters();
    if(searchFilters != null) {
      for (SearchFilter searchFilter : searchFilters) {
        final String name = searchFilter.getName();
        String filterValue = searchFilter.getFilterValue();
        if(StringUtil.isEmpty(filterValue)) continue;

        namedQueryParams.set(name, searchFilter);
        if("search".equals(name)) {
          filterValue = filterValue.replace("*", "%");
          namedQueryParams.set("searchPattern", filterValue);
        }
      }
    }
    namedQueryParams.set("maxReturn", getMaxReturn());
    namedQueryParams.set("orderBy", getOrderBy());

    if(params != null) {
      final Set<Entry<String, Object>> paramSet = params.entrySet();
      for(Entry<String, Object> entry: paramSet) {
        final String formatParam =  entry.getKey();
        final Object val = entry.getValue();
        if(val == null) {
          namedQueryParams.remove(formatParam);
          continue;
        }

        if(val instanceof Date date) {
          final Timestamp timestamp = new Timestamp(date.getTime());
          namedQueryParams.set(formatParam, timestamp);
        } else if(val instanceof String) {
          final String s = val.toString();
          try {
            if(s.length() == DateUtil.LOCAL_DATETIME_FORMAT.length()) {
              final Date date = DateUtil.parseLocalDateTime(s);
              final Timestamp timestamp = new Timestamp(date.getTime());
              namedQueryParams.set(formatParam, timestamp);
            } else if(s.length() == DateUtil.COMPACT_DATE_FORMAT.length()) {
              final Date date = DateUtil.parseCompactDate(s);
              final Timestamp timestamp = new Timestamp(date.getTime());
              namedQueryParams.set(formatParam, timestamp);
            } else if(s.length() == DateUtil.COMPACT_DATETIME_FORMAT.length()){
              final Date date = DateUtil.parseCompactDateTime(s);
              final Timestamp timestamp = new Timestamp(date.getTime());
              namedQueryParams.set(formatParam, timestamp);
            } else {
              namedQueryParams.set(formatParam, s);
            }
          } catch (Exception e) {
            namedQueryParams.set(formatParam, s);
          }
        } else {
          namedQueryParams.set(formatParam, val);
        }
      }
    }
    return namedQueryParams;
  }

}