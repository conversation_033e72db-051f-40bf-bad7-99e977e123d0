package net.datatp.module.data.input.http;

import java.util.concurrent.Callable;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpSession;
import net.datatp.module.backend.BackendResponse;
import net.datatp.security.client.ClientContext;
import net.datatp.security.client.DeviceInfo;
import net.datatp.module.company.CompanyService;
import net.datatp.module.company.entity.Company;
import net.datatp.module.core.security.AuthorizedToken;
import net.datatp.module.core.security.SecurityService;
import net.datatp.module.core.security.entity.AccessToken;
import net.datatp.module.data.input.InputDataService;
import net.datatp.module.data.input.InputMethod;
import net.datatp.module.data.input.InputProcessStatus;
import net.datatp.module.data.input.models.InputData;
import net.datatp.module.data.input.models.InputDataProcessResult;
import net.datatp.module.http.rest.v1.BaseController;
import net.datatp.module.http.util.HttpServletRequestUtil;
import net.datatp.module.session.HttpClientSessionService;

/**
 * <NAME_EMAIL>
 **/

@ConditionalOnBean(HttpClientSessionService.class)
@Api(value = "datatp", tags = {"data"})
@RestController
@RequestMapping("/rest/v1.0.0/data/webhook")
public class WebhookController extends BaseController {
  @Autowired
  private InputDataService service;
  
  @Autowired
  private CompanyService companyService ;
  
  @Autowired
  protected SecurityService securityService;
  
  protected WebhookController() {
    super("data", "data/webhook");
  }
  /*
     curl -X POST http://localhost:7080/rest/v1.0.0/data/webhook \
          -H "Content-ContactType: application/json" \
          -H "DataTP-Authorization: HSWUMSQ2GZBRLMUU3IQ25ONH5OII5XKREGJOMUYS3GMCGDHQHBLWOK65XCQYFDAXVCC2QMIFXTYCVS35NLYZ46NDKRJOLZ4U4BQZNBSA42UA33M3K2476HIRZBRVS5LVEBRDYXZB4YHHW6VXGQIKF5TLGZ2NETRLLPQX54SJOSSJNLJTRUDOV5VHV76SIOTS5ZSVVXJ7HD6XG===" \
          -d '{"companyCode":"companyCode", "module":"data", "plugin":"helo", "records": []}'
  **/
  
  @ApiOperation(value = "Webhook", response = InputDataProcessResult.class)
  @RequestMapping(value="/",  method = {RequestMethod.POST, RequestMethod.PUT } )
  public BackendResponse webhook(HttpServletRequest httpReq, @RequestBody InputData webhookData) {
    Callable<InputDataProcessResult> executor = () -> {
     
      AuthorizedToken authorizedToken = getAuthorizedToken(httpReq);
      String token = null ;
      if(authorizedToken != null) {
        token = authorizedToken.getToken();
      }
      ClientContext client = new ClientContext("default", "anon", getRemoteIp(httpReq));
      DeviceInfo device = HttpServletRequestUtil.getDeviceInfo(httpReq);
      client.setDeviceInfo(device);
      AccessToken accessToken = securityService.validateAccessToken(client, token);
      client.setToken(accessToken.getToken());
      if(!accessToken.isAuthorized()) {
        InputDataProcessResult result = new InputDataProcessResult();
        result.setProcessStatus(InputProcessStatus.FAIL);
        result.setError("You are not authorized");
        return result;
      }
      HttpSession session = httpReq.getSession();
      client.setAccountId(accessToken.getAccountId());
      client.setLoginId(accessToken.getLoginId());
      client.setSessionId(session.getId());
      webhookData.setInputMethod(InputMethod.Webhook);
      Company company = companyService.getCompany(client, webhookData.getCompanyCode());
      client.setCompany(company);
      return service.runInputData(client, company, webhookData);
    };
    return execute(Method.POST, "data/webhook", executor);
  }
  
//  @ApiOperation(value = "Account ACL authenticate", response = ACLModel.class)
//  @GetMapping("token/{token}")
//  public @ResponseBody BackendResponse getAuthorization(HttpServletRequest httpReq, @PathVariable("token") String token) throws Exception {
//    Callable<AuthorizationInfo> executor = () -> {
//      HttpSession session = httpReq.getSession();
//      ClientContext client = new ClientContext("default", "system", getRemoteIp(httpReq));
//      client.setDeviceInfo(HttpServletRequestUtil.getDeviceInfo(httpReq));
//      client.setSessionId(session.getId());
//      AccessToken accessToken = securityService.validateAccessToken(client, token);
//      AuthorizationInfo authorization = authCipherTool.createAuthorization(accessToken);
//      return authorization;
//    };
//    return execute(Method.GET, "token/{token}", executor);
//  }

}