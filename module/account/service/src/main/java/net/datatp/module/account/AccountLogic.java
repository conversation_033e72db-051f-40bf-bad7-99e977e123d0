package net.datatp.module.account;

import java.util.*;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import net.datatp.module.account.entity.Account;
import net.datatp.module.account.entity.AccountGroup;
import net.datatp.module.account.entity.AccountMembership;
import net.datatp.module.account.entity.AccountType;
import net.datatp.module.account.entity.BaseProfile;
import net.datatp.module.account.entity.DynamicAccount;
import net.datatp.module.account.entity.LoginIdType;
import net.datatp.module.account.entity.OrgProfile;
import net.datatp.module.account.entity.UserProfile;
import net.datatp.module.account.plugin.AccountServicePlugin;
import net.datatp.module.account.repository.AccountGroupRepository;
import net.datatp.module.account.repository.AccountMembershipRepository;
import net.datatp.module.account.repository.AccountRepository;
import net.datatp.module.account.repository.OrgProfileRepository;
import net.datatp.module.account.repository.UserProfileRepository;
import net.datatp.security.client.ClientContext;
import net.datatp.module.common.Result;
import net.datatp.module.data.cache.CachingConfig;
import net.datatp.module.data.db.DAOService;
import net.datatp.module.data.db.JPAService;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.entity.StorageState;
import net.datatp.module.data.db.query.EntityTable;
import net.datatp.module.data.db.query.Join;
import net.datatp.module.data.db.query.OptionFilter;
import net.datatp.module.data.db.query.RangeFilter;
import net.datatp.module.data.db.query.SearchFilter;
import net.datatp.module.data.db.query.SqlQuery;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.resource.entity.OwnerType;
import net.datatp.module.resource.misc.BankAccountLogic;
import net.datatp.module.resource.misc.ContactLogic;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;
import net.datatp.util.error.ErrorType;
import net.datatp.util.error.RuntimeError;
import net.datatp.util.text.StringUtil;

@Slf4j
@Component
public class AccountLogic extends DAOService {
  @Getter
  @Autowired
  private AccountRepository accountRepo;

  @Autowired
  private ProfileLogic profileLogic;

  @Autowired
  private UserLogic userLogic;

  @Autowired @Getter
  private BankAccountLogic bankAccountLogic;

  @Autowired
  private UserProfileRepository userProfileRepo;

  @Autowired
  private OrgProfileRepository orgProfileRepo;

  @Autowired @Getter
  private AccountMembershipRepository membershipRepo;

  @Autowired
  private AccountGroupRepository accountGroupRepo;

  @Autowired @Getter
  private ContactLogic contactLogic;

  @Autowired
  private PasswordEncoder passwordEncoder;

  @Autowired
  private PasswordGenerator passwordGenerator;

  @Autowired
  private JPAService jpaService;

  @Autowired
  private ILoginIdAnalyzer loginIdAnalyzer;

  @Autowired(required = false)
  List<AccountServicePlugin> plugins = new ArrayList<>();

  public Account authenticate(ClientContext client, String pattern, String password) {
    Account account = getEditable(client, pattern);
    if (account != null) {
      if (passwordEncoder.matches(password, account.getPassword())) {
        jpaService.getEntityManager().detach(account);
        account.setPassword(null);
        account.setModifiable(false);
        return account;
      }
    }
    log.error("User {} try to login into system, but fail", pattern);
    return null;
  }

  public Account getActiveAccountByLoginId(ClientContext client, String loginId) {
    return accountRepo.getActiveByLoginId(loginId);
  }
  
  public Account getAccountByLoginId(ClientContext client, String loginId) {
    return accountRepo.getByLoginId(loginId);
  }
  
  public Account getAccountByEmail(ClientContext client, String email) {
    return accountRepo.getByEmailAllStorageState(email);
  }

  public Account getAccountByMobile(ClientContext client, String mobile) {
    return accountRepo.getByMobile(mobile);
  }

  public Account getAccountById(ClientContext client, Long accountId) {
    return accountRepo.findById(accountId).get();
  }

  public Account getReadOnlyById(ClientContext client, Long accountId) {
    Account account = accountRepo.findById(accountId).get();
    jpaService.getEntityManager().detach(account);
    account.setPassword(null);
    account.setModifiable(false);
    return account;
  }

  public Account getReadOnly(ClientContext client, String loginIdPattern) {
    Account account = getEditable(client, loginIdPattern);
    if (account != null) {
      jpaService.getEntityManager().detach(account);
      account.setPassword(null);
      account.setModifiable(false);
    }
    return account;
  }

  public Account getEditable(ClientContext client, String pattern) {
    LoginIdType loginType = loginIdAnalyzer.getLoginIdType(pattern);
    if (loginType == LoginIdType.LegacyCompanyLoginId) {
      throw RuntimeError.IllegalArgument("Login id {} is company login id type, require the company code", pattern);
    }
    String patternNormalized = pattern.toLowerCase(Locale.ROOT);
    if (loginType == LoginIdType.LegacyLoginId) {
      return accountRepo.getByLegacyLoginId(patternNormalized);
    }
    if (loginType == LoginIdType.Email) {
      return accountRepo.getByEmail(patternNormalized);
    }
    if (loginType == LoginIdType.Mobile) {
      return accountRepo.getByMobile(patternNormalized);
    }
    return accountRepo.getActiveByLoginId(patternNormalized);
  }

  public List<Account> findAccountByLoginIdOrMobileOrEmail(ClientContext client, String loginId, String mobile, String email) {
    return accountRepo.findByLoginIdOrMobileOrEmail(loginId, mobile, email);
  }

  public Account saveAccount(ClientContext client, Account account) {
    return accountRepo.save(client, account);
  }

  public Account syncWithProfile(ClientContext client, BaseProfile profile) {
    Account account = getEditable(client, profile.getLoginId());
    account.setEmail(profile.getEmail());
    account.setMobile(profile.getMobile());
    account.setFullName(profile.getFullName());
    return accountRepo.save(client, account);
  }

  @CacheEvict(value = CachingConfig.REGION_ENTITY, key = "{'" + Account.TABLE_NAME + "', #account.loginId}")
  public Account updateAccount(ClientContext client, Account account) {
    if (!account.isModifiable()) {
      throw new RuntimeError(ErrorType.IllegalArgument, "Account is not modifiable");
    }
    final String loginId = account.getLoginId().toLowerCase(Locale.ROOT);

    account.setPassword(passwordEncoder.encode(account.getPassword()));

    //TODO[Tuan] need review and remove preSave and postSave
    for (AccountServicePlugin plugin : plugins) {
      plugin.onPreSave(client, account, false);
    }

    if (account.getAccountType() == AccountType.USER) {
      UserProfile profile = userProfileRepo.getByLoginId(loginId);
      profile.setEmail(account.getEmail());
      profile.setMobile(account.getMobile());
      profile.setFullName(account.getFullName());
      userProfileRepo.save(client, profile);
    } else {
      OrgProfile profile = orgProfileRepo.getByLoginId(loginId);
      profile.setEmail(account.getEmail());
      profile.setMobile(account.getMobile());
      profile.setFullName(account.getFullName());
      orgProfileRepo.save(client, profile);
    }
    account = accountRepo.save(client, account);

    //TODO[Tuan] need review and remove preSave and postSave
    for (AccountServicePlugin plugin : plugins) {
      plugin.onPostSave(client, account, false);
    }
    return account;
  }

  public List<DynamicAccount> bulkSave(ClientContext client, List<DynamicAccount> dAccounts) {

    for (DynamicAccount dAccount : dAccounts) {
      if (dAccount.getEditState().equals("NEW")) {
      } else {
        Account account = accountRepo.getById(dAccount.id());
        if (dAccount.getEditState().equals("DELETED")) {
        } else {
          dAccount.update(account);
        }
      }
    }

    return dAccounts;
  }

  public NewAccountModel createNewAccount(ClientContext client, NewAccountModel model) {
    Account account = model.getAccount();
    boolean isNew = account.isNew();

    for (AccountServicePlugin plugin : plugins) {
      plugin.onPreSave(client, account, isNew);
    }

    if (Objects.isNull(account.getPassword())) account.setPassword(account.getLoginId());
    account.setPassword(passwordEncoder.encode(account.getPassword()));
    account = accountRepo.save(client, account);

    if (Objects.nonNull(model.getAccountGroupIds())) {
      for (Long groupPathId : model.getAccountGroupIds()) {
        AccountGroup accountGroup = accountGroupRepo.findById(groupPathId).get();
        createMembership(client, accountGroup, account.getLoginId());
      }
    }

    NewAccountModel retModel = new NewAccountModel();
    retModel.withAccount(account);
    String fullName = account.getFullName();
    if (fullName == null) {
      fullName = account.getLoginId();
    } else {
      fullName = fullName.trim();
      if (StringUtil.isEmpty(fullName)) fullName = account.getLoginId();
    }

    if (account.getAccountType() == AccountType.USER) {
      UserProfile profile = model.getUserProfile();
      if (Objects.isNull(profile)) {
        profile = new UserProfile(account);
        profile.setMobile(account.getMobile());
      }
      profile.setAccountId(account.getId());
      profile.set(client);
      profile = userProfileRepo.save(profile);
      retModel.withUserProfile(profile);
    } else {
      OrgProfile profile = model.getOrgProfile();
      if (Objects.isNull(profile)) {
        profile = new OrgProfile(account);
        profile.setMobile(account.getMobile());
      }
      profile.setAccountId(account.getId());
      profile.set(client);
      profile = orgProfileRepo.save(profile);
      retModel.withOrgProfile(profile);
    }
    for (AccountServicePlugin plugin : plugins) {
      plugin.onPostSave(client, account, isNew);
    }

    return retModel;
  }

  public Account mergeAccount(ClientContext client, String srcLoginId, List<String> targetLoginIds) {
    Account srcAcc = getEditable(client, srcLoginId);
    for (String targetLoginId : targetLoginIds) {
      Account targetAcc = getEditable(client, targetLoginId);
      srcAcc.merge(targetAcc);
      profileLogic.mergeProfile(client, srcAcc, targetAcc);
      contactLogic.mergeContact(client, OwnerType.Account, srcAcc.getId(), targetAcc.getId());
      bankAccountLogic.mergeBackAccount(client, OwnerType.Account, srcAcc.getId(), targetAcc.getId());
      userLogic.mergeUserRelation(client, srcAcc, targetAcc);
      userLogic.mergeUserWork(client, srcAcc, targetAcc);
      userLogic.mergeUserIdentity(client, srcAcc, targetAcc);
      userLogic.mergeUserEducation(client, srcAcc, targetAcc);
      targetAcc.setMoveToLoginId(srcAcc.getLoginId());
      targetAcc.setStorageState(StorageState.DEPRECATED);
      updateAccount(client, targetAcc);
    }
    srcAcc = updateAccount(client, srcAcc);
    return srcAcc;
  }

  @CacheEvict(value = CachingConfig.REGION_ENTITY, key = "{'" + Account.TABLE_NAME + "', #request.loginId}")
  public Result<Boolean> changePassword(ClientContext client, ChangePasswordRequest request) {
    Account account = getEditable(client, request.getLoginId());
    if (account != null) {
      if (passwordEncoder.matches(request.getOldPassword(), account.getPassword())) {
        account.setPassword(passwordEncoder.encode(request.getNewPassword()));
        account.set(client);
        account = accountRepo.save(account);
        return new Result<>(Result.Status.Success, true).withMessage("Change password successfully!");
      }
      return new Result<>(Result.Status.Fail, true).withMessage("The old password is not matched");
    }
    return new Result<>(Result.Status.Fail, false).withMessage("Cannot find the account");
  }

  @CacheEvict(value = CachingConfig.REGION_ENTITY, key = "{'" + Account.TABLE_NAME + "', #loginId}")
  public void changePassword(ClientContext client, String loginId, String newPassword) {
    Account account = getEditable(client, loginId);
    if (account == null) {
      RuntimeError.IllegalArgument("Cannot find the account {}", loginId);
    }
    account.setPassword(passwordEncoder.encode(newPassword));
    account.set(client);
    account = accountRepo.save(account);
  }

  @CacheEvict(value = CachingConfig.REGION_ENTITY, key = "{'" + Account.TABLE_NAME + "', #request.loginId}")
  public Result<MapObject> resetPassword(ClientContext client, ResetPasswordRequest request) {
    String email = request.getEmail();
    MapObject result = new MapObject();
    result.put("email", email);
    if(StringUtil.isEmpty(email)) {
      return new Result<>(Result.Status.Fail, result).withMessage("Email is required!!!");
    }
    Account account = accountRepo.getByEmail(email);
    if(account == null) {
      return new Result<>(Result.Status.Fail, result).withMessage("Cannot find the account with email = " + email + "!!!");
    }
    if (!email.equalsIgnoreCase(account.getEmail())) {
      return new Result<>(Result.Status.Fail, result).withMessage("Cannot find email: " + email + " for account: " + email);
    }
    String newPassword = passwordGenerator.generatePassword(8);
    account.setPassword(passwordEncoder.encode(newPassword));
    account.set(client);
    accountRepo.save(account);
    String loginIdLower = account.getLoginId().toLowerCase(Locale.ROOT);
    result.put("username", loginIdLower);
    result.put("newPassword", newPassword);
    String msg = "The Username: " + account.getLoginId().toUpperCase(Locale.ROOT) + " A new password is generated successfully. The new password is " + newPassword;
    return new Result<>(Result.Status.Success, result).withMessage(msg);
  }

  public List<AccountMembership> getAccountMembershipByLoginId(String loginId) {
    return membershipRepo.findByLoginId(loginId);
  }

  public AccountModel loadAccountModel(ClientContext client, String loginId) {
    //TODO: clear password
    Account account = accountRepo.getActiveByLoginId(loginId);
    if (account == null) return null;

    account.setPassword(null);
    AccountModel model = new AccountModel(account);

    if (account.getAccountType() == AccountType.USER) {
      UserProfile uProfile = userProfileRepo.getByLoginId(loginId);
      if (uProfile == null) uProfile = new UserProfile(loginId);
      model.setUserProfile(uProfile);
    } else {
      OrgProfile orgProfile = orgProfileRepo.findById(account.getId()).get();
      if (orgProfile == null) orgProfile = new OrgProfile(account);
      model.setOrgProfile(orgProfile);
    }
    return model;
  }

  public AccountMembership createMembership(ClientContext client, AccountGroup group, String loginId) {
    AccountMembership m = membershipRepo.getMembershipByGroupIdAndLoginId(group.getId(), loginId);
    if (Objects.nonNull(m)) return null;

    m = new AccountMembership(loginId, group.getId());
    return membershipRepo.save(client, m);
  }

  public boolean changeStorageState(ClientContext client, ChangeStorageStateRequest req) {
    List<Account> accounts = accountRepo.findAccounts(req.getEntityIds());
    for (Account account : accounts) {
      changeStorageState(client, account, req.getNewStorageState());
    }
    return true;
  }

  public List<Account> findByAccountIds(ClientContext clientContext, List<Long> ids) {
    return accountRepo.findAccounts(ids);
  }

  public Map<Long, Account> findMapAccountByIds(ClientContext clientContext, List<Long> ids) {
    Map<Long, Account> accountMap = new HashMap<>();
    List<Account> accounts = accountRepo.findAccounts(ids);
    for(Account account: accounts) {
      accountMap.put(account.getId(), account);
    }
    return accountMap;
  }



  public boolean changeStorageState(ClientContext client, Account account, StorageState state) {
    final String LOGIN_ID = account.getLoginId();
    plugins.forEach(plugin -> {
      plugin.onPreStateChange(client, account, state);
    });
    contactLogic.getContactRepo().setStorageState(OwnerType.Account, account.getId(), state);
    membershipRepo.setStorageState(LOGIN_ID, state);

    if (account.getAccountType() == AccountType.USER) {
      userProfileRepo.setStorageState(LOGIN_ID, state);
    } else {
      orgProfileRepo.setStorageState(LOGIN_ID, state);
    }
    accountRepo.setStorageState(LOGIN_ID, state);
    plugins.forEach(plugin -> {
      plugin.onPostStateChange(client, account, state);
    });
    return true;
  }

  public List<SqlMapRecord> searchDeactivateEmployeeAccounts(ClientContext client, SqlQueryParams sqlParams) {
    String scriptDir = appEnv.addonPath("core", "groovy");
    String scriptFile = "net/datatp/module/account/groovy/AccountSql.groovy";
    return searchDbRecords(client, scriptDir, scriptFile, "SearchDeactivateEmployeeAccounts", sqlParams);
  }

  public List<Account> searchAccounts(ClientContext client, SqlQueryParams params) {
    //TODO: manually select field to exclude the password field
    String[] SEARCH_FIELDS = new String[]{"loginId", "email", "fullName", "mobile"};
    SqlQuery query =
      new SqlQuery()
        .ADD_TABLE(new EntityTable(Account.class).selectAllFields().rmSelectField("password"))
        .FILTER(
          SearchFilter.isearch(Account.class, SEARCH_FIELDS))
        .FILTER(
          OptionFilter.storageState(Account.class),
          OptionFilter.create(Account.class, "accountType", AccountType.ALL),
          RangeFilter.date(Account.class, "lastLoginTime"),
          RangeFilter.createdTime(Account.class),
          RangeFilter.modifiedTime(Account.class))
        .ORDERBY(new String[] {"loginId", "email", "fullName", "mobile", "modifiedTime"}, "loginId", "DESC");
    if(params.hasParam("groupId")) {
      query
        .JOIN(new Join("JOIN", AccountMembership.class).ON("loginId", Account.class, "loginId"))
        .JOIN(
          new Join("JOIN", AccountGroup.class)
            .ON("id", AccountMembership.class, "groupId")
            .AND("id", "=", ":groupId"));
    }
    return query(client, query, params, Account.class);
  }

  public boolean deleteAccountById(ClientContext client, Long accountId) {
    final Account account = getAccountById(client, accountId);
    String loginId = account.getLoginId();
    Objects.assertNotNull(account, "Account not exist in db! with ID = {}", accountId);
    if(AccountType.USER.equals(account.getAccountType())) {
      userProfileRepo.deleteByLoginId(loginId);
    } else {
      orgProfileRepo.deleteByLoginId(loginId);
    }
    bankAccountLogic.deleteBankAccounts(client, OwnerType.Account, account.getId());
    userLogic.deleteUserEducations(client, account);
    userLogic.deleteUserWorks(client, account);
    userLogic.deleteUserIdentities(client, account);
    userLogic.deleteUserRelations(client, account);
    contactLogic.getContactRepo().deleteByOwner(OwnerType.Account, accountId);

    membershipRepo.deleteByLoginId(loginId);
    accountRepo.delete(account);
    return true;
  }


  // Account Group
}