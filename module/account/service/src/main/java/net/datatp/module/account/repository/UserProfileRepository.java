package net.datatp.module.account.repository;

import java.io.Serializable;
import java.util.List;

import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import net.datatp.module.account.entity.UserProfile;
import net.datatp.module.data.db.entity.StorageState;
import net.datatp.module.data.db.repository.DataTPRepository;

public interface UserProfileRepository extends DataTPRepository<UserProfile, Serializable> {

  @Query("SELECT a FROM UserProfile a WHERE lower(a.loginId) = lower(:loginId) AND a.storageState = 'ACTIVE'")
  public UserProfile getByLoginId(@Param("loginId") String loginId);

  @Query("SELECT a FROM UserProfile a WHERE a.accountId IN :accountIds")
  public List<UserProfile> findByAccountIds(@Param("accountIds") List<Long> accountIds);

  @Query("SELECT a FROM UserProfile a WHERE a.loginId IN :loginIds")
  public List<UserProfile> findByLoginIds(@Param("loginIds") List<String> loginIds);
  
  @Modifying
  @Query("update UserProfile a SET a.storageState = :state WHERE a.loginId = :loginId")
  int setStorageState(@Param("loginId") String loginId, @Param("state") StorageState state);
  
  @Modifying
  @Query("Delete FROM UserProfile r where r.loginId = :loginId")
  int deleteByLoginId(@Param("loginId") String loginId);
}