package net.datatp.module.account;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import lombok.Getter;
import net.datatp.module.account.entity.Account;
import net.datatp.module.account.entity.AccountType;
import net.datatp.module.account.entity.BaseProfile;
import net.datatp.module.account.entity.LoginIdType;
import net.datatp.module.account.entity.OrgProfile;
import net.datatp.module.account.entity.UserProfile;
import net.datatp.module.account.repository.AccountRepository;
import net.datatp.module.account.repository.OrgProfileRepository;
import net.datatp.module.account.repository.UserProfileRepository;
import net.datatp.security.client.ClientContext;
import net.datatp.module.data.db.DAOService;
import net.datatp.module.http.upload.UploadResource;
import net.datatp.module.http.upload.UploadService;
import net.datatp.module.storage.IStorageService;
import net.datatp.module.storage.StorageResource;
import net.datatp.module.storage.UserStorage;
import net.datatp.util.ds.Objects;
import net.datatp.util.error.RuntimeError;
import net.datatp.util.image.PNGUtil;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class ProfileLogic extends DAOService {
  final static String PROFILE_PREFIX_USER = "account_user_profile" ;
  
  final static String PROFILE_PREFIX_ORG = "account_org_profile" ;
 
  @Getter
  @Autowired
  private AccountRepository           accountRepo;
  
  @Autowired @Getter
  private UserProfileRepository userProfileRepo;
  
  @Autowired @Getter
  private OrgProfileRepository orgProfileRepo;

  
  @Autowired
  private UploadService uploadService;
  
  @Autowired
  private IStorageService storageService;
  
  @Autowired
  private ILoginIdAnalyzer loginIdAnalyzer;
  
  @SuppressWarnings("unchecked")
  public <T extends BaseProfile> T getProfile(ClientContext clientCtx, String loginId) {
    Account account = getModifiableAccountByPattern(clientCtx, loginId);
    if(account == null) return null;
    if(account.getAccountType() == AccountType.USER) {
      return (T) getUserProfile(clientCtx, loginId);
    }
    return (T) getOrgProfile(clientCtx, loginId);
  }
  
  public void mergeProfile(ClientContext client, Account srcAcc, Account targetAcc) {
    if(AccountType.USER.equals(srcAcc.getAccountType()) && AccountType.USER.equals(targetAcc.getAccountType())) {
      UserProfile srcProfile = userProfileRepo.getByLoginId(srcAcc.getLoginId());
      UserProfile targetProfile = userProfileRepo.getByLoginId(targetAcc.getLoginId());
      srcProfile.merge(targetProfile);
      userProfileRepo.save(client, srcProfile);
    } else if(AccountType.ORGANIZATION.equals(srcAcc.getAccountType()) && AccountType.ORGANIZATION.equals(targetAcc.getAccountType())) {
      OrgProfile srcProfile = orgProfileRepo.getByLoginId(srcAcc.getLoginId());
      OrgProfile targetProfile = orgProfileRepo.getByLoginId(targetAcc.getLoginId());
      srcProfile.merge(targetProfile);
      orgProfileRepo.save(client, srcProfile);
    }
  }

  public UserProfile getUserProfile(ClientContext clientCtx, Long id) {
    return userProfileRepo.findById(id).get();
  }

  public List<UserProfile> findUserProfileByAccountIds(ClientContext clientCtx, List<Long> accountIds) {
    return userProfileRepo.findByAccountIds(accountIds);
  }

  public Map<Long, UserProfile> findMapUserProfileByAccountIds(ClientContext client, List<Long> accountIds) {
    List<UserProfile> userProfiles = this.findUserProfileByAccountIds(client, accountIds);
    Map<Long, UserProfile> map = new HashMap<>();
    for(UserProfile userProfile: userProfiles) {
      map.put(userProfile.getAccountId(), userProfile);
    }
    return map;
  }
  
  public UserProfile getUserProfile(ClientContext clientCtx, String loginId) {
    return userProfileRepo.getByLoginId(loginId);
  }
  
  public OrgProfile getOrgProfile(ClientContext clientCtx, Long id) {
    return orgProfileRepo.findById(id).get();
  }
  
  public OrgProfile getOrgProfile(ClientContext clientCtx, String loginId) {
    return orgProfileRepo.getByLoginId(loginId);
  }
  
  //@CacheEvict(value = CachingConfig.REGION_ENTITY , key= "{'" + PROFILE_PREFIX_USER + "', #profile.loginId}")
  public UserProfile saveUserProfile(ClientContext client, UserProfile profile) {
    Account account = getModifiableAccountById(client, profile.getAccountId());
    account
    .withEmail(profile.getEmail())
    .withMobile(profile.getMobile())
    .withFullName(profile.getFullName());
    
    accountRepo.save(client, account);
    profile = userProfileRepo.save(client, profile);
    return profile;
  }

  //@CacheEvict(value = CachingConfig.REGION_ENTITY , key= "{'" + PROFILE_PREFIX_ORG + "', #profile.loginId}")
  public OrgProfile saveOrgProfile(ClientContext client, OrgProfile profile) {
    Account account = getModifiableAccountById(client, profile.getAccountId());
    account
    .withEmail(profile.getEmail())
    .withMobile(profile.getMobile())
    .withFullName(profile.getFullName());
    profile = orgProfileRepo.save(client, profile);
    return profile;
  }
  
  public boolean deleteUserProfile(ClientContext client, String loginId) {
    return userProfileRepo.deleteByLoginId(loginId) == 1;
  }
  
  public boolean deleteOrgProfile(ClientContext client, String loginId) {
    return orgProfileRepo.deleteByLoginId(loginId) == 1;
  }

  public UploadResource uploadAvatar(ClientContext client, String loginId, UploadResource resource, boolean saveOrigin) {
    Account account = getModifiableAccountByPattern(client, loginId);
    if(account == null) {
      Objects.assertNotNull(account, "Cannot find the account for {}", loginId);
    }
    byte[] imgData = uploadService.load(resource.getStoreId());
    byte[] pngImgData = PNGUtil.toPng(imgData);
    UserStorage storage = storageService.createUserStorage(client, loginId);
    if(saveOrigin) {
      StorageResource origAvatarResource = new StorageResource("orig-avatar.png", pngImgData);
      origAvatarResource = storage.wwwSave("avatar", origAvatarResource);
    }
    StorageResource avatarResource = new StorageResource("avatar.png", pngImgData);
    avatarResource = storage.wwwSave("avatar", avatarResource);

    if(account.getAccountType() == AccountType.USER) {
      UserProfile profile = getUserProfile(client, loginId) ;
      profile.setAvatarUrl(avatarResource.getPublicDownloadUri());
      saveUserProfile(client, profile);
    } else {
      OrgProfile profile = getOrgProfile(client, loginId) ;
      profile.setAvatarUrl(avatarResource.getPublicDownloadUri());
      saveOrgProfile(client, profile);
    }
    return resource;
  }

  
  public Account getModifiableAccountById(ClientContext client, Long id) {
    return accountRepo.findById(id).get();
  }
  
  public Account getModifiableAccountByPattern(ClientContext client, String pattern) {
    LoginIdType loginType = loginIdAnalyzer.getLoginIdType(pattern);
    if(loginType == LoginIdType.LegacyCompanyLoginId) {
      throw RuntimeError.IllegalArgument("Login id {} is company login id type, require the company code",  pattern);
    }
    if(loginType == LoginIdType.LegacyLoginId) {
      return accountRepo.getByLegacyLoginId(pattern);
    }
    if(loginType == LoginIdType.Email) {
      return accountRepo.getByEmail(pattern);
    }
    if(loginType == LoginIdType.Mobile) {
      return accountRepo.getByMobile(pattern);
    }
    return accountRepo.getActiveByLoginId(pattern);
  }

}