package cloud.datatp.tms.ops;

import java.util.*;

import net.datatp.module.account.ProfileLogic;
import net.datatp.module.account.entity.UserProfile;
import net.datatp.module.resource.location.entity.Location;
import org.apache.commons.collections4.map.HashedMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.stereotype.Component;

import cloud.datatp.fleet.vehicle.VehicleTripGoodsTrackingLogic;
import cloud.datatp.fleet.vehicle.entity.VehicleTripGoodsTracking;
import cloud.datatp.tms.TMSGeneralLogic;
import cloud.datatp.tms.bill.entity.TMSBillForwarderTransport.TransportationMode;
import cloud.datatp.tms.ops.entity.TMSOperations;
import cloud.datatp.tms.ops.entity.TMSOperations.TMSOperationsStatus;
import cloud.datatp.tms.ops.entity.TMSOpsAttachment;
import cloud.datatp.tms.ops.repository.TMSOperationsRepository;
import cloud.datatp.tms.ops.repository.TMSOpsAttachmentRepository;
import groovy.lang.Binding;
import jakarta.annotation.PostConstruct;
import lombok.Getter;
import net.datatp.module.account.AccountLogic;
import net.datatp.module.account.entity.Account;
import net.datatp.security.client.ClientContext;
import net.datatp.module.company.entity.Company;
import net.datatp.module.data.db.DAOService;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.SqlQueryManager;
import net.datatp.module.data.db.SqlSelectView;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.entity.EditState;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.entity.StorageState;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.data.db.seq.SeqService;
import net.datatp.module.storage.CompanyStorage;
import net.datatp.module.storage.IStorageService;
import net.datatp.util.ds.Collections;
import net.datatp.util.ds.MapObject;
import net.datatp.util.text.DateUtil;
import net.datatp.util.text.StringUtil;

@Component
public class TMSOperationsLogic extends DAOService {

  @Getter
  @Autowired
  private TMSOperationsRepository repository;

  @Autowired
  private TMSOpsAttachmentRepository attachmentRepository;

  @Autowired
  private SeqService seqService;

  @Autowired
  private AccountLogic accountLogic;
  
  @Autowired
  private TMSGeneralLogic             tmsGeneralLogic;
  
  @Autowired
  private VehicleTripGoodsTrackingLogic goodsTrackingLogic;

  @Autowired
  private IStorageService storageService;

  @Autowired
  private ProfileLogic profileLogic;

  private String QUERY_SCRIPT_DIR;


  @PostConstruct
  public void onInit() {
    seqService.createIfNotExists(TMSOperations.SEQUENCE, 10);
    if ("test".equals(env)) return;
    QUERY_SCRIPT_DIR = appEnv.addonPath("logistics", "groovy/lgc/tms");
  }
  
  public TMSOperations getOperationsByTMSBillId(ClientContext client , ICompany company, Long tmsBillId) {
    return repository.getOperationsByTMSBillId(company.getId(), tmsBillId);
  }
  
  public TMSOperations getOperationsById(ClientContext client , ICompany company, Long id) {
    return repository.getOperations(company.getId(), id);
  }
  
  public List<SqlMapRecord> findTMSOperationsByBillId(ClientContext client, ICompany company, Object tmsBillId) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/tms/groovy/TMSSql.groovy";

    SqlQueryParams params = new SqlQueryParams();
    params.addSearchTerm("search", null);
    params.addParam("companyId", company.getId());
    params.addParam("tmsBillId", tmsBillId);
    return searchDbRecords(client, scriptDir, scriptFile, "FindTMSOperationsByBillId", params);
  }

  public List<SqlMapRecord> searchOperations(ClientContext client , ICompany company, SqlQueryParams sqlParams) {
    Account account = accountLogic.getEditable(client, client.getRemoteUser());
    sqlParams.addParam("companyId", company.getId());
    sqlParams.addParam("accountId", account.getId());
    String scriptName = "TMSOperationsQuery.groovy" ;
    SqlQueryManager.QueryContext queryContext = sqlQueryManager.create(QUERY_SCRIPT_DIR, scriptName);
    MapSqlParameterSource params = sqlParams.toSqlParameter();
    Binding binding = new Binding();
    binding.setVariable("sqlparams", sqlParams);
    SqlSelectView view = queryContext.createSqlSelectView(binding, params);
    List<SqlMapRecord> records = view.renameColumWithJavaConvention().getSqlMapRecords();
    Set<Long> responsibleAccountIds = new HashSet<>();
    for(SqlMapRecord record: records) {
      responsibleAccountIds.add(record.getLong("responsibleAccountId"));
    }
    Map<Long, UserProfile> userProfileAccountIdMap = profileLogic.findMapUserProfileByAccountIds(client, new ArrayList<>(responsibleAccountIds));

    for(SqlMapRecord record: records) {
      UserProfile userProfile = userProfileAccountIdMap.get(record.getLong("responsibleAccountId"));
      if (userProfile != null) {
        if (StringUtil.isNotBlank(userProfile.getNickname())) {
          record.add("userName", userProfile.getNickname());
        } else {
          record.add("userName", userProfile.getFullName());
        }
      }
    }
    return records;
  }
  
  public List<SqlMapRecord> searchOperationsRecentlyChange(ClientContext client , ICompany company, SqlQueryParams sqlParams) {
    sqlParams.addParam("companyId", company.getId());
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/tms/ops/groovy/TMSOperationsRecentlyChangeSql.groovy";
    return searchDbRecords(client, scriptDir, scriptFile, "SearchOperationsRecentlyChange", sqlParams);
  }

  public boolean deleteOperations( ClientContext client , ICompany company, List<Long> ids) {
      repository.deleteAllById(ids);
      return true;
  }

  public List<TMSOperations> saveOperationsList(ClientContext client , ICompany company, List<TMSOperations> operations) {
    List<TMSOperations> result = new ArrayList<>();
    for(TMSOperations operation : operations) {
      final String uiKey = operation.getUikey();
      if(operation.isNew()) {
        operation.set(client, company);
        operation.genCode(seqService.nextSequence(TMSOperations.SEQUENCE));
        Account account = accountLogic.getEditable(client, client.getRemoteUser());
        operation.setResponsibleFullName(account.getFullName());
        operation.setResponsibleAccountId(account.getId());
        operation = saveOperations(client, company, operation);
        operation.setUikey(uiKey);
        result.add(operation);
        continue;
      }
      TMSOperations operationsResult = repository.getOperations(company.getId() , operation.getId());
      if(operationsResult != null){
        operation.mergeOPS(operationsResult);
        if(operation.getEditState() == EditState.DELETED ) {
          operationsResult.setStorageState(StorageState.ARCHIVED);
          if(operationsResult.getTmsBillId() != null) {
            operationsResult.setLabel(operation.getLabel());
            operationsResult.setTmsBillId(null);
          }
        }
        operation.set(client, company);
        operationsResult = saveOperations(client, company, operationsResult);
        result.add(operationsResult);
      }
    }
    return result;
  }
  
  public TMSOperations saveOperations(ClientContext client , ICompany company, TMSOperations operations) {
    operations.set(client, company);
    return repository.save(operations);
  }

  public boolean updateStatusOperations(ClientContext client , ICompany company, Long id, TMSOperations.TMSOperationsStatus status) {
    TMSOperations ops = getOperationsById(client, company, id);
    if(ops != null && ops.getHandlerAccountId() == null) {
      Account account  = accountLogic.getEditable(client, client.getRemoteUser());
      ops.setHandlerAccountId(account.getId());
      ops.setHandlerFullName(account.getFullName());
      ops.setStatus(status);
      ops = saveOperations(client, company, ops);
      return true;
    }
    repository.updateStatus(id, status);
    return true;
  }

  public boolean changeStorageState(ClientContext client, ICompany company, ChangeStorageStateRequest req) {
    repository.setStorageState(req.getNewStorageState(), req.getEntityIds());
    return true;
  }

  public List<TMSOpsAttachment> findOpsAttachments(ClientContext client, ICompany company, Long id) {
    return attachmentRepository.findTMSOpsAttachment(company.getId(), id);
  }

  public List<TMSOpsAttachment> saveOpsAttachments(ClientContext client, ICompany company, Long opsId, List<TMSOpsAttachment> attachments,  boolean removeOrphan) {
    CompanyStorage storage = storageService.createCompanyStorage(client, company.getCode());
    String storagePath = TMSOpsAttachment.getTMSOpsAttachmentStoragePath(opsId);
    storage.saveAttachments(storagePath, attachments, removeOrphan);
    for (TMSOpsAttachment attachment : attachments) {
      attachment.setOpsId(opsId);
      attachment.set(client, company.getId());
    }
    attachments = attachmentRepository.saveAll(attachments);
    if(removeOrphan) {
      List<Long> idSet = TMSOpsAttachment.getIds(attachments);
      if(Collections.isEmpty(idSet)) {
        attachmentRepository.deleteWithOpsId(company.getId(), opsId);
      } else {
        attachmentRepository.deleteOrphan(company.getId(), opsId, idSet);
      }
    }
    return attachments;
  }

  public List<TMSOperations> requestOPS(ClientContext client, ICompany company, List<Long> tmsBillIds) {
    if(tmsBillIds == null || tmsBillIds.isEmpty()) return null;

    List<TMSOperations> operationsArray = new ArrayList<>();
    for(Long tmsBillId : tmsBillIds) {
      TMSOperations ops = requestOperations(client, company, tmsBillId);
      operationsArray.add(ops);
    }
    return operationsArray;
  }
  
  public TMSOperations requestOperations(ClientContext client, ICompany company, Long tmsBillId) {
    TMSOperations ops = repository.getOperationsByTMSBillId(company.getId(), tmsBillId);
    if(ops == null) {
      TMSOperations operation = new TMSOperations();
      operation.genCode(seqService.nextSequence(TMSOperations.SEQUENCE));
      operation.setTmsBillId(tmsBillId);
      return saveOperations(client, company, operation);
    } else {
      ops.setStatus(TMSOperationsStatus.NEED_CONFIRM);
      return saveOperations(client, company, ops);
    }
  }
  
  //TODO: clean code
  public boolean noticeMessageToPic(ClientContext client, ICompany company, List<MapObject> operations) {
    Map<Long, String> contentMaps = new HashedMap<>();
    for(MapObject ops : operations) {
      Long responsibleAccountId = ops.getLong("responsibleAccountId", null);
      if(responsibleAccountId == null) continue;
      if(contentMaps.containsKey(responsibleAccountId)) {
        String content = contentMaps.get(responsibleAccountId);
        content += "\n\n" + computeMessageOpsInfo(ops);
        contentMaps.put(responsibleAccountId, content);
      } else {
        String content = computeMessageOpsInfo(ops);
        contentMaps.put(responsibleAccountId, content);
      }
      
      //send Coordinator
      Long tmsBillId            = ops.getLong("tmsBillId", null);
      if(tmsBillId != null) {
        List<VehicleTripGoodsTracking> trackings = goodsTrackingLogic.findVehicleTripGoodTrackingByTMSBillId(client, company, tmsBillId);
        if(Collections.isEmpty(trackings)) continue;
        for(VehicleTripGoodsTracking sel : trackings) {
          Long coordinatorAccountId = sel.getCoordinatorAccountId();
          if(coordinatorAccountId == null) continue;
          if(contentMaps.containsKey(coordinatorAccountId)) {
            String content = contentMaps.get(coordinatorAccountId);
            content += "\n\n" + computeMessageOpsInfo(ops);
            contentMaps.put(coordinatorAccountId, content);
          } else {
            String content = computeMessageOpsInfo(ops);
            contentMaps.put(coordinatorAccountId, content);
          }
        }
      }
    }
    for(Long accountId : contentMaps.keySet()) {
      String content = contentMaps.get(accountId);
      tmsGeneralLogic.sentZaloMessage(client, company, accountId, content, "NOTICE: OPS INFO");
    }
    return true;
  }
  
  private String computeMessageOpsInfo(MapObject ops) {
    String content = "📢 NOTICE: OPS\n";
//    content += "🇻🇳 🇻🇳 🇻🇳\n";
    String label   = ops.getString("label");
    TransportationMode mode = TransportationMode.parse(ops.getString("mode"));
    String customerFullName = ops.getString("customerFullName");
    content += label + " " + mode.getLabel() + " " + customerFullName;
    
    Date dateTime  = ops.getDate("deliveryPlan", null);
    String sDate   = "";
    String sTime   = "";
    if(dateTime != null) {
      sDate = DateUtil.asCompactDate(dateTime);
      sTime = DateUtil.asCompactTimeWithoutSecond(dateTime);
    }
    content += "\n- Date & Time: " + sDate + " " + sTime;
    if(TransportationMode.isExport(mode)) {
      content += "\n- Address: " + ops.getString("senderAddress");
    } else if(TransportationMode.isImport(mode)) {
      content += "\n- Address: " + ops.getString("receiverAddress");
    } else {
      content += "\nPickup: " + ops.getString("senderAddress");
      content += "\nDelivery: " + ops.getString("receiverAddress");
    }
    Double tmsBillQuantity = ops.getDouble("quantity", 0D);
    String tmsBillQuantityUnit = ops.getString("tmsBillQuantityUnit");
    Double tmsBillWeight = ops.getDouble("tmsBillWeight", 0D);
    String tmsBillVolume = ops.getString("tmsBillVolume");
    content += "\n- Shipment: " + tmsBillQuantity + "(" + tmsBillQuantityUnit + ") ";
    if(tmsBillWeight > 0) content += tmsBillWeight + "(" + ops.getString("tmsBillWeightUnit") + ") ";
    if(StringUtil.isNotBlank(tmsBillVolume)) content += tmsBillVolume + "(" + ops.getString("tmsBillVolumeUnit") + ")";
    content += "\n🚀 🚀 🚀";
    String opsName = ops.getString("opsAccountFullName");
    String opsAccountMobile = ops.getString("opsAccountMobile");
    String identificationNo = ops.getString("identificationNo");
    content += "\n- Ops: " + opsName + " Mobile: " + opsAccountMobile + " ID: " + identificationNo;
    
    TMSOperationsStatus status = TMSOperationsStatus.valueOf(ops.getString("status"));
    content += "\n- Status: " + status;
    return content;
  }
}