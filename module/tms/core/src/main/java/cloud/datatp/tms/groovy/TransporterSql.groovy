package cloud.datatp.tms.groovy;

import org.springframework.context.ApplicationContext

import net.datatp.lib.executable.ExecutableContext;
import net.datatp.lib.executable.Executor;
import net.datatp.module.data.db.ExecutableSqlBuilder;
import net.datatp.util.ds.MapObject;

public class TransporterSql extends Executor {
  static public class SelectTransporter extends ExecutableSqlBuilder {
    public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
      MapObject sqlParams = ctx.getParam("sqlParams");
      Long vehicleFleetId = sqlParams.getLong("vehicleFleetId", null);
      String JOIN_MEMBER_SHIP = "";
      if (vehicleFleetId != null) {
        JOIN_MEMBER_SHIP = """
             JOIN lgc_fleet_transporter_membership 
                ON lgc_fleet_transporter_membership.transporter_id = lgc_fleet_transporter.id
                AND lgc_fleet_transporter_membership.vehicle_fleet_id = :vehicleFleetId 
            """;
      }
      String query = """
            SELECT 
                lgc_fleet_transporter.storage_state AS storage_state,
                lgc_fleet_transporter.odoo_journal_code AS odoo_journal_code,
                lgc_fleet_transporter.created_time AS created_time,
                lgc_fleet_transporter.full_name AS full_name,
                lgc_fleet_transporter.mobile AS mobile,
                lgc_fleet_transporter.odoo_partner_code AS odoo_partner_code,
                lgc_fleet_transporter.modified_time AS modified_time,
                lgc_fleet_transporter.birthday AS birthday,
                lgc_fleet_transporter.id AS id,
                lgc_fleet_transporter.description AS description,
                lgc_fleet_transporter.deliver AS deliver,
                lgc_fleet_transporter.created_by AS created_by,
                lgc_fleet_transporter.odoo_company_code AS odoo_company_code,
                lgc_fleet_transporter.account_id AS account_id,
                lgc_fleet_transporter.email AS email,
                lgc_fleet_transporter.id_card AS id_card,
                lgc_fleet_transporter.modified_by AS modified_by,
                lgc_fleet_transporter.transporter_type AS transporter_type,
                lgc_fleet_transporter.driver AS driver,
                lgc_fleet_transporter.code AS code,
                lgc_fleet_transporter.company_id AS company_id,
                lgc_fleet_transporter.version AS version
            FROM lgc_fleet_transporter
            ${JOIN_MEMBER_SHIP}
            WHERE
               ${FILTER_BY_STORAGE_STATE('lgc_fleet_transporter', sqlParams)}
               ${AND_FILTER_BY_PARAM('lgc_fleet_transporter.company_id', 'companyId', sqlParams)}
               ${AND_SEARCH_BY_PARAMS(['lgc_fleet_transporter.code', 'lgc_fleet_transporter.full_name', 'lgc_fleet_transporter.odoo_partner_code'], 'search', sqlParams)}
            ORDER BY lgc_fleet_transporter.full_name DESC  
            ${MAX_RETURN(sqlParams)}   
        """
      return query;
    }
  }

  public TransporterSql() {
    register(new SelectTransporter());
  }
}