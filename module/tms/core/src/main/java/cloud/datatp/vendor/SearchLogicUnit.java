package cloud.datatp.vendor;

import java.util.*;

import net.datatp.module.account.ProfileLogic;
import net.datatp.module.account.entity.UserProfile;
import net.datatp.util.text.StringUtil;
import org.springframework.context.ApplicationContext;

import net.datatp.lib.executable.ExecutableContext;
import net.datatp.lib.executable.ExecutableUnit;
import net.datatp.lib.executable.Executor;
import net.datatp.module.account.entity.Account;
import net.datatp.module.data.db.RecordGroupByMap;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.security.client.ClientContext;
import net.datatp.util.ds.Collections;

public class SearchLogicUnit extends Executor {

  static public class SearchTMSVendorBill extends ExecutableUnit {
    private RecordGroupByMap<Long, SqlMapRecord> findStopLocationsByTMSBillIds(ClientContext client, ICompany company,
        TMSVendorBillLogic logic, List<Long> TMSBillIds) {
      SqlQueryParams params = new SqlQueryParams();
      params.addParam("tmsBillIds", TMSBillIds);
      String scriptDir = logic.appEnv.addonPath("logistics", "groovy");
      String scriptFile = "cloud/datatp/tms/groovy/TMSBillSql.groovy";
      List<SqlMapRecord> trackings = logic.searchDbRecords(client, scriptDir, scriptFile,
          "FindStopLocationsByTMSBillIds", params);
      return new RecordGroupByMap<>(trackings, tracking -> (Long) tracking.getLong("tmsBillId"));
    }

    public List<SqlMapRecord> execute(ApplicationContext appCtx, ExecutableContext ctx) {
      TMSVendorBillLogic logic = ctx.getParam(TMSVendorBillLogic.class);
      ClientContext client = ctx.getParam(ClientContext.class);
      ICompany company = ctx.getParam(ICompany.class);
      SqlQueryParams params = ctx.getParam(SqlQueryParams.class);
      ProfileLogic profileLogic = ctx.getParam(ProfileLogic.class);


      String scriptDir = logic.appEnv.addonPath("logistics", "groovy");
      String scriptFile = "cloud/datatp/vendor/groovy/TMSVendorBillSql.groovy";

      Account account = logic.accountLogic.getReadOnly(client, client.getRemoteUser());
      params.addParam("responsibleAccountId", account.getId());
      params.addParam("companyId", company.getId());
      List<SqlMapRecord> records = logic.searchDbRecords(client, scriptDir, scriptFile, "SelectTMSVendorBill", params);

      List<Long> vendorTrackingIds = Collections.transform(records, rec -> rec.getLong("id"));
      SqlQueryParams trackingParams = ctx.getParam(SqlQueryParams.class);
      trackingParams.addParam("vendorBillIds", vendorTrackingIds);
      List<SqlMapRecord> trakings = logic.searchDbRecords(client, scriptDir, scriptFile,
          "FindVendorBillTrackingByTrackingIds", params);
      RecordGroupByMap<Long, SqlMapRecord> trakingGroup = new RecordGroupByMap<>(trakings,
          rec -> (Long) rec.getLong("vendorBillId"));
      Set<Long> responsibleAccountIds = new HashSet<>();
      for (SqlMapRecord rec : records) {
        Long id = rec.getLong("id");
        rec.add("trackings", trakingGroup.get(id));
        responsibleAccountIds.add(rec.getLong("responsibleAccountId"));
      }

      List<Long> tmsBillIds = Collections.transform(records, rec -> rec.getLong("tmsBillId"));
      RecordGroupByMap<Long, SqlMapRecord> stopLocations = findStopLocationsByTMSBillIds(client, company, logic,
          tmsBillIds);
      Map<Long, UserProfile> userProfileMap = profileLogic.findMapUserProfileByAccountIds(client, new ArrayList<>(responsibleAccountIds));
      for (SqlMapRecord rec : records) {
        Long tmsBillId = rec.getLong("tmsBillId");
        rec.add("stopLocations", stopLocations.get(tmsBillId));

        UserProfile userProfile = userProfileMap.get(rec.getLong("responsibleAccountId"));
        if(userProfile != null) {
          if(StringUtil.isNotBlank(userProfile.getNickname())) {
            rec.add("userName",userProfile.getNickname() );
          } else {
            rec.add("userName",userProfile.getFullName() );
          }
        }
      }
      return records;
    }
  }

  static public class SearchTMSVendorBillNotLinkTMSBill extends ExecutableUnit {
    public List<SqlMapRecord> execute(ApplicationContext appCtx, ExecutableContext ctx) {
      TMSVendorBillLogic logic = ctx.getParam(TMSVendorBillLogic.class);
      ClientContext client = ctx.getParam(ClientContext.class);
      ICompany company = ctx.getParam(ICompany.class);
      SqlQueryParams params = ctx.getParam(SqlQueryParams.class);

      String scriptDir = logic.appEnv.addonPath("logistics", "groovy");
      String scriptFile = "cloud/datatp/vendor/groovy/TMSVendorBillSql.groovy";
      params.addParam("companyId", company.getId());

      List<SqlMapRecord> recordList = logic.searchDbRecords(client, scriptDir, scriptFile,
          "SelectTMSVendorBillNotLinkTMSBill", params);
      return recordList;
    }
  }

  public SearchLogicUnit() {
    register(new SearchTMSVendorBill());
    register(new SearchTMSVendorBillNotLinkTMSBill());
  }
}