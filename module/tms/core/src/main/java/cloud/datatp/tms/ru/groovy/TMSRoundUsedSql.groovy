package cloud.datatp.tms.ru.groovy

import org.springframework.context.ApplicationContext

import net.datatp.lib.executable.ExecutableContext
import net.datatp.lib.executable.Executor
import net.datatp.module.data.db.ExecutableSqlBuilder
import net.datatp.util.ds.MapObject

public class TMSRoundUsedSql extends Executor {

    static public class SearchTMSRoundUsed extends ExecutableSqlBuilder {

        public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
            MapObject sqlParams = ctx.getParam("sqlParams");

            String query = """
                SELECT
                    ru.id                            AS id,
                    ru.file_ru                       AS file_ru,
                    ru.ru_tms_bill_id                AS ru_tms_bill_id,
                    ru.job_tracking_id               AS job_tracking_id,
                    ru.tms_bill_id                   AS tms_bill_id,
                    ru.is_modified_file_truck        AS is_modified_file_truck,
                    ru.opened_date                   AS opened_date,
                    ru.responsible_account_id        AS responsible_account_id,
                    ru.responsible_full_name         AS responsible_full_name,
                    lgc_tms_bill.delivery_plan       AS date,
                    lgc_tms_bill.time                AS time,
                    ru.container_type                AS container_type,
                    ru.purpose                       AS purpose,
                    ru.type                          AS type,
                    ru.status                        AS status,
                    ru.carrier_id                    AS carrier_id,
                    ru.carrier_full_name             AS carrier_full_name,
                    ru.gross_weight                  AS gross_weight,
                    ru.delivery_contact              AS delivery_contact,
                    ru.delivery_address              AS delivery_address,
                    ru.delivery_location_id          AS delivery_location_id,
                    ru.container_in_hand_plan_id     AS container_in_hand_plan_id,
                    ru.note                          AS note,
                    ru.vehicle_id                    AS vehicle_id,
                    ru.vehicle_label                 AS vehicle_label,
                    ru.vehicle_driver_id             AS vehicle_driver_id,
                    ru.vehicle_driver_full_name      AS vehicle_driver_full_name,
                    ru.vehicle_driver_identification_no AS vehicle_driver_identification_no,
                    ru.vehicle_driver_mobile         AS vehicle_driver_mobile,
                    ru.cost_trucking_truck           AS cost_trucking_truck,
                    ru.cost_trucking_lift_on         AS cost_trucking_lift_on,
                    ru.cost_trucking_lift_off        AS cost_trucking_lift_off,
                    ru.cost_trucking_extra           AS cost_trucking_extra,
                    ru.cost_ru_truck                 AS cost_ru_truck,
                    ru.cost_ru_combine               AS cost_ru_combine,
                    ru.cost_ru_lift_on               AS cost_ru_lift_on,
                    ru.cost_ru_lift_off              AS cost_ru_lift_off,
                    ru.cost_ru_profit                AS cost_ru_profit,
                    job_tracking.step_done_count     AS step_done_count,
                    job_tracking.last_step_name      AS current_step,
                    ru.version                       AS version,
                    ru.created_time                  AS created_time,
                    ru.created_by                    AS created_by,
                    ru.modified_time                 AS modified_time,
                    ru.modified_by                   AS modified_by,
                    ru.storage_state                 AS storage_state,
                    ru.company_id                    AS company_id,
                    ru.container_no                  AS container_no,
                    ru.seal_no                       AS seal_no,
                    ft.container_no                  AS tms_container_no,
                    ft.seal_no                       AS tms_seal_no,
                    CASE WHEN lgc_tms_bill.responsible_account_id =:remoteUserId 
                         THEN TRUE 
                         ELSE FALSE
                    END                              AS bill_owner,
                    lgc_tms_bill.customer_id         AS customer_id,
                    lgc_tms_bill.customer_full_name  AS customer_full_name,
                    lgc_tms_bill.label               AS file_truck,
                    ft.booking_code                  AS booking_code,
                    ft.eta_cut_off_time              AS eta_cut_off_time,
                    lgc_tms_bill.office              AS office,
                    ru.vendor_id                     AS vendor_id,  
                    ru.vendor_full_name              AS vendor_full_name,
                    TO_CHAR(lgc_tms_bill.delivery_plan, 'yyyy/MM/dd') AS formatted_delivery_plan
                  FROM lgc_tms_round_used AS ru
                  LEFT JOIN lgc_job_tracking        AS job_tracking      ON job_tracking.id      = ru.job_tracking_id
                  LEFT JOIN lgc_tms_bill                                 ON ru.tms_bill_id       = lgc_tms_bill.id
                  LEFT JOIN lgc_tms_bill_forwarder_transport AS ft       ON ft.id                = lgc_tms_bill.tms_bill_forwarder_transport_id
                  WHERE 
                    ${FILTER_BY_STORAGE_STATE('ru', sqlParams)}
                    ${AND_FILTER_BY_PARAM('ru.company_id','companyId', sqlParams)}
                     ${AND_FILTER_BY_RANGE('lgc_tms_bill.delivery_plan', 'deliveryPlan', sqlParams)}
                    ${AND_SEARCH_BY_PARAMS(['ru.file_ru', 'ru.file_truck'], 'search', sqlParams)} 
                    ORDER BY formatted_delivery_plan DESC, customer_full_name, ru.status
                """;
            return query;
        }
    }

    public TMSRoundUsedSql() {
        register(new SearchTMSRoundUsed());
    }
}
