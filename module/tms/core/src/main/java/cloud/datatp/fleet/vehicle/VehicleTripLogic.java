package cloud.datatp.fleet.vehicle;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cloud.datatp.fleet.vehicle.entity.MessageStatus;
import cloud.datatp.fleet.vehicle.entity.VehicleTrip;
import cloud.datatp.fleet.vehicle.entity.VehicleTrip.VehicleTripTransporterStatus;
import cloud.datatp.fleet.vehicle.models.Message;
import cloud.datatp.fleet.vehicle.models.VehicleTripGoodsTrackingModel;
import cloud.datatp.fleet.vehicle.repository.VehicleTripRepository;
import net.datatp.module.data.db.DAOService;
import net.datatp.module.data.db.RecordGroupByMap;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.EditMode;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.zalo.ZaloLogic;
import net.datatp.security.client.ClientContext;
import net.datatp.security.client.DataScope;
import net.datatp.util.ds.Collections;
import net.datatp.util.ds.MapObject;
import net.datatp.util.error.RuntimeError;
import net.datatp.util.text.DateUtil;
import net.datatp.util.text.StringUtil;

@Component
public class VehicleTripLogic  extends DAOService {
  @Autowired
  private ZaloLogic                     zaloLogic;

  @Autowired
  private VehicleTripRepository         repo;
  
  @Autowired
  private VehicleLogic                  vehicleLogic;
  
  public MapObject findTripInfo(ClientContext client, ICompany company, Long tripId) {
    SqlQueryParams params = new SqlQueryParams();
    params.addParam("dataScope", DataScope.Company);
    params.addParam("id", tripId);
    List<SqlMapRecord> results = vehicleLogic.searchVehicleTrips(client, company, params);
    if(results.size() == 0) return null;
    return results.get(0);
  }
  
  public List<Message> generateMessage(ClientContext client, ICompany company, List<VehicleTripGoodsTrackingModel> models) {
    List<Message> messages = new ArrayList<>();
    RecordGroupByMap<Long, VehicleTripGoodsTrackingModel> recordGroupByMap = new RecordGroupByMap<>(models, model -> model.getVehicleTripId());
    Map<Long, List<VehicleTripGoodsTrackingModel>> modelMap = recordGroupByMap.getAll();
    for(Map.Entry<Long, List<VehicleTripGoodsTrackingModel>> entry : modelMap.entrySet()) {
      List<VehicleTripGoodsTrackingModel> modelTrackings = entry.getValue();
      Long vehicleTripId = entry.getKey();
      List<String> labels = Collections.transform(modelTrackings, model -> model.getLabel());
      String content = computeMessage(modelTrackings);
      Message message = new Message();
      message.setContent(content);
      message.setVehicleTripId(vehicleTripId);
      message.setLabels(labels);
      messages.add(message);
    }
    return messages;
  }

  public List<Message> sendMessage(ClientContext client, ICompany company, List<Message> messages) {
    for(Message message: messages) {
      VehicleTrip vehicleTrip = repo.getById(company.getId(), message.getVehicleTripId());
      String mobile = vehicleTrip.getMobile();
      try {
        if(StringUtil.isBlank(mobile)) throw RuntimeError.UnknownError("Driver's Mobile is null!!!");
        zaloLogic.sendZaloMessageByPhone(company, client, mobile, message.getContent());
        repo.updateMessageStatus(MessageStatus.SEND_SUCCESS, message.getVehicleTripId());
        message.setStatus(MessageStatus.SEND_SUCCESS);
      } catch (Exception e) {
        repo.updateMessageStatus(MessageStatus.SEND_FAILED, message.getVehicleTripId());
        message.setStatus(MessageStatus.SEND_FAILED);
      }
    }
    return messages;
  }


  private String computeMessage(List<VehicleTripGoodsTrackingModel> models) {
    String content = "📢 THÔNG BÁO: THÔNG TIN LÔ HÀNG";

    content += "\n🇻🇳 🇻🇳 🇻🇳";
    content += "\n* THÔNG TIN LÔ HÀNG:";
    for (VehicleTripGoodsTrackingModel model : models) {
      content += "\n";
      String label   = model.getBillLabel();
      content += (models.indexOf(model) + 1) + ") " + label + " " + model.getModeLabel();
      String customerFullName = model.getCustomerFullName() == null ? "" : model.getCustomerFullName();
      content += "\n- Khách hàng: " + customerFullName;
      Date dateTime = model.getDeliveryPlan();
      String sDate   = "";
      String time   = model.getTime() == null ? "": model.getTime() ;
      if(dateTime != null) {
        sDate = DateUtil.asCompactDate(dateTime);
      }
      content += "\n- Ngày & Giờ: " + sDate + " " + time;
      if(model.isImport() && model.isFcl()) {
        content += createContentImportFCL(model);
        continue;
      }
      if(model.isExport() && model.isFcl()) {
        content += createContentExportFCL(model);
        continue;
      }
      String pickupAddress = model.getPickupAddress() == null ? "" : model.getPickupAddress();
      String deliveryAddress = model.getDeliveryAddress() == null ? "" : model.getDeliveryAddress();
      content += "\n- Điểm lấy hàng : " + pickupAddress;
      content += "\n- Điểm giao hàng: " + deliveryAddress;
      String unit = model.getQuantityUnit() == null ? "" : model.getQuantityUnit();
      content += "\n- Số lượng      : " + model.getQuantity() + " " + unit;
      content += "\n- Khối lượng    : " + model.getWeight();
    }
    content += "\n\uD83D\uDE9A \uD83D\uDE9A \uD83D\uDE9A";
    content += "\n* THÔNG TIN CHUYẾN: ";
    String fleet = models.get(0).getFleetLabel() == null ? "" : models.get(0).getFleetLabel();
    Date deliveryPlan = models.get(0).getDeliveryPlan();
    String sDeliveryPlan = "";
    if(deliveryPlan != null) {
      sDeliveryPlan = DateUtil.asCompactDate(deliveryPlan);
    }
    String vehicleLabel = models.get(0).getVehicleLabel() == null ? "" : models.get(0).getVehicleLabel();
    String driverFullName = models.get(0).getDriverFullName() == null ? "" : models.get(0).getDriverFullName();
    String mobile = models.get(0).getMobile() == null ? "" : models.get(0).getMobile();
    content += "\n- Ngày vận chuyển: " + sDeliveryPlan;
    content += "\n- Đội xe: " + fleet;
    content += "\n- Biển số: " + vehicleLabel;
    content += "\n- Lái xe: " + driverFullName + " " + mobile;
    return content;
  }
  
  private String createContentImportFCL(VehicleTripGoodsTrackingModel model) {
    String content = "";
    content += "\n- Địa chỉ     : " + model.getDeliveryAddress();
    content += "\n- Liên hệ     : " + model.getReceiverContact();
    content += "\n- Nơi lấy cont: " + model.getPickupContainerLocation();
    content += "\n- Loại cont   : " + model.getVehicleType();
    content += "\n- Container No: " + model.getContainerNo();
    content += "\n- Khối lượng  : " + model.getWeight() + "(KG)";
    content += "\n- Hãng tàu    : " + model.getCarrierFullName();
    content += "\n- Ghi chú     : " + 
        (model.getTmsBillDescription() != null ? model.getTmsBillDescription() + "\n" : "") +
        (model.getDescription() != null ? model.getDescription() : "");
    return content;
  }
  private String createContentExportFCL(VehicleTripGoodsTrackingModel model) {
    String content = "";
    content += "\n- Địa chỉ     : " + model.getPickupAddress();
    content += "\n- Liên hệ     : " + model.getSenderContact();
    content += "\n- Nơi lấy cont: " + model.getReturnContainerLocation();
    content += "\n- Loại cont   : " + model.getVehicleType();
    content += "\n- Container No: " + model.getBookingCode();
    content += "\n- Khối lượng  : " + model.getWeight() + "(KG)";
    content += "\n- Hãng tàu    : " + model.getCarrierFullName();
    content += "\n- Ghi chú     : " + 
        (model.getTmsBillDescription() != null ? model.getTmsBillDescription() + "\n" : "") +
        (model.getDescription() != null ? model.getDescription() : "");
    return content;
  }

  public VehicleTrip updateVehicleTripEditMode(ClientContext client, ICompany company, Long vehicleTripId, EditMode editMode) {
    VehicleTrip trip = vehicleLogic.getVehicleTrip(client, company, vehicleTripId);
    trip.setEditMode(editMode);
    if(EditMode.LOCKED.equals(trip.getEditMode())) {
      trip.setTransporterStatus(VehicleTripTransporterStatus.NEED_CONFIRM);
    }
    return vehicleLogic.saveVehicleTrip(client, company, trip);
  }
}
