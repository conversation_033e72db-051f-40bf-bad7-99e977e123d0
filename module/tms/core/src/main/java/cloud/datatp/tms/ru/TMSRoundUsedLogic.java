package cloud.datatp.tms.ru;

import java.util.*;

import net.datatp.module.resource.location.StateLogic;
import net.datatp.module.resource.location.entity.Location;
import net.datatp.module.resource.location.entity.State;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cloud.datatp.tms.TMSGeneralLogic;
import cloud.datatp.tms.bill.TMSBillLogic;
import cloud.datatp.tms.bill.entity.TMSBill;
import cloud.datatp.tms.bill.entity.TMSBillForwarderTransport;
import cloud.datatp.tms.bill.models.TMSBillModel;
import cloud.datatp.tms.ru.entity.TMSContainerType;
import cloud.datatp.tms.ru.entity.TMSRoundUsed;
import cloud.datatp.tms.ru.entity.TMSRoundUsed.Status;
import cloud.datatp.tms.ru.model.TMSRoundUsedModel;
import cloud.datatp.tms.ru.repository.TMSRoundUsedRepository;
import cloud.datatp.vendor.TMSVendorBillLogic;
import cloud.datatp.vendor.entity.TMSVendorBill;
import net.datatp.module.account.AccountLogic;
import net.datatp.module.account.entity.Account;
import net.datatp.module.data.db.DAOService;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.entity.StorageState;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.data.db.seq.SeqService;
import net.datatp.module.hr.DepartmentLogic;
import net.datatp.module.hr.EmployeeLogic;
import net.datatp.module.hr.entity.Employee;
import net.datatp.module.hr.entity.HRDepartment;
import net.datatp.module.hr.entity.HRDepartmentMembership;
import net.datatp.module.resource.location.LocationLogic;
import net.datatp.security.client.ClientContext;
import net.datatp.util.ds.Arrays;
import net.datatp.util.ds.Collections;
import net.datatp.util.ds.Objects;
import net.datatp.util.error.ErrorType;
import net.datatp.util.error.RuntimeError;
import net.datatp.util.text.DateUtil;
import net.datatp.util.text.StringUtil;

@Component
public class TMSRoundUsedLogic extends DAOService {
  @Autowired
  private TMSRoundUsedRepository ruRepo;

  @Autowired
  private LocationLogic locationLogic;

  @Autowired
  private StateLogic stateLogic;

  @Autowired
  private AccountLogic accountLogic;

  @Autowired
  private TMSBillLogic billLogic;
  
  @Autowired
  private TMSVendorBillLogic vendorBillLogic;

  @Autowired
  private TMSContainerLogic containerLogic;

  @Autowired
  private TMSGeneralLogic generalLogic;

  @Autowired
  private DepartmentLogic departmentLogic;

  @Autowired
  private EmployeeLogic employeeLogic;
  
  @Autowired
  private SeqService seqService;

  public TMSRoundUsed getTMSRoundUsed(ClientContext client, ICompany company, Long id) {
    return ruRepo.getById(id);
  }
  
  public TMSRoundUsed ruLinkTMSBill(ClientContext client, ICompany company, Long ruId, Long tmsBillId) {
    TMSBill bill = billLogic.getTMSBillById(client, company, tmsBillId);
    TMSRoundUsed tmsRoundUsed = getTMSRoundUsed(client, company, ruId);
    TMSBillForwarderTransport fTransport = bill.getTmsBillForwarderTransport();
    if(fTransport != null) {
      tmsRoundUsed.setContainerNo(fTransport.getContainerNo());
      tmsRoundUsed.setSealNo(fTransport.getSealNo());
    }
    tmsRoundUsed.setRuTmsBillId(tmsBillId);
    return saveTMSRoundUsed(client, company, tmsRoundUsed);
  }
  
  public TMSRoundUsed ruUnlinkTMSBill(ClientContext client, ICompany company, Long ruId) {
    TMSRoundUsed tmsRoundUsed = getTMSRoundUsed(client, company, ruId);
    tmsRoundUsed.setContainerNo(null);
    tmsRoundUsed.setSealNo(null);
    tmsRoundUsed.setRuTmsBillId(null);
    return saveTMSRoundUsed(client, company, tmsRoundUsed);
  }

  public List<TMSRoundUsedModel> createTMSRoundUsedByTMSBills(ClientContext client, ICompany company, List<TMSBillModel> billModels) {
    List<TMSRoundUsedModel> holder = new ArrayList<>();
    for (TMSBillModel billModel : billModels) {
      TMSBill bill = billLogic.getTMSBillById(client, company, billModel.getId());
      if (Objects.isNull(bill)) continue;
      TMSRoundUsed roundUsed = createTMSRoundUsed(client, company, bill);
      List<TMSRoundUsedModel> ruModels = saveTMSRoundUsedModels(client, company, java.util.Arrays.asList(new TMSRoundUsedModel(roundUsed)));
      holder.addAll(ruModels);
      if (!generalLogic.autoSendMessage(client, company, TMSRoundUsed.AUTO_SEND_MESSAGE)) {
        continue;
      }
      String content = computeMessage(bill, roundUsed, false);
      generalLogic.sentZaloMessage(client, company, bill.getResponsibleAccountId(), content, "REQUEST RU");
    }

    return holder;
  }

  public TMSRoundUsed createTMSRoundUsed(ClientContext client, ICompany company, TMSBill bill) {
    validateTMSBill(client, company, bill);
    TMSRoundUsed roundUsed = new TMSRoundUsed(bill);
    processContainerType(client, company, roundUsed);
    Account account = accountLogic.getEditable(client, client.getRemoteUser());
    roundUsed.setResponsibleAccountId(account.getId());
    roundUsed.setResponsibleFullName(account.getFullName());
    roundUsed.setOpenedDate(new Date());
    roundUsed.setStatus(Status.SendingFromRu);
    roundUsed = saveTMSRoundUsed(client, company, roundUsed);
    return roundUsed;
  }

  private void processContainerType(ClientContext client, ICompany company, TMSRoundUsed roundUsed) {
    String containerType = roundUsed.getContainerType();
    if (Objects.isNull(containerType)) {
      return;
    }
    containerType = containerType.replaceAll("'", "");
    containerType = containerType.replaceAll("`", "");
    containerType = containerType.replaceAll("´", "");
    containerType = containerType.replaceAll("\"", "");
    containerType = containerType.replaceAll(",", "");
    containerType = containerType.replaceAll(";", "");
    containerType = containerType.replaceAll(" ", "");
    TMSContainerType tmsContainerType = containerLogic.getTMSContainerType(client, company, containerType);
    if (Objects.isNull(tmsContainerType)) {
      roundUsed.setContainerType(null);
      return;
    }
    roundUsed.setContainerType(containerType);
  }

  private void validateTMSBill(ClientContext client, ICompany company, TMSBill bill) {
    List<TMSRoundUsed> roundUseds = ruRepo.findByBillId(bill.getId());
    if (Collections.isNotEmpty(roundUseds))
      throw new RuntimeError(ErrorType.IllegalState, "TMSBill: {0} is requested", bill.getCode());
  }

  private void validateContainerType(ClientContext client, ICompany company, TMSBill bill) {
    String containerType = bill.getTmsBillForwarderTransport().getTruckType();
    if (StringUtil.isEmpty(containerType)) return;
    TMSContainerType tmsContainerType = containerLogic.getTMSContainerType(client, company, containerType);
    if (Objects.isNull(tmsContainerType))
      throw new RuntimeError(ErrorType.IllegalArgument, "ContainerType = {0} is not found", containerType);
  }

  public TMSRoundUsed newTMSRoundUsed(ClientContext client, ICompany company, TMSRoundUsed roundUsed) {
    Account account = accountLogic.getEditable(client, client.getRemoteUser());
    roundUsed.setResponsibleAccountId(account.getId());
    roundUsed.setResponsibleFullName(account.getFullName());
    roundUsed.setOpenedDate(new Date());
    roundUsed.setStatus(Status.Processing);
    return roundUsed;
  }

  public TMSRoundUsedModel newTMSRoundUsedModel(ClientContext client, ICompany company, TMSRoundUsedModel roundUsedModel) {
    Account account = accountLogic.getEditable(client, client.getRemoteUser());
    roundUsedModel.setResponsibleAccountId(account.getId());
    roundUsedModel.setResponsibleFullName(account.getFullName());
    roundUsedModel.setOpenedDate(new Date());
    roundUsedModel.setStatus(Status.Processing);
    return roundUsedModel;
  }

  public TMSRoundUsed saveTMSRoundUsed(ClientContext client, ICompany company, TMSRoundUsed roundUsed) {
    roundUsed.set(client, company);
//    TMSContainerInHandPlan containerInHandPlan = containerLogic.updateTMSContainerInHandByRoundUsed(client, company, roundUsed);
//    if (Objects.nonNull(containerInHandPlan)) roundUsed.setContainerInHandPlanId(containerInHandPlan.getId());
    return ruRepo.save(roundUsed);
  }

  public List<TMSRoundUsedModel> saveTMSRoundUsedModels(ClientContext client, ICompany company, List<TMSRoundUsedModel> models) {
    List<Long> archivedIds = new ArrayList<>();
    for (TMSRoundUsedModel model : models) {
      Long id = model.getId();
      if (model.isDeleted()) {
        if (id == null) continue;
        archivedIds.add(id);
      }

      TMSRoundUsed roundUsed = new TMSRoundUsed();
      Account account = accountLogic.getEditable(client, client.getRemoteUser());
      if (id != null) {
        roundUsed = getTMSRoundUsed(client, company, id);
      } else {
        roundUsed.setResponsibleAccountId(account.getId());
        roundUsed.setResponsibleFullName(account.getFullName());
        roundUsed.setOpenedDate(new Date());
      }
      roundUsed = model.mapRoundUsed(roundUsed);
      roundUsed = model.updateCustomerLocation(client, locationLogic, roundUsed);

      if(model.isBillOwner() || id == null || model.getTmsBillId() == null) {
        TMSBill bill;
        boolean newBill = false;
        if(model.getTmsBillId() == null) {
          newBill = true;
          bill = model.newTMSBill(account);
          bill.genCode(company.getCode(), seqService.nextSequence(TMSBill.SEQUENCE));
        } else {
          bill = billLogic.getTMSBillById(client, company, model.getTmsBillId());
          bill = model.mergeTMSBill(bill);
        }
        if(bill.getTmsBillForwarderTransport() != null) bill.getTmsBillForwarderTransport().setHwbNo(model.getFileTruck());
        bill = billLogic.saveTMSBill(client, company, bill);
        model.setTmsBillId(bill.getId());
        roundUsed.setTmsBillId(bill.getId());
        
        if(newBill) {
          TMSVendorBill vendorBill = new TMSVendorBill();
          vendorBill = vendorBill.updateByTMSBill(bill);
          if(model.isVehicleInfoModified()) vendorBill.setBillTrackings(Arrays.asList(model.convertToVendorBillTracking()));
          model.setVehicleInfoModified(false);
          vendorBill = vendorBillLogic.saveVendorBill(client, company, vendorBill); 
        }
      }
      roundUsed = saveTMSRoundUsed(client, company, roundUsed);
      
      if(model.isVehicleInfoModified()) {
        updateVendorBill(client, company, model);
        if(generalLogic.autoSendMessage(client, company, TMSRoundUsed.AUTO_SEND_MESSAGE) && !model.isBillOwner()) {
          TMSBill tmsBill = billLogic.getTMSBillById(client, company, roundUsed.getTmsBillId());
          if(tmsBill != null) {
            String content = computeMessage(tmsBill, roundUsed, true);
            generalLogic.sentZaloMessage(client, company, tmsBill.getResponsibleAccountId(), content, "VEHICLE INFO MODIFIED FROM RU");
          }
        }
      }
      model.setId(roundUsed.getId());
    }

    if (Collections.isNotEmpty(archivedIds)) {
      for (Long id : archivedIds) {
        TMSRoundUsed ru = getTMSRoundUsed(client, company, id);
        ru.setStorageState(StorageState.ARCHIVED);
      }
    }
    ruRepo.flush();
    return models;
  }
  
  public TMSVendorBill updateVendorBill(ClientContext client, ICompany company, TMSRoundUsedModel model) {
    if(model.getTmsBillId() == null) return null;
    TMSVendorBill vendorBill = vendorBillLogic.getByTMSBillId(client, company, model.getTmsBillId());
    if(vendorBill == null) return null;
    vendorBill.getBillTrackings().clear();
    vendorBill.getBillTrackings().add(model.convertToVendorBillTracking());
    return vendorBillLogic.saveVendorBill(client, company, vendorBill);
  }

  public List<TMSRoundUsed> requestRoundUsedBill(ClientContext client, ICompany company, Long[] billIds) {
    List<TMSRoundUsed> holder = new ArrayList<>();
    List<TMSBill> tmsBills = billLogic.findTMSBills(client, company, billIds);
    for (TMSBill tmsBill : tmsBills) {
      validateTMSBill(client, company, tmsBill);
      validateContainerType(client, company, tmsBill);
      List<TMSRoundUsed> roundUseds = ruRepo.findByBillId(tmsBill.getId());
      if (Collections.isNotEmpty(roundUseds)) 
        throw new RuntimeError(ErrorType.IllegalState, "TMSBill: {0} is requested", tmsBill.getCode());
      TMSRoundUsed roundUsed = newTMSRoundUsed(client, company, new TMSRoundUsed(tmsBill));
      roundUsed.setStatus(Status.SendingFromBill);
      roundUsed = saveTMSRoundUsed(client, company, roundUsed);
      holder.add(roundUsed);
      if(!generalLogic.autoSendMessage(client, company, TMSRoundUsed.AUTO_SEND_MESSAGE)) {
        continue;
      }
      String content = computeMessage(tmsBill, roundUsed, false);
      HRDepartment hrDepartment = departmentLogic.getHRDepartment(client, company, "RU");
      List<HRDepartmentMembership> memberships = departmentLogic.findByDepartmentId(client, company, hrDepartment.getId());
      List<Long> toAccountIds = new ArrayList<>();
      for (HRDepartmentMembership membership: memberships) {
        Employee employee = employeeLogic.getEmployee(client, company, membership.getEmployeeId());
        toAccountIds.add(employee.getAccountId());
      }
      generalLogic.sentZaloMessageToAccounts(client, company,toAccountIds ,content , "REQUEST RU FROM TMSBILL");
    }
    return holder;
  }

  private String computeMessage(TMSBill tmsBill, TMSRoundUsed roundUsed, boolean isVehicleInfoModified) {
    TMSBillForwarderTransport tmsBillForwarderTransport = tmsBill.getTmsBillForwarderTransport();
    String content = "📢 NOTICE: REQUEST RU";
    if(roundUsed.getStatus() == Status.SendingFromBill) {
      content = "📢 NOTICE: REQUEST RU FROM TMSBILL";
      content += "\nPIC: "+ tmsBill.getResponsibleFullName();
    }
    if(isVehicleInfoModified) {
      content = "📢 NOTICE RU: CHANGED VEHICLE INFO";
    }
    content += "\n🇻🇳 🇻🇳 🇻🇳\n";
    String label   = tmsBill.getLabel();
    String customerFullName = tmsBill.getCustomer().getCustomerFullName() != null ? tmsBill.getCustomer().getCustomerFullName(): "" ;
    content += label + " " + tmsBillForwarderTransport.getModeLabel() + " " + customerFullName;

    Date dateTime  = tmsBill.getDeliveryTime();
    String sDate   = "";
    String sTime   = "";
    if(dateTime != null) {
      sDate = DateUtil.asCompactDate(dateTime);
      sTime = DateUtil.asCompactTimeWithoutSecond(dateTime);
    }
    String senderAddress = tmsBill.getSender().getSenderAddress() == null ? "" : tmsBill.getSender().getSenderAddress();
    String receiverAddress = tmsBill.getReceiver().getReceiverAddress() == null ? "" : tmsBill.getReceiver().getReceiverAddress();
    content += "\n- Date & Time: " + sDate + " " + sTime;
    if(tmsBillForwarderTransport.isExport()) {
      content += "\n- Address: " + senderAddress;
    } else if(tmsBillForwarderTransport.isImport()) {
      content += "\n- Address: " + receiverAddress;
    } else {
      content += "\n- Pickup: " + senderAddress;
      content += "\n- Delivery: " + receiverAddress;
    }
    String description = tmsBill.getDescription() == null ? "": tmsBill.getDescription();
    content += "\n- Bill Note:" + description;

    content += "\n\uD83D\uDE9A \uD83D\uDE9A \uD83D\uDE9A";
    String status = roundUsed.getStatus().getLabel();
    content += "\n- Status: " + status;
    return content;
  }
  
  public void updateRoundUsedWhenModifyBill(ClientContext client, ICompany company, TMSBill bill) {
    if (bill.isNew()) return;
    List<TMSRoundUsed> roundUseds = ruRepo.findByBillId(bill.getId());
    if (roundUseds.size() != 1) return;
    TMSRoundUsed roundUsed = roundUseds.get(0);
    roundUsed.mapRoundUsed(bill);
    roundUsed.setModifiedFileTruck(true);
    saveTMSRoundUsed(client, company, roundUsed);
  }

  public boolean updateTMSRoundUsedStatus(ClientContext client, ICompany company, Long id, Status status) {
    TMSRoundUsed roundUsed = getTMSRoundUsed(client, company, id);
    Objects.assertNotNull(roundUsed, "TMSRoundUsed is not found by id = {0}", id);
    roundUsed.setStatus(status);
    saveTMSRoundUsed(client, company, roundUsed);
    return true;
  }

  public List<SqlMapRecord> searchTMSRoundUsed(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    Account account = accountLogic.getEditable(client, client.getRemoteUser());
    sqlParams.addParam("remoteUserId", account.getId());
    sqlParams.addParam("companyId", company.getId());
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/tms/ru/groovy/TMSRoundUsedSql.groovy";
    List<SqlMapRecord> records = searchDbRecords(client, scriptDir, scriptFile, "SearchTMSRoundUsed", sqlParams);
    Set<Long> deliveryStateIds = new HashSet<>();
    for(SqlMapRecord record: records) {
      deliveryStateIds.add(record.getLong("deliveryStateId"));
    }

    List<State> states =  stateLogic.findStateByIds(client, new ArrayList<>(deliveryStateIds));
    Map<Long, State> stateMap = new HashMap<>();
    for(State state: states) {
      stateMap.put(state.getId(), state);
    }

    for(SqlMapRecord record: records) {
      State state = stateMap.get( record.getLong("deliveryStateId"));
      record.add("deliveryStateId", state.getId());
      record.add("deliveryStateLabel", state.getLabel());
    }
    return records;
  }
  
  public boolean updateStorageState(ClientContext client, ICompany company, ChangeStorageStateRequest req) {
    ruRepo.updateStorageState(req.getEntityIds(), req.getNewStorageState());
    return true;
  }

  public TMSRoundUsed updateJobTrackingId(ClientContext client, ICompany company, Long id, Long jobTrakingId) {
    TMSRoundUsed roundUsed = getTMSRoundUsed(client, company, id);
    roundUsed.setJobTrackingId(jobTrakingId);
    return saveTMSRoundUsed(client, company, roundUsed);
  }

  public boolean deleteRoundUseds(ClientContext client, ICompany company, List<Long> ids) {
    ruRepo.deleteAllById(ids);
    return true;
  }
}