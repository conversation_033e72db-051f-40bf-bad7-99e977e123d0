package cloud.datatp.fleet.vehicle.groovy

import org.springframework.context.ApplicationContext

import jakarta.persistence.Column
import net.datatp.lib.executable.ExecutableContext
import net.datatp.lib.executable.Executor
import net.datatp.module.data.db.ExecutableSqlBuilder
import net.datatp.module.data.db.query.OptionFilter
import net.datatp.security.client.DataScope
import net.datatp.util.ds.MapObject

class VehicleTripGoodsTrackingSql extends Executor{

  public class SearchVehicleTripGoodsTrackingRecentlyChanged extends ExecutableSqlBuilder {

    public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
      MapObject sqlParams  = ctx.getParam("sqlParams");
      String dataScope     = sqlParams.getString("dataScope");
      boolean isOwner      = DataScope.Owner.toString().equals(dataScope);
      
      String query = """
         SELECT 
           goods_tracking.id                                AS id, 
           goods_tracking.version                           AS version, 
           goods_tracking.company_id                        AS company_id,
           goods_tracking.label                             AS label,
           goods_tracking.status                            AS status,
           CASE
             WHEN goods_tracking.status = 'NEED_CONFIRM' THEN 0
             WHEN goods_tracking.status IS NULL          THEN 1
             WHEN goods_tracking.status = 'CONFIRMED'    THEN 1
             ELSE 2
           END                                              AS process_status_index,
           CONCAT(
             goods_tracking.id, '/',
             goods_tracking.version, '/',
             COALESCE(lgc_fleet_vehicle_trip.version, 0)) AS idx
         FROM lgc_fleet_vehicle_trip_goods_tracking goods_tracking
         LEFT JOIN lgc_fleet_vehicle_trip ON lgc_fleet_vehicle_trip.id = goods_tracking.vehicle_trip_id
         LEFT JOIN lgc_tms_bill                     
              ON lgc_tms_bill.id                                       = goods_tracking.tms_bill_id
         LEFT JOIN lgc_tms_bill_forwarder_transport forwarder_transport
              ON forwarder_transport.id              = lgc_tms_bill.tms_bill_forwarder_transport_id
         WHERE
          ${FILTER_BY_OPTION('goods_tracking.storage_state', 'storageState', sqlParams, ['ACTIVE'])}
          ${AND_FILTER_BY_OPTION('goods_tracking.status', 'status', sqlParams)}
          ${AND_FILTER_BY_PARAM('goods_tracking.company_id', 'companyId', sqlParams)}
          ${AND_FILTER_BY_PARAM('forwarder_transport.mode', 'mode', sqlParams)}
          ${isOwner ? "" : "--"} AND goods_tracking.coordinator_account_id IS NULL
          ${AND_FILTER_BY_RANGE('lgc_tms_bill.delivery_plan', 'dateTime', sqlParams)}
         ORDER BY process_status_index, goods_tracking.modified_time DESC
         ${MAX_RETURN(sqlParams)}
      """
      return query;
    }
  }
  
  public class FindVehicleTripGoodsTrackingByTripIds extends ExecutableSqlBuilder {
    public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
      MapObject sqlParams  = ctx.getParam("sqlParams");
      String query = """
          SELECT 
            gt.id                         AS id,
            ltb.id                        AS tms_bill_id,
            ltb.label                     AS tms_bill_label,
            ltb.customer_full_name        AS customer_full_name,
            ltb.sender_location_id        AS sender_location_id,
            ltb.receiver_location_id      AS receiver_location_id,
            ltb.delivery_plan             AS delivery_plan,
            ltbft.container_no            AS container_no,
            ltbft.mode                    AS mode,
            gt.vehicle_trip_id            AS vehicle_trip_id,
            gt.estimate_distance_in_km    AS estimate_distance_in_km,
            gt.transporter_status         AS transporter_status,
            ops.id                        AS ops_id,
            ops.status                    AS ops_status,
            ops.ops_account_id            AS ops_account_id,
            ops.ops_account_full_name     AS ops_account_full_name,
            ops.ops_account_mobile        AS ops_account_mobile,
            ops.identification_no         AS ops_identification_no,
            ops.note                      AS ops_note
          FROM  lgc_fleet_vehicle_trip_goods_tracking gt
          INNER JOIN lgc_tms_bill                     ltb   ON gt.tms_bill_id = ltb.id
          INNER JOIN lgc_tms_bill_forwarder_transport ltbft ON ltb.tms_bill_forwarder_transport_id = ltbft.id
          LEFT JOIN lgc_tms_operations                ops   ON ops.tms_bill_id = gt.tms_bill_id
          WHERE ${FILTER_BY_PARAM('gt.vehicle_trip_id', 'tripIds', sqlParams)}
      """
      return query;
    }
  }
  
  public class FindVehicleTripGoodsTrackingByDate extends ExecutableSqlBuilder {
    public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
      MapObject sqlParams  = ctx.getParam("sqlParams");
      String query = """
          SELECT 
            gt.id                         AS id,
            ltb.id                        AS tms_bill_id,
            ltb.delivery_plan             AS delivery_plan,
            gt.vehicle_trip_id            AS vehicle_trip_id
          FROM  lgc_fleet_vehicle_trip_goods_tracking gt
          INNER JOIN lgc_tms_bill                     ltb   ON gt.tms_bill_id = ltb.id
          LEFT JOIN lgc_fleet_vehicle_trip            trip  ON trip.id        = gt.vehicle_trip_id
          INNER JOIN lgc_tms_bill_forwarder_transport ltbft ON ltb.tms_bill_forwarder_transport_id = ltbft.id
          WHERE 
            ${FILTER_BY_RANGE('ltb.delivery_plan', 'dateTime', sqlParams)}
            ${AND_FILTER_BY_PARAM('gt.company_id', 'companyId', sqlParams)}
            ${AND_FILTER_BY_PARAM('trip.fleet_id', 'fleetId', sqlParams)}
      """
      return query;
    }
  }

  public class SearchVehicleTripGoodsTracking extends ExecutableSqlBuilder {
    private String ENTITY_FIELDS(Boolean moderatorPermission) {
      if(moderatorPermission) 
        return """
          tracking.created_by,
          tracking.created_time,
          tracking.modified_by,
          tracking.modified_time,
        """;
      return "";
    }
    
    private String AND_FILTER_BY_ID_OR_TRIP_ID(MapObject sqlParams) {
      if(sqlParams.getBoolean("searchWithIdOrTripId", false)) {
        return """
            AND (${FILTER_BY_PARAM('id', 'trackingIds', sqlParams)} 
            ${FILTER_BY_PARAM('or vehicle_trip_id', 'vehicleTripIds', sqlParams)}
            )
            """
      } else if (sqlParams.has("vehicleTripIds")) {
        return AND_FILTER_BY_PARAM('vehicle_trip_id', 'vehicleTripIds', sqlParams);
      }
      return "-- AND No filter by field id, trip id"
    }

    public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
      MapObject sqlParams  = ctx.getParam("sqlParams");
      OptionFilter dataScopeFilter = (OptionFilter) sqlParams.get("dataScope");
      String dataScope             = DataScope.Company.toString();
      if(dataScopeFilter != null) dataScope = dataScopeFilter.getSelectOption();
      boolean isOwner              = DataScope.Owner.toString().equals(dataScope);
      boolean isReportQuery        = sqlParams.getBoolean("isReport", false);
      boolean inputDataApp         = sqlParams.getBoolean("inputDataApp", false);
      String  deliveryPlanCol      = !inputDataApp? "lgc_tms_bill.delivery_plan, lgc_tms_bill.time," : "tracking.delivery_plan, tracking.time,";

      Long responsibleAccountId = sqlParams.getLong("clientAccountId", null)
      String goodsTrackingQuery = """
        WITH tracking_group_by_tms_bill AS (
              SELECT
                tms_bill_id                                AS tms_bill_id,
                count(tracking.tms_bill_id)                AS count_bill_id,
                (CASE WHEN COUNT(tracking.tms_bill_id) > 1 
                THEN tracking.tms_bill_id ELSE 0 END)      AS split_index
              FROM lgc_fleet_vehicle_trip_goods_tracking tracking
              GROUP BY tms_bill_id
            ),
            tracking_group_by_vehicle_trip AS (
              SELECT
                vehicle_trip_id                            AS vehicle_trip_id,
                COUNT(tracking.vehicle_trip_id)            AS count_trip_id,
                (CASE
                   WHEN COUNT(tracking.vehicle_trip_id) > 1
                   THEN tracking.vehicle_trip_id
                   ELSE 0 END)                                                          AS combine_index
              FROM lgc_fleet_vehicle_trip_goods_tracking tracking
              WHERE tracking.storage_state = 'ACTIVE'
              GROUP BY tracking.vehicle_trip_id
            ),
        group_trip_by_customer            AS (
              select ltb.customer_id, lfvt.id
              from lgc_fleet_vehicle_trip lfvt 
              inner join lgc_fleet_vehicle_trip_goods_tracking lfvtgt  on lfvtgt.vehicle_trip_id = lfvt.id
              inner join lgc_tms_bill ltb on lfvtgt.tms_bill_id = ltb.id
              where lfvtgt.storage_state = 'ACTIVE' AND lfvtgt.transport_type = 'Nguyên chuyến'
              group by ltb.customer_id, lfvt.id
        )
        SELECT
          ${ENTITY_FIELDS(sqlParams.getBoolean("moderatorPermission", false))}
          ${deliveryPlanCol}
          tracking.transporter_status,
          tracking.transporter_note,
          tracking.pickup_success,
          tracking.delivery_success,
          tracking.storage_state,
          tracking.company_id,
          tracking.weight_unit,
          tracking.coordinator_id,
          tracking.goods,
          tracking.chargeable_weight,
          tracking.pickup_location,
          tracking.file_trucking,
          tracking.description,
          tracking.id,
          tracking.status,
          tracking.estimate_distance_in_km,
          tracking.transport_type,
          tracking.vehicle_type,
          tracking.issue,
          tracking.extra_charge,
          tracking.profit,
          tracking.quantity,
          tracking.code,
          tracking.vehicle_trip_id,
          tracking.delivery_location,
          tracking.quantity_unit,
          tracking.volume,
          tracking.volume_unit,
          tracking.responsible_full_name,
          tracking.total_charge,
          tracking.chargeable_volume,
          tracking.volume_as_text,
          tracking.tms_bill_id,
          tracking.goods_description,
          tracking.coordinator_full_name,
          tracking.coordinator_account_id,
          tracking.label,
          tracking.fixed_charge,
          tracking.weight,
          tracking.extra_cost,
          tracking.fixed_cost,
          tracking.vetc,
          tracking.travel_cost,
          tracking.fuel_cost,
          tracking.driver_salary,
          tracking.total_cost,
          tracking.liters_of_fuel,
          tracking.extra_liters_of_fuel,
          tracking.total_liters_of_fuel,
          tracking.version,
          tracking.pickup_container_location,
          tracking.pickup_container_location_id,
          tracking.return_container_location,
          tracking.return_container_location_id,

          tracking.pickup_contact,
          tracking.pickup_address,
          tracking.pickup_inv_address,
          tracking.pickup_location_id,
          tracking.pickup_partner_address_id,
          tracking.delivery_contact,
          tracking.delivery_address,
          tracking.delivery_inv_address,
          tracking.delivery_location_id,
          tracking.delivery_partner_address_id,
          tracking.type_of_transport,
  
          --lgc_fleet_vehicle.oil_consumption,
          CONCAT(
            tracking.id, '/',
            tracking.version, '/',
            COALESCE(lgc_fleet_vehicle_trip.version, 0))                  AS idx,
    
          --Location
          lgc_tms_bill.label                                              AS bill_label,
          lgc_tms_bill.job_tracking_id                                    AS job_tracking_id,
          lgc_tms_bill.type                                               AS tms_bill_type,
          lgc_tms_bill.responsible_account_id                             AS responsible_account_id,
          lgc_tms_bill.sender_location_id                                 AS sender_location_id,
          lgc_tms_bill.sender_full_name                                   AS sender_full_name,
          lgc_tms_bill.sender_contact                                     AS sender_contact,
          lgc_tms_bill.sender_address                                     AS sender_address,
          lgc_tms_bill.customer_full_name                                 AS customer_full_name,
          lgc_tms_bill.customer_id                                        AS customer_id,
          lgc_tms_bill.customer_mobile                                    AS customer_mobile,
          lgc_tms_bill.customer_address                                   AS customer_address,
          lgc_tms_bill.receiver_location_id                               AS receiver_location_id,
          lgc_tms_bill.receiver_full_name                                 AS receiver_full_name,
          lgc_tms_bill.receiver_contact                                   AS receiver_contact,
          lgc_tms_bill.receiver_address                                   AS receiver_address,
          lgc_tms_bill.office                                             AS office,
          lgc_tms_bill.description                                        AS tms_bill_description,
          lgc_tms_bill.quantity                                           AS tms_bill_quantity,
          lgc_tms_bill.quantity_unit                                      AS tms_bill_quantity_unit,
          lgc_tms_bill.weight                                             AS tms_bill_weight,
          lgc_tms_bill.volume_as_text                                     AS tms_bill_volume_as_text,
          lgc_tms_bill.volume                                             AS tms_bill_volume,
          lgc_tms_bill.booking_date                                       AS tms_booking_date,
          lgc_tms_bill.delivery_plan                                      AS tms_delivery_plan,
          lgc_tms_bill.time                                               AS tms_time,
          CASE
                  WHEN lgc_tms_bill.responsible_account_id = ${responsibleAccountId}
                  THEN TRUE
                  ELSE FALSE END                                          AS is_bill_owner,
          CASE
                  WHEN lgc_tms_bill.label <> tracking.label
                  THEN TRUE
                  ELSE FALSE
                  END                                                     AS confirm_file,
    
          CASE
                  WHEN tracking.status = 'NEED_CONFIRM' THEN 0
                  WHEN tracking.status IS NULL THEN 1
                  WHEN tracking.status = 'CONFIRMED' THEN 1
                  ELSE 2 END                                              AS process_status_number,
          tracking_group_by_tms_bill.count_bill_id                        AS count_bill_id,
          tracking_group_by_tms_bill.split_index                          AS split_index,
    
          lgc_tms_bill_forwarder_transport.mode                           AS mode,
          lgc_tms_bill_forwarder_transport.hwb_no                         AS hbl_no,
          lgc_tms_bill_forwarder_transport.eta_cut_off_time               AS eta_cut_off_time,
          lgc_tms_bill_forwarder_transport.warehouse_id                   AS warehouse_id,
          lgc_tms_bill_forwarder_transport.truck_type                     AS truck_type,
          lgc_tms_bill_forwarder_transport.container_no                   AS container_no,
          lgc_tms_bill_forwarder_transport.seal_no                        AS seal_no,
          lgc_tms_bill_forwarder_transport.booking_code                   AS booking_code,
          lgc_tms_bill_forwarder_transport.carrier_id                     AS carrier_id,
          lgc_tms_bill_forwarder_transport.carrier_full_name              AS carrier_full_name,
          CASE
                WHEN lgc_tms_bill_forwarder_transport.mode = 'EXPORT_FCL' OR
                     lgc_tms_bill_forwarder_transport.mode = 'IMPORT_FCL'
                     THEN '1.FCL'
                WHEN lgc_tms_bill_forwarder_transport.mode = 'IMPORT_LCL' OR
                     lgc_tms_bill_forwarder_transport.mode = 'EXPORT_LCL' OR
                     lgc_tms_bill_forwarder_transport.mode = 'EXPORT_AIR' OR
                     lgc_tms_bill_forwarder_transport.mode = 'IMPORT_AIR'
                     THEN '2.LCL'
                ELSE '3.DOM'
          END                                                              AS group_mode,
          lgc_fleet_vehicle_trip.code                                      AS vehicle_trip_code,
          lgc_fleet_vehicle_trip.two_way                                   AS two_way,
          lgc_fleet_vehicle_trip.transporter_status                        AS trip_transporter_status,
          lgc_fleet_vehicle_trip.mobile                                    AS mobile,
          lgc_fleet_vehicle_trip.route                                     AS route,
          lgc_fleet_vehicle_trip.driver_full_name                          AS driver_full_name,
          lgc_fleet_vehicle_trip.driver_id                                 AS driver_id,
          lgc_fleet_vehicle_trip.identification_no                         AS identification_no,
          lgc_fleet_vehicle_trip.vehicle_label                             AS vehicle_label,
          lgc_fleet_vehicle_trip.trailer_number                            AS trailer_number,
          lgc_fleet_vehicle_trip.vehicle_id                                AS vehicle_id,
          lgc_fleet_vehicle_trip.task_status                               AS task_status,
          lgc_fleet_vehicle_trip.fleet_label                               AS fleet_label,
          lgc_fleet_vehicle_trip.fleet_id                                  AS fleet_id,
          lgc_fleet_vehicle_trip.coordinator_full_name                     AS coordinator_full_name,
          lgc_fleet_vehicle_trip.actual_start_time                         AS actual_start_time,
          lgc_fleet_vehicle_trip.actual_end_time                           AS actual_end_time,
          lgc_fleet_vehicle_trip.plan_start_time                           AS plan_start_time,
          lgc_fleet_vehicle_trip.plan_end_time                             AS plan_end_time,
          lgc_fleet_vehicle_trip.actual_distance_in_km                     AS actual_distance_in_km,
          lgc_fleet_vehicle_trip.delivery_plan                             AS trip_delivery_plan,
          lgc_fleet_vehicle_trip.edit_mode                                 AS edit_mode,
          tracking_group_by_vehicle_trip.count_trip_id,
          tracking_group_by_vehicle_trip.count_trip_id                     AS total_file_on_trip,
          CASE
            WHEN tracking_group_by_vehicle_trip.count_trip_id > 1 THEN true
            ELSE                                                       false
          END                                                              AS combine_file,
          lgc_fleet_vehicle_fleet.gps,
          lgc_fleet_vehicle_fleet.owner_login_id                           AS fleet_owner_login_id,
          lgc_tms_operations.id                                            AS ops_id,
          lgc_tms_operations.status                                        AS ops_status,
          lgc_tms_operations.ops_account_full_name                         AS ops_account_full_name,
          lgc_tms_operations.ops_account_mobile                            AS ops_account_mobile,
          lgc_tms_operations.identification_no                             AS ops_identification_no,
          lgc_tms_vendor_bill.id                                           AS vendor_bill_id,
          lgc_tms_vendor_bill.task_id                                      AS task_id,
          CASE
               WHEN lgc_tms_vendor_bill.last_task_commenter_account_id = ${responsibleAccountId} THEN 'REPLIED'
               WHEN lgc_tms_vendor_bill.task_id IS NULL THEN 'N/A'
               ELSE                                          'NOT_REPLIED'
          END                                                              AS comment_status,
          tms_partner_input_data.auto_push                                 AS allow_push,

          --Fields used sort
          tracking_group_by_vehicle_trip.combine_index,
          CASE
            WHEN lgc_fleet_vehicle_trip.id IS NULL THEN 0
            ELSE 1
          END                                                        AS created_trip_index,
          CASE
            WHEN task_status = 'DONE' THEN 1
            WHEN lgc_fleet_vehicle_trip.vehicle_label IS NULL OR lgc_fleet_vehicle_trip.vehicle_label = '' THEN 0
            ELSE 1
          END                                                        AS license_plate_index,
          CASE
            WHEN task_status IS NULL THEN 0
            WHEN task_status = 'PLAN' THEN 1
            WHEN task_status = 'SUBMITTED_PLAN' THEN 2
            WHEN task_status = 'TRANSPORTING' THEN 3
            WHEN task_status = 'DONE' THEN 4
            ELSE 5
          END                                                        AS task_index,
          CASE
            WHEN lgc_fleet_vehicle_fleet.index IS NULL THEN 10000
            ELSE lgc_fleet_vehicle_fleet.index
          END                                                        AS fleet_index,
          CASE  
            WHEN tracking_group_by_vehicle_trip.count_trip_id > 1 
              THEN CONCAT(
                COALESCE(CAST(gtbc.customer_id AS varchar), 'N/A'),
                COALESCE(
                  CASE
                    WHEN tracking.transport_type = 'Nguyên chuyến' THEN 0
                    ELSE 1 
                  END, 1)
              )
            ELSE lgc_tms_bill.customer_full_name
          END                                                              AS conbine_customer_index,
          --Message fields,
          mm.status                                   AS message_status,
          mm.id                                       AS message_id
    
          FROM lgc_fleet_vehicle_trip_goods_tracking tracking
          LEFT JOIN lgc_tms_bill                     
            ON lgc_tms_bill.id                                  = tracking.tms_bill_id
          LEFT JOIN lgc_tms_vendor_bill              
            ON lgc_tms_vendor_bill.tms_bill_id                  = lgc_tms_bill.id
          LEFT JOIN lgc_tms_operations               
            ON lgc_tms_operations.tms_bill_id                   = tracking.tms_bill_id
          LEFT JOIN lgc_tms_bill_forwarder_transport 
            ON lgc_tms_bill_forwarder_transport.id              = lgc_tms_bill.tms_bill_forwarder_transport_id
          LEFT JOIN lgc_fleet_vehicle_trip           
            ON lgc_fleet_vehicle_trip.id                        = tracking.vehicle_trip_id
          LEFT JOIN lgc_fleet_vehicle                
            ON lgc_fleet_vehicle.id                             = lgc_fleet_vehicle_trip.vehicle_id
          LEFT JOIN lgc_fleet_vehicle_fleet          
            ON lgc_fleet_vehicle_fleet.id                       = lgc_fleet_vehicle_trip.fleet_id
          LEFT JOIN tracking_group_by_tms_bill       
            ON tracking_group_by_tms_bill.tms_bill_id           = tracking.tms_bill_id
          LEFT JOIN tracking_group_by_vehicle_trip   
            ON tracking_group_by_vehicle_trip.vehicle_trip_id   = tracking.vehicle_trip_id
          LEFT JOIN lgc_tms_partner              tms_partner_input_data 
            ON tms_partner_input_data.third_party_code         = lgc_tms_bill.original_customer_code
          LEFT JOIN group_trip_by_customer gtbc
            ON (gtbc.id = tracking.vehicle_trip_id AND gtbc.customer_id = lgc_tms_bill.customer_id)
          LEFT JOIN message_message mm 
           ON mm.id                                             = lgc_tms_bill.message_id
        """;

      String query = """
            SELECT 
              *,
              TO_CHAR(delivery_plan, 'yyyy/MM/dd') AS format_date_time
            FROM (${goodsTrackingQuery}) AS vehicel_trip_goods_tracking
            WHERE
              ${FILTER_BY_OPTION('storage_state', 'storageState', sqlParams, ['ACTIVE'])}
              ${AND_FILTER_BY_PARAM('company_id', 'companyId', sqlParams)}
              ${AND_SEARCH_BY_PARAMS(['bill_label'], 'search', sqlParams)}
              ${AND_FILTER_BY_PARAM('tms_bill_id', 'tmsBillId', sqlParams)}
              ${AND_FILTER_BY_PARAM('vehicle_trip_id', 'vehicleTripId', sqlParams)}
              ${AND_FILTER_BY_PARAM('id', 'id', sqlParams)}
              ${AND_FILTER_BY_PARAM('fleet_id', 'fleetId', sqlParams)}
              ${AND_FILTER_BY_PARAM('id', 'ids', sqlParams)}
              ${AND_FILTER_BY_PARAM('type_of_transport', 'typeOfTransport', sqlParams)}
              ${isOwner ? "" : "--"}AND (coordinator_account_id = :clientAccountId OR responsible_account_id = :clientAccountId)
              ${AND_FILTER_BY_PARAM('tms_bill_id', 'tmsBillIds', sqlParams)}
              ${AND_FILTER_BY_ID_OR_TRIP_ID(sqlParams)}
              ${AND_FILTER_BY_RANGE('delivery_plan', 'dateTime', sqlParams)}
              ${isReportQuery ? "AND vehicle_label IS NOT NULL" : ""}
              ORDER BY 
                ${isReportQuery ? "format_date_time ASC, --" : ""} process_status_number, format_date_time DESC, 
                created_trip_index, license_plate_index,
                fleet_index, task_index, combine_index, conbine_customer_index, tms_bill_weight DESC,
                time, bill_label, mode ASC
              ${MAX_RETURN(sqlParams)}
      """
      return query;
    }
  }

  public VehicleTripGoodsTrackingSql() {
    register(new SearchVehicleTripGoodsTrackingRecentlyChanged());
    register(new SearchVehicleTripGoodsTracking());
    register(new FindVehicleTripGoodsTrackingByTripIds());
    register(new FindVehicleTripGoodsTrackingByDate());
  }
}
