package cloud.datatp.tms.groovy

import org.springframework.context.ApplicationContext

import net.datatp.lib.executable.ExecutableContext;
import net.datatp.lib.executable.Executor;
import net.datatp.module.data.db.ExecutableSqlBuilder;
import net.datatp.util.ds.MapObject

public class TMSSql extends Executor {
  static public class FindTMSBillQuery extends ExecutableSqlBuilder {

    private String AND_FILTER_BY_CARRIER(MapObject sqlParams) {
      String carrier = sqlParams.getString("carrier", null);
      if(carrier == null) {
        return "-- AND_FILTER_BY_CARRIER(sqlParams)"
      }
      return """AND (carrier_full_name = '${carrier}' OR carrier_full_name IS NULL)"""
    }

    public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
      MapObject sqlParams = ctx.getParam("sqlParams");
      String containerType = sqlParams.getString("containerType", null);

      
      String query = """
            SELECT * FROM
              (SELECT
                  lgc_tms_bill.id,
                  lgc_tms_bill.code,
                  lgc_tms_bill.label,
                  lgc_tms_bill.state,
                  lgc_tms_bill.process_status,
                  lgc_tms_bill.type,
                  lgc_tms_bill.time,
                  lgc_tms_bill.description,
                  lgc_tms_bill.office,
                  lgc_tms_bill.container_in_hand_plan_id,
                  lgc_tms_bill.estimate_time,
                  lgc_tms_bill.modified_estimate_time,
                  lgc_tms_bill.delayed_time,
                  lgc_tms_bill.responsible_account_id,
                  lgc_tms_bill.responsible_full_name,
            
                  lgc_tms_bill.storage_state,
                  lgc_tms_bill.company_id,
                  lgc_tms_bill.customer_id,
                  lgc_tms_bill.customer_full_name,
                  lgc_tms_bill.customer_mobile,
                  lgc_tms_bill.customer_address,
            
                  lgc_tms_bill.sender_full_name,
                  lgc_tms_bill.sender_contact,
                  CASE
                    WHEN lgc_tms_bill_forwarder_transport.mode = 'DOMESTIC'   THEN lgc_tms_bill.sender_address
                    WHEN lgc_tms_bill_forwarder_transport.mode = 'EXPORT_FCL' THEN lgc_tms_bill.sender_address
                    WHEN lgc_tms_bill_forwarder_transport.mode = 'EXPORT_LCL' THEN lgc_tms_bill.sender_address
                    WHEN lgc_tms_bill_forwarder_transport.mode = 'EXPORT_AIR' THEN lgc_tms_bill.sender_address
                    ELSE lgc_tms_bill_forwarder_transport.warehouse_label
                  END AS sender_address,
                  lgc_tms_bill.sender_location_id    AS sender_location_id,
            
                  lgc_tms_bill.receiver_full_name    AS receiver_full_name,
                  lgc_tms_bill.receiver_contact      AS receiver_contact,
                  CASE
                    WHEN lgc_tms_bill_forwarder_transport.mode = 'DOMESTIC'   THEN lgc_tms_bill.receiver_address
                    WHEN lgc_tms_bill_forwarder_transport.mode = 'IMPORT_FCL' THEN lgc_tms_bill.receiver_address
                    WHEN lgc_tms_bill_forwarder_transport.mode = 'IMPORT_LCL' THEN lgc_tms_bill.receiver_address
                    WHEN lgc_tms_bill_forwarder_transport.mode = 'IMPORT_AIR' THEN lgc_tms_bill.receiver_address
                    ELSE lgc_tms_bill_forwarder_transport.warehouse_label
                  END                  AS receiver_address,
                  lgc_tms_bill.receiver_location_id  AS receiver_location_id,
            
                  lgc_tms_bill.quantity              AS quantity,
                  lgc_tms_bill.quantity_unit         AS quantity_unit,
                  lgc_tms_bill.volume                AS volume,
                  lgc_tms_bill.chargeable_volume     AS chargeable_volume,
                  lgc_tms_bill.volume_as_text        AS volume_as_text,
                  lgc_tms_bill.volume_unit           AS volume_unit,
                  lgc_tms_bill.weight                AS weight,
                  lgc_tms_bill.chargeable_weight     AS chargeable_weight,
                  lgc_tms_bill.weight_unit           AS weight_unit,
                  lgc_tms_bill.goods_type            AS goods_type,
                  lgc_tms_bill.goods_description     AS goods_description,
            
                  lgc_tms_bill_forwarder_transport.mode                 AS mode,
                  lgc_tms_bill_forwarder_transport.eta_cut_off_time     AS eta_cut_off_time,
                  lgc_tms_bill_forwarder_transport.truck_type           AS truck_type,
                  lgc_tms_bill_forwarder_transport.container_no         AS container_no,
                  lgc_tms_bill_forwarder_transport.seal_no              AS seal_no,
                  lgc_tms_bill_forwarder_transport.warehouse_label      AS warehouse_label,
                  lgc_tms_bill_forwarder_transport.warehouse_id         AS warehouse_id,
                  lgc_tms_bill_forwarder_transport.warehouse_contact    AS warehouse_contact,
                  lgc_tms_bill_forwarder_transport.carrier_full_name    AS carrier_full_name,
                  lgc_tms_bill_forwarder_transport.carrier_id           AS carrier_id,
                  lgc_tms_bill_forwarder_transport.declaration_number   AS declaration_number,
                  lgc_tms_bill_forwarder_transport.booking_code         AS booking_code,
                  lgc_tms_bill_forwarder_transport.hwb_no               AS hwb_no,
                  lgc_tms_bill_forwarder_transport.truck_no             AS truck_no,
                  CASE
                    WHEN process_status = 'PLAN'    THEN 1
                    WHEN process_status = 'PENDING'   THEN 2
                    WHEN process_status = 'PROCESSING'  THEN 3
                    ELSE 4
                  END AS status_number,
                  CASE
                    WHEN lgc_tms_bill_forwarder_transport.mode = 'IMPORT_FCL' OR
                         lgc_tms_bill_forwarder_transport.mode = 'IMPORT_LCL' OR
                         lgc_tms_bill_forwarder_transport.mode = 'IMPORT_AIR'
                    THEN plan_delivery_success_time
                    ELSE plan_pickup_success_time
                  END AS date_time,
                  CASE
                    WHEN lgc_tms_bill_forwarder_transport.mode = 'EXPORT_FCL' THEN 1
                    WHEN lgc_tms_bill_forwarder_transport.mode = 'EXPORT_LCL' THEN 2
                    WHEN lgc_tms_bill_forwarder_transport.mode = 'EXPORT_AIR' THEN 3
                    WHEN lgc_tms_bill_forwarder_transport.mode = 'IMPORT_FCL' THEN 4
                    WHEN lgc_tms_bill_forwarder_transport.mode = 'IMPORT_LCL' THEN 5
                    WHEN lgc_tms_bill_forwarder_transport.mode = 'IMPORT_AIR' THEN 6
                    ELSE 7
                  END AS mode_number,
                  ru.ru_tms_bill_id                                   AS ru_tms_bill_id
              FROM lgc_tms_bill
              LEFT JOIN lgc_tms_bill_forwarder_transport
                   ON lgc_tms_bill_forwarder_transport.id           = lgc_tms_bill.tms_bill_forwarder_transport_id
              LEFT JOIN lgc_tms_round_used ru
                   ON ru.ru_tms_bill_id = lgc_tms_bill.id
              )
            WHERE date_time is not null
                  AND mode = 'IMPORT_FCL'
                  ${AND_FILTER_BY_PARAM('company_id', 'companyId', sqlParams)}  
                  ${AND_SEARCH_BY_PARAMS(['label', 'booking_code'], 'search', sqlParams)}
                  ${AND_FILTER_BY_OPTION('storage_state', 'storageState', sqlParams, ['ACTIVE'])}
                  ${AND_FILTER_BY('truck_type', containerType === null ? null : """'${containerType}'""")}
                  ${AND_FILTER_BY_CARRIER(sqlParams)}
            ORDER BY carrier_full_name, date_time desc, truck_type, mode_number
            ${MAX_RETURN(sqlParams)}
            """
      return query;
    }
  }
  
  static public class FindTMSOperationsByBillId extends ExecutableSqlBuilder {
    public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
      MapObject sqlParams = ctx.getParam("sqlParams");
      
      String query = """
      SELECT
         ops_account_full_name,
         ops_account_mobile ,
         identification_no,
         note,
         status
      FROM lgc_tms_operations 
      WHERE
        ${FILTER_BY_OPTION('storage_state', 'storageState', sqlParams, ['ACTIVE'])}
        ${AND_FILTER_BY_PARAM('company_id', 'companyId', sqlParams)}
        ${AND_FILTER_BY_PARAM('tms_bill_id', 'tmsBillId', sqlParams)}
      LIMIT 100
    """
      return query;
    }
  }
  
  static public class FindTMSBillTrackingIssue extends ExecutableSqlBuilder {
    private FILTER_RECORDS_BY_HANDLER(MapObject sqlParams) {
      boolean filterByHandler = sqlParams.getBoolean("filterByHandler", false);
      if(!filterByHandler) return "--Assert All Records";
      return """
         ${AND_FILTER_BY_PARAM('handlerAccountId', sqlParams)}
      """
    }
    
    public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
      MapObject sqlParams  = ctx.getParam("sqlParams");

      String query = """
        SELECT *,
          CASE WHEN status = 'NEW'        THEN 1
               WHEN status = 'DISCUSSION' THEN 2
               WHEN status = 'CONFIRMED'  THEN 3
               ELSE                            4 END AS status_index
        FROM lgc_tms_bill_tracking_issue  
        WHERE
          ${FILTER_BY_OPTION('storage_state', 'storageState', sqlParams, ['ACTIVE'])}
          ${FILTER_RECORDS_BY_HANDLER(sqlParams)}
          ${AND_FILTER_BY_PARAM('company_id', 'companyId', sqlParams)}
          ${AND_SEARCH_BY_PARAMS(['label', 'customer_full_name'], 'search', sqlParams)}
          ${AND_FILTER_BY_PARAM('tms_bill_id', 'tmsBillId', sqlParams)}
          ${AND_FILTER_BY_PARAM('status', 'status', sqlParams)}
         ORDER BY status_index, date DESC, label, created_time DESC
         ${MAX_RETURN(sqlParams)}
        """
      return query;
    }
  }
  
  public TMSSql() {
    register(new FindTMSBillQuery())
    register(new FindTMSOperationsByBillId())
    register(new FindTMSBillTrackingIssue())
  }
}
