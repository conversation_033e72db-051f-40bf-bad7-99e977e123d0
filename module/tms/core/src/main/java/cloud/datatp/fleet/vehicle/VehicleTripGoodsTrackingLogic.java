package cloud.datatp.fleet.vehicle;

import java.util.*;

import net.datatp.module.account.ProfileLogic;
import net.datatp.module.account.entity.UserProfile;
import net.datatp.module.resource.location.LocationLogic;
import net.datatp.module.resource.location.entity.Location;
import org.codehaus.groovy.util.ListHashMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import cloud.datatp.fleet.vehicle.entity.MessageStatus;
import cloud.datatp.fleet.vehicle.entity.TransporterActivity;
import cloud.datatp.fleet.vehicle.entity.Vehicle;
import cloud.datatp.fleet.vehicle.entity.VehicleFleetCoordinator;
import cloud.datatp.fleet.vehicle.entity.VehicleTrip;
import cloud.datatp.fleet.vehicle.entity.VehicleTripGoodsTracking;
import cloud.datatp.fleet.vehicle.entity.VehicleTripGoodsTracking.TransporterStatus;
import cloud.datatp.fleet.vehicle.entity.VehicleTripGoodsTracking.VehicleTripGoodsTrackingStatus;
import cloud.datatp.fleet.vehicle.entity.VehicleTripGoodsTrackingConfig;
import cloud.datatp.fleet.vehicle.event.TMSBotTrackingSendZaloEvent;
import cloud.datatp.fleet.vehicle.models.BFSFileTruckingConfig;
import cloud.datatp.fleet.vehicle.models.CombineTrackingParams;
import cloud.datatp.fleet.vehicle.models.ConfirmTrackingParams;
import cloud.datatp.fleet.vehicle.models.GoodsTrackingAndTripModel;
import cloud.datatp.fleet.vehicle.models.TransporterTrackingParams;
import cloud.datatp.fleet.vehicle.models.VehicleTripGoodsTrackingModel;
import cloud.datatp.fleet.vehicle.models.VehicleTripGoodsTrackingSplitModel;
import cloud.datatp.fleet.vehicle.models.VehicleTripTrackingModel;
import cloud.datatp.fleet.vehicle.repository.VehicleTripGoodsTrackingConfigRepository;
import cloud.datatp.fleet.vehicle.repository.VehicleTripGoodsTrackingRepository;
import cloud.datatp.gps.GPSConfigLogic;
import cloud.datatp.gps.GPSPluginManager;
import cloud.datatp.gps.entity.GPSConfig;
import cloud.datatp.gps.provider.entity.GPSInfo;
import cloud.datatp.tms.TMSProcessSyncLogic;
import cloud.datatp.tms.bill.TMSBillLogic;
import cloud.datatp.tms.bill.entity.TMSBill;
import cloud.datatp.tms.ops.TMSOperationsLogic;
import jakarta.annotation.PostConstruct;
import lombok.Getter;
import net.datatp.module.account.AccountLogic;
import net.datatp.module.account.entity.Account;
import net.datatp.module.app.AppEnv;
import net.datatp.module.bot.BotEvent.ProcessMode;
import net.datatp.module.bot.BotService;
import net.datatp.module.communication.CommunicationMessageService;
import net.datatp.module.communication.entity.CommunicationAccount;
import net.datatp.module.communication.entity.Message;
import net.datatp.module.communication.entity.MessageDeliverType;
import net.datatp.module.communication.entity.TargetRecipient;
import net.datatp.module.company.CompanyConfigLogic;
import net.datatp.module.company.entity.CompanyConfig;
import net.datatp.module.core.security.SecurityLogic;
import net.datatp.module.data.db.DAOService;
import net.datatp.module.data.db.RecordGroupByMap;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.entity.StorageState;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.data.db.seq.SeqService;
import net.datatp.module.data.db.seq.entity.Sequence;
import net.datatp.module.monitor.SourceType;
import net.datatp.security.client.ClientContext;
import net.datatp.util.dataformat.DataSerializer;
import net.datatp.util.ds.Arrays;
import net.datatp.util.ds.Collections;
import net.datatp.util.ds.MapObject;
import net.datatp.util.error.RuntimeError;
import net.datatp.util.text.DateUtil;
import net.datatp.util.text.StringUtil;

@Component
public class VehicleTripGoodsTrackingLogic extends DAOService {
  @Getter
  @Autowired
  private VehicleTripGoodsTrackingRepository goodsTrackingRepository;
  
  @Autowired
  private VehicleTripGoodsTrackingConfigRepository configRepo;
  
  @Autowired
  private AccountLogic                       accountLogic;

  @Getter
  @Autowired
  private VehicleLogic                       vehicleLogic;

  @Autowired
  private TMSBillLogic                       tmsBillLogic;
  
  @Autowired
  private TMSOperationsLogic                 tmsOperationsLogic;

  @Getter
  @Autowired
  private VehicleFleetLogic                  vehicleFleetLogic;

  @Autowired
  private CommunicationMessageService        messageService;

  @Autowired
  private CompanyConfigLogic                 companyConfigLogic;
  
  @Autowired
  private BotService                        botService;

  @Autowired
  private SeqService                         seqService;

  @Autowired
  private SecurityLogic                      securityLogic;
  
  @Autowired
  private TMSProcessSyncLogic                processSyncLogic;
  
  @Autowired
  private GPSConfigLogic                gpsConfigLogic;

  @Autowired
  private ProfileLogic                  profileLogic;

  @Autowired
  private LocationLogic                 locationLogic;
  
  @Autowired
  private GPSPluginManager              gpsPluginManager;

  @Autowired
  private AppEnv                             appEnv;

  @Value("${app.env:#{null}}")
  private String                             env;

  @PostConstruct
  public void onInit() {
    seqService.createIfNotExists(VehicleTripGoodsTracking.SEQUENCE, 10);
    seqService.createIfNotExists(VehicleTripGoodsTracking.BT_SEQUENCE, 1);
  }
  
  //Congfig

  public boolean hasModeratorPermission(ClientContext client, ICompany company) {
    return securityLogic.hasModeratorPermission(client, company.getId(), "tms", "vehicle-fleets");
  }
  public BFSFileTruckingConfig getBFSFileTruckingConfig(ClientContext client, ICompany company) {
    BFSFileTruckingConfig config = new BFSFileTruckingConfig();
    Sequence sequence = seqService.getSequence(VehicleTripGoodsTracking.BT_SEQUENCE);
    config.setSequence(sequence);
    CompanyConfig companyConfig = companyConfigLogic.getCompanyConfigByCompanyId(client, company.getId());
    String prefix = companyConfig.getAttributeAsString(VehicleTripGoodsTracking.COMPANY_CONFIG_PREFIX_CODE, "BT");
    config.setPrefix(prefix);
    config.genCode();
    return config;
  }
  
  public VehicleTripGoodsTrackingConfig getVehicleTripGoodsTrackingConfig(ClientContext client, ICompany company) {
    return configRepo.getByCompany(company.getId());
  }
  
  public VehicleTripGoodsTrackingConfig saveVehicleTripGoodsTrackingConfig(ClientContext client, ICompany company, VehicleTripGoodsTrackingConfig config) {
    config.set(client, company);
    return configRepo.save(config);
  }

  public VehicleTripGoodsTracking getVehicleTripGoodsTrackingById(ClientContext client, ICompany company, Long id) {
    return goodsTrackingRepository.getById(company.getId(), id);
  }

  public VehicleTripGoodsTracking getVehicleTripGoodsTrackingByCode(ClientContext client, ICompany company, String code) {
    return goodsTrackingRepository.getByCode(company.getId(), code);
  }

  public List<VehicleTripGoodsTracking> findVehicleTripGoodTrackingByTMSBillIds(ClientContext client, ICompany company, List<Long> ids) {
    return goodsTrackingRepository.findByTMSBillIds(ids);
  }
  
  public List<VehicleTripGoodsTracking> findByIds(ClientContext client, ICompany company, List<Long> ids) {
    return goodsTrackingRepository.findByIds(company.getId(),ids);
  }

  public List<VehicleTripGoodsTracking> findVehicleTripGoodTrackingByTMSBillId(ClientContext client, ICompany company, Long id) {
    return goodsTrackingRepository.findByTMSBillId(id);
  }

  public List<VehicleTripGoodsTracking> findByVehicleTripId(ClientContext client, ICompany company, Long id) {
    return goodsTrackingRepository.findByVehicleTripId(id);
  }

  public  List<VehicleTripGoodsTracking> findByCompanyId(ClientContext client, ICompany company) {
    return goodsTrackingRepository.findByCompanyId(company.getId());
  }

  public boolean deleteVehicleTripGoodsTrackings(ClientContext client, ICompany company, List<Long> ids) {
    List<VehicleTripGoodsTracking> trackings = goodsTrackingRepository.findByIds(company.getId(), ids);
    List<Long> tmsBillIds = new ArrayList<>();
    for(VehicleTripGoodsTracking tracking : trackings) {
      tmsBillIds.add(tracking.getTmsBillId());
    }
    tmsBillLogic.deleteTMSBillOwnerByIds(client, company, tmsBillIds);
    goodsTrackingRepository.deleteAllById(ids);
    return true;
  }
  
  public VehicleTripGoodsTracking processTransporterTrackingBill(ClientContext client, ICompany company, TransporterTrackingParams params) {
    VehicleTripGoodsTracking tracking = getVehicleTripGoodsTrackingById(client, company, params.getId());
    TransporterActivity transporterActivity = tracking.getTransporterActivity();
    transporterActivity.setTransporterStatus(params.getTransporterStatus());
    if(TransporterStatus.REJECT.equals(params.getTransporterStatus())) {
      transporterActivity.setTransporterNote(params.getTransporterNote());
    }
    if(TransporterStatus.PICKUP_SUCCESS.equals(params.getTransporterStatus())) {
      transporterActivity.setPickupSuccess(params.getDate());
    }
    if(TransporterStatus.DELIVERY_SUCCESS.equals(params.getTransporterStatus())) {
      transporterActivity.setDeliverySuccess(params.getDate());
    }
    //clear status
    if(params.getTransporterStatus() == null) {
      transporterActivity.setTransporterNote(null);
      transporterActivity.setPickupSuccess(null);
      transporterActivity.setDeliverySuccess(null);
      transporterActivity.setTransporterStatus(null);
    }
    return saveVehicleTripGoodsTracking(client, company, tracking);
  }
  
  public MapObject findTrackingInfoByTMSBillId(ClientContext client, ICompany company, Long tmsBillId) {
    List<SqlMapRecord> trackings = findVehicleGoodsTrackingInfoByBillId(client, company, tmsBillId);
    List<SqlMapRecord> ops       = tmsOperationsLogic.findTMSOperationsByBillId(client, company, tmsBillId);
    MapObject result = new MapObject("trackings", trackings, "ops", ops);
    List<GPSInfo> gpsInfos = findVehicleLocationByTMSBillId(client, company, tmsBillId);
    result.put("gps", gpsInfos);
    return result;
  }
  
  private List<GPSInfo> findVehicleLocationByTMSBillId(ClientContext client, ICompany company, Long tmsBillId) {
    List<VehicleTripGoodsTracking> goodsTrackings = findVehicleTripGoodTrackingByTMSBillId(client, company, tmsBillId);
    List<Long> ids = new ArrayList<>();
    for(VehicleTripGoodsTracking goodsTracking : goodsTrackings) {
      ids.add(goodsTracking.getVehicleTripId());
    }
    List<VehicleTrip> trips = vehicleLogic.findVehicleTripByIds(client, company, ids);
    
    Map<Long, List<String>> gpsMap = new LinkedHashMap<>();
    for(VehicleTrip trip : trips) {
      if(trip.getVehicleId() == null) continue;
      Vehicle vehicle = vehicleLogic.getVehicleById(client, company, trip.getVehicleId());
      if(vehicle == null || vehicle.getGpsConfigId() == null) continue;
      if(gpsMap.containsKey(vehicle.getGpsConfigId())) {
        gpsMap.get(vehicle.getGpsConfigId()).add(vehicle.getLicensePlate());
      } else {
        List<String> plates = Arrays.asList(vehicle.getLicensePlate());
        gpsMap.put(vehicle.getGpsConfigId(), plates);
      }
    }
    
    List<GPSInfo> infos   = new ArrayList<>();
    for(Long gpsConfigId : gpsMap.keySet()) {
      List<String> plates = gpsMap.get(gpsConfigId);
      GPSConfig gpsConfig = gpsConfigLogic.getGPSConfig(client, company, gpsConfigId);
      List<GPSInfo> selInfos = gpsPluginManager.getVehicleLocationByPlates(gpsConfig, plates);
      infos.addAll(selInfos);
    }
    return infos;
  }
  
  public List<SqlMapRecord> findVehicleGoodsTrackingInfoByBillId(ClientContext client, ICompany company, Object tmsBillId) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fleet/vehicle/groovy/VehicleSql.groovy";

    SqlQueryParams params = new SqlQueryParams();
    params.addSearchTerm("search", null);
    params.addParam("companyId", company.getId());
    params.addParam("tmsBillId", tmsBillId);
    return searchDbRecords(client, scriptDir, scriptFile, "FindVehicleGoodsTrackingByBillId", params);
  }
  
  public GoodsTrackingAndTripModel findPodAndVehicleTrip(ClientContext client, ICompany company, Long trackingId) {
    GoodsTrackingAndTripModel model = new GoodsTrackingAndTripModel();
    VehicleTripGoodsTracking tracking = getVehicleTripGoodsTrackingById(client, company, trackingId);
    Long vehicleTripId = tracking.getVehicleTripId();
    List<VehicleTripGoodsTracking> trackings = new ArrayList<>();
    List<SqlMapRecord> tmsBills = new ArrayList<>();
    if(vehicleTripId != null) {
      VehicleTrip vehicleTrip = vehicleLogic.getVehicleTrip(client, company, vehicleTripId);
      model.setVehicleTrip(vehicleTrip);
      trackings = findByVehicleTripId(client, company, vehicleTripId);

      Set<Long> tmsBillIds = new HashSet<>();
      for(VehicleTripGoodsTracking sel : trackings) {
        tmsBillIds.add(sel.getTmsBillId());
      }
      SqlQueryParams params =  new SqlQueryParams();
      params.addParam("ids", tmsBillIds);
      tmsBills = tmsBillLogic.searchTMSBills(client, company, params);
    } else {
      trackings.add(tracking);
      VehicleTrip newTrip = vehicleLogic.newVehicleTrip(client, company, trackingId);
      model.setVehicleTrip(newTrip);

      SqlQueryParams params =  new SqlQueryParams();
      params.addParam("ids", Arrays.addToList(new ArrayList<>(), tracking.getTmsBillId()));
      tmsBills = tmsBillLogic.searchTMSBills(client, company, params);
    }
    model.setTrackings(trackings);
    model.setTmsBills(tmsBills);
    return model;
  }

  public GoodsTrackingAndTripModel saveVehicleTripGoodsTrackingAndTrip(ClientContext client, ICompany company, GoodsTrackingAndTripModel model) {
    VehicleTrip trip = model.getVehicleTrip();
    trip = vehicleLogic.saveVehicleTrip(client, company, trip);
    model.setVehicleTrip(trip);

     List<VehicleTripGoodsTracking> trackings = model.getTrackings();
     for(VehicleTripGoodsTracking tracking : trackings) {
       tracking.setVehicleTripId(trip.getId());
       tracking = saveVehicleTripGoodsTracking(client, company, tracking);
     }
     model.setTrackings(trackings);
    return model;
  }
  
  private void onPostSave(ClientContext client, ICompany company, VehicleTripGoodsTracking tracking) {
    Long [] ids = new Long[] {tracking.getTmsBillId()};
    processSyncLogic.syncVendorBillWithVehicleTripGoodsTrackings(client, company, ids);
  }
  
  public VehicleTripGoodsTracking saveVehicleTripGoodsTrackingAndSyncVendorBill(ClientContext client, ICompany company, VehicleTripGoodsTracking tracking) {
    if(tracking.isNew()) {
      VehicleTripGoodsTracking checkExistTraking = getVehicleTripGoodsTrackingByCode(client, company, tracking.getCode());
      if(checkExistTraking != null) tracking.genCodeWithDateTimeId(company.getCode());
    }
    tracking.updateSellingAndCosting();
    onPostSave(client, company, tracking);
    tracking.set(client, company);
    return goodsTrackingRepository.save(tracking);
  }
  
  public VehicleTripGoodsTracking saveVehicleTripGoodsTracking(ClientContext client, ICompany company, VehicleTripGoodsTracking tracking) {
    if(tracking.isNew()) {
      VehicleTripGoodsTracking checkExistTraking = getVehicleTripGoodsTrackingByCode(client, company, tracking.getCode());
      if(checkExistTraking != null) tracking.genCodeWithDateTimeId(company.getCode());
    }
    tracking.updateSellingAndCosting();
    tracking.set(client, company);
    return goodsTrackingRepository.save(tracking);
  }

  public List<VehicleTripGoodsTracking> createVehicleTripGoodsTrackings(ClientContext client, ICompany company, List<TMSBill> tmsBills, boolean combineTrip) {
    if(Collections.isEmpty(tmsBills)) return null;
    List<VehicleTripGoodsTracking> trackings = new ArrayList<>();
    for(TMSBill bill : tmsBills) {
      List<VehicleTripGoodsTracking> trackingsDb = findVehicleTripGoodTrackingByTMSBillId(client, company, bill.getId());
      VehicleTripGoodsTracking tracking = bill.toVehicleTripGoodsTracking();
      if(Collections.isNotEmpty(trackingsDb)) {
        for(VehicleTripGoodsTracking trackingDb : trackingsDb) {
          trackingDb.setStatus(VehicleTripGoodsTrackingStatus.NEED_CONFIRM);
          saveVehicleTripGoodsTracking(client, company, trackingDb);
        }
      } else {
        saveVehicleTripGoodsTracking(client, company, tracking);
        trackings.add(tracking);
      }
    }
    if(combineTrip && trackings.size() > 1) {
      VehicleTripGoodsTracking tracking = trackings.get(0);
      TMSBill tmsBill = tmsBillLogic.getTMSBillById(client, company, tracking.getTmsBillId());
      VehicleTrip trip = new VehicleTrip().createLocationTripInfo(tmsBill, tracking);
      trip.setLabel(tracking.getLabel());
      trip.setCode("VT" + DateUtil.asCompactDateId(new Date()) + "/" + String.format("%04d", seqService.nextSequence(VehicleTrip.SEQUENCE)));
      trip = vehicleLogic.saveVehicleTrip(client, company, trip);
      for(VehicleTripGoodsTracking goodsTracking: trackings) {
        goodsTracking.setVehicleTripId(trip.getId());
        saveVehicleTripGoodsTracking(client, company, tracking);
      }
    }
    return trackings;
  }
  
  public VehicleTripGoodsTracking createVehicleTripGoodsTracking(ClientContext client, ICompany company, TMSBill tmsBill) {
    if(tmsBill == null) return null;
      List<VehicleTripGoodsTracking> trackingsDb = findVehicleTripGoodTrackingByTMSBillId(client, company, tmsBill.getId());
      if(Collections.isNotEmpty(trackingsDb)) {
        for(VehicleTripGoodsTracking tracking : trackingsDb) {
          tracking.setStatus(VehicleTripGoodsTrackingStatus.NEED_CONFIRM);
          tracking = saveVehicleTripGoodsTracking(client, company, tracking);
        }
        return trackingsDb.get(0);
      }
      VehicleTripGoodsTracking tracking = tmsBill.toVehicleTripGoodsTracking();
      tracking = saveVehicleTripGoodsTracking(client, company, tracking);
    return tracking;
  }
  
  public List<VehicleTripGoodsTracking> updateFileTruckings(ClientContext client, ICompany company, BFSFileTruckingConfig config, List<VehicleTripGoodsTrackingModel> models) {
    Sequence sequence = seqService.saveSequence(config.getSequence());
    config.setSequence(sequence);
    config.genCode();
    
    List<VehicleTripGoodsTracking> trackings = new ArrayList<>();
    Account account = accountLogic.getEditable(client, client.getRemoteUser());
    VehicleFleetCoordinator coordinator = vehicleFleetLogic.getVehicleFleetCoordinatorByAccountId(client, company, account.getId());
    for(VehicleTripGoodsTrackingModel model : models) {
      VehicleTripGoodsTracking tracking = null;
      if(model.getId() != null) {
        tracking = getVehicleTripGoodsTrackingById(client, company, model.getId());
        tracking.setFileTrucking(model.getFileTrucking());
      } else {
        tracking = new VehicleTripGoodsTracking();
        tracking.genCode(seqService.nextSequence(VehicleTripGoodsTracking.SEQUENCE), company.getCode());
        tracking.setStatus(VehicleTripGoodsTrackingStatus.CONFIRMED);
        if(StringUtil.isBlank(model.getResponsibleFullName())) {
          tracking.setResponsibleAccountId(account.getId());
          tracking.setResponsibleFullName(account.getFullName());
        }
        if(coordinator != null) {
          tracking.setCoordinatorFullName(coordinator.getLabel());
          tracking.setCoordinatorId(coordinator.getId());
        }
        tracking.setFileTrucking(model.getFileTrucking());
      }
      tracking = saveVehicleTripGoodsTracking(client, company, tracking);
      trackings.add(tracking);
    }
    return trackings;
  }

  public List<VehicleTripGoodsTrackingModel> saveVehicleTripGoodsTrackingModels(ClientContext client, ICompany company, List<VehicleTripGoodsTrackingModel> models) {
    Account account = accountLogic.getEditable(client, client.getRemoteUser());
    Map<Long, String> uikeyMap = new ListHashMap<>();
    List<VehicleTripGoodsTrackingModel> updateRecords = new ArrayList<>();
    for(VehicleTripGoodsTrackingModel sel : models) {
      if(sel.isDeleted()) {
        if(sel.getId() == null || sel.getTmsBillId() == null) continue;
        VehicleTripGoodsTracking tracking = getVehicleTripGoodsTrackingById(client, company, sel.getId());
        TMSBill bill = tmsBillLogic.getTMSBillById(client, company, tracking.getTmsBillId());
        tracking.setStorageState(StorageState.ARCHIVED);
        tracking.setTmsBillId(null);
        tracking.setDescription("Remove with TMS Bill Id " + bill.getId() + "/" + bill.getLabel());
        tracking = saveVehicleTripGoodsTracking(client, company, tracking);
        if(account.getId().equals(bill.getResponsibleAccountId())) {
          bill.setStorageState(StorageState.ARCHIVED);
          tmsBillLogic.saveTMSBill(client, company, bill);
        }
        continue;
      }

      VehicleTripGoodsTracking tracking;
      VehicleFleetCoordinator coordinator = vehicleFleetLogic.getVehicleFleetCoordinatorByAccountId(client, company, account.getId());
      if(sel.getId() != null) {
        tracking = getVehicleTripGoodsTrackingById(client, company, sel.getId());
      } else {
        tracking = new VehicleTripGoodsTracking();
        tracking.genCode(seqService.nextSequence(VehicleTripGoodsTracking.SEQUENCE), company.getCode());
        tracking.setStatus(VehicleTripGoodsTrackingStatus.NEED_CONFIRM);
        tracking.setResponsibleAccountId(account.getId());
        tracking.setResponsibleFullName(account.getFullName());
        tracking.setStatus(VehicleTripGoodsTrackingStatus.CONFIRMED);
        if(coordinator != null) {
          tracking.withCoordinator(account);
        }
       if(sel.getStatus() ==  null) sel.setStatus(tracking.getStatus());
      }

      //update bill
      if(sel.isUpdateTMSBill()) {
        Long tmsBillId = sel.getTmsBillId();
        TMSBill bill = new TMSBill();
        if(tmsBillId != null) {
          bill = tmsBillLogic.getTMSBillById(client, company, tmsBillId);
          bill = sel.updateTMSBill(bill);
        } else {
          bill = sel.createTMSBill(account);
          bill.genCode(company.getCode(), seqService.nextSequence(TMSBill.SEQUENCE));
        }
        bill = tmsBillLogic.saveTMSBill(client, company, bill);
        sel.setTmsBillId(bill.getId());
        
        tracking.setDeliveryPlan(sel.getDeliveryPlan());
        tracking.setTime(sel.getTime());
        
        
      }
      tracking = sel.merge(tracking);

      //update trip
      if(sel.isUpdateVehicleTrip()) {
        VehicleTrip trip = new VehicleTrip();
        if(sel.getVehicleTripId() != null) {
          trip = vehicleLogic.getVehicleTrip(client, company, sel.getVehicleTripId());
          if(trip == null) throw RuntimeError.UnknownError("Trip is null {0}", DataSerializer.JSON.toString(sel));
          List<VehicleTripGoodsTracking> trackings = findByVehicleTripId(client, company, trip.getId());
          for(VehicleTripGoodsTracking tra :trackings) {
            if(!tra.getId().equals(tracking.getId())) uikeyMap.put(tra.getId(), tra.getId().toString());
          }
        } else {
          trip.genCode(seqService.nextSequence(VehicleTrip.SEQUENCE));
          trip.setLabel(sel.getBillLabel());
          trip = sel.createVehicleTrip(trip).updateRouteLabel();
        }
        trip = sel.updateVehicleTrip(trip);
        trip = vehicleLogic.saveVehicleTrip(client, company, trip);
        tracking.setVehicleTripId(trip.getId());
      }
      if(coordinator != null || sel.isUpdateVehicleTrip()) tracking.withCoordinator(account);
      tracking = saveVehicleTripGoodsTracking(client, company, tracking);
      sel.setId(tracking.getId());
      updateRecords.add(sel);
    }
    TMSBotTrackingSendZaloEvent botEvent =  new TMSBotTrackingSendZaloEvent(client, company, updateRecords, ProcessMode.Queueable);
    botService.broadcast(SourceType.UserBot, botEvent);
    return updateRecords;
  }

  public List<SqlMapRecord> searchVehicleTripGoodsTrackingsRecentlyChanged(ClientContext client, ICompany company, SqlQueryParams params) {
    Account account = accountLogic.getEditable(client, client.getRemoteUser());
    params.addParam("clientAccountId", account.getId());
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fleet/vehicle/groovy/VehicleTripGoodsTrackingSql.groovy";
    params.addParam("companyId", company.getId());
    return searchDbRecords(client, scriptDir, scriptFile, "SearchVehicleTripGoodsTrackingRecentlyChanged", params);
  }

  private RecordGroupByMap<Long, SqlMapRecord> findStopLocationsByTMSBillIds(ClientContext client, ICompany company, List<Long> TMSBillIds) {
    SqlQueryParams params = new SqlQueryParams();
    params.addParam("tmsBillIds", TMSBillIds);

    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/tms/groovy/TMSBillSql.groovy";
    List<SqlMapRecord> stopLocations = searchDbRecords(client, scriptDir, scriptFile, "FindStopLocationsByTMSBillIds", params);
    return new RecordGroupByMap<>(stopLocations, stopLocation -> stopLocation.getLong("tmsBillId"));
  }

  public List<SqlMapRecord> searchVehicleTripGoodsTrackings(ClientContext client, ICompany company, SqlQueryParams params) {
    Account account = accountLogic.getEditable(client, client.getRemoteUser());
    params.addParam("clientAccountId", account.getId());
//    params.addParam("moderatorPermission", hasModeratorPermission(client, company));
    params.addParam("companyId", company.getId());
    String scriptDir  = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fleet/vehicle/groovy/VehicleTripGoodsTrackingSql.groovy";
    List<SqlMapRecord> records =  searchDbRecords(client, scriptDir, scriptFile, "SearchVehicleTripGoodsTracking", params);
    List<Long> tmsBillIds = new ArrayList<>();
    Set<Long> reponsibleAccountIds = new HashSet<>();
    Set<Long> locationIds = new HashSet<>();
    for(SqlMapRecord rec : records) {
      tmsBillIds.add(rec.getLong("tmsBillId"));
      reponsibleAccountIds.add(rec.getLong("responsibleAccountId"));
      Long  senderLocationId = rec.getLong("senderLocationId");
      Long receiverLocationId = rec.getLong("receiverLocationId");
      if(senderLocationId != null) locationIds.add(senderLocationId);
      if(receiverLocationId != null) locationIds.add(receiverLocationId);
    }
    if(Collections.isNotEmpty(records)) {
      RecordGroupByMap<Long, SqlMapRecord> stopLocationMap = findStopLocationsByTMSBillIds(client, company,
        tmsBillIds);
      Map<Long, UserProfile> userProfileMap = profileLogic.findMapUserProfileByAccountIds(client, new ArrayList<>(reponsibleAccountIds));
      Map<Long, Location> locationMap = locationLogic.findMapLocationByIds(client,  new ArrayList<>(locationIds));

      for(SqlMapRecord rec : records) {
        if( rec.getLong("tmsBillId") != null) {
          rec.add("stopLocations", stopLocationMap.get(rec.getLong("tmsBillId")));
        }

        Location senderLocation = locationMap.get(rec.getLong("senderLocationId"));
        if(senderLocation != null) {
          rec.add("senderLocationShortLabel", senderLocation.getShortLabel());
          rec.add("senderLocationAddress", senderLocation.getAddress());
        }

        Location receiverLocation = locationMap.get(rec.getLong("receiverLocationId"));
        if(receiverLocation != null) {
          rec.add("receiverLocationShortLabel", receiverLocation.getShortLabel());
          rec.add("receiverLocationAddress", receiverLocation.getAddress());
        }

        UserProfile userProfile = userProfileMap.get(rec.getLong("responsibleAccountId"));
        if(userProfile != null) {
          if(StringUtil.isNotBlank(userProfile.getNickname())) {
            rec.add("userName",userProfile.getNickname() );
          } else {
            rec.add("userName",userProfile.getFullName() );
          }
        }
      }
    }
    return records;
  }
  public <T> List<T> searchVehicleTripGoodsTrackings(ClientContext client, ICompany company, SqlQueryParams params, Class<T> className) {
    Account account = accountLogic.getEditable(client, client.getRemoteUser());
    params.addParam("clientAccountId", account.getId());
//    params.addParam("moderatorPermission", hasModeratorPermission(client, company));
    params.addParam("companyId", company.getId());
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fleet/vehicle/groovy/VehicleTripGoodsTrackingSql.groovy";
    return searchDbRecords(client, scriptDir, scriptFile, "SearchVehicleTripGoodsTracking", params, className);
  }

  public VehicleTripGoodsTracking removeVehicleTrip(ClientContext client, ICompany company, Long trackingId) {
    VehicleTripGoodsTracking tracking = getVehicleTripGoodsTrackingById(client, company, trackingId);
    Long tripId = tracking.getVehicleTripId();
    List<VehicleTripGoodsTracking> trackings = findByVehicleTripId(client, company, tripId);
    trackings.removeIf(sel -> sel.getId().equals(trackingId));
    if(Collections.isEmpty(trackings)) vehicleLogic.deleteVehicleTripById(client, company, tripId);
    tracking.setVehicleTripId(null);
    tracking.setTransportType(null);
    tracking = saveVehicleTripGoodsTracking(client, company, tracking);
    return tracking;
  }

  public  List<VehicleTripGoodsTrackingModel> splitVehicleTripGoodsTracking(ClientContext client, ICompany company, VehicleTripGoodsTrackingSplitModel model) {
    List<VehicleTripGoodsTrackingModel> splitTrackingModes = model.getSplitGoodsTrackingsMerged();
    List<VehicleTripGoodsTrackingModel> records = saveVehicleTripGoodsTrackingModels(client, company, splitTrackingModes);
    return records;
  }

  public VehicleTripTrackingModel createVehicleTripForTracking(ClientContext client, ICompany company, VehicleTripTrackingModel model) {
    Account account = accountLogic.getEditable(client, client.getRemoteUser());
    VehicleTrip trip = model.getVehicleTrip();
    if(trip != null) {
      trip = vehicleLogic.saveVehicleTrip(client, company, trip);
      vehicleLogic.flushRepositories();
    } else {
      trip = vehicleLogic.getVehicleTrip(client, company, model.getVehicleTripId());
    }

    List<VehicleTripGoodsTracking> trackings = new ArrayList<>();

    for(Long id : model.getTrackingIds()) {
      VehicleTripGoodsTracking trackingDb = getVehicleTripGoodsTrackingById(client, company, id);
      trackingDb.setCoordinatorFullName(account.getFullName());
      trackingDb.setCoordinatorAccountId(account.getId());
      trackingDb.setVehicleTripId(trip.getId());
      trackingDb = saveVehicleTripGoodsTracking(client, company, trackingDb);
      trackings.add(trackingDb);
    }
    model.setVehicleTrip(trip);
    model.setTrackingResults(trackings);
    return model;
  }

  public List<VehicleTripGoodsTracking> combineVehicleTripGoodsTrackings(ClientContext client, ICompany company, CombineTrackingParams params) {
    VehicleTrip trip;
    VehicleTripGoodsTrackingModel mainGoodsTracking = params.getMainGoodsTracking();
    Long mainGoodsTrackingId = mainGoodsTracking.getId();
    if(mainGoodsTracking.getVehicleTripId() == null) {
      trip = vehicleLogic.newVehicleTrip(client, company, mainGoodsTrackingId);
      trip = vehicleLogic.saveVehicleTrip(client, company, trip);
    } else {
      trip = vehicleLogic.getVehicleTrip(client, company, mainGoodsTracking.getVehicleTripId());
    }
    
    List<VehicleTripGoodsTrackingModel>     combineTrackings  = params.getCombineTrackings();
    Optional<VehicleTripGoodsTrackingModel> maxWeightTracking = combineTrackings.stream().max(Comparator.comparingDouble(VehicleTripGoodsTrackingModel::getWeight));
    maxWeightTracking.get().getId();

    Account                        account    = accountLogic.getEditable(client, client.getRemoteUser());
    List<Long>                     tmsBillIds = new ArrayList<>();
    List<VehicleTripGoodsTracking> trackings  = new ArrayList<>();
    List<VehicleTripGoodsTrackingModel> holderNotificePICGoodsTrackings = new ArrayList<>();
    for(VehicleTripGoodsTrackingModel trackingModel : combineTrackings) {
      VehicleTripGoodsTracking trackingDb = getVehicleTripGoodsTrackingById(client, company, trackingModel.getId());
      if(trackingDb.getTransportType() == null) {
        if(trackingDb.getId().equals(maxWeightTracking.get().getId())) {
          trackingDb.setTransportType("Nguyên chuyến");
        } else {
          trackingDb.setTransportType("Kết hợp");
        }
      }
      trackingDb.withCoordinator(account);
      trackingDb.setVehicleTripId(trip.getId());
      trackingDb = saveVehicleTripGoodsTracking(client, company, trackingDb);
      trackings.add(trackingDb);
      
      if(!trackingDb.getId().equals(mainGoodsTrackingId)) {
        trackingModel.mergeVehicleTrip(trip);
        holderNotificePICGoodsTrackings.add(trackingModel);
        trackingModel.setTotalFileOnTrip(combineTrackings.size());
        tmsBillIds.add(trackingDb.getTmsBillId());
      }
    }
    boolean updateTrip = false;
    if(!MessageStatus.UNSENT.equals(trip.getMessageStatus())) {
      updateTrip = true;
      trip.setMessageStatus(MessageStatus.UNSENT);
    }
    if(updateTrip) trip = vehicleLogic.saveVehicleTrip(client, company, trip);
    
    processSyncLogic.syncVendorBillWithVehicleTripGoodsTrackings(client, company, tmsBillIds.toArray(new Long[tmsBillIds.size()]));
    TMSBotTrackingSendZaloEvent botEvent = new TMSBotTrackingSendZaloEvent(client, company, holderNotificePICGoodsTrackings, ProcessMode.Queueable);
    botService.broadcast(SourceType.UserBot, botEvent);
    return trackings;
  }

  
  public VehicleTripGoodsTracking confirmVehicleTripTracking(ClientContext client, ICompany company, ConfirmTrackingParams params) {
    VehicleTripGoodsTracking tracking = getVehicleTripGoodsTrackingById(client, company, params.getTrackingId());
    TMSBill bill = tmsBillLogic.getTMSBillById(client, company, tracking.getTmsBillId());
    Account account = accountLogic.getEditable(client, client.getRemoteUser());
    tracking.withCoordinator(account);
    tracking.setStatus(params.getStatus());
    if(params.getStatus().equals(VehicleTripGoodsTrackingStatus.REJECT)) {
      TransporterActivity transporterActivity = tracking.getTransporterActivity();
      transporterActivity.setIssue(params.getIssue());
    }
    tracking = saveVehicleTripGoodsTracking(client, company, tracking);
    
    VehicleTripGoodsTrackingConfig config = getVehicleTripGoodsTrackingConfig(client, company);
    if(config == null || !config.isAutoForwardMessage() || account.getId().equals(tracking.getResponsibleAccountId())) return tracking;
    Account       receiverAccount = accountLogic.getAccountById(client, tracking.getResponsibleAccountId());
    CommunicationAccount receiver = messageService.getCommunicationAccount(client, receiverAccount.getLoginId());
    
    Message message = new Message(account.getLoginId());
    message.setSubject(
        "[" + params.getStatus() + "] " + account.getFullName() + ": " +
            bill.getLabel() + " " + bill.getCustomer().getCustomerFullName()
        );
    if(params.getIssue() != null) message.setSubject(message.getSubject() + ", " + params.getIssue());
    message.setContent(
        params.getStatus() + ": " + bill.getLabel() +
        appendContent("Customer", bill.getCustomer().getCustomerFullName()) +
        appendContent("Booking", bill.getTmsBillForwarderTransport().getBookingCode()) +
        appendContent("HWB No", bill.getTmsBillForwarderTransport().getHwbNo())
        );
    
    TargetRecipient targetRecipient = new TargetRecipient(MessageDeliverType.Private, receiver.getLoginId(), receiver.getFullName());
    targetRecipient.setForwardEmail(receiver.isAutoForward());
    message.withRecipient(targetRecipient);
    messageService.sendMessage(client, company, message);
    return tracking;
  };
  
  private String appendContent(String name, String label) {
    if(label == null) return "";
    return  "<div>" + name + ": " + label + "</div>";
  }
  
  public List<VehicleTripGoodsTracking> confirmVehicleTripGoodsTrackings(ClientContext client, ICompany company, List<Long> ids) {
    List<VehicleTripGoodsTracking> trackings = goodsTrackingRepository.findByIds(company.getId(), ids);
    Account account = accountLogic.getEditable(client, client.getRemoteUser());
    for(VehicleTripGoodsTracking tracking : trackings) {
      tracking.setCoordinatorAccountId(account.getId());
      tracking.setCoordinatorFullName(account.getFullName());
      tracking.setStatus(VehicleTripGoodsTrackingStatus.CONFIRMED);
      tracking.set(client, company);
    }
    trackings = goodsTrackingRepository.saveAll(trackings);
    return trackings;
  };
}