package cloud.datatp.fleet.vehicle;

import java.util.*;

import net.datatp.module.resource.location.LocationLogic;
import net.datatp.module.resource.location.entity.Location;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cloud.datatp.fleet.fuel.FuelPriceLogic;
import cloud.datatp.fleet.fuel.entity.FuelPrice;
import cloud.datatp.fleet.vehicle.entity.Vehicle;
import cloud.datatp.fleet.vehicle.entity.VehicleTrip;
import cloud.datatp.fleet.vehicle.entity.VehicleTrip.TaskStatus;
import cloud.datatp.fleet.vehicle.entity.VehicleTripGoodsTracking;
import cloud.datatp.fleet.vehicle.entity.VehicleTripProfitEstimate;
import cloud.datatp.fleet.vehicle.models.VehicleTripGoodsTrackingModel;
import cloud.datatp.fleet.vehicle.models.VehicleTripProfitReportParams;
import cloud.datatp.fleet.vehicle.repository.VehicleTripProfitEstimateRepository;
import lombok.Getter;
import net.datatp.module.data.db.DAOService;
import net.datatp.module.data.db.RecordGroupByMap;
import net.datatp.module.data.db.RecordMap;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.EditState;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.RangeFilter;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.security.client.ClientContext;
import net.datatp.util.text.DateUtil;

@Component
public class VehicleTripProfitLogic extends DAOService {
  @Autowired @Getter
  private VehicleTripProfitEstimateRepository repo;
  
  @Autowired
  private VehicleLogic                    vehicleLogic;
  
  @Autowired
  private FuelPriceLogic                  fuelPriceLogic;
  
  @Autowired
  private VehicleTripGoodsTrackingLogic   goodsTrackingLogic;

  @Autowired
  private LocationLogic locationLogic;

  public VehicleTripProfitEstimate getVehicleTripProfitEstimate(ClientContext client, ICompany company, Long id) {
    return repo.getById(company.getId(), id);
  }
  
  public VehicleTripProfitEstimate getVehicleTripProfitEstimateByTripId(ClientContext client, ICompany company, Long tripId) {
    return repo.getByVehicleTripId(company.getId(), tripId);
  }
  
  public List<VehicleTripProfitEstimate> findVehicleTripProfitEstimateByTripIds(ClientContext client, ICompany company, List<Long> tripIds) {
    return repo.findByVehicleTripIds(company.getId(), tripIds);
  }
  
  public List<VehicleTripProfitEstimate> findByVehicleProfitReportId(ClientContext client, ICompany company, Long reportId) {
    return repo.findByVehicleProfitReportId(company.getId(), reportId);
  }
  
  public int updateDriverSalaryReportId(ClientContext client, ICompany company, Long driverSalaryReportId, List<Long> profitEstimateIds) {
    return repo.updateDriverSalaryReportId(company.getId(), driverSalaryReportId, profitEstimateIds);
  }
  
  public VehicleTripProfitEstimate saveVehicleTripProfitEstimate(ClientContext client, ICompany company, VehicleTripProfitEstimate profitEstimate) {
    profitEstimate.set(client, company);
    return repo.save(profitEstimate);
  }
  
  public List<SqlMapRecord> searchVehicleTripGoodsTrackingProfitEstimate(ClientContext client, ICompany company, SqlQueryParams params) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fleet/vehicle/groovy/VehicleTripGoodsTrackingProfitSql.groovy";
    params.addParam("companyId", company.getId());
    List<SqlMapRecord> records =  searchDbRecords(client, scriptDir, scriptFile, "SearchVehicleGoodsTrackingProfitEstimate", params);
    Set<Long> locationIds = new HashSet<>();
    for(SqlMapRecord record: records) {
      Long  senderLocationId = record.getLong("senderLocationId");
      Long receiverLocationId = record.getLong("receiverLocationId");
      if(senderLocationId != null) locationIds.add(senderLocationId);
      if(receiverLocationId != null) locationIds.add(receiverLocationId);
    }
    if(!records.isEmpty()) {
      Map<Long, Location> locationMap = locationLogic.findMapLocationByIds(client,  new ArrayList<>(locationIds));
      for(SqlMapRecord rec: records) {

        Location senderLocation = locationMap.get(rec.getLong("senderLocationId"));
        if(senderLocation != null) {
          rec.add("senderLocationShortLabel", senderLocation.getShortLabel());
        }

        Location receiverLocation = locationMap.get(rec.getLong("receiverLocationId"));
        if(receiverLocation != null) {
          rec.add("receiverLocationShortLabel", receiverLocation.getShortLabel());
        }
      }
    }
    return records;
  }
  
  private List<Long> searchVehicleTripGoodsTrackingTripIds(ClientContext client, ICompany company,  VehicleTripProfitReportParams params) {
    SqlQueryParams sqlQueryParams = new SqlQueryParams();
    sqlQueryParams.addParam("companyId", company.getId());
    sqlQueryParams.addParam("fleetId", params.getFleetId());
    
    RangeFilter filter = new RangeFilter("dateTime");
    filter.setFromValue(DateUtil.asCompactDateTime(params.getReportFrom()));
    filter.setToValue(DateUtil.asCompactDateTime(params.getReportTo()));
    sqlQueryParams.add(filter);
    
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fleet/vehicle/groovy/VehicleTripGoodsTrackingSql.groovy";
    List<SqlMapRecord> records = searchDbRecords(client, scriptDir, scriptFile, "FindVehicleTripGoodsTrackingByDate", sqlQueryParams);
    
    Set<Long> tripIds = new HashSet<>();
    for(SqlMapRecord rec : records) {
      Long vehicleTripId = rec.getLong("vehicleTripId");
      if(vehicleTripId != null) tripIds.add(vehicleTripId);
    }
    
    return new ArrayList<>(tripIds);
  }
  
  private List<VehicleTripGoodsTrackingModel> searchVehicleTripGoodsTrackingByTrip(ClientContext client, ICompany company, VehicleTripProfitReportParams params) {
    List<Long> tripIds = searchVehicleTripGoodsTrackingTripIds(client, company, params);
    SqlQueryParams sqlQueryParams = new SqlQueryParams();
    sqlQueryParams.addParam("vehicleTripIds", tripIds);
    sqlQueryParams.setMaxReturn(10000);
    sqlQueryParams.addParam("isReport", true);
    return goodsTrackingLogic.searchVehicleTripGoodsTrackings(client, company, sqlQueryParams, VehicleTripGoodsTrackingModel.class);
  }
  
  public List<VehicleTripGoodsTrackingModel> calculateFuelVehicleTripGoodsTracking(ClientContext client, ICompany company,  VehicleTripProfitReportParams params) {
    List<VehicleTripGoodsTrackingModel> models = searchVehicleTripGoodsTrackingByTrip(client, company, params);
    RecordGroupByMap<Long, VehicleTripGoodsTrackingModel> trackingsByTripId  = new RecordGroupByMap<>(models, tracking  -> tracking.getVehicleTripId());
    
    SqlQueryParams sqlQueryParams = new SqlQueryParams();
    RangeFilter    filter         = new RangeFilter("date");
    filter.setFromValue(DateUtil.asCompactDateTime(params.getReportFrom()));
    filter.setToValue(DateUtil.asCompactDateTime(params.getReportTo()));
    sqlQueryParams.add(filter);
    
    List<FuelPrice>               fuelPrices = fuelPriceLogic.searchFuelPrices(client, company, sqlQueryParams, FuelPrice.class);
    RecordMap<String, FuelPrice> fuelPriceMap = new RecordMap<>(fuelPrices, sel -> DateUtil.asCompactDate(sel.getDate()));
    
    for(Long vehicleTripId : trackingsByTripId.getAll().keySet()) {
      List<VehicleTripGoodsTrackingModel> trackings = trackingsByTripId.get(vehicleTripId);
      boolean isImport = false;
      boolean isExport = false;
      if(!trackings.get(0).isTwoWay()) {
        for(VehicleTripGoodsTrackingModel tracking : trackings) {
          if(tracking.isImport()) isImport = true;
          if(tracking.isExport()) isExport = true;
        }
      }
      
      for(VehicleTripGoodsTrackingModel tracking : trackings) {
        if(isImport && isExport) {
          tracking.setUpdateVehicleTrip(true);
          tracking.setTwoWay(true);
        }
        FuelPrice price = fuelPriceMap.get(DateUtil.asCompactDate(tracking.getDeliveryPlan()));
        tracking.calculateFuel(price);
        tracking.setEditState(EditState.MODIFIED);
      }
    }
    
    return models;
  }
  
  public List<VehicleTripGoodsTrackingModel> updateExpenseVehicleTripGoodsTracking(ClientContext client, ICompany company, List<VehicleTripGoodsTrackingModel> models) {
    List<VehicleTripGoodsTracking> trackingDbs = new ArrayList<>();
    for(VehicleTripGoodsTrackingModel tracking : models) {
      if(EditState.MODIFIED.equals(tracking.getEditState())) {
        VehicleTripGoodsTracking trackingDb = goodsTrackingLogic.getVehicleTripGoodsTrackingById(client, company, tracking.getId());
        trackingDb.calculateDriverSalary();
        trackingDb.updateExpense(tracking);
        tracking.updateExpense(trackingDb);
        trackingDbs.add(trackingDb);
      }
    }
    goodsTrackingLogic.getGoodsTrackingRepository().saveAll(trackingDbs);
    goodsTrackingLogic.getGoodsTrackingRepository().flush();
    //
    List<VehicleTripProfitEstimate> profitEstimates = new ArrayList<>();
    RecordGroupByMap<Long, VehicleTripGoodsTracking> trackingsByTripId  = new RecordGroupByMap<>(trackingDbs, tracking  -> tracking.getVehicleTripId());
    for(Long tripId : trackingsByTripId.getAll().keySet()) {
      List<VehicleTripGoodsTracking> trackings = goodsTrackingLogic.findByVehicleTripId(client, company, tripId);
      VehicleTripProfitEstimate profitEstimate = getVehicleTripProfitEstimateByTripId(client, company, tripId);
      
      if(profitEstimate == null) {
        profitEstimate = new VehicleTripProfitEstimate();
        profitEstimate.setVehicleTripId(tripId);
      }
      
      profitEstimate.calculateProfit(trackings);
      profitEstimate.set(client, company);
      profitEstimates.add(profitEstimate);
    }
    repo.saveAll(profitEstimates);
    return models;
  }
  
  public List<VehicleTripGoodsTrackingModel> routeCalculateAndGetVehicleTripGoodsTrackings(ClientContext client, ICompany company, VehicleTripProfitReportParams params) {
    List<Long> tripIds = searchVehicleTripGoodsTrackingTripIds(client, company, params);
    SqlQueryParams sqlQueryParams = new SqlQueryParams();
    sqlQueryParams.addParam("vehicleTripIds", tripIds);
    sqlQueryParams.setMaxReturn(10000);
    sqlQueryParams.addParam("isReport", true);
    
    List<VehicleTripGoodsTrackingModel> models = goodsTrackingLogic.searchVehicleTripGoodsTrackings(client, company, sqlQueryParams, VehicleTripGoodsTrackingModel.class);
    RecordGroupByMap<Long, VehicleTripGoodsTrackingModel> trackingsByTripId  = new RecordGroupByMap<>(models, tracking  -> tracking.getVehicleTripId());
    for(List<VehicleTripGoodsTrackingModel> trackings : trackingsByTripId.getAll().values()) {
      boolean isImport = false;
      boolean isExport = false;
      String mode = trackings.get(0).getMode();
      boolean mixModeTrip = false;
      for(VehicleTripGoodsTrackingModel tracking : trackings) {
        if(tracking.isImport()) isImport = true;
        if(tracking.isExport()) isExport = true;
        if(!mode.equals(tracking.getMode())) mixModeTrip = true;
      }
      for(VehicleTripGoodsTrackingModel tracking : trackings) {
        tracking.setMixModeTrip(mixModeTrip);
        if(isImport && isExport) {
          tracking.setEditState(EditState.MODIFIED);
          tracking.setUpdateVehicleTrip(true);
          tracking.setTwoWay(true);
        }
        if(tracking.getVehicleId() == null && tracking.getVehicleLabel() != null) {
          Vehicle vehicle = vehicleLogic.getVehicleByLicensePlate(client, company, tracking.getVehicleLabel().trim());
          if(vehicle != null) {
            tracking.setVehicleId(vehicle.getId());
            tracking.setUpdateVehicleTrip(true);
            tracking.setEditState(EditState.MODIFIED);
          }
        }
      }
    }
    return models;
  };
  
  public List<VehicleTripGoodsTrackingModel> calculateDriverSalary(ClientContext client, ICompany company, List<VehicleTripGoodsTrackingModel> models) {
    RecordGroupByMap<Long, VehicleTripGoodsTrackingModel> trackingMap = new RecordGroupByMap<>(models, tracking  -> tracking.getVehicleTripId());
    for(Long tripId : trackingMap.getAll().keySet()) {
      List<VehicleTripGoodsTrackingModel> trackings = trackingMap.get(tripId);
      VehicleTrip vehicleTrip = vehicleLogic.getVehicleTrip(client, company, tripId);
      VehicleTripGoodsTrackingModel tracking = trackings.get(0);
      
      if(EditState.MODIFIED.equals(tracking.getEditState()) && tracking.isUpdateVehicleTrip()) {
        vehicleTrip.setTwoWay(tracking.isTwoWay());
        vehicleTrip.setTaskStatus(TaskStatus.DONE);
        vehicleTrip.setVehicleId(tracking.getVehicleId());
        vehicleTrip = vehicleLogic.saveVehicleTrip(client, company, vehicleTrip);
      }
      
      VehicleTripProfitEstimate profitEstimate = getVehicleTripProfitEstimateByTripId(client, company, tripId);
      if(profitEstimate == null) {
        profitEstimate = new VehicleTripProfitEstimate();
        profitEstimate.setVehicleTripId(tripId);
      }
      
      List<VehicleTripGoodsTracking> trackingDbs = calculateDriverSalary(client, company, profitEstimate, vehicleTrip, trackings);
      goodsTrackingLogic.getGoodsTrackingRepository().saveAll(trackingDbs);
      
      profitEstimate = saveVehicleTripProfitEstimate(client, company, profitEstimate);
      for(VehicleTripGoodsTrackingModel model : trackings) {
        model.withProfitEstimate(profitEstimate);
        model.setAverageDriverLaborRatio(profitEstimate.getDriverLaborRatio()/trackings.size());
      }
    }
    
    return models;
  }
  
  private List<VehicleTripGoodsTracking> calculateDriverSalary(
      ClientContext client, ICompany company,
      VehicleTripProfitEstimate profitEstimate, VehicleTrip trip, List<VehicleTripGoodsTrackingModel> trackings) {
    double driverLaborRatio = 1;
    if(trip.isTwoWay()) driverLaborRatio++;
    
    //Count delivery plan
    RecordGroupByMap<String, VehicleTripGoodsTrackingModel> trackingByDeliveryPlan = new RecordGroupByMap<>(trackings, tracking -> DateUtil.asCompactDate(tracking.getDeliveryPlan()));
    if(trackingByDeliveryPlan.getAll().size() > 2) driverLaborRatio++;
    
    List<Long> trackingIds = new ArrayList<>();
    for(VehicleTripGoodsTrackingModel tracking : trackings) {
      trackingIds.add(tracking.getId());
    }
    List<VehicleTripGoodsTracking> goodTrackings = goodsTrackingLogic.findByIds(client, company, trackingIds);
    
    //Count km
    double totalKm = 0;
    double totalDriverSalary     = 0;
    for(VehicleTripGoodsTracking rec : goodTrackings) {
      totalKm += rec.getEstimateDistanceInKm();
      driverLaborRatio += Math.floor(rec.getEstimateDistanceInKm() / 800);
      
      double driverSalary = rec.calculateDriverSalary().getDriverSalary();
      totalDriverSalary  += driverSalary;
    }
    
    profitEstimate.setTotalKm(totalKm);
    profitEstimate.setDriverLaborRatio(driverLaborRatio);
    profitEstimate.setDriverSalary(totalDriverSalary);
    profitEstimate.setTotalFile(trackings.size());
    
    return goodTrackings;
  }
}