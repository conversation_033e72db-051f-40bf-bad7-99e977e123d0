package cloud.datatp.fleet.vehicle;

import java.util.*;
import java.util.stream.Collectors;

import net.datatp.module.account.AccountLogic;
import net.datatp.module.account.entity.Account;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cloud.datatp.fleet.vehicle.entity.Transporter;
import cloud.datatp.fleet.vehicle.entity.TransporterMembership;
import cloud.datatp.fleet.vehicle.entity.VehicleFleet;
import cloud.datatp.fleet.vehicle.repository.TransporterMembershipRepository;
import cloud.datatp.fleet.vehicle.repository.TransporterRepository;
import cloud.datatp.fleet.vehicle.repository.VehicleFleetRepository;
import net.datatp.security.client.ClientContext;
import net.datatp.module.company.entity.Company;
import net.datatp.module.data.db.DAOService;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.entity.EditState;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.util.ds.Collections;
import net.datatp.util.text.TokenUtil;

@Component
public class TransporterLogic extends DAOService {
  @Autowired
  private TransporterRepository transporterRepo;
  
  @Autowired
  private TransporterMembershipRepository transporterMembershipRepo;
  
  @Autowired
  private VehicleFleetRepository vehicleFleetRepo;

  @Autowired
  private AccountLogic accountLogic;

  // Transporter
  public Transporter loadTransporterByCode(ClientContext client, ICompany company, String code) {
    return transporterRepo.loadByCode(company.getId(), code);
  }
  
  public Transporter getTransporterByMobile(ClientContext client, ICompany company, String mobile) {
    return transporterRepo.getByMobile(company.getId(), mobile);
  }
  
  public Transporter getByIdCard(ClientContext client, ICompany company, String idCard) {
    return transporterRepo.getByIdCard(company.getId(), idCard);
  }
  
  public Transporter getTransporterByAccountId(ClientContext client, ICompany company, Long transporterAccountId) {
    return transporterRepo.getByAccountId(company.getId(), transporterAccountId);
  }
  
  public Transporter loadTransporterById(ClientContext client, ICompany company, Long id) {
    return transporterRepo.loadById(company.getId(), id);
  }
  
  public List<Transporter> findTransporterByIds(ClientContext client, ICompany company, Long [] ids) {
    return transporterRepo.findByIds(company.getId(), ids);
  }


  public Transporter saveTransporter(ClientContext client, ICompany company, Transporter transporter) {
    transporter.set(client, company);
    return transporterRepo.save(transporter);
  }

  public Boolean changeTransportersStorageState(ClientContext client, ChangeStorageStateRequest req) {
    transporterRepo.setStorageState(req.getNewStorageState(), req.getEntityIds());
    return true;
  }
  
  public List<SqlMapRecord> searchTransporters(ClientContext client, ICompany company, SqlQueryParams params) {
    params.addParam("companyId", company.getId());
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/tms/groovy/TransporterSql.groovy";
    List<SqlMapRecord> records = searchDbRecords(client, scriptDir, scriptFile, "SelectTransporter", params);
    Set<Long> accountIds = new HashSet<>();
    for(SqlMapRecord record: records) {
      accountIds.add(record.getLong("accountId"));
    }
    Map<Long, Account> accountMap = accountLogic.findMapAccountByIds(client, new ArrayList<>(accountIds));
    for(SqlMapRecord record: records) {
      Account account = accountMap.get(record.getLong("accountId"));
      if(account != null) {
        record.add("accountFullName", account.getFullName());
        record.add("accountMobile", account.getMobile());
        record.add("accountEmail", account.getEmail());
      }
    }
    return  records;
  }
  
  // TransportersMembership
  public TransporterMembership addTransporterMembership(ClientContext client, ICompany company, TransporterMembership membership) {
    membership.set(client, company);
    return transporterMembershipRepo.save(membership);
  }

  public boolean removeTransporterMemberships(ClientContext client, ICompany company, List<TransporterMembership> memberships) {
    for (TransporterMembership membership : memberships) {
      VehicleFleet vehicleFleet = vehicleFleetRepo.getById(company.getId(), membership.getVehicleFleetId());
      if (vehicleFleet != null) {
        transporterMembershipRepo.remove(membership.getTransporterId(), vehicleFleet.getId());
      }
    }
    return true;
  }
  
  public boolean removeMembershipByTransporter(ClientContext client, ICompany company, Transporter transporter) {
    transporterMembershipRepo.removeByTransporter(company.getId(), transporter.getId());
    return true;
  }

  public List<Transporter> processTransporters(ClientContext client, ICompany company,VehicleFleet fleet, List<Transporter> transporters) {
    List<Transporter> result = new ArrayList<>();
    List<Transporter> transportersModified = transporters.stream().filter(transporter -> transporter.getEditState() == EditState.MODIFIED).collect(Collectors.toList());
    List<Transporter> transportersNew = transporters.stream().filter(transporter -> transporter.getEditState() == EditState.NEW).collect(Collectors.toList());
    if (transportersModified.size() != 0 ) {
      List<Long> ids = Collections.transform(transportersModified, (transporterModified) -> transporterModified.getId());
      List<Transporter> transportsModifiedResult = transporterRepo.findByIds(company.getId(), ids.toArray(new Long[ids.size()]));
      for (Transporter transporterResult: transportsModifiedResult) {
        Transporter transporter = Collections.findFirst(transportersModified, (transporterModified) -> Objects.equals(transporterResult.getId(),transporterModified.getId()));
        if (Objects.nonNull(transporter)) {
          transporterResult.update(transporter);
        }
        transporterResult.set(client, company);
      }
      transportsModifiedResult = transporterRepo.saveAll(transportsModifiedResult);
      result.addAll(transportsModifiedResult);
    }
    if (transportersNew.size() != 0) {
      Collections.apply(transporters, (transporter)->{
        String code = TokenUtil.idWithDateTime("transporter");
        transporter.setCode(code);
        transporter.set(client, company);
      });
      transportersNew = transporterRepo.saveAll(transportersNew);
      result.addAll(transportersNew);
      List<TransporterMembership> transporterMemberships = new ArrayList<>();
      for (Transporter transporter :transportersNew) {
        TransporterMembership transporterMembership = new TransporterMembership(transporter, fleet);
        transporterMemberships.add(transporterMembership);
      }
      addTransporterMembershipList(client, company, transporterMemberships);
    }
    return result;
  }

  public List<TransporterMembership> addTransporterMembershipList(ClientContext client, ICompany company, List<TransporterMembership> memberships) {
    Collections.apply(memberships, membership -> membership.set(client, company));
    return transporterMembershipRepo.saveAll(memberships);
  }

  public Boolean deleteTransporters(ClientContext client, ICompany company, List<Long> ids) {
    transporterMembershipRepo.removeByTransporters(company.getId(), ids);
    transporterRepo.deleteAllById(ids);
    return true;
  }
}