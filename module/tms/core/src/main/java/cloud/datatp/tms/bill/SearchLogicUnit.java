package cloud.datatp.tms.bill;

import java.util.*;

import net.datatp.module.account.AccountLogic;
import net.datatp.module.account.ProfileLogic;
import net.datatp.module.account.entity.UserProfile;
import net.datatp.module.account.repository.AccountRepository;
import net.datatp.module.resource.location.LocationLogic;
import net.datatp.module.resource.location.entity.Location;
import org.springframework.context.ApplicationContext;

import net.datatp.lib.executable.ExecutableContext;
import net.datatp.lib.executable.ExecutableUnit;
import net.datatp.lib.executable.Executor;
import net.datatp.module.account.entity.Account;
import net.datatp.module.data.db.RecordGroupByMap;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.SearchFilter;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.security.client.ClientContext;
import net.datatp.util.ds.Arrays;
import net.datatp.util.ds.Collections;
import net.datatp.util.text.StringUtil;

public class SearchLogicUnit extends Executor {

  static public class SearchTMSBill extends ExecutableUnit {

    private RecordGroupByMap<Long, SqlMapRecord> findGoodsTrackingByTMSBillIds(ClientContext client, ICompany company, TMSBillLogic logic, List<Long> TMSBillIds) {
      SqlQueryParams params = new SqlQueryParams();
      params.addParam("tmsBillIds", TMSBillIds);

      String scriptDir = logic.appEnv.addonPath("logistics", "groovy");
      String scriptFile = "cloud/datatp/fleet/groovy/VehicleGoodsTrackingSql.groovy";
      List<SqlMapRecord> trackings = logic.searchDbRecords(client, scriptDir, scriptFile, "FindVehicleGoodsTrackingByTMSBillIds", params);
      return new RecordGroupByMap<>(trackings, tracking -> (Long)tracking.getLong("tmsBillId"));
    }

    private RecordGroupByMap<Long, SqlMapRecord> findStopLocationsByTMSBillIds(ClientContext client, ICompany company, TMSBillLogic logic, List<Long> TMSBillIds) {
      SqlQueryParams params = new SqlQueryParams();
      params.addParam("tmsBillIds", TMSBillIds);

      String scriptDir = logic.appEnv.addonPath("logistics", "groovy");
      String scriptFile = "cloud/datatp/tms/groovy/TMSBillSql.groovy";
      List<SqlMapRecord> trackings = logic.searchDbRecords(client, scriptDir, scriptFile, "FindStopLocationsByTMSBillIds", params);
      return new RecordGroupByMap<>(trackings, tracking -> (Long)tracking.getLong("tmsBillId"));
    }

    private RecordGroupByMap<Long, SqlMapRecord> findVendorBillByTMSBillIds(ClientContext client, ICompany company, TMSBillLogic logic, List<Long> TMSBillIds) {
      SqlQueryParams params = new SqlQueryParams();
      params.addParam("tmsBillIds", TMSBillIds);

      String scriptDir = logic.appEnv.addonPath("logistics", "groovy");
      String scriptFile = "cloud/datatp/tms/groovy/TMSBillSql.groovy";
      List<SqlMapRecord> trackings = logic.searchDbRecords(client, scriptDir, scriptFile, "FindVendorBillByTMSBillIds", params);
      return new RecordGroupByMap<>(trackings, tracking -> (Long)tracking.getLong("tmsBillId"));
    }

    public List<SqlMapRecord> execute(ApplicationContext appCtx, ExecutableContext ctx) {
      TMSBillLogic   logic    = ctx.getParam(TMSBillLogic.class);
      ClientContext     client   = ctx.getParam(ClientContext.class);
      ICompany        company = ctx.getParam(ICompany.class);
      SqlQueryParams params   = ctx.getParam(SqlQueryParams.class);
      ProfileLogic profileLogic = ctx.getParam(ProfileLogic.class);
      LocationLogic locationLogic = ctx.getParam(LocationLogic.class);


      String scriptDir = logic.appEnv.addonPath("logistics", "groovy");
      String scriptFile = "cloud/datatp/tms/groovy/TMSBillSql.groovy";

      Account account = logic.accountLogic.getReadOnly(client, client.getRemoteUser());
      params.addParam("responsibleAccountId", account.getId());
      params.addParam("companyId", company.getId());
      
      SearchFilter hblNoFilter  = params.getFilter("searchHblNo");
      if(hblNoFilter != null) {
        String patten = hblNoFilter.getFilterValue();
        if(StringUtil.isNotBlank(patten)) {
          patten = patten.replaceAll(",", " ").replaceAll("\n", " ").replaceAll("  ", " ");
          String [] hblNos = patten.split(" ");
          params.addParam("hblNos", Arrays.asList(hblNos));
        }
      }
      
      SearchFilter fileNoFilter = params.getFilter("searchFileNo");
      if(fileNoFilter != null) {
        String patten = fileNoFilter.getFilterValue();
        if(StringUtil.isNotBlank(patten)) {
          patten = patten.replaceAll(",", " ").replaceAll("\n", " ").replaceAll("  ", " ");
          String [] fileNos = patten.split(" ");
          params.addParam("fileNos", Arrays.asList(fileNos));
        }
      }
      

      List<SqlMapRecord> recordList     = logic.searchDbRecords(client, scriptDir, scriptFile, "SearchTMSBill", params);
      List<Long>         tmsBillIds     = Arrays.asList(0L);
      List<Long>         jobTrackingIds = Arrays.asList(0L);
      Set<Long>         responsibleAccountIds = new HashSet<>();
      Set<Long> locationIds = new HashSet<>();
      for(SqlMapRecord rec : recordList) {
        Long tmsBillId     = rec.getLong("id");
        Long jobTrackingId = rec.getLong("jobTrackingId");
        Long responsibleAccountId = rec.getLong("responsibleAccountId");
        Long  senderLocationId = rec.getLong("senderLocationId");
        Long receiverLocationId = rec.getLong("receiverLocationId");

        tmsBillIds.add(tmsBillId);
        if(jobTrackingId != null) jobTrackingIds.add(jobTrackingId);
        if(responsibleAccountId != null) responsibleAccountIds.add(responsibleAccountId);
        if(senderLocationId != null) locationIds.add(senderLocationId);
        if(receiverLocationId != null) locationIds.add(receiverLocationId);
      }
      if(Collections.isNotEmpty(tmsBillIds)) {
        RecordGroupByMap<Long, SqlMapRecord> trackingMap         = findGoodsTrackingByTMSBillIds(client, company, logic, tmsBillIds);
        RecordGroupByMap<Long, SqlMapRecord> vendorMap           = findVendorBillByTMSBillIds(client, company, logic, tmsBillIds);
        RecordGroupByMap<Long, SqlMapRecord> stopLocationMap     = findStopLocationsByTMSBillIds(client, company, logic, tmsBillIds);
        Map<Long, UserProfile> userProfileAccountIdMap = profileLogic.findMapUserProfileByAccountIds(client, new ArrayList<>(responsibleAccountIds));
        Map<Long, Location> locationMap = locationLogic.findMapLocationByIds(client, new ArrayList<>(locationIds));
        for(SqlMapRecord rec : recordList) {
          rec.add("trackings", trackingMap.get(rec.getLong("id")));
          rec.add("vendorBills", vendorMap.get(rec.getLong("id")));
          rec.add("stopLocations", stopLocationMap.get(rec.getLong("id")));
          Location senderLocation = locationMap.get(rec.getLong("senderLocationId"));
          if(senderLocation != null) {
            rec.add("senderLocationStorageState", senderLocation.getStorageState());
            rec.add("senderLocationState", senderLocation.getStateLabel() );
            rec.add("senderLocationSubdistrict", senderLocation.getSubdistrictLabel());
          }

          Location receiverLocation = locationMap.get(rec.getLong("receiverLocationId"));
          if(receiverLocation != null) {
            rec.add("receiverLocationStorageState", receiverLocation.getStorageState());
            rec.add("receiverLocationState", receiverLocation.getStateLabel() );
            rec.add("receiverLocationSubdistrict", receiverLocation.getSubdistrictLabel());
          }

          UserProfile userProfile = userProfileAccountIdMap.get(rec.getLong("responsibleAccountId"));
          if(userProfile != null) {
            if(StringUtil.isNotBlank(userProfile.getNickname())) {
              rec.add("userName",userProfile.getNickname() );
            } else {
              rec.add("userName",userProfile.getFullName() );
            }
          }


        }
      }
      return recordList;
    }
  }

  static public class SearchTMSBillReport extends ExecutableUnit {
    public List<SqlMapRecord> execute(ApplicationContext appCtx, ExecutableContext ctx) {
      TMSBillLogic   logic   = ctx.getParam(TMSBillLogic.class);
      ClientContext     client  = ctx.getParam(ClientContext.class);
      ICompany        company = ctx.getParam(ICompany.class);
      SqlQueryParams params  = ctx.getParam(SqlQueryParams.class);
      ProfileLogic profileLogic = ctx.getParam(ProfileLogic.class);

      String scriptDir = logic.appEnv.addonPath("logistics", "groovy");
      String scriptFile = "cloud/datatp/tms/groovy/TMSBillReportSql.groovy";

      Account account = logic.accountLogic.getReadOnly(client, client.getRemoteUser());
      params.addParam("responsibleAccountId", account.getId());
      params.addParam("companyId", company.getId());

      List<SqlMapRecord> records = logic.searchDbRecords(client, scriptDir, scriptFile, "SearchTMSBill", params);
      Set<Long> responsibleAccountIds = new HashSet<>();
      for(SqlMapRecord record: records) {
        responsibleAccountIds.add(record.getLong("responsibleAccountId"));
      }
      Map<Long, UserProfile> userProfileAccountIdMap = profileLogic.findMapUserProfileByAccountIds(client, new ArrayList<>(responsibleAccountIds));
      for(SqlMapRecord record: records) {
        UserProfile userProfile = userProfileAccountIdMap.get(record.getLong("responsibleAccountId"));
        if(userProfile != null) {
          record.add("responsibleFullName", userProfile.getFullName());
          record.add("responsibleNickname", userProfile.getNickname());
        }
      }
      return records;
    }
  }

  public SearchLogicUnit() {
    register(new SearchTMSBillReport());
    register(new SearchTMSBill());
  }
  
  
}