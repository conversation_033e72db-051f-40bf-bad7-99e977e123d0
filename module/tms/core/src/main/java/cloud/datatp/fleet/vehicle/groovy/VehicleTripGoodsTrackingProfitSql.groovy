package cloud.datatp.fleet.vehicle.groovy

import org.springframework.context.ApplicationContext

import net.datatp.lib.executable.ExecutableContext
import net.datatp.lib.executable.Executor
import net.datatp.module.data.db.ExecutableSqlBuilder
import net.datatp.util.ds.MapObject

class VehicleTripGoodsTrackingProfitSql extends Executor{
  public class SearchVehicleGoodsTrackingProfitEstimate extends ExecutableSqlBuilder {
    public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
      MapObject sqlParams  = ctx.getParam("sqlParams");

      String query = """
            WITH tracking_group_by_vehicle_trip AS (
              SELECT
                vehicle_trip_id                                                         AS vehicle_trip_id,
                COUNT(lgc_fleet_vehicle_trip_goods_tracking.vehicle_trip_id)            AS total_file_on_trip,
                (
                   CASE
                     WHEN COUNT(lgc_fleet_vehicle_trip_goods_tracking.vehicle_trip_id) > 1
                     THEN lgc_fleet_vehicle_trip_goods_tracking.vehicle_trip_id
                     ELSE 0 
                   END
                 )                                                                      AS combine_index
              FROM lgc_fleet_vehicle_trip_goods_tracking
              WHERE lgc_fleet_vehicle_trip_goods_tracking.storage_state = 'ACTIVE'
              GROUP BY lgc_fleet_vehicle_trip_goods_tracking.vehicle_trip_id
            )
            SELECT 
              gt.id                                AS id, 
              gt.created_by                        AS created_by,
              gt.created_time                      AS created_time, 
              gt.modified_by                       AS modified_by,
              gt.modified_time                     AS modified_time, 
              gt.storage_state                     AS storage_state, 
              gt.version                           AS version, 
              gt.company_id                        AS company_id, 
              gt.file_trucking                     AS file_trucking,
              gt.pickup_location                   AS pickup_location,
              gt.delivery_location                 AS delivery_location,
              gt.description                       AS description,
              gt.vehicle_type                      AS vehicle_type,
              gt.total_charge                      AS total_charge,
              gt.estimate_distance_in_km           AS estimate_distance_in_km,
              gt.driver_salary                     AS driver_salary,
              gt.revenue,
              gt.expense,
              gt.extra_fuel_cost,
              gt.fuel_cost,
              gt.travel_cost,
              gt.fuel_before_vat,
              gt.liters_of_fuel,
              gt.profit,
              gt.vehicle_trip_id                   AS vehicle_trip_id,

              ltb.label                            AS bill_label,
              ltb.delivery_plan                    AS date_time,
              TO_CHAR(ltb.delivery_plan, 'yyyy/MM/dd') AS formarted_date_time,
              ltb.time                             AS time,
              ltb.office                           AS office,
              ltb.responsible_full_name            AS responsible_full_name,
              ltb.responsible_account_id           AS responsible_account_id,
              ltb.customer_full_name               AS customer_full_name,
              ltbft.mode                           AS mode,
              ltb.quantity                         AS tms_bill_quantity,
              ltb.quantity_unit                    AS tms_bill_quantity_unit,
              ltb.weight                           AS tms_bill_weight,
              ltb.volume_as_text                   AS tms_bill_volume_as_text,
              ltb.volume                           AS tms_bill_volume,
              ltb.sender_address                   AS sender_address,
              ltb.receiver_address                 AS receiver_address,
              ltb.sender_location_id               AS sender_location_id,
              ltb.receiver_location_id             AS receiver_location_id,

              trip.vehicle_label                   AS vehicle_label,
              trip.driver_full_name                AS driver_full_name,
              trip.two_way                         AS two_way,
              trip.vehicle_id                      AS vehicle_id,
              gbvt.total_file_on_trip              AS total_file_on_trip,
              
              profit.driver_labor_ratio            AS total_driver_labor_ratio,
              profit.total_file                    AS total_file,
              profit.driver_salary                 AS total_driver_salary,
              (profit.driver_labor_ratio / profit.total_file) AS average_driver_labor_ratio
              
            FROM  lgc_fleet_vehicle_trip_goods_tracking   gt
            INNER JOIN lgc_fleet_vehicle_trip_profit_estimate profit ON profit.vehicle_trip_id              = gt.vehicle_trip_id
            INNER JOIN lgc_fleet_vehicle_trip            trip ON trip.id                             = gt.vehicle_trip_id
            INNER JOIN lgc_tms_bill                       ltb ON ltb.id                              = gt.tms_bill_id
            INNER JOIN lgc_tms_bill_forwarder_transport ltbft ON ltb.tms_bill_forwarder_transport_id = ltbft.id
            LEFT JOIN tracking_group_by_vehicle_trip     gbvt ON gbvt.vehicle_trip_id                = gt.vehicle_trip_id
            WHERE
              ${FILTER_BY_OPTION('gt.storage_state', 'storageState', sqlParams, ['ACTIVE'])}
              ${AND_FILTER_BY_PARAM('gt.company_id', 'companyId', sqlParams)}
              ${AND_FILTER_BY_PARAM('profit.driver_salary_report_id', 'driverSalaryReportIds', sqlParams)}
              ${AND_FILTER_BY_PARAM('profit.vehicle_profit_report_id', 'vehicleProfitReportIds', sqlParams)}
              ORDER BY formarted_date_time ASC, gbvt.combine_index, ltb.customer_full_name
            ${MAX_RETURN(sqlParams)}
      """
      return query;
    }
  }
  public VehicleTripGoodsTrackingProfitSql() {
    register(new SearchVehicleGoodsTrackingProfitEstimate());
  }
}
