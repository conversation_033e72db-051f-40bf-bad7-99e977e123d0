package cloud.datatp.tms.partner;

import java.util.*;

import net.datatp.module.resource.location.LocationLogic;
import net.datatp.module.resource.location.entity.Location;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cloud.datatp.tms.partner.entity.TMSPartnerAddress;
import cloud.datatp.tms.partner.repository.TMSPartnerAddressRepository;
import net.datatp.security.client.ClientContext;
import net.datatp.module.data.db.DAOService;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.util.ds.MapObject;

@Component
public class TMSPartnerAddressLogic extends DAOService {
  @Autowired
  private TMSPartnerAddressRepository repo;

  @Autowired
  private LocationLogic locationLogic;
  
  public TMSPartnerAddress loadById(ClientContext client, ICompany company, Long id) {
    TMSPartnerAddress address = repo.loadById(company.getId(), id);
    return address;
  }

  public List<TMSPartnerAddress> findTMSPartnerAddress(ClientContext client, ICompany company, Long tmsPartnerId) {
    return repo.findByPartnerId(company.getId(), tmsPartnerId);
  }
  
  public List<SqlMapRecord> searchPartnerAddresses(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/tms/groovy/TMSPartnerSql.groovy";
    sqlParams.addParam("companyId", company.getId());
    List<SqlMapRecord> sqlMapRecords = searchDbRecords(client, scriptDir, scriptFile, "FindTMSPartnerAddress", sqlParams);

    Set<Long> locationIds = new HashSet<>();
    for(SqlMapRecord sqlMapRecord: sqlMapRecords) {
      locationIds.add(sqlMapRecord.getLong("locationId"));
    }
    Map<Long, Location> locationMap = locationLogic.findMapLocationByIds(client, new ArrayList<>(locationIds));
    List<SqlMapRecord> recordResults = new ArrayList<>();

    for(SqlMapRecord sqlMapRecord: sqlMapRecords) {
      Long locationId = sqlMapRecord.getLong("locationId");
      Location location = locationMap.get(locationId);

      if(location != null) {
        sqlMapRecord.add("locStorageState", location.getStorageState());
        if(sqlParams.getString("locationStorageState") == null || location.getStorageState().toString().equals(sqlParams.getString("locationStorageState"))) {
          recordResults.add(sqlMapRecord);
        }
      }
    }
    return recordResults;
  }
  
  public boolean deleteByIds(ClientContext client, ICompany company, List<Long> ids) {
    repo.deleteByIds(company.getId(), ids);
    return true;
  }
  
  public TMSPartnerAddress saveTMSPartnerAddress(ClientContext client, ICompany company, TMSPartnerAddress address) {
    address.set(client, company);
    return repo.save(address);
  }

  public List<TMSPartnerAddress> saveTMSPartnerAddresses(ClientContext client, ICompany company, List<TMSPartnerAddress> records) {
    for(TMSPartnerAddress address: records) {
      address = saveTMSPartnerAddress(client, company, address);
    }
    return records;
  }
  
  public List<TMSPartnerAddress> bulkSaveTMSPartnerAddresses(ClientContext client, ICompany company, List<MapObject> records) {
    List<TMSPartnerAddress> addresses = new ArrayList<>();
    for(MapObject ob: records) {
      Long id = ob.getLong("id", null);
      TMSPartnerAddress address = new TMSPartnerAddress();
      if(id != null) {
        address = loadById(client, company, id);
      }
      address.merge(ob);
      address = saveTMSPartnerAddress(client, company, address);
      addresses.add(address);
    }
    return addresses;
  }
  
}