package cloud.datatp.fleet.vehicle.entity;

import java.io.Serial;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;

import cloud.datatp.fleet.vehicle.entity.VehicleTripGoodsTrackingChargeItem.Type;
import cloud.datatp.fleet.vehicle.models.VehicleTripGoodsTrackingModel;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Embedded;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.FetchType;
import jakarta.persistence.Index;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OrderBy;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.account.entity.Account;
import net.datatp.module.company.entity.CompanyEntity;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.security.client.ClientContext;
import net.datatp.util.ds.Collections;
import net.datatp.util.text.DateUtil;
import net.datatp.util.text.TokenUtil;

@Entity
@Table(
  name = VehicleTripGoodsTracking.TABLE_NAME,
  uniqueConstraints = { @UniqueConstraint( name = VehicleTripGoodsTracking.TABLE_NAME + "_code", columnNames = { "company_id", "code" }) },
  indexes = {
    @Index(name = VehicleTripGoodsTracking.TABLE_NAME + "_company_id_idx", columnList = "company_id"),
    @Index(name = VehicleTripGoodsTracking.TABLE_NAME + "_storage_state_idx", columnList = "storage_state"),
    @Index(name = VehicleTripGoodsTracking.TABLE_NAME + "_code_idx", columnList = "code"),
    @Index(name = VehicleTripGoodsTracking.TABLE_NAME + "_tms_bill_id_idx",columnList = "tms_bill_id"),
    @Index(name = VehicleTripGoodsTracking.TABLE_NAME + "_vehicle_trip_id_idx",columnList = "vehicle_trip_id"),
  }
)

@JsonInclude(Include.NON_NULL)
@NoArgsConstructor @Getter @Setter
public class VehicleTripGoodsTracking extends CompanyEntity {
  @Serial
  private static final long serialVersionUID = 1L;

  public static final String TABLE_NAME                 = "lgc_fleet_vehicle_trip_goods_tracking";
  public static final String SEQUENCE                   = "fleet:vehicle-trip-goods-tracking";
  public static final String BT_SEQUENCE                = "fleet:vehicle-trip-goods-tracking_bt";
  public static final String COMPANY_CONFIG_PREFIX_CODE = "lgc.tms.vehicle-trip-goods-tracking.prefix";
  public static final String AUTO_FORWARD_MESSAGE       = "lgc.tms.vehicle-trip-goods-tracking.auto-forward-message";
  public static final String DRIVER_SALARY_PERCENT      = "lgc.tms.vehicle-trip-goods-tracking.driver-salary-percent";
  
  public enum VehicleTripGoodsTrackingStatus {
    NEED_CONFIRM, CONFIRMED, REJECT
  }
  
  public enum TransporterStatus {
    REJECT, PICKUP_SUCCESS, DELIVERY_SUCCESS
  }

  public enum OilPriceType {
     AUTO, MANUAL;
  }

  @Column(name = "tms_bill_id")
  private Long tmsBillId;
  
  @NotNull
  @Column(length=50)
  private String code;
  
  @NotNull @Column(length=1024)
  private String label;
  
  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "delivery_plan")
  private Date deliveryPlan;
 
  private String time;
  
  @Column(name = "type_of_transport")
  private String typeOfTransport;
  
  @Enumerated(EnumType.STRING)
  private VehicleTripGoodsTrackingStatus status = VehicleTripGoodsTrackingStatus.CONFIRMED;
  
  @Column(name = "vehicle_trip_id")
  private Long vehicleTripId;
  
  @Column(name = "file_trucking")
  private String fileTrucking;
  
  //rename typeOfTransport
  @Column(name = "transport_type")
  private String transportType;
  
  @Column(name = "vehicle_type")
  private String vehicleType;
  
  @Column(name="coordinator_id")
  private Long coordinatorId;
  
  @Column(name="coordinator_account_id")
  private Long coordinatorAccountId;

  @Column(name="coordinator_full_name")
  private String coordinatorFullName;
  
  @Column(name="responsible_account_id")
  private Long responsibleAccountId;

  @Column(name="responsible_full_name")
  private String responsibleFullName;
  
  @Column(name = "pickup_location")
  protected String pickupLocation;
  
  @Column(name = "delivery_location")
  protected String deliveryLocation;
  
  @Column(name = "pickup_container_location")
  protected String pickupContainerLocation;
  
  @Column(name = "pickup_container_location_id")
  protected Long   pickupContainerLocationId;
  
  @Column(name = "return_container_location")
  protected String returnContainerLocation;
  
  @Column(name = "return_container_location_id")
  protected Long   returnContainerLocationId;
  
  //pickup
  @Column(name = "pickup_contact")
  private String pickupContact;

  @Column(name = "pickup_address", length = 16*1024)
  private String pickupAddress;
  
  @Column(name = "pickup_inv_address", length = 16*1024)
  private String pickupInvAddress;
  
  @Column(name = "pickup_location_id")
  private Long pickupLocationId;
  
  @Column(name = "pickup_partner_address_id")
  private Long pickupPartnerAddressId;
  
//delivery
  @Column(name = "delivery_contact")
  private String deliveryContact;

  @Column(name = "delivery_address", length = 16*1024)
  private String deliveryAddress;
  
  @Column(name = "delivery_inv_address", length = 16*1024)
  private String deliveryInvAddress;
  
  @Column(name = "delivery_location_id")
  private Long deliveryLocationId;
  
  @Column(name = "delivery_partner_address_id")
  private Long deliveryPartnerAddressId;
  
  @Column(length=1024 * 3)
  private String description;
  
  @Column(name = "estimate_distance_in_km")
  private double estimateDistanceInKm;
  
  @Embedded
  private VehicelTripGoods goods;
  
  @Embedded
  private TransporterActivity transporterActivity;
  
  //TOTAL
  //Selling
  @Column(name="fixed_charge")
  private double fixedCharge;
  
  @Column(name="extra_charge")
  private double extraCharge;
  
  //rename to revenue
  @Column(name="total_charge")
  private double totalCharge;

  //Costing
  @Column(name="fixed_cost")
  private double fixedCost;
  
  @Column(name="extra_cost")
  private double extraCost;
  
  private double vetc;
  
  @Column(name="travel_cost")
  private double travelCost;

  @Column(name="driver_salary")
  private double driverSalary;
  
  @Column(name="total_cost")
  private double totalCost;

  private double profit;
  
  //fuel info
  @Column(name="fuel_price")
  private double fuelPrice;
  
  @Column(name="fuel_cost")
  private double fuelCost;
  
  @Column(name="extra_fuel_cost")
  private double extraFuelCost;
  
  @Column(name="liters_of_fuel")
  private double litersOfFuel;
  
  @Column(name="extra_liters_of_fuel")
  private double extraLitersOfFuel;
  
  @Column(name="total_liters_of_fuel")
  private double totalLitersOfFuel;
  
  @OneToMany(cascade = CascadeType.ALL, fetch=FetchType.EAGER, orphanRemoval = true)
  @JoinColumn(name = "vehicle_trip_goods_tracking_id", referencedColumnName = "id")
  @OrderBy("id")
  private List<VehicleTripGoodsTrackingChargeItem> items = new ArrayList<>();
  
  public VehicleTripGoodsTracking genCode(Long seq, String companyCode) {
    this.code = companyCode.toUpperCase() + DateUtil.asCompactYearMonth(new Date()) + "/" + String.format("%04d", seq);
    return this;
  }
  
  public VehicleTripGoodsTracking genCodeWithDateTimeId(String companyCode) {
    code = TokenUtil.idWithDateTime(companyCode);
    code.replace("-","/");
    code.toUpperCase();
    return this;
  }
  
  public VehicelTripGoods getGoods() {
    if(goods == null) goods = new VehicelTripGoods();
    return goods;
  }
  
  public TransporterActivity getTransporterActivity() {
    if(transporterActivity == null) transporterActivity = new TransporterActivity();
    return transporterActivity;
  }
  
  public VehicleTripGoodsTracking withCoordinator(Account account) {
    if(this.coordinatorAccountId != null) return this;
    this.coordinatorAccountId = account.getId();
    this.coordinatorFullName  = account.getFullName();
    return this;
  }
  
  public VehicleTripGoodsTracking updateExpense(VehicleTripGoodsTrackingModel goodsTrackingModel) {
    this.fuelPrice      = goodsTrackingModel.getFuelPrice();
    this.fuelCost       = goodsTrackingModel.getFuelCost();
    this.extraFuelCost  = goodsTrackingModel.getExtraFuelCost();
    this.travelCost     = goodsTrackingModel.getTravelCost();
    this.driverSalary   = goodsTrackingModel.getDriverSalary();
    this.litersOfFuel   = goodsTrackingModel.getEstimateDistanceInKm() * goodsTrackingModel.getOilConsumption();
    this.extraLitersOfFuel = goodsTrackingModel.getExtraLitersOfFuel();
    this.totalLitersOfFuel = this.litersOfFuel + this.extraLitersOfFuel;
    this.calculateProfit();
    return this;
  }
  
  public VehicleTripGoodsTracking calculateDriverSalary() {
    this.driverSalary = this.totalCharge * 0.1;
    return this;
  }
  
  public void calculateProfit() {
    this.profit    = this.totalCharge - this.totalCost;
  }
  
  public void updateFixedCharge(double fixedCharge) {
    VehicleTripGoodsTrackingChargeItem fixedChargeItem = Collections.findFirst(items, item -> item.isFixedCharge() && item.isSelling());
    if(fixedChargeItem == null) {
      VehicleTripGoodsTrackingChargeItem item = new VehicleTripGoodsTrackingChargeItem(Type.SELLING);
      item.setLabel("DOMESTIC TRUCKING FEE");
      item.setBfsFeeCode("S_TRUCK");
      item.setGroup(VehicleTripGoodsTrackingChargeItem.Group.FIXED_CHARGE);
      item.setTotal(fixedCharge);
      item.calculate();
      items.add(item);
    } else {
      fixedChargeItem.setTotal(fixedCharge);
      fixedChargeItem.calculate();
    }
  }
  public void updateTravelCost(double amount) {
    VehicleTripGoodsTrackingChargeItem travelCostItem = Collections.findFirst(items, item -> item.isTravelCost() && item.isBuying());
    if(travelCostItem == null) {
      VehicleTripGoodsTrackingChargeItem item = new VehicleTripGoodsTrackingChargeItem(Type.BUYING);
      item.setLabel("TIỀN ĐƯỜNG");
      item.setGroup(VehicleTripGoodsTrackingChargeItem.Group.TRAVEL_COST);
      item.setTotal(amount);
      item.calculate();
      items.add(item);
    } else {
      travelCostItem.setTotal(amount);
      travelCostItem.calculate();
    }
  }
  
  public void updateSellingAndCosting() {
    this.extraCharge = 0;
    this.fixedCharge = 0;
    this.extraCost = 0;
    this.fixedCost = 0;
    this.vetc = 0;
    this.driverSalary = 0;
    this.travelCost = 0;
    for(VehicleTripGoodsTrackingChargeItem item : items) {
      if(item.isSelling()) {
        if(item.isExtraCharge()) extraCharge += item.getFinalCharge();
        if(item.isFixedCharge()) fixedCharge += item.getFinalCharge();
      }
      if(item.isBuying()) {
        if(item.isExtraCharge())  extraCost     += item.getFinalCharge();
        if(item.isFixedCharge())  fixedCost     += item.getFinalCharge();
        if(item.isVetc())         vetc          += item.getFinalCharge();
        if(item.isDriverSalary()) driverSalary  += item.getFinalCharge();
        if(item.isTravelCost())   travelCost    += item.getFinalCharge();
      }
    }
    this.totalCharge = this.fixedCharge + this.extraCharge;
    this.totalCost   = this.fuelCost + this.fixedCost + this.vetc + this.extraCost + this.travelCost + this.driverSalary;
  }
  
  public void set(ClientContext client, ICompany company) {
    super.set(client, company);
  }
}