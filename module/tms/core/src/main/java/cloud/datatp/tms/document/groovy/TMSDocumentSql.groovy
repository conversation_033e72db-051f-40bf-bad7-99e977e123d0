package cloud.datatp.tms.document.groovy


import org.springframework.context.ApplicationContext
import net.datatp.lib.executable.ExecutableContext
import net.datatp.lib.executable.Executor
import net.datatp.module.data.db.ExecutableSqlBuilder
import net.datatp.util.ds.MapObject

public class TMSDocumentSql extends Executor {

    static public class SearchTMSLOLOInvoiceReconciles extends ExecutableSqlBuilder {

        private AND_FILTER_BY_NEED_CONFIRM(MapObject sqlParams) {
          boolean needConfirm = sqlParams.getBoolean("needConfirm", false);
          if(needConfirm) {
            return "AND ir.responsible_account_id IS NULL"
          }
          return "--AND ir.responsible_account_id IS NULL"
        }


        public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
            MapObject sqlParams = ctx.getParam("sqlParams");

            String query = """
                SELECT 
                    ir.*
                FROM lgc_invoice_summary_reconcile ir
                LEFT JOIN lgc_fleet_vehicle_fleet fleet ON ir.vendor_id = fleet.id 
                WHERE
                    ${FILTER_BY_OPTION('ir.storage_state', 'storageState', sqlParams, ['ACTIVE'])}
                    ${AND_FILTER_BY_PARAM('ir.company_id','companyId', sqlParams)}
                    ${AND_FILTER_BY_PARAM('ir.type','type', sqlParams)}
                    ${AND_SEARCH_BY_PARAMS(['ir.label', 'ir.vendor_name'],'search', sqlParams)}
                    ${AND_FILTER_BY(['ir.responsible_account_id', 'fleet.owner_account_id'], 'accountId', sqlParams)}
                    ${AND_FILTER_BY_NEED_CONFIRM(sqlParams)}
                    ${AND_FILTER_BY_RANGE('ir.payment_request_date', 'paymentRequestDate', sqlParams)}
                ORDER BY ir.payment_request_date DESC
                ${MAX_RETURN(sqlParams)};
                """;
            return query;
        }
    };
    
    static public class FindTMSLOLOInvoiceReconcileDocumentsByLoLoId extends ExecutableSqlBuilder {
      
      public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
        MapObject sqlParams = ctx.getParam("sqlParams");
        
        String query = """
            SELECT 
              ir_doc.invoice_reconcile_id  AS ir_id,
              ir_doc.document_set_id
              --doc_set.*
            FROM lgc_invoice_reconcile_document  ir_doc
            --INNER JOIN document_document_set doc_set ON ir_doc.document_set_id = doc_set.id
            WHERE
              ${FILTER_BY_PARAM('ir_doc.invoice_reconcile_id', 'loloIds', sqlParams)}
            """;
        return query;
      }
    }
    public TMSDocumentSql() {
      register(new SearchTMSLOLOInvoiceReconciles());
      register(new FindTMSLOLOInvoiceReconcileDocumentsByLoLoId());
    }
}
