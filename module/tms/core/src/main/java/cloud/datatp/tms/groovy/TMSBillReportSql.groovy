package cloud.datatp.tms.groovy

import org.springframework.context.ApplicationContext

import net.datatp.lib.executable.ExecutableContext;
import net.datatp.lib.executable.Executor;
import net.datatp.module.data.db.ExecutableSqlBuilder;
import net.datatp.security.client.DataScope
import net.datatp.util.ds.MapObject;

public class TMSBillReportSql extends Executor {
  static public class SearchTMSBill extends ExecutableSqlBuilder {
    public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
      MapObject sqlParams          = ctx.getParam("sqlParams");
      String searchPattern         = sqlParams.getString("searchPattern", null);
      String dataScope             = sqlParams.getString("dataScope");
      boolean isOwner              = DataScope.Owner.toString().equals(dataScope);
      
      String query = """
        SELECT * FROM (
          SELECT
            lgc_tms_bill.id,
            lgc_tms_bill.ref_source,
            lgc_tms_bill.code,
            lgc_tms_bill.label,
            lgc_tms_bill.state,
            lgc_tms_bill.process_status,
            lgc_tms_bill.type,
            lgc_tms_bill.delivery_plan                              AS date_time,
            lgc_tms_bill.time,
            lgc_tms_bill.description,
            lgc_tms_bill.status_issue,
            lgc_tms_bill.collect,
            lgc_tms_bill.is_combine,
            lgc_tms_bill.office,
            lgc_tms_bill.estimate_time,
            lgc_tms_bill.modified_estimate_time,
            lgc_tms_bill.delayed_time,
            lgc_tms_bill.responsible_account_id,
      
            lgc_tms_bill.version,
            lgc_tms_bill.storage_state,
            lgc_tms_bill.company_id,
            lgc_tms_bill.customer_id,
            lgc_tms_bill.customer_full_name,
            lgc_tms_bill.customer_mobile,
            lgc_tms_bill.customer_address,
      
            lgc_tms_bill.sender_full_name,
            lgc_tms_bill.sender_contact,
            lgc_tms_bill.sender_address,
      
            lgc_tms_bill.receiver_full_name,
            lgc_tms_bill.receiver_contact,
            lgc_tms_bill.receiver_address,
      
            lgc_tms_bill.quantity,
            lgc_tms_bill.quantity_unit,
            lgc_tms_bill.volume,
            lgc_tms_bill.chargeable_volume,
            lgc_tms_bill.volume_as_text,
            lgc_tms_bill.volume_unit,
            lgc_tms_bill.weight,
            lgc_tms_bill.chargeable_weight,
            lgc_tms_bill.weight_unit,
            lgc_tms_bill.goods_type,
            lgc_tms_bill.goods_description,
            lgc_tms_bill.job_tracking_id,
      
            lgc_tms_bill_forwarder_transport.mode,
            lgc_tms_bill_forwarder_transport.vendor_id,
            lgc_tms_bill_forwarder_transport.vendor_full_name,
     
            lgc_tms_bill_fee.fixed_payment,
            lgc_tms_bill_fee.extra_payment,
            lgc_tms_bill_fee.total_payment,
            lgc_tms_bill_fee.final_payment,
      
            lgc_job_tracking.step_done_count
            
          FROM lgc_tms_bill
          LEFT JOIN lgc_tms_bill_forwarder_transport
           ON lgc_tms_bill_forwarder_transport.id           = lgc_tms_bill.tms_bill_forwarder_transport_id
          LEFT JOIN lgc_tms_bill_fee
           ON lgc_tms_bill_fee.id                           = lgc_tms_bill.tms_bill_fee_id
          LEFT JOIN lgc_job_tracking
            ON lgc_job_tracking.id                          = lgc_tms_bill.job_tracking_id

        ) as lgc_tms_bill
        WHERE
          ${FILTER_BY_STORAGE_STATE(sqlParams)}
          ${AND_FILTER_BY_PARAM("companyId", sqlParams)}
          AND (ref_source <> 'lgc_tms_bill' OR ref_source is null)
          ${AND_SEARCH_BY(["label", "responsible_full_name", "customer_full_name"], searchPattern)}
          ${AND_FILTER_BY_PARAM("(lgc_tms_bill.responsible_account_id","responsibleAccountId", sqlParams)} OR ${!isOwner} = TRUE)
          ${AND_FILTER_BY_RANGE('lgc_tms_bill.date_time', 'dateTime', sqlParams)}
          ${MAX_RETURN(sqlParams)};
      """;
      
      return query
    }
  }
  static public class ReportTMSBillWithVendor extends ExecutableSqlBuilder {
    public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
      MapObject sqlParams          = ctx.getParam("sqlParams");
      
      String query = """
          SELECT * FROM (
            SELECT
            bill.id,
            bill.delivery_plan,
            bill.route,
            bill.company_id,
            bill.storage_state,
            bill.sender_location_id,
            bill.receiver_location_id,
            
            f_transport.mode,
            CASE
              WHEN trip.fleet_id IS NOT NULL
              THEN trip.fleet_id
            ELSE f_transport.vendor_id END                                          AS vendor_id,
            CASE
              WHEN trip.fleet_label IS NOT NULL
              THEN trip.fleet_label
            ELSE f_transport.vendor_full_name END                                   AS vendor_full_name
          
            FROM lgc_tms_bill bill
            LEFT JOIN lgc_tms_bill_forwarder_transport f_transport
              ON f_transport.id                                = bill.tms_bill_forwarder_transport_id
            LEFT JOIN lgc_fleet_vehicle_trip_goods_tracking tracking
              ON tracking.tms_bill_id                          = bill.id
            LEFT JOIN lgc_fleet_vehicle_trip trip
              ON trip.id                                       = tracking.vehicle_trip_id
            ) as lgc_tms_bill
          WHERE
          vendor_full_name IS NOT NULL
          ${AND_FILTER_BY_PARAM("companyId", sqlParams)}
          ${AND_FILTER_BY_PARAM("vendorId", sqlParams)}
          ${AND_FILTER_BY_RANGE('delivery_plan', 'deliveryPlan', sqlParams)}
          ${MAX_RETURN(sqlParams)};
          """;
      return query
    }
  }
  public TMSBillReportSql() {
    register(new SearchTMSBill())
    register(new ReportTMSBillWithVendor())
  }
}
