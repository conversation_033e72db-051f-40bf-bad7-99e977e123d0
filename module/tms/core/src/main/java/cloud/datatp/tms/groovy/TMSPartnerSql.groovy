package cloud.datatp.tms.groovy

import org.springframework.context.ApplicationContext

import net.datatp.lib.executable.ExecutableContext;
import net.datatp.lib.executable.Executor
import net.datatp.security.client.DataScope;
import net.datatp.module.data.db.ExecutableSqlBuilder
import net.datatp.module.data.db.query.OptionFilter;
import net.datatp.util.ds.MapObject;

public class TMSPartnerSql extends Executor {

  public class FindPartnerPermission extends ExecutableSqlBuilder {
    public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
      MapObject sqlParams = ctx.getParam("sqlParams");
      String query = """
        SELECT
               per.*
            FROM lgc_tms_partner_permission per
            WHERE
              ${FILTER_BY_PARAM('per.company_id', 'companyId', sqlParams)}
              ${AND_FILTER_BY_PARAM('per.tms_partner_id', 'tmsPartnerIds', sqlParams)}
              ${AND_FILTER_BY_PARAM('per.tms_partner_id', 'tmsPartnerId', sqlParams)}
      """;
      return query;
    }
  }

  public class SearchTMSPartners extends ExecutableSqlBuilder {
    public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
      MapObject sqlParams = ctx.getParam("sqlParams");

      String query = """
          SELECT
                lgc_tms_partner.*,
                lgc_tms_partner.id AS tms_partner_id               
          FROM lgc_tms_partner
          WHERE
            ${FILTER_BY_STORAGE_STATE('lgc_tms_partner', sqlParams)}
            ${AND_FILTER_BY_PARAM('lgc_tms_partner.company_id', 'companyId', sqlParams)}
            ${AND_SEARCH_BY_PARAMS(['lgc_tms_partner.short_name', 'lgc_tms_partner.description'], 'search', sqlParams)}
          ORDER BY lgc_tms_partner.short_name
          ${MAX_RETURN(sqlParams)}
      """;
      return query;
    }
  }
  
  public class FindTMSJobTrackingColumnByRuleId extends ExecutableSqlBuilder {
    public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
      MapObject sqlParams = ctx.getParam("sqlParams");
      int maxReturn = sqlParams.getInteger('maxReturn', 1000);
      
      String query = """
          SELECT
            tracking_column.id,
            tracking_column.name, 
            tracking_column.label,
            tracking_column.description,
            tracking_plugin.plugin       AS plugin_name
          FROM lgc_tms_job_tracking_rule rule
          INNER JOIN lgc_tms_job_tracking_rule_membership   membership ON membership.tms_job_tracking_rule_id = rule.id
          INNER JOIN lgc_job_tracking_column           tracking_column ON membership.job_tracking_column_id     = tracking_column.id
          INNER JOIN lgc_job_tracking_plugin           tracking_plugin ON tracking_plugin.id                    = tracking_column.job_tracking_plugin_id
          WHERE 
            ${FILTER_BY_PARAM('rule.id' ,'ruleId', sqlParams)}
            ${AND_SEARCH_BY_PARAMS(['column.name', 'column.label'], 'search', sqlParams)}
          LIMIT ${maxReturn}
      """;
      return query;
    }
  }
  
  public class SearchTMSJobTrackingRule extends ExecutableSqlBuilder {
    public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
      MapObject sqlParams = ctx.getParam("sqlParams");
      int maxReturn = sqlParams.getInteger('maxReturn', 1000);
      
      String query = """
          WITH customer AS (
            SELECT 
             tms_job_tracking_rule_id,
             count(*) AS number_of_customer
            FROM lgc_tms_partner 
            GROUP BY tms_job_tracking_rule_id
          )
          SELECT 
            rule.*,
            customer.number_of_customer
          FROM lgc_tms_job_tracking_rule rule
          LEFT JOIN customer ON rule.id = customer.tms_job_tracking_rule_id
          WHERE 
            ${FILTER_BY_STORAGE_STATE(sqlParams)}
            ${AND_FILTER_BY_PARAM('companyId', sqlParams)}
            ${AND_SEARCH_BY_PARAMS(['label'], 'search', sqlParams)}
          ORDER BY created_time DESC
          LIMIT ${maxReturn}
          """;
      return query;
    }
  }

  public class SearchTMSCustomers extends ExecutableSqlBuilder {
    private String JOIN_PERMISSION_FILTER(MapObject sqlParams) {
      OptionFilter filter = (OptionFilter) sqlParams.get("dataScope");
      String sScope = null;
      if(filter != null) {
        sScope = filter.getSelectOption()
      }
      DataScope scope = sScope == null ? DataScope.Company : DataScope.valueOf(sScope);
      boolean isOwnerScope = DataScope.Owner.equals(scope);
      return """${isOwnerScope ? "" : "--"} INNER JOIN permission_filter per_filter ON per_filter.tms_partner_id  = lgc_tms_customer.tms_partner_id""";
    }

    private String WITH_PERMISSION_FILTER(MapObject sqlParams) {
      OptionFilter filter = (OptionFilter) sqlParams.get("dataScope");
      String sScope = null;
      if(filter != null) {
        sScope = filter.getSelectOption()
      }
      DataScope scope = sScope == null ? DataScope.Company : DataScope.valueOf(sScope);
      boolean isOwnerScope = DataScope.Owner.equals(scope);
      if(isOwnerScope == true) {
        return """ 
          ,permission_filter AS (
            SELECT
              per.tms_partner_id,
              string_agg(
                per.label, ', '
              )                           AS permissions
            FROM lgc_tms_partner_permission per
            WHERE
              ${FILTER_BY_PARAM("per.company_id", "companyId", sqlParams)}
              ${AND_FILTER_BY_PARAM("per.user_id", "userId", sqlParams)}
            GROUP BY per.tms_partner_id
          )
        """
      }
      return "";
    }

    String AND_FILTER_BY_JOB_TRACKING_RULE_OPTION(MapObject sqlParams) {
      OptionFilter filter = (OptionFilter) sqlParams.get("jobTrackingRule");
      List<String> opts = new ArrayList<>();
      if(filter != null) {
        opts = filter.getSelectOptions();
      }
      if(opts.size() == 1) {
        if("no-rule".equals(opts.get(0))) {
          return """AND lgc_tms_customer.tms_job_tracking_rule_id IS NULL"""
        } else if ("default-rule".equals(opts.get(0))) {
          return """AND lgc_tms_customer.tms_job_tracking_rule_id IS NOT NULL 
                    AND job_tracking_project_rule_config.custom_job_tracking_id IS NULL
                 """;
        } else if ("custom-rule".equals(opts.get(0))) {
          return """AND lgc_tms_customer.tms_job_tracking_rule_id IS NOT NULL 
                    AND job_tracking_project_rule_config.custom_job_tracking_id IS NOT NULL
                 """;
        }
      }
      return "--AND_FILTER_BY_JOB_TRACKING_RULE_OPTION(${opts})"
    }


    public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
      MapObject sqlParams = ctx.getParam("sqlParams");

      String query = """
          WITH tms_bill AS (
            SELECT lgc_tms_bill.customer_id, COUNT(lgc_tms_bill.id) AS number_of_bill
            FROM lgc_tms_bill
            WHERE lgc_tms_bill.storage_state = 'ACTIVE'      
            GROUP BY lgc_tms_bill.customer_id
          ),
          tms_partner_address AS (
            SELECT COUNT(id) AS total_address, tms_partner_id
            FROM lgc_tms_partner_address
            GROUP BY tms_partner_id
          )
          ${WITH_PERMISSION_FILTER(sqlParams)}
          SELECT
                lgc_tms_customer.id                              AS id,
                lgc_tms_partner.id                               AS tms_partner_id,
                lgc_tms_partner.code                             AS code,
                lgc_tms_partner.ref_account_id                   AS ref_account_id,
                lgc_tms_partner.ref_account_full_name            AS ref_account_full_name,
                lgc_tms_partner.allow_request_tracking_app       AS allow_request_tracking_app,
                lgc_tms_partner.type                             AS type,
                lgc_tms_partner.company_id                       AS company_id,
                lgc_tms_partner.invoice_company_tax_code         AS invoice_company_tax_code,
                lgc_tms_partner.address                          AS address,
                lgc_tms_partner.created_time                     AS created_time,
                lgc_tms_partner.odoo_partner_code                AS odoo_partner_code,
                lgc_tms_partner.invoice_company_label            AS invoice_company_label,
                lgc_tms_partner.created_by                       AS created_by,
                lgc_tms_partner.description                      AS description,
                lgc_tms_customer.modified_by                     AS modified_by,
                lgc_tms_customer.modified_time                   AS modified_time,
                lgc_tms_partner.label                            AS label,
                lgc_tms_partner.emails                           AS emails,
                lgc_tms_partner.short_name                       AS short_name,
                lgc_tms_partner.sale_account_id                  AS sale_account_id,
                lgc_tms_partner.group_name                       AS group_name,
                lgc_tms_partner.source                           AS source,
                lgc_tms_partner.invoice_company_address          AS invoice_company_address,
                lgc_tms_partner.third_party_code                 AS third_party_code,
                lgc_tms_partner.partner_type                     AS partner_type,
                lgc_tms_partner.partner_id                       AS partner_id,
                lgc_tms_partner.parent_partner_id                AS parent_partner_id,
                lgc_tms_partner.parent_partner_name              AS parent_partner_name,
                lgc_tms_customer.tms_job_tracking_rule_id        AS tms_job_tracking_rule_id,
                lgc_tms_customer.tms_job_tracking_rule_label     AS tms_job_tracking_rule_label,
                lgc_tms_partner.bfs_one_code                     AS bfs_one_code,
                lgc_tms_partner.tax_code                         AS tax_code,
                lgc_tms_partner.version                          AS version,
                lgc_tms_partner.storage_state                    AS storage_state,
                job_tracking_project_rule_config.config_status    AS project_rule_config_status,
                CASE
                  WHEN tms_partner_address.total_address IS NOT NULL
                  THEN tms_partner_address.total_address
                  ELSE 0
                END AS total_address,
                CASE
                  WHEN tms_bill.customer_id IS NULL
                  THEN 0
                  ELSE tms_bill.number_of_bill
                END AS number_of_bill
          FROM  lgc_tms_customer               
            INNER JOIN lgc_tms_partner        ON lgc_tms_partner.id                 = lgc_tms_customer.tms_partner_id
            LEFT JOIN tms_bill                ON tms_bill.customer_id               = lgc_tms_customer.id
            LEFT JOIN tms_partner_address     ON tms_partner_address.tms_partner_id = lgc_tms_customer.tms_partner_id
            LEFT JOIN job_tracking_project_rule_config ON lgc_tms_customer.tms_job_tracking_rule_id = job_tracking_project_rule_config.id
            ${JOIN_PERMISSION_FILTER(sqlParams)}
          WHERE
            ${FILTER_BY_OPTION('lgc_tms_customer.storage_state',   'storageState', sqlParams, ['ACTIVE'])}
            ${AND_FILTER_BY_PARAM('lgc_tms_customer.company_id', 'companyId', sqlParams)}
            ${AND_FILTER_BY_PARAM('lgc_tms_partner.tms_job_tracking_rule_id', 'jobTrackingRuleId', sqlParams)}
            ${AND_SEARCH_BY_PARAMS(['lgc_tms_partner.short_name', 'lgc_tms_partner.description'], 'search', sqlParams)}
            ${AND_FILTER_BY_JOB_TRACKING_RULE_OPTION(sqlParams)}
          ORDER BY number_of_bill DESC, lgc_tms_partner.short_name
          ${MAX_RETURN(sqlParams)}
      """;
      return query;
    }
  }

  public class SearchTMSVendors extends ExecutableSqlBuilder {

    public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
      MapObject sqlParams = ctx.getParam("sqlParams");

      String query = """
          WITH tms_bill AS (
            SELECT lgc_tms_bill_forwarder_transport.vendor_id, COUNT(lgc_tms_bill.id) AS number_of_bill
            FROM lgc_tms_bill
            INNER JOIN lgc_tms_bill_forwarder_transport
            ON lgc_tms_bill.tms_bill_forwarder_transport_id = lgc_tms_bill_forwarder_transport.id 
            WHERE lgc_tms_bill.storage_state = 'ACTIVE'           
            GROUP BY lgc_tms_bill_forwarder_transport.vendor_id
          )
          SELECT
                lgc_tms_vendor.id                                AS id,
                lgc_tms_partner.id                               AS tms_partner_id,
                lgc_tms_partner.code                             AS code,
                lgc_tms_partner.ref_account_id                   AS ref_account_id,
                lgc_tms_partner.ref_account_full_name            AS ref_account_full_name,
                lgc_tms_vendor.allow_request_tracking_app        AS allow_request_tracking_app,
                lgc_tms_partner.type                             AS type,
                lgc_tms_partner.invoice_company_tax_code         AS invoice_company_tax_code,
                lgc_tms_partner.address                          AS address,
                lgc_tms_partner.odoo_partner_code                AS odoo_partner_code,
                lgc_tms_partner.invoice_company_label            AS invoice_company_label,
                lgc_tms_partner.description                      AS description,
                lgc_tms_partner.company_id                       AS company_id,
               
                lgc_tms_partner.label                            AS label,
                lgc_tms_partner.emails                           AS emails,
                lgc_tms_partner.short_name                       AS short_name,
                lgc_tms_partner.sale_account_id                  AS sale_account_id,               
                lgc_tms_partner.group_name                       AS group_name,
                lgc_tms_partner.source                           AS source,
                lgc_tms_partner.invoice_company_address          AS invoice_company_address,
                lgc_tms_partner.third_party_code                 AS third_party_code,
                lgc_tms_partner.partner_type                     AS partner_type,
                lgc_tms_partner.partner_id                       AS partner_id,
                lgc_tms_partner.parent_partner_id                AS parent_partner_id,
                lgc_tms_partner.parent_partner_name              AS parent_partner_name,
                lgc_tms_partner.tms_job_tracking_rule_id,
                lgc_tms_partner.tms_job_tracking_rule_label,
                lgc_tms_partner.bfs_one_code                     AS bfs_one_code,
                lgc_tms_partner.tax_code                         AS tax_code,
                lgc_tms_partner.version                          AS version,
                lgc_tms_partner.storage_state                    AS storage_state,
                lgc_tms_partner.created_time                     AS created_time,
                lgc_tms_partner.created_by                       AS created_by,
                lgc_tms_vendor.modified_by                       AS modified_by,
                lgc_tms_vendor.modified_time                     AS modified_time,
                CASE
                  WHEN tms_bill.vendor_id IS NULL
                  THEN 0
                  ELSE tms_bill.number_of_bill
                END AS number_of_bill
          FROM lgc_tms_vendor               
            INNER JOIN lgc_tms_partner        ON lgc_tms_partner.id = lgc_tms_vendor.tms_partner_id
            LEFT JOIN tms_bill                ON tms_bill.vendor_id = lgc_tms_vendor.id
          WHERE
            ${FILTER_BY_STORAGE_STATE('lgc_tms_vendor', sqlParams)}
            ${AND_FILTER_BY_PARAM('lgc_tms_vendor.company_id', 'companyId', sqlParams)}
            ${AND_FILTER_BY_PARAM('lgc_tms_partner.bfs_one_code', 'bfsOneCode', sqlParams)}
            ${AND_SEARCH_BY_PARAMS(['lgc_tms_partner.short_name', 'lgc_tms_partner.description'], 'search', sqlParams)}
          ORDER BY number_of_bill DESC, lgc_tms_partner.short_name
          ${MAX_RETURN(sqlParams)}
      """;
      return query;
    }
  }


  public class SearchTMSCarriers extends ExecutableSqlBuilder {

    public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
      MapObject sqlParams = ctx.getParam("sqlParams");

      String query = """
          WITH tms_bill AS (
            SELECT lgc_tms_bill_forwarder_transport.carrier_id, COUNT(lgc_tms_bill.id) AS number_of_bill
            FROM lgc_tms_bill
            INNER JOIN lgc_tms_bill_forwarder_transport
            ON lgc_tms_bill.tms_bill_forwarder_transport_id = lgc_tms_bill_forwarder_transport.id
            WHERE lgc_tms_bill.storage_state = 'ACTIVE'      
            GROUP BY lgc_tms_bill_forwarder_transport.carrier_id
          )
          SELECT
                lgc_tms_carrier.id                                AS id,
                lgc_tms_partner.id                               AS tms_partner_id,
                lgc_tms_partner.code                             AS code,
                lgc_tms_partner.ref_account_id                   AS ref_account_id,
                lgc_tms_partner.ref_account_full_name            AS ref_account_full_name,
                lgc_tms_partner.type                             AS type,
                lgc_tms_partner.company_id                       AS company_id,
                lgc_tms_partner.invoice_company_tax_code         AS invoice_company_tax_code,
                lgc_tms_partner.address                          AS address,
                lgc_tms_partner.created_time                     AS created_time,
                lgc_tms_partner.odoo_partner_code                AS odoo_partner_code,
                lgc_tms_partner.invoice_company_label            AS invoice_company_label,
                lgc_tms_partner.created_by                       AS created_by,
                lgc_tms_partner.description                      AS description,
                lgc_tms_carrier.modified_by                       AS modified_by,
                lgc_tms_carrier.modified_time                     AS modified_time,
                lgc_tms_partner.label                            AS label,
                lgc_tms_partner.emails                           AS emails,
                lgc_tms_partner.short_name                       AS short_name,
                lgc_tms_partner.sale_account_id                  AS sale_account_id,               
                lgc_tms_partner.group_name                       AS group_name,
                lgc_tms_partner.source                           AS source,
                lgc_tms_partner.invoice_company_address          AS invoice_company_address,
                lgc_tms_partner.third_party_code                 AS third_party_code,
                lgc_tms_partner.partner_type                     AS partner_type,
                lgc_tms_partner.partner_id                       AS partner_id,
                lgc_tms_partner.parent_partner_id                AS parent_partner_id,
                lgc_tms_partner.parent_partner_name              AS parent_partner_name,
                lgc_tms_partner.tms_job_tracking_rule_id,
                lgc_tms_partner.tms_job_tracking_rule_label,
                lgc_tms_partner.bfs_one_code                     AS bfs_one_code,
                lgc_tms_partner.tax_code                         AS tax_code,
                lgc_tms_partner.version                          AS version,
                lgc_tms_partner.storage_state                    AS storage_state,
                CASE
                  WHEN tms_bill.carrier_id IS NULL
                  THEN 0
                  ELSE tms_bill.number_of_bill
                END AS number_of_bill
          FROM lgc_tms_carrier              
            INNER JOIN lgc_tms_partner        ON lgc_tms_partner.id = lgc_tms_carrier.tms_partner_id
            LEFT JOIN tms_bill                ON tms_bill.carrier_id = lgc_tms_carrier.id
          WHERE
            ${FILTER_BY_STORAGE_STATE('lgc_tms_carrier', sqlParams)}
            ${AND_FILTER_BY_PARAM('lgc_tms_carrier.company_id', 'companyId', sqlParams)}
            ${AND_SEARCH_BY_PARAMS(['lgc_tms_partner.short_name', 'lgc_tms_partner.description'], 'search', sqlParams)}
          ORDER BY number_of_bill DESC, lgc_tms_partner.short_name
          ${MAX_RETURN(sqlParams)}
      """;
      return query;
    }
  }

  public class SearchTMSAgents extends ExecutableSqlBuilder {
    private String JOIN_PERMISSION_FILTER(MapObject sqlParams) {
      OptionFilter filter = (OptionFilter) sqlParams.get("dataScope");
      String sScope = null;
      if(filter != null) {
        sScope = filter.getSelectOption()
      }
      DataScope scope = sScope == null ? DataScope.Company : DataScope.valueOf(sScope);
      boolean isOwnerScope = DataScope.Owner.equals(scope);
      return """${isOwnerScope ? "" : "--"} INNER JOIN permission_filter per_filter ON per_filter.tms_partner_id = lgc_tms_agent.tms_partner_id""";
    }

    private String WITH_PERMISSION_FILTER(MapObject sqlParams) {
      OptionFilter filter = (OptionFilter) sqlParams.get("dataScope");
      String sScope = null;
      if(filter != null) {
        sScope = filter.getSelectOption()
      }
      DataScope scope = sScope == null ? DataScope.Company : DataScope.valueOf(sScope);
      boolean isOwnerScope = DataScope.Owner.equals(scope);
      if(isOwnerScope == true) {
        return """ 
          WITH permission_filter AS (
            SELECT
              per.tms_partner_id,
              string_agg(
                per.label, ', '
              )                           AS permissions
            FROM lgc_tms_partner_permission per
            WHERE
              ${FILTER_BY_PARAM("per.company_id", "companyId", sqlParams)}
              ${AND_FILTER_BY_PARAM("per.user_id", "userId", sqlParams)}
            GROUP BY per.tms_partner_id
          )
        """
      }
      return "";
    }

    public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
      MapObject sqlParams = ctx.getParam("sqlParams");

      String query = """
          ${WITH_PERMISSION_FILTER(sqlParams)}
          SELECT
                lgc_tms_agent.id                                 AS id,
                lgc_tms_partner.id                               AS tms_partner_id,
                lgc_tms_partner.code                             AS code,
                lgc_tms_partner.ref_account_id                   AS ref_account_id,
                lgc_tms_partner.ref_account_full_name            AS ref_account_full_name,
                lgc_tms_partner.allow_request_tracking_app       AS allow_request_tracking_app,
                lgc_tms_partner.type                             AS type,
                lgc_tms_partner.company_id                       AS company_id,
                lgc_tms_partner.invoice_company_tax_code         AS invoice_company_tax_code,
                lgc_tms_partner.address                          AS address,
                lgc_tms_partner.created_time                     AS created_time,
                lgc_tms_partner.odoo_partner_code                AS odoo_partner_code,
                lgc_tms_partner.invoice_company_label            AS invoice_company_label,
                lgc_tms_partner.created_by                       AS created_by,
                lgc_tms_partner.description                      AS description,
                lgc_tms_agent.modified_by                        AS modified_by,
                lgc_tms_agent.modified_time                      AS modified_time,
                lgc_tms_partner.label                            AS label,
                lgc_tms_partner.emails                           AS emails,
                lgc_tms_partner.short_name                       AS short_name,
                lgc_tms_partner.sale_account_id                  AS sale_account_id,
                lgc_tms_partner.group_name                       AS group_name,
                lgc_tms_partner.source                           AS source,
                lgc_tms_partner.invoice_company_address          AS invoice_company_address,
                lgc_tms_partner.third_party_code                 AS third_party_code,
                lgc_tms_partner.partner_type                     AS partner_type,
                lgc_tms_partner.partner_id                       AS partner_id,
                lgc_tms_partner.parent_partner_id                AS parent_partner_id,
                lgc_tms_partner.parent_partner_name              AS parent_partner_name,
                lgc_tms_partner.bfs_one_code                     AS bfs_one_code,
                lgc_tms_partner.tax_code                         AS tax_code,
                lgc_tms_partner.version                          AS version,
                lgc_tms_partner.storage_state                    AS storage_state
            FROM  lgc_tms_agent        
            INNER JOIN lgc_tms_partner        ON lgc_tms_partner.id = lgc_tms_agent.tms_partner_id
            ${JOIN_PERMISSION_FILTER(sqlParams)}
          WHERE
            ${FILTER_BY_OPTION('lgc_tms_agent.storage_state',   'storageState', sqlParams, ['ACTIVE'])}
            ${AND_FILTER_BY_PARAM('lgc_tms_agent.company_id', 'companyId', sqlParams)}
            ${AND_SEARCH_BY_PARAMS(['lgc_tms_partner.short_name', 'lgc_tms_partner.description'], 'search', sqlParams)}
          ORDER BY lgc_tms_partner.short_name
          ${MAX_RETURN(sqlParams)}
      """;
      return query;
    }
  }
  
  public class FindTMSPartnerAddress extends ExecutableSqlBuilder {
    public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
      MapObject sqlParams = ctx.getParam("sqlParams");
      String join ="";
      if(sqlParams.has("customerId")) {
        join = "INNER JOIN  lgc_tms_customer customer ON address.tms_partner_id = customer.tms_partner_id";
      }
      if(sqlParams.has("accountId")) {
        join = """
          ${join}
          LEFT JOIN lgc_tms_partner_permission permission ON permission.tms_partner_id = address.tms_partner_id
        """
      }

      String query = """
          SELECT address.*, partner.short_name
            FROM  lgc_tms_partner_address address
            ${join}
            LEFT JOIN  lgc_tms_partner partner ON address.tms_partner_id = partner.id
          WHERE
            ${FILTER_BY_OPTION('address.storage_state', 'storageState', sqlParams, ['ACTIVE'])}
            ${AND_FILTER_BY_PARAM('address.company_id', 'companyId', sqlParams)}
            ${AND_FILTER_BY_OPTION('address.type', 'type', sqlParams)}
            ${AND_SEARCH_BY_PARAMS(['address.address', 'address.location_label'], 'search', sqlParams)}
            ${AND_FILTER_BY_PARAM('customer.id', 'customerId', sqlParams)}
            ${AND_FILTER_BY_PARAM('permission.user_id', 'accountId', sqlParams)}
            ${AND_FILTER_BY_PARAM('address.tms_partner_id', 'tmsPartnerId', sqlParams)}
            ${AND_FILTER_BY_PARAM('address.tms_partner_id', 'tmsPartnerIds', sqlParams)}
      """;
      return query;
    }
  }
 
  
  public TMSPartnerSql() {
    register(new FindTMSPartnerAddress());
    register(new FindPartnerPermission());
    register(new SearchTMSPartners());
    register(new SearchTMSVendors());
    register(new SearchTMSCustomers());
    register(new SearchTMSCarriers());
    register(new SearchTMSAgents());
    register(new SearchTMSJobTrackingRule());
    register(new FindTMSJobTrackingColumnByRuleId());
  }
}
