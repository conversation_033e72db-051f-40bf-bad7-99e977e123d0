package cloud.datatp.fforwarder.price;

import cloud.datatp.fforwarder.core.common.Purpose;
import cloud.datatp.fforwarder.core.common.TransportationMode;
import cloud.datatp.fforwarder.core.db.CRMDaoService;
import cloud.datatp.fforwarder.core.message.CRMMessageLogic;
import cloud.datatp.fforwarder.core.message.entity.CRMMessageSystem;
import cloud.datatp.fforwarder.price.entity.InquiryRequest;
import cloud.datatp.fforwarder.price.entity.InquiryRequest.InquiryStatus;
import cloud.datatp.fforwarder.price.http.Params.ShareableUpdate;
import cloud.datatp.fforwarder.price.repository.InquiryRequestRepository;
import jakarta.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.Getter;
import net.datatp.module.account.AccountLogic;
import net.datatp.module.account.ProfileLogic;
import net.datatp.module.account.entity.Account;
import net.datatp.module.account.entity.UserProfile;
import net.datatp.module.bot.BotService;
import net.datatp.module.communication.CommunicationMessageLogic;
import net.datatp.module.communication.MailMessage;
import net.datatp.module.communication.entity.CommunicationAccount;
import net.datatp.module.company.CompanyConfigLogic;
import net.datatp.module.company.CompanyLogic;
import net.datatp.module.company.entity.CompanyConfig;
import net.datatp.module.core.security.SecurityLogic;
import net.datatp.module.core.security.entity.AppPermission;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.data.db.seq.SeqService;
import net.datatp.module.graphapi.GraphApiService;
import net.datatp.module.hr.EmployeeLogic;
import net.datatp.module.hr.entity.Employee;
import net.datatp.module.zalo.ZaloLogic;
import net.datatp.security.client.ClientContext;
import net.datatp.util.ds.Collections;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;
import net.datatp.util.text.DateUtil;
import net.datatp.util.text.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Getter
@Component
public class InquiryRequestLogic extends CRMDaoService {

  @Autowired
  private EmployeeLogic employeeLogic;

  @Autowired
  private AccountLogic accountLogic;

  @Autowired
  private ProfileLogic profileLogic;

  @Autowired
  private CompanyConfigLogic companyConfigLogic;

  @Autowired
  private SecurityLogic securityLogic;

  @Autowired
  private CompanyLogic companyLogic;

  @Autowired
  private CommunicationMessageLogic messageLogic;

  @Autowired
  private InquiryRequestRepository inquiryRequestRepo;

  @Autowired
  private GraphApiService graphApiService;

  @Autowired
  private SeqService seqService;

  @Autowired
  private ZaloLogic zaloLogic;

  @Autowired
  private BotService botService;

  @Autowired
  private CRMMessageLogic crmMessageLogic;

  @PostConstruct
  private void onInit() {
    seqService.createIfNotExists(InquiryRequest.SEQUENCE, 10);
  }

  public InquiryRequest getInquiryRequest(ClientContext client, ICompany company, Long requestId) {
    return inquiryRequestRepo.findById(requestId).get();
  }

  public InquiryRequest getInquiryRequest(ClientContext client, ICompany company, String requestCode) {
    return inquiryRequestRepo.getByCode(company.getId(), requestCode);
  }

  public InquiryRequest initInquiryRequest(ClientContext client, ICompany company, InquiryRequest request) {
    Account account = accountLogic.getAccountById(client, client.getAccountId());
    Objects.assertNotNull(account, "Account not found!!!, login id: " + client.getRemoteUser());
    CommunicationAccount messageAccount = messageLogic.getCommunicationAccount(client, client.getRemoteUser());
    if (messageAccount != null) {
      request.setSalemanEmail(messageAccount.getEmail());
      request.setSalemanPhone(messageAccount.getMobile());
    }
    UserProfile userProfile = profileLogic.getUserProfile(client, account.getLoginId());
    if (userProfile != null) request.setSalemanJobTitle(userProfile.getJobPosition());

    if (request.getCargoReadyDate() == null) request.setCargoReadyDate(new Date());
    request.setSalemanAccountId(account.getId());
    request.setSalemanLabel(account.getFullName());
    Purpose purpose = request.getPurpose();
    if (purpose == null) {
      request.setPurpose(Purpose.EXPORT);
      purpose = Purpose.EXPORT;
    }
    if (request.getMode() == null) request.setMode(TransportationMode.SEA_FCL);

    //set default mail to in request
    StringBuilder builder = new StringBuilder("mail.request.pricing.");
    builder.append(request.getMode().toString().toLowerCase());
    if (!TransportationMode.isTruckTransport(request.getMode())) {
      builder.append(".");
      builder.append(purpose.toString().toLowerCase(Locale.ROOT));
    }
    CompanyConfig companyConfig = companyConfigLogic.getCompanyConfigByCompanyId(client, company.getId());
    String mailTo = companyConfig.getAttributeAsString(builder.toString(), null);
    if (StringUtil.isNotEmpty(mailTo)) request.withTo(mailTo);
    return request;
  }

  public List<MapObject> saveInquiryRequestRecords(ClientContext client, ICompany company, List<MapObject> requests) {
    if (Collections.isNotEmpty(requests)) {
      for (MapObject request : requests) {
        InquiryRequest priceCheckRequest = new InquiryRequest();
        Long id = request.getLong("id", null);
        if (id != null) priceCheckRequest = getInquiryRequest(client, company, id);
        InquiryStatus oldInquiryStatus = InquiryStatus.parse(priceCheckRequest.getStatus().toString());
        priceCheckRequest = priceCheckRequest.computeFromMapObject(request);

        final boolean ownerEdit = client.getAccountId() == priceCheckRequest.getSalemanAccountId();
        InquiryStatus newInquiryStatus = InquiryStatus.parse(request.getString("status", null));

        if (!priceCheckRequest.isNew() && ownerEdit && !oldInquiryStatus.equals(newInquiryStatus)) {
          CRMMessageSystem message = priceCheckRequest.toSalemanChangeStatusMessage(client);
          crmMessageLogic.scheduleMessage(client, company, message);
        }

        if (!ownerEdit && newInquiryStatus.equals(InquiryStatus.REJECTED) && !priceCheckRequest.isNew()) {
          Account accountById = accountLogic.getAccountById(client, client.getAccountId());
          CRMMessageSystem message = priceCheckRequest.toPricingRejectMessage(client, accountById.getEmail());
          crmMessageLogic.scheduleMessage(client, company, message);
        }

        if (StringUtil.isEmpty(priceCheckRequest.getTo())) {
          CommunicationAccount messageAccount = messageLogic.getCommunicationAccount(client, client.getRemoteUser());
          Objects.assertNotNull(messageAccount, "Message Account not found!!!, login id: " + client.getRemoteUser());
          priceCheckRequest.setTo(messageAccount.getEmail());
        }
        InquiryRequest savedRequest = saveInquiryRequest(client, company, priceCheckRequest);
        request.put("id", savedRequest.getId());
      }
    }
    return requests;
  }

  public InquiryRequest saveInquiryRequest(ClientContext client, ICompany company, InquiryRequest request) {
    if (request.isNew() || StringUtil.isEmpty(request.getCode())) {
      String prefix = request.getPurpose().getAbb() + request.getMode().getBfsAbb() + DateUtil.asCompactDateId(new Date()).substring(2);
      String code = prefix + String.format("%04d", seqService.nextSequence(InquiryRequest.SEQUENCE));
      request.setCode(code);
    }

    request.computeMailSubject();
    request.computeVolumeReport();

    if(StringUtil.isEmpty(request.getSalemanBranchName())) {
      request.setSalemanBranchName(client.getCompanyCode());
    }

    if (request.getRequestDate() == null) {
      request.setRequestDate(new Date());
    }
    if (request.getSalemanAccountId() == null) {
      CommunicationAccount messageAccount = messageLogic.getCommunicationAccount(client, client.getRemoteUser());
      Objects.assertNotNull(messageAccount, "Message Account not found!!!, login id: " + client.getRemoteUser());
      request.setSalemanEmail(messageAccount.getEmail());
      request.setSalemanPhone(messageAccount.getMobile());
    }
    if (request.isNew()) {
      request.set(client, company);
    }
    return inquiryRequestRepo.save(request);
  }

  public InquiryRequest sendInquiryRequest(ClientContext client, ICompany company, InquiryRequest request) {
    InquiryRequest priceCheckRequest = saveInquiryRequest(client, company, request);
    MailMessage mailMessage = new MailMessage();
    mailMessage.setMessage(priceCheckRequest.getMailMessage());
    mailMessage.setFrom(priceCheckRequest.getSalemanEmail());
    mailMessage.setSubject(priceCheckRequest.getMailSubject());
    mailMessage.setTo(priceCheckRequest.getToList());
    mailMessage.setCc(priceCheckRequest.getCcList());
    mailMessage.setAttachments(request.getAttachments());
    graphApiService.sendEmailWithHtmlFormat(client, company, mailMessage);
    return priceCheckRequest;
  }

  public int updateShareableScope(ClientContext client, ICompany company, ShareableUpdate shareableUpdate) {
    final Employee employee = employeeLogic.getEmployee(client, company, client.getRemoteUser());
    Objects.assertNotNull(employee, "Employee not found!!!, login id: " + client.getRemoteUser());
    String sqlUpdate = "UPDATE " + shareableUpdate.computeTargetTableName() + " SET scope = :scope WHERE id IN (:target_ids)";
    Map<String, Object> params = new HashMap<>();
    params.put("scope", shareableUpdate.getScope().toString());
    params.put("target_ids", shareableUpdate.getTargetIds());
    final int updateCount = daoSupport.update(sqlUpdate, params);
    Objects.assertTrue(shareableUpdate.getTargetIds().size() - updateCount == 0, "Update failed!!!, login id: " + client.getRemoteUser());
    return updateCount;
  }

  public List<SqlMapRecord> inquiryRequestReport(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    AppPermission pricePermission = securityLogic.getAppPermission(client, company.getId(), "logistics", "user-logistics-prices");
    AppPermission salePermission = securityLogic.getAppPermission(client, company.getId(), "logistics", "user-logistics-sales");
    if (pricePermission == null && salePermission == null) return java.util.Collections.emptyList();

    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fforwarder/price/groovy/InquiryRequestSql.groovy";
    sqlParams.addParam("accessAccountId", client.getAccountId());

    ICompany targetCompany = client.getCompany();
    if (sqlParams.hasParam("companyCode") && StringUtil.isNotEmpty(sqlParams.getString("companyCode"))) {
      String companyCode = sqlParams.getString("companyCode");
      targetCompany = companyLogic.getCompany(client, companyCode);
      Objects.assertNotNull(targetCompany, "Company not found!!!, code : " + companyCode);
    }
    sqlParams.addParam("companyId", targetCompany.getId());

    if (salePermission.isGroupScope()) {
      String coreScriptDir = appEnv.addonPath("core", "groovy");
      String coreScriptFile = "net/datatp/module/hr/groovy/EmployeeSql.groovy";
      List<SqlMapRecord> accountIds = searchPlatformDbRecords(client, coreScriptDir, coreScriptFile, "FindEmployeeIdsByManagerId", sqlParams);
      List<Long> participantAccountIds = accountIds.stream()
        .map(record -> record.getLong("accountId", null))
        .collect(Collectors.toList());
      sqlParams.addParam("salemanAccountIds", participantAccountIds);
    }

    List<SqlMapRecord> inquiryRequests = searchDbRecords(client, scriptDir, scriptFile, "InquiryRequestReport", sqlParams);

    if (inquiryRequests.isEmpty()) {
      return inquiryRequests;
    }

    Set<Long> accountIds = inquiryRequests.stream()
      .map(record -> record.getLong("salemanAccountId"))
      .filter(Objects::nonNull)
      .collect(Collectors.toSet());

    Set<Long> companyIds = inquiryRequests.stream()
      .map(record -> record.getLong("companyId"))
      .filter(Objects::nonNull)
      .collect(Collectors.toSet());

    Map<String, SqlMapRecord> departmentMap = getCompanyAndDepartmentInfo(client, accountIds, companyIds);

    return mergeInquiryRequestWithAllInfo(inquiryRequests, departmentMap);
  }

  private Map<String, SqlMapRecord> getCompanyAndDepartmentInfo(ClientContext client, Set<Long> accountIds, Set<Long> companyIds) {
    if (accountIds.isEmpty() || companyIds.isEmpty()) {
      return new HashMap<>();
    }

    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fforwarder/price/groovy/InquiryRequestSql.groovy";

    SqlQueryParams params = new SqlQueryParams();
    params.addParam("companyIds", new ArrayList<Long>(companyIds));
    params.addParam("accountIds", new ArrayList<Long>(accountIds));

    List<SqlMapRecord> resultList = searchPlatformDbRecords(client, scriptDir, scriptFile, "InquiryRequestCompanyAndDepartmentInfo", params);

    return resultList.stream()
      .collect(Collectors.toMap(
        record -> record.getString("accountId") + "_" + record.getLong("companyId"),
        record -> record,
        (existing, replacement) -> existing
      ));
  }

  private List<SqlMapRecord> mergeInquiryRequestWithAllInfo(List<SqlMapRecord> inquiryRequests, Map<String, SqlMapRecord> departmentMap) {

    return inquiryRequests.stream()
      .map(request -> {
        SqlMapRecord mergedRecord = new SqlMapRecord();
        mergedRecord.putAll(request);

        Long companyId = request.getLong("companyId");
        String accountId = request.getString("salemanAccountId");

        if (accountId != null && companyId != null) {
          String deptKey = accountId + "_" + companyId;
          SqlMapRecord deptInfo = departmentMap.get(deptKey);

          if (deptInfo != null) {
            mergedRecord.put("companyLabel", deptInfo.getString("companyLabel"));
            mergedRecord.put("departmentLabel", deptInfo.getString("departmentLabel"));
            mergedRecord.put("departmentName", deptInfo.getString("departmentName"));
            mergedRecord.put("parentDepartmentLabel", deptInfo.getString("parentDepartmentLabel"));
          }
        }

        return mergedRecord;
      })
      .collect(Collectors.toList());
  }

  public List<SqlMapRecord> searchInquiryRequests(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fforwarder/price/groovy/InquiryRequestSql.groovy";
    sqlParams.addParam("accessAccountId", client.getAccountId());
    ICompany targetCompany = client.getCompany();
    if (sqlParams.hasParam("companyCode") && StringUtil.isNotEmpty(sqlParams.getString("companyCode"))) {
      String companyCode = sqlParams.getString("companyCode");
      targetCompany = companyLogic.getCompany(client, companyCode);
      Objects.assertNotNull(targetCompany, "Company not found!!!, code : " + companyCode);
    }
    sqlParams.addParam("companyId", targetCompany.getId());
    AppPermission pricePermission = securityLogic.getAppPermission(client, company.getId(), "logistics", "user-logistics-prices");
    AppPermission salePermission = securityLogic.getAppPermission(client, company.getId(), "logistics", "user-logistics-sales");
    if (pricePermission == null && salePermission == null) return java.util.Collections.emptyList();
    if (!sqlParams.hasParam("space")) sqlParams.addParam("space", "User");
    if (sqlParams.hasParam("filterMode") && StringUtil.isNotEmpty(sqlParams.getString("filterMode"))) {
      CommunicationAccount messageAccount = messageLogic.getCommunicationAccountByAccountId(client, client.getAccountId());
      Objects.assertNotNull(messageAccount, "Message Account not found!!!, login id: " + client.getRemoteUser());
      sqlParams.addParam("mailPattern", messageAccount.getEmail());
    }
    return searchDbRecords(client, scriptDir, scriptFile, "SearchInquiryRequest", sqlParams);
  }

  public List<SqlMapRecord> searchInquiryRequestSpace(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fforwarder/price/groovy/InquiryRequestSql.groovy";
    sqlParams.addParam("accessAccountId", client.getAccountId());
    sqlParams.addParam("companyId", client.getCompanyId());
    AppPermission pricePermission = securityLogic.getAppPermission(client, company.getId(), "logistics", "user-logistics-prices");
    AppPermission salePermission = securityLogic.getAppPermission(client, company.getId(), "logistics", "user-logistics-sales");
    if (pricePermission == null && salePermission == null) return java.util.Collections.emptyList();
    if (!sqlParams.hasParam("space")) sqlParams.addParam("space", "User");
    return searchDbRecords(client, scriptDir, scriptFile, "SearchInquiryRequestSpace", sqlParams);
  }

  public boolean deleteInquiryRequests(ClientContext client, ICompany company, List<Long> ids) {
    inquiryRequestRepo.deleteAllById(ids);
    return true;
  }

  public List<InquiryRequest> findByCompany(ClientContext client, ICompany company) {
    return inquiryRequestRepo.findByCompany(company.getId());
  }

  public List<InquiryRequest> findNoResponseInquiryRequests(ClientContext client, ICompany company) {
    return inquiryRequestRepo.findNoResponseInquiryRequests(company.getId());
  }

  public void updateToNoResponse(ClientContext client, ICompany company, List<Long> requestIds) {
    inquiryRequestRepo.updateToNoResponse(requestIds);
  }
}