package cloud.datatp.fforwarder.price.cron;

import cloud.datatp.fforwarder.core.common.Purpose;
import cloud.datatp.fforwarder.core.message.entity.CRMMessageSystem;
import cloud.datatp.fforwarder.price.InquiryRequestLogic;
import cloud.datatp.fforwarder.price.TransportPriceMiscService;
import cloud.datatp.fforwarder.price.entity.InquiryRequest;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import net.datatp.lib.executable.ExecutableContext;
import net.datatp.lib.executable.ExecutableUnit;
import net.datatp.lib.executable.Executor;
import net.datatp.module.backend.Notification;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.hr.EmployeeLogic;
import net.datatp.module.hr.entity.HRDepartmentMembership;
import net.datatp.security.client.ClientContext;
import net.datatp.util.ds.Arrays;
import net.datatp.util.ds.MapObject;
import net.datatp.util.text.DateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationContext;

public class InquiryAutomationExecutor extends Executor {

  private static final Logger log = LoggerFactory.getLogger(InquiryAutomationExecutor.class);

  public static class UpdateToNoResponseStatus extends ExecutableUnit {
    @Override
    public Notification execute(ApplicationContext appContext, ExecutableContext ctx) {
      ClientContext client = ctx.getClient();
      ICompany company = ctx.getCompany();
      InquiryRequestLogic inquiryRequestLogic = appContext.getBean(InquiryRequestLogic.class);
      TransportPriceMiscService transportPriceMiscService = appContext.getBean(TransportPriceMiscService.class);
      EmployeeLogic employeeLogic = appContext.getBean(EmployeeLogic.class);

      List<InquiryRequest> requests = inquiryRequestLogic.findNoResponseInquiryRequests(client, company);
      
      // Get today's start (00:00:00)
      Calendar cal = Calendar.getInstance();
      cal.set(Calendar.HOUR_OF_DAY, 0);
      cal.set(Calendar.MINUTE, 0);
      cal.set(Calendar.SECOND, 0);
      cal.set(Calendar.MILLISECOND, 0);
      Date todayStart = cal.getTime();

      // Filter requests with pricingDate before today
      List<InquiryRequest> filteredRequests = requests.stream()
          .filter(request -> request.getPricingDate() != null && request.getPricingDate().before(todayStart))
          .toList();

      log.info("Found {} no-response requests, {} requests before {}", requests.size(), filteredRequests.size(), DateUtil.asCompactDateTime(todayStart));

      if (!filteredRequests.isEmpty()) {
        log.info("Updating {} DONE (Pricing) requests to NO_RESPONSE", filteredRequests.size());
        List<Long> requestIds = filteredRequests.stream().map(InquiryRequest::getId).collect(Collectors.toList());
        transportPriceMiscService.updateToNoResponse(client, company, requestIds);

        for(InquiryRequest request : filteredRequests) {
          if(request.getPricingDate() == null) continue;
          CRMMessageSystem overDueZaloMessage = request.toOverDueZaloMessage(client);
          inquiryRequestLogic.getCrmMessageLogic().scheduleMessage(client, company, overDueZaloMessage);

          List<String> mailCcList = Arrays.asList("<EMAIL>");

          SqlQueryParams param = new SqlQueryParams();
          param.addParam("accessAccountId", request.getSalemanAccountId());
          List<SqlMapRecord> employeeIdsInDepartment = employeeLogic.findEmployeeIdsInDepartment(client, company, param);

          String firstManagerEmail = null;
          for (SqlMapRecord record : employeeIdsInDepartment) {
            String role = record.getString("employeeRole", null);
            if(HRDepartmentMembership.Role.parse(role) == HRDepartmentMembership.Role.Manager) {
              firstManagerEmail = record.getString("employeeEmail");
              mailCcList.add(firstManagerEmail);
              break;
            }
          }

          if(request.getPurpose() == Purpose.EXPORT) {
            mailCcList.add("<EMAIL>");
          } else {
            mailCcList.add("<EMAIL>");
          }

          CRMMessageSystem overDueMailMessage = request.toOverDueMailMessage(client, 1);
          Calendar calendar = Calendar.getInstance();
          calendar.setTime(request.getPricingDate());
          calendar.add(Calendar.HOUR, 48);
          overDueMailMessage.setScheduledAt(calendar.getTime());
          MapObject metadata = overDueMailMessage.getMetadata();
          metadata.put("ccList", mailCcList);
          overDueMailMessage.setMetadata(metadata);
          inquiryRequestLogic.getCrmMessageLogic().scheduleMessage(client, company, overDueMailMessage);
        }
      }
      return null;
    }
    
  }

  public InquiryAutomationExecutor() {
    register(new UpdateToNoResponseStatus());
  }
}