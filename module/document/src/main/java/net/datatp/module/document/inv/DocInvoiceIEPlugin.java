package net.datatp.module.document.inv;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.StringJoiner;

import net.datatp.module.document.DocumentLogic;
import net.datatp.module.document.accounting.entity.DocAccountingChargeSummary;
import net.datatp.module.document.accounting.entity.DocAccountingChargeSummaryItem;
import net.datatp.module.document.accounting.repo.DocAccountingChargeSummaryRepo;
import org.apache.commons.codec.binary.Base64;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import net.datatp.module.backend.Notification;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.document.DocumentIEPlugin;
import net.datatp.module.document.DocumentSetLogic;
import net.datatp.module.document.entity.Document;
import net.datatp.module.document.entity.DocumentSet;
import net.datatp.module.document.entity.DocumentSetDocumentRelation;
import net.datatp.module.document.entity.DocumentTask;
import net.datatp.module.document.entity.DocumentTaskStatus;
import net.datatp.module.document.entity.DocumentTaskType;
import net.datatp.module.document.entity.IDocumentIEEntity;
import net.datatp.module.document.inv.entity.DocInvoice;
import net.datatp.module.document.inv.entity.DocInvoiceHblDistribution;
import net.datatp.module.document.inv.entity.DocInvoiceSeller;
import net.datatp.module.document.inv.repo.DocInvoiceRepo;
import net.datatp.module.document.repo.DocumentSetDocumentRelationRepo;
import net.datatp.module.document.repo.DocumentSetRepo;
import net.datatp.module.msa.MSAClient;
import net.datatp.module.msa.MSAClientService;
import net.datatp.module.msa.ie.IEReporter;
import net.datatp.module.msa.ie.pdf.PDFDocument;
import net.datatp.module.storage.CompanyStorage;
import net.datatp.security.client.ClientContext;
import net.datatp.util.dataformat.DataSerializer;
import net.datatp.util.ds.MapObject;
import net.datatp.util.error.ErrorType;
import net.datatp.util.error.RuntimeError;
import net.datatp.util.text.StringUtil;

@Component
public class DocInvoiceIEPlugin extends DocumentIEPlugin {
  static String API_URL = "https://msa.datatp.cloud/api";
//  static String API_URL = "http://localhost:8888/api";

  @Autowired
  private DocumentLogic docLogic;

  @Autowired
  private DocInvoiceRepo invDocumentRepo;

  @Autowired
  private DocumentSetRepo documentSetRepo;

  @Autowired
  private DocumentSetLogic docSetLogic;

  @Autowired
  private DocAccountingChargeSummaryRepo chargeSummaryRepo;

  @Autowired
  private DocumentSetDocumentRelationRepo relationRepo;

  protected DocInvoiceIEPlugin() {
    super("invoice");
  }

  public DocInvoice saveDocInvoice(ClientContext client, ICompany company, DocInvoice invDocument) {
    invDocument.set(client, company);
    return invDocumentRepo.save(invDocument);
  }

  public DocInvoice getDocInvoice(ClientContext client, ICompany company, Long id) {
    return invDocumentRepo.getByDocumentId(company.getId(), id);
  }

  @Override
  @SuppressWarnings("unchecked")
  public <T> T getDocumentIEModel(ClientContext client, ICompany company, Long documentId) {
    return (T) getDocInvoice(client, company, documentId);
  }

  @Override
  public <T> int deleteDocumentIEModel(ClientContext client, ICompany company, Long documentId) {
    return invDocumentRepo.deleteByDocumentId(company.getId(), documentId);
  }

  private String padLeftWithZeros(String input, int length) {
    if (input == null) {
      return "";
    }
    return String.format("%1$" + length + "s", input).replace(' ', '0');
  }

  public DocInvoice getMappedVNInvoice(ClientContext client, ICompany company, Document doc, IEReporter reporter) {
    CompanyStorage storage = documentLogic.createCompanyStorage(client, company);
    String dataPath = doc.getPath();
    byte[] data = storage.getContent(dataPath);

    MSAClientService msaService = new MSAClientService();
    msaService.setMsaApiUrl(API_URL);
    MSAClient masClient = msaService.createMSAClient();
    String base64String = Base64.encodeBase64String(data);

    MapObject userParams = new MapObject();
    userParams.set("fileName", doc.getName());
    userParams.set("data", base64String);
    PDFDocument ieDoc =
        masClient.call("PDFDocumentIEService", "vninvoice_ie", userParams, PDFDocument.class);
    //System.out.println(DataSerializer.JSON.toString(ieDoc));

    DocInvoice docInvoice = new DocInvoice(doc.getId());
    docInvoice.mapFromVNInvoice(ieDoc, reporter, doc);

    List<DocInvoice> docInvoices = invDocumentRepo.findByDocumentId(company.getId(), doc.getId());
    invDocumentRepo.deleteAll(docInvoices);
    invDocumentRepo.flush();
    docInvoice.set(client, company);
    return invDocumentRepo.save(docInvoice);
  }

  private void setProcessStatus(ClientContext client, ICompany company, Document doc) {
    String processStatus = DocumentTaskType.AutoIE.toString() + "[" + DocumentTaskStatus.Done.toString() + "]";
    DocumentTask ieTask  = doc.getOrCreateTask(DocumentTaskType.AutoIE);
    ieTask.setAssigneeId(client.getAccountId());
    ieTask.setAssigneeName(client.getRemoteUser());
    ieTask.setStatus(DocumentTaskStatus.Done);
    ieTask.setProcessStatus(processStatus);
  }

  private String getDocumentCode(ClientContext client, ICompany company, DocInvoice docInvoice, Document doc) {
    String sellerTaxCode = docInvoice.getSeller().getSellerTaxCode();
    String invoiceNo = docInvoice.getSeries();

    String code = "";
    if(StringUtil.isNotBlank(sellerTaxCode) && StringUtil.isNotBlank(invoiceNo)) {
      code = sellerTaxCode + "/" + padLeftWithZeros(invoiceNo, 8);
    }

    if (!code.isEmpty()) {
      List<Document> foundDocuments = documentLogic.documentRepo.findByCode(company.getId(), code);
      if (!foundDocuments.isEmpty()) {
        Long setId = foundDocuments.get(0).getOriginDocumentSetId();
        Long docSetId = doc.getOriginDocumentSetId();

        if(!Objects.equals(setId, docSetId)) {
          DocumentSet documentSet = documentSetRepo.getById(company.getId(), setId);
          doc.setDescription("Document đã tồn tại trong hệ thống thuộc bộ [" + documentSet.getName() + "]");
        }
      }
    }

    return code;
  }

  @Override
  public IEReporter process(ClientContext client, ICompany company, Document doc) {
    IEReporter reporter = new IEReporter(doc.getName());
    DocInvoice docInvoice = getMappedVNInvoice(client, company, doc, reporter);

    String code = getDocumentCode(client, company, docInvoice, doc);
    setProcessStatus(client, company, doc);

    doc.setCode(code);
    documentLogic.saveDocument(client, company, doc);
    return reporter;
  }

  @Override
  public List<IEReporter> process(ClientContext client, ICompany company, List<Long> docIds) {
    List<Document> documents = documentLogic.findDocumentByIds(client, company, docIds);
    List<IEReporter> reporters = new ArrayList<>();
    for(Document document : documents) {
      IEReporter reporter = process(client, company, document);
      reporters.add(reporter);
    }
    return reporters;
  }

  @Override
  public <T> Notification process(ClientContext client, ICompany company, T model, String instruction) {
    // TODO Auto-generated method stub
    return null;
  }

  @Override
  public <T extends IDocumentIEEntity> T saveDocumentIEModel(ClientContext client, ICompany company, T model) {
    DocInvoice invDocument = DataSerializer.JSON.getObjectMapper().convertValue(model, DocInvoice.class);
    invDocument.set(client, company);
    onPostSave(client, company, invDocument);
    return (T) invDocumentRepo.save(invDocument);
  }

  public void onPostSave(ClientContext client, ICompany company, DocInvoice docInvoice) {
    Long docId = docInvoice.getDocumentId();
    Document document = documentLogic.getDocument(client, company, docId);
    String missingField = docInvoice.buildMissingField(docInvoice.getType());
    document.setMissingField(missingField);
  }

  @Override
  public Document renameDocument(ClientContext client, ICompany company, Document doc) {
    CompanyStorage companyStorage = documentLogic.createCompanyStorage(client, company);
    DocumentSet docSet = docSetLogic.getDocumentSet(client, company, doc.getOriginDocumentSetId());
    String fileExtension = doc.getFileExtension();
    if (fileExtension == null) {
      throw new RuntimeError(ErrorType.Unknown, "The file \"" + doc.getName() + "\" has no extension.");
    }
    DocInvoice docInvoice = invDocumentRepo.getByDocumentId(company.getId(), doc.getId());
    StringJoiner fileName = new StringJoiner("-");
    DocInvoiceSeller seller = docInvoice.getSeller();
    if (seller != null && StringUtil.isNotBlank(seller.getSellerTaxCode())) {
      fileName.add(seller.getSellerTaxCode().trim());
    }
    if (StringUtil.isNotBlank(docInvoice.getSeries())) {
      fileName.add(docInvoice.getSeries().trim());
    }
    if(StringUtil.isNotBlank(fileName.toString())) {
      String file = fileName + "." + fileExtension;
      String oldPath = doc.getPath();
      String newPath = docSet.getPath() + "/" + file;
      if(!newPath.equals(oldPath)) {
        doc.setName(file);
        doc.setPath(newPath);
        documentLogic.saveDocument(client, company, doc);
        companyStorage.move(client, oldPath, newPath);
      }
    }
    return doc;
  }

  public boolean linkInvDocumentsToHouseBillDocSets(ClientContext client, ICompany company, List<Long> docIds) {

    for(Long docId: docIds) {
      List<DocumentSetDocumentRelation> relations = relationRepo.findDocumentSetDocumentRelationByDocumentId(company.getId(), docId);

      for(DocumentSetDocumentRelation relation: relations) {
        Long documentSetId = relation.getDocumentSetId();
        DocumentSet documentSet = documentSetRepo.getById(company.getId(), documentSetId);
        if(documentSet != null && "HBL".equals(documentSet.getType())) {
          relationRepo.delete(relation);
          List<Long> documentIds = relationRepo.findDocumentIdWithDocumentSetId(company.getId(), documentSetId);
          if(documentIds.isEmpty()) {
            documentSetRepo.deleteById(documentSetId);
          }
        }
      }
    }

    for(Long docId: docIds) {
      DocInvoice docInvoice = invDocumentRepo.getByDocumentId(company.getId(), docId);
      if(docInvoice == null) continue;
      List<DocInvoiceHblDistribution> distributions = docInvoice.getDistributions();

      if(distributions != null) {
        for(DocInvoiceHblDistribution distribution: distributions) {
          String hblNo = distribution.getHblNo();
          if(StringUtil.isNotBlank(hblNo)) {
            List<DocumentSet> documentSets = documentSetRepo.findByTypeAndName(company.getId(), "HBL", hblNo);
            DocumentSet documentSet = new DocumentSet();
            if(documentSets.isEmpty()) {
              documentSet.setType("HBL");
              documentSet.setName(hblNo);
              documentSet.set(client, company);
              documentSetRepo.save(documentSet);
            } else {
              documentSet = documentSets.get(0);
            }
            List<DocumentSetDocumentRelation> relations = relationRepo.findDocumentSetDocumentRelation(company.getId(), documentSet.getId(), docId);
            if(relations.isEmpty()) {
              DocumentSetDocumentRelation relation = new DocumentSetDocumentRelation(documentSet.getId(), docId);
              docSetLogic.saveDocumentSetDocumentRelation(client, company, relation);
            }
          }
        }
      }
    }
    return true;
  }
}
