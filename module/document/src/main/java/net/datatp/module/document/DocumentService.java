package net.datatp.module.document;

import java.io.IOException;
import java.util.List;

import net.datatp.module.document.fee.DocFeeChargeSummaryIEPlugin;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import net.datatp.module.backend.Notification;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.document.accounting.DocAccountingChargeSummaryIEPlugin;
import net.datatp.module.document.accounting.entity.DocAccountingChargeSummary;
import net.datatp.module.document.entity.Document;
import net.datatp.module.document.entity.DocumentSet;
import net.datatp.module.document.entity.DocumentSetTag;
import net.datatp.module.document.inv.DocInvoiceIEPlugin;
import net.datatp.module.document.inv.entity.DocInvoice;
import net.datatp.module.document.receipt.DocReceiptChargeSummaryIEPlugin;
import net.datatp.module.document.tms.DocBFSOneLogic;
import net.datatp.module.document.tms.DocTmsFclSummaryIEPlugin;
import net.datatp.module.document.tms.entity.DocTmsChargeSummary;
import net.datatp.module.http.get.StoreInfo;
import net.datatp.module.http.upload.UploadResource;
import net.datatp.security.client.ClientContext;

@Service("DocumentService")
public class DocumentService {

  @Autowired
  private DocumentLogic documentLogic;

  @Autowired
  private DocumentSetLogic documentSetLogic;

  @Autowired
  private DocumentIELogic documentIELogic;

  @Autowired
  private DocBFSOneLogic bfsOneLogic;

  @Autowired
  private DocTmsFclSummaryIEPlugin docTmsFclSummaryIEPlugin;

  @Autowired
  private DocAccountingChargeSummaryIEPlugin docAccountingChargeSummaryIEPlugin;

  @Autowired
  private DocReceiptChargeSummaryIEPlugin docReceiptChargeSummaryIEPlugin;

  @Autowired
  private DocFeeChargeSummaryIEPlugin docFeeChargeSummaryIEPlugin;

  @Autowired
  private DocInvoiceIEPlugin docInvoiceIEPlugin;


  @Transactional(value = "documentTransactionManager", readOnly = true)
  public DocumentSetTag getDocumentSetTag(ClientContext client, ICompany company, Long id) {
    return documentLogic.getDocumentSetTag(client, company, id);
  }

  @Transactional("documentTransactionManager")
  public DocumentSetTag saveDocumentSetTag(ClientContext client, ICompany company, DocumentSetTag tag) {
    return documentLogic.saveDocumentSetTag(client, company, tag);
  }

  @Transactional(value = "documentTransactionManager", readOnly = true)
  public List<SqlMapRecord> searchDocumentSetCategories(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    return documentLogic.searchDocumentSetCategories(client, company, sqlParams);
  }

  @Transactional(value = "documentTransactionManager", readOnly = true)
  public List<SqlMapRecord> searchDocumentSetTags(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    return documentLogic.searchDocumentSetTags(client, company, sqlParams);
  }

  //DocumentSet
  @Transactional(value = "documentTransactionManager", readOnly = true)
  public DocumentSet getDocumentSet(ClientContext client, ICompany company, Long id) {
    return documentSetLogic.getDocumentSet(client, company, id);
  }

  @Transactional("documentTransactionManager")
  public DocumentSet saveDocumentSet(ClientContext client, ICompany company, DocumentSet set) {
    return documentSetLogic.saveDocumentSet(client, company, set);
  }

  @Transactional(value = "documentTransactionManager", readOnly = true)
  public List<SqlMapRecord> searchDocumentSets(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    return documentSetLogic.searchDocumentSets(client, company, sqlParams);
  }

  @Transactional("documentTransactionManager")
  public Notification deleteDocumentSets(ClientContext client, ICompany company, List<Long> docSetIds) {
    return documentSetLogic.deleteDocumentSets(client, company, docSetIds);
  }

  //Document
  @Transactional(value = "documentTransactionManager", readOnly = true)
  public Document getDocument(ClientContext client, ICompany company, Long id) {
    return documentLogic.getDocument(client, company, id);
  }

  @Transactional("documentTransactionManager")
  public Document saveDocument(ClientContext client, ICompany company, Document doc) {
    return documentLogic.saveDocument(client, company, doc);
  }

  @Transactional("documentTransactionManager")
  public List<Document> renameDocuments(ClientContext client, ICompany company, List<Long> docIds) {
    return documentIELogic.renameDocuments(client, company, docIds);
  }


  @Transactional("documentTransactionManager")
  public Notification uploadDocuments(ClientContext client, ICompany company, Long docSet, List<UploadResource> uploadResources) {
    return documentLogic.uploadDocuments(client, company, docSet, uploadResources);
  }

  @Transactional("documentTransactionManager")
  public List<Document> bulkSaveDocuments(ClientContext client, ICompany company, List<Document> docs) {
    return documentLogic.bulkSaveDocuments(client, company, docs);
  }

  @Transactional("documentTransactionManager")
  public boolean updateDocumentTypes(ClientContext client, ICompany company, List<Long> docIds, String docType) {
    return documentLogic.updateDocumentTypes(client, company, docIds, docType);
  }

  @Transactional(value = "documentTransactionManager", readOnly = true)
  public List<SqlMapRecord> searchDocuments(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    return documentLogic.searchDocuments(client, company, sqlParams);
  }


  @Transactional("documentTransactionManager")
    public Notification deleteDocuments(ClientContext client, ICompany company, List<Long> docIds) {
    return documentLogic.deleteDocuments(client, company, docIds);
  }

  @Transactional("documentTransactionManager")
  public StoreInfo downloadDocuments(ClientContext client, ICompany company, List<Long> documentIds, boolean keepFileName) {
    return documentLogic.downloadDocuments(client, company, documentIds, keepFileName);
  }

  @Transactional("documentTransactionManager")
  public StoreInfo mergePdfDocuments(ClientContext client, ICompany company, List<Long> documentIds) throws IOException {
    return documentLogic.mergePdfDocuments(client, company, documentIds);
  }

  @Transactional("documentTransactionManager")
  public Notification batchRequestDocumentIE(ClientContext client, ICompany company, List<Long> ids) {
    return documentIELogic.batchRequestDocumentIE(client, company, ids);
  }

  @Transactional("documentTransactionManager")
  public IEProcessReport requestDocumentIE(ClientContext client, ICompany company, List<Long> ids) {
    return documentIELogic.requestDocumentIE(client, company, ids);
  }

  @Transactional("documentTransactionManager")
  public Notification cancelDocumentIE(ClientContext client, ICompany company, List<Long> ids) {
    return documentIELogic.cancelDocumentIE(client, company, ids);
  }

  @Transactional(value = "documentTransactionManager", readOnly = true)
  public <T> T getDocumentIEModel(ClientContext client, ICompany company, Long documentId) {
    return documentIELogic.getDocumentIEModel(client, company, documentId);
  }

  @Transactional("documentTransactionManager")
  public IEProcessReport processIE(ClientContext client, ICompany company, List<Long> documentIds) {
    return documentIELogic.process(client, company, documentIds);
  }
  
  public List<String> getDocumentIEPluginNames(ClientContext client, ICompany company) {
    return documentIELogic.getPluginNames();
  }

  @Transactional("documentTransactionManager")
  public <T> Notification processIE(ClientContext client, ICompany company, Long documentId, T model, String instruction) {
    return documentIELogic.process(client, company, documentId, model, instruction);
  }

  @Transactional("documentTransactionManager")
  public Notification processDocumentSet(ClientContext client, ICompany company, DocumentSet set, String instruction) {
    return null;
  }

  //Inv Document
  @Transactional("documentTransactionManager")
  public DocInvoice saveInvDocument(ClientContext client, ICompany company, DocInvoice doc) {
    return documentIELogic.saveDocumentIEModel(client, company, doc);
  }

  @Transactional("documentTransactionManager")
  public DocTmsChargeSummary saveDocTmsChargeSummary(ClientContext client, ICompany company, DocTmsChargeSummary doc) {
    return documentIELogic.saveDocumentIEModel(client, company, doc);
  }

  @Transactional("documentTransactionManager")
  public DocAccountingChargeSummary saveDocAccountingChargeSummary(ClientContext client, ICompany company, DocAccountingChargeSummary doc) {
    return documentIELogic.saveDocumentIEModel(client, company, doc);
  }

  //TMS Inv Document
  @Transactional(value = "documentTransactionManager", readOnly = true)
  public List<SqlMapRecord> searchBFSOneHwbNoByContainer(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    return bfsOneLogic.searchBFSOneHwbNoByContainer(client, company, sqlParams);
  }

  @Transactional(value = "documentTransactionManager", readOnly = true)
  public List<SqlMapRecord> searchBFSOneHwbNoByDeclaration(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    return bfsOneLogic.searchBFSOneHwbNoByDeclaration(client, company, sqlParams);
  }

  @Transactional("documentTransactionManager")
  public DocTmsChargeSummary verifyInvoiceDocument(ClientContext client, ICompany company, Long documentSetId, DocTmsChargeSummary docTmsChargeSummary) {
    return docTmsFclSummaryIEPlugin.verifyInvoiceDocument(client, company, documentSetId, docTmsChargeSummary);
  }

  @Transactional("documentTransactionManager")
  public Document getAccountingChargeSummary(ClientContext client, ICompany company, Long docSetId) {
    return docAccountingChargeSummaryIEPlugin.getAccountingChargeSummary(client, company, docSetId);
  }

  @Transactional("documentTransactionManager")
  public Document getReceiptChargeSummary(ClientContext client, ICompany company, Long docSetId) {
    return docReceiptChargeSummaryIEPlugin.getReceiptChargeSummary(client, company, docSetId);
  }

  @Transactional("documentTransactionManager")
  public Document getFeeChargeSummary(ClientContext client, ICompany company, Long docSetId) {
    return docFeeChargeSummaryIEPlugin.getFeeChargeSummary(client, company, docSetId);
  }

  @Transactional("documentTransactionManager")
  public DocAccountingChargeSummary updateChargeSummary(ClientContext client, ICompany company, DocAccountingChargeSummary summary) {
    return docAccountingChargeSummaryIEPlugin.updateChargeSummary(client, company, summary);
  }

  @Transactional("documentTransactionManager")
  public DocAccountingChargeSummary createFeeChargeSummary(ClientContext client, ICompany company, List<Long> docIds) {
    return docFeeChargeSummaryIEPlugin.createFeeSummary(client, company, docIds);
  }

  @Transactional("documentTransactionManager")
  public DocAccountingChargeSummary createAccountingChargeSummary(ClientContext client, ICompany company, List<Long> docIds) {
    return docAccountingChargeSummaryIEPlugin.createChargeSummary(client, company, docIds);
  }

  @Transactional("documentTransactionManager")
  public DocAccountingChargeSummary createReceiptChargeSummary(ClientContext client, ICompany company, List<Long> docIds) {
    return docReceiptChargeSummaryIEPlugin.createChargeSummary(client, company, docIds);
  }

  @Transactional("documentTransactionManager")
  public boolean distributeToDocInvoice(ClientContext client, ICompany company, Long chargeSummaryId) {
    return docTmsFclSummaryIEPlugin.distributeToDocInvoice(client, company, chargeSummaryId);
  }

  @Transactional("documentTransactionManager")
  public boolean linkInvDocumentsToHouseBillDocSets(ClientContext client, ICompany company, List<Long> docIds) {
    return docInvoiceIEPlugin.linkInvDocumentsToHouseBillDocSets(client, company, docIds);
  }

}