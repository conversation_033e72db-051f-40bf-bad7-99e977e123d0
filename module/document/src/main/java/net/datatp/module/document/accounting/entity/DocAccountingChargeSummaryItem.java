package net.datatp.module.document.accounting.entity;

import java.io.Serial;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.fasterxml.jackson.annotation.JsonFormat;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.company.entity.CompanyEntity;
import net.datatp.module.document.inv.entity.DocInvoice;
import net.datatp.module.document.inv.entity.DocInvoiceItem;
import net.datatp.util.text.DateUtil;
import net.datatp.util.text.StringUtil;

@Entity
@Table(name = DocAccountingChargeSummaryItem.TABLE_NAME)
@Getter @Setter @NoArgsConstructor
public class DocAccountingChargeSummaryItem extends CompanyEntity {
	@Serial
	private static final long   serialVersionUID = 1L;
	public  static final String TABLE_NAME       = "document_accounting_charge_summary_item";

	@Column(name = "document_id")
	private Long documentId;

	@Column(name = "invoice_no")
	private String invoiceNo;

	@Column(name = "serial_no")
	private String serialNo;

	@Column(name = "receipt_no")
	private String receiptNo;

	@Column(name = "receipt_serial")
	private String receiptSerial;

	@Column(name = "receipt_number")
	private String receiptNumber;

	@Column(name = "declaration_number")
	private String declarationNumber;

	@Column(length = 5 * 1024)
	private String label;

	@Column(name = "fee_name")
	private String feeName;

	@Column(name = "seller_tax_code")
	private String sellerTaxCode;

	@Column(name = "seller_name")
	private String sellerName;

	@Column(name = "buyer_tax_code")
	private String buyerTaxCode;

	@Column(name = "buyer_name")
	private String buyerName;

	@Column(name = "payer_tax_code")
	private String payerTaxCode;

	@Column(name = "payer_name")
	private String payerName;

	@JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
	@Column(name = "invoice_date")
	private Date invoiceDate;

	@JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
	@Column(name = "receipt_date")
	private Date receiptDate;

	private int stt;

	@Column(name = "file_no")
	private String fileNo;

	@Column(name = "cont_no")
	private String contNo;

	private String symbol;

	private double total;

	@Column(name = "receipt_total")
	private double receiptTotal;

	@Column(name = "vat_charge")
	private double vatCharge;

	@Column(name = "final_charge")
	private double finalCharge;

	@Column(name = "lookup_code")
	private String lookupCode;

	@Column(name = "customer_lookup_code")
	private String customerLookupCode;

	@Column(name = "organization_lookup_code")
	private String organizationLookupCode;

	@Column(name = "lookup_website")
	private String lookupWebsite;

	@Column(name = "source_file")
	private String sourceFile;

	@Column(name = "missing_field")
	private String missingField;

	@Column(length = 5 * 1024)
	private String description;

	public DocAccountingChargeSummaryItem roundCharge() {
		BigDecimal totalBd       = BigDecimal.valueOf(total).setScale(0, RoundingMode.HALF_UP);
		BigDecimal vatChargeBd   = BigDecimal.valueOf(vatCharge).setScale(0, RoundingMode.HALF_UP);
		BigDecimal finalChargeBd = BigDecimal.valueOf(finalCharge).setScale(0, RoundingMode.HALF_UP);
		this.total       = totalBd.doubleValue();
		this.vatCharge   = vatChargeBd.doubleValue();
		this.finalCharge = finalChargeBd.doubleValue();
		return this;
	}

	public String getProcessStatus() {
		String missingField = this.missingField;
		String count = missingField.split("-")[0];
		String trimmed = count.substring(1, count.length() - 1);

		String[] parts = trimmed.split("/");

		if (parts.length == 2) {
			int res = Integer.parseInt(parts[0]);
			int expect = Integer.parseInt(parts[1]);

			if (res != expect) {
				return "MISSING";
			} else {
				return "PASS";
			}
		}

		return "MISSING";
	}

	private LinkedHashMap<String, Boolean> getInvProcessBooleanField() {
		LinkedHashMap<String, Boolean> processField = new LinkedHashMap<>();
		processField.put("inv:no", StringUtil.isEmpty(this.getInvoiceNo()));
		processField.put("inv:serial", StringUtil.isEmpty(this.getSerialNo()));
		processField.put("seller:tax_code", StringUtil.isEmpty(this.getSellerTaxCode()));
		processField.put("buyer:tax_code", StringUtil.isEmpty(this.getBuyerTaxCode()));
		processField.put("items", StringUtil.isEmpty(this.getLabel()));
		processField.put("charge:total", this.getTotal() == 0.0);
		processField.put("charge:vat", this.getVatCharge() == 0.0);
		processField.put("charge:final", this.getFinalCharge() == 0.0);
		processField.put("lookup_code", StringUtil.isEmpty(this.getLookupCode()));
		processField.put("lookup_website", StringUtil.isEmpty(this.getLookupWebsite()));
		return processField;
	}

	private LinkedHashMap<String, Boolean> getReceiptProcessBooleanField() {
		LinkedHashMap<String, Boolean> processField = new LinkedHashMap<>();
		processField.put("receipt_no", StringUtil.isEmpty(this.getReceiptNo()));
		processField.put("receipt_serial", StringUtil.isEmpty(this.getReceiptSerial()));
		processField.put("receipt_number", StringUtil.isEmpty(this.getReceiptNumber()));
		processField.put("declaration_number", StringUtil.isEmpty(this.getDeclarationNumber()));
		processField.put("seller:tax_code", StringUtil.isEmpty(this.getSellerTaxCode()));
		processField.put("payer:tax_code", StringUtil.isEmpty(this.getPayerTaxCode()));
		processField.put("items", StringUtil.isEmpty(this.getLabel()));
		processField.put("charge:total", this.getReceiptTotal() == 0.0);
		processField.put("lookup_code", StringUtil.isEmpty(this.getLookupCode()));
		processField.put("lookup_website", StringUtil.isEmpty(this.getLookupWebsite()));
		return processField;
	}

	public void buildMissingField(String type) {
		LinkedHashMap<String, Boolean> processField = new LinkedHashMap<>();

		if (type.equals("invoice")) {
			processField = getInvProcessBooleanField();
		}

		if (type.equals("receipt")) {
			processField = getReceiptProcessBooleanField();
		}

		List<String> missingFields = new ArrayList<>();
		for (Map.Entry<String, Boolean> entry : processField.entrySet()) {
			if (entry.getValue()) {
				missingFields.add(entry.getKey());
			}
		}

		StringBuilder output = new StringBuilder();

		output.append("[").append(
				processField.size() - missingFields.size()
		).append("/").append(processField.size()).append("]");

		if (!missingFields.isEmpty()) {
			output.append("-");
			for (int i = 0; i < missingFields.size(); i++) {
				output.append(missingFields.get(i));
				if (i < missingFields.size() - 1) {
					output.append(", ");
				}
			}
		}

		this.setMissingField(output.toString());
	}

	public void mapFromDocInvoice(DocInvoice docInvoice, String type) {
		String summaryItemLabel = docInvoice.getItems().stream()
				.map(DocInvoiceItem::getName)
				.collect(Collectors
						.joining("/"));

		this.setLabel(summaryItemLabel);

		if(type.equals("receipt")) {
			this.setReceiptDate(docInvoice.getReceiptDate());
			this.setReceiptNo(docInvoice.getReceiptNo());
			this.setReceiptSerial(docInvoice.getReceiptSerial());
			this.setReceiptNumber(docInvoice.getReceiptNumber());
			this.setDeclarationNumber(docInvoice.getDeclarationNumber());
			this.setSellerTaxCode(docInvoice.getSeller().getSellerTaxCode());
			this.setSellerName(docInvoice.getSeller().getSellerName());
			this.setPayerName(docInvoice.getPayer().getPayerName());
			this.setPayerTaxCode(docInvoice.getPayer().getPayerTaxCode());
			this.setReceiptTotal(docInvoice.getReceiptTotal());
		}

		if(type.equals("invoice")) {
			this.setInvoiceDate(docInvoice.getInvoiceDate());
			this.setInvoiceNo(docInvoice.getSeries());
			this.setSerialNo(docInvoice.getSymbol());
			this.setSellerTaxCode(docInvoice.getSeller().getSellerTaxCode());
			this.setSellerName(docInvoice.getSeller().getSellerName());
			this.setBuyerTaxCode(docInvoice.getBuyer().getBuyerTaxCode());
			this.setBuyerName(docInvoice.getBuyer().getBuyerName());
			this.setContNo(docInvoice.getAttrFromDocInvoiceItem("cont-no"));
			this.setFeeName(docInvoice.getAttrFromDocInvoiceItem("fee-name"));
			this.setTotal(docInvoice.getTotal());
			this.setVatCharge(docInvoice.getVatCharge());
			this.setFinalCharge(docInvoice.getFinalCharge());
		}

		this.setLookupCode(docInvoice.getLookupCode());
		this.setLookupWebsite(docInvoice.getLookupWebsite());
		this.setCustomerLookupCode(docInvoice.getCustomerLookupCode());
		this.setOrganizationLookupCode(docInvoice.getOrganizationLookupCode());
	}
}
