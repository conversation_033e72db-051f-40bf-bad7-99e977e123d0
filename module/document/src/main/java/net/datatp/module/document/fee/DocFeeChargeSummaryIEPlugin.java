package net.datatp.module.document.fee;

import lombok.extern.slf4j.Slf4j;
import net.datatp.module.backend.Notification;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.document.DocumentLogic;
import net.datatp.module.document.DocumentSetLogic;
import net.datatp.module.document.accounting.entity.DocAccountingChargeSummary;
import net.datatp.module.document.accounting.entity.DocAccountingChargeSummaryItem;
import net.datatp.module.document.accounting.repo.DocAccountingChargeSummaryRepo;
import net.datatp.module.document.entity.Document;
import net.datatp.module.document.entity.DocumentSet;
import net.datatp.module.document.entity.DocumentSetDocumentRelation;
import net.datatp.module.document.entity.IDocumentIEEntity;
import net.datatp.module.document.inv.DocInvoiceIEPlugin;
import net.datatp.module.document.inv.entity.DocInvoice;
import net.datatp.module.document.repo.DocumentSetDocumentRelationRepo;
import net.datatp.module.document.tms.DocTmsSummaryIEPlugin;
import net.datatp.module.msa.ie.IEReporter;
import net.datatp.security.client.ClientContext;
import net.datatp.util.dataformat.DataSerializer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
public class DocFeeChargeSummaryIEPlugin extends DocTmsSummaryIEPlugin {

  @Autowired
  DocInvoiceIEPlugin docInvoiceIEPlugin;

  @Autowired
  private DocumentLogic docLogic;

  @Autowired
  private DocumentSetLogic docSetLogic;

  @Autowired
  private DocAccountingChargeSummaryRepo chargeSummaryRepo;

  @Autowired
  DocumentSetDocumentRelationRepo documentRelationRepo;

  protected DocFeeChargeSummaryIEPlugin() {
    super("fee-inv-summary");
  }

  @Override
  @SuppressWarnings("unchecked")
  public <T> T getDocumentIEModel(ClientContext client, ICompany company, Long documentId) {
    return (T) chargeSummaryRepo.getByDocumentId(company.getId(), documentId);
  }

  @Override
  public <T> int deleteDocumentIEModel(ClientContext client, ICompany company, Long documentId) {
    return chargeSummaryRepo.deleteByDocumentId(company.getId(), documentId);
  }

  @Override
  @SuppressWarnings("unchecked")
  public <T extends IDocumentIEEntity> T saveDocumentIEModel(ClientContext client, ICompany company, T model) {
    DocAccountingChargeSummary docTmsChargeSummary = DataSerializer.JSON.getObjectMapper().convertValue(model, DocAccountingChargeSummary.class);
    docTmsChargeSummary.calculate();
    docTmsChargeSummary.set(client, company);

    return (T) chargeSummaryRepo.save(docTmsChargeSummary);
  }


  public Document getFeeChargeSummary(ClientContext client, ICompany company, Long documentSetId) {
    List<Long> docIds = documentRelationRepo.findDocumentIdWithDocumentSetId(company.getId(), documentSetId);

    for(Long docId : docIds) {
      Document document = docLogic.documentRepo.getById(company.getId(), docId);
      if (!Objects.isNull(document) && document.getType().equalsIgnoreCase("fee-inv-summary")) return document;
    }

    return null;
  }

  private Document createNewFeeDocument(ClientContext client, ICompany company, Document originDocument) {
    List<Long> docIds = documentRelationRepo.findDocumentIdWithDocumentSetId(company.getId(), originDocument.getOriginDocumentSetId());

    for(Long docId : docIds) {
      Document document = docLogic.documentRepo.getById(company.getId(), docId);
      if (!Objects.isNull(document) && document.getType().equalsIgnoreCase( "fee-inv-summary")) {
        return document;
      }
    }

    Document doc = new Document();
    String docName = "fee-inv-summary-auto";

    DocumentSet docSet = docSetLogic.getDocumentSet(client, company, originDocument.getOriginDocumentSetId());

    doc.setOriginDocumentSetId(originDocument.getOriginDocumentSetId());
    doc.setName(docName);
    doc.setPath(docSet.getPath() + "/" + docName);
    doc.setType("fee-inv-summary");
    doc.set(client, company);
    doc = docLogic.documentRepo.save(doc);

    DocumentSetDocumentRelation relation = new DocumentSetDocumentRelation(docSet, doc);
    docSetLogic.saveDocumentSetDocumentRelation(client, company, relation);

    return doc;
  }

  public DocAccountingChargeSummary createFeeSummary(ClientContext client, ICompany company, List<Long> docIds) {
    List<Document> documents = docLogic.findDocumentByIds(client, company, docIds);
    List<IEReporter> reporters = new ArrayList<>();

    Document firstDocument = documents.get(0);
    Document newDocument = createNewFeeDocument(client, company, firstDocument);
    DocAccountingChargeSummary chargeSummary = chargeSummaryRepo.getByDocumentId(company.getId(), newDocument.getId());

    if (Objects.isNull(chargeSummary)) {
      chargeSummary = new DocAccountingChargeSummary(newDocument.getId());
    } else {
      chargeSummary.getItems().clear();
    }

    for(Document document : documents) {
      if (Objects.equals(document.getType(), "invoice")) {
        IEReporter reporter = new IEReporter(document.getName());
        DocAccountingChargeSummaryItem chargeSummaryItem = new DocAccountingChargeSummaryItem();
        chargeSummaryItem.set(client, company);

        DocInvoice docInvoice = invDocumentRepo.getByDocumentId(company.getId(), document.getId());

        chargeSummaryItem.mapFromDocInvoice(docInvoice, document.getType());

        chargeSummaryItem.buildMissingField(document.getType());

        chargeSummaryItem.setDocumentId(document.getId());
        chargeSummaryItem.setSourceFile(document.getName());

        chargeSummary.getItems().add(chargeSummaryItem);
        reporters.add(reporter);
      }
    }

    chargeSummary.calculate();

    chargeSummary.set(client, company);
    return chargeSummaryRepo.save(chargeSummary);
  }

  public DocAccountingChargeSummary updateChargeSummary(ClientContext client, ICompany company, DocAccountingChargeSummary summary) {
    if (Objects.isNull(summary)) return null;

    for (DocAccountingChargeSummaryItem item : summary.getItems()) {
      item.set(client, company);

      Long documentId = item.getDocumentId();
      Document document = docLogic.documentRepo.getById(company.getId(), documentId);
      DocInvoice docInvoice = invDocumentRepo.getByDocumentId(company.getId(), documentId);

      if (docInvoice != null) {
        item.mapFromDocInvoice(docInvoice, document.getType());
        item.buildMissingField(document.getType());
      }
    }

    return chargeSummaryRepo.save(summary);
  }

  @Override
  public <T> T renameDocument(ClientContext client, ICompany company, Document doc) {
    return null;
  }

  @Override
  public IEReporter process(ClientContext client, ICompany company, Document doc) {
    return null;
  }

  @Override
  public <T> Notification process(ClientContext client, ICompany company, T model, String instruction) {
    return null;
  }

  @Override
  public List<IEReporter> process(ClientContext client, ICompany company, List<Long> documentIds) {
    return List.of();
  }
}
