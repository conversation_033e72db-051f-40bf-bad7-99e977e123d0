package cloud.datatp.fforwarder.sales.booking;

import java.util.Date;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cloud.datatp.fforwarder.sales.TmplModel;
import cloud.datatp.fforwarder.sales.booking.entity.Booking;

import net.datatp.module.account.AccountService;
import net.datatp.module.account.entity.Account;
import net.datatp.module.app.AppEnv;
import net.datatp.security.client.ClientContext;
import net.datatp.module.communication.entity.Message;
import net.datatp.module.company.CompanyService;
import net.datatp.module.company.entity.Company;
import net.datatp.module.company.tmpl.TmplService;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.http.get.GETContent;
import net.datatp.module.http.get.GETTmpStoreHandler;
import net.datatp.module.http.get.StoreInfo;
import net.datatp.module.partner.PartnerLogic;
import net.datatp.module.partner.entity.Partner;
import net.datatp.module.storage.IStorageService;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;
import net.datatp.util.text.DateUtil;

@Service
public class BookingPrintService {
  @Autowired
  private AccountService accountService;

  @Autowired
  private IStorageService storageService;

  @Autowired
  private TmplService tmplService;

  @Autowired
  private GETTmpStoreHandler tmpStoreHandler;

  @Autowired
  private PartnerLogic partnerLogic;

  @Autowired
  CompanyService companyService;

  @Autowired
  AppEnv appEnv;

  public Message createBookingMessage(ClientContext client, ICompany company, BookingMessageRequest req) {
    Message message = new Message() ;
    message.setSenderAccountId(client.getAccountId());
    Booking booking =  req.getBooking();

    MapObject model =  new MapObject("bookingMessageRequest", req);
    Long clientPartnerId = booking.getInquiry().getClientPartnerId();
    if (clientPartnerId != null) {
      Partner clientPartner = partnerLogic.getById(client, company, clientPartnerId);
      Objects.assertNotNull(clientPartner, "Client {0} is not found", clientPartnerId);
      addClientContext(model, client, clientPartner.getLoginId());
    }

    model.add("dateNow", DateUtil.COMPACT_DATE.format(new Date()));
    model.add("booking", booking);
    String content = tmplService.render(company, "db:email:sales/booking/booking.hbs", model);
    message.setContent(content);
    return message ;
  }

  public StoreInfo createBookingPrint(ClientContext client, ICompany company, String format, Booking booking) {
    TmplModel tmplModel = new TmplModel(client, company);
    tmplModel.initCompanyInfo(companyService);
    tmplModel.initStoragePath(appEnv, storageService);

    MapObject model = new MapObject("booking", booking);
    model.add("model", tmplModel);

    byte[] data = tmplService.render(company, "db:print:logistics/sales/booking/booking-note.hbs", format, model);
    GETContent getContent = new GETContent("booking." + format, data);
    return tmpStoreHandler.store(client, getContent, true);
  }

  private void addClientContext(MapObject model, ClientContext client, String loginId) {
    Account account = accountService.getAccount(client, loginId);
    if(Objects.nonNull(account)) model.add("ClientContext", account);
  }
}