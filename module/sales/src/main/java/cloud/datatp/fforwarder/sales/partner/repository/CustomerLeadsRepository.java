package cloud.datatp.fforwarder.sales.partner.repository;

import cloud.datatp.fforwarder.sales.partner.entity.CustomerLeads;
import java.io.Serializable;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface CustomerLeadsRepository extends JpaRepository<CustomerLeads, Serializable> {

  @Query("SELECT t FROM CustomerLeads t WHERE t.taxCode = :taxCode ORDER BY t.date DESC")
  List<CustomerLeads> findByTaxCode(@Param("taxCode") String taxCode);

  @Query("SELECT t FROM CustomerLeads t WHERE t.taxCode = :searchPattern OR t.name LIKE CONCAT('%', :searchPattern, '%') ORDER BY t.date DESC")
  List<CustomerLeads> findByTaxCodeOrName(@Param("searchPattern") String searchPattern);

  @Query("SELECT t FROM CustomerLeads t WHERE t.companyId = :companyId")
  List<CustomerLeads> findByCompanyId(@Param("companyId") Long companyId);

  @Query("SELECT t FROM CustomerLeads t WHERE t.id = :id")
  CustomerLeads getById(@Param("id") Long id);
  
  @Query("SELECT t FROM CustomerLeads t WHERE t.code = :code")
  CustomerLeads getByCode(@Param("code") String code);

  @Query("SELECT t FROM CustomerLeads t WHERE t.id IN :ids")
  List<CustomerLeads> findByIds(@Param("ids") List<Long> ids);
  
}