package cloud.datatp.fforwarder.sales.integration;

import cloud.datatp.fforwarder.core.db.CRMDaoService;
import cloud.datatp.fforwarder.core.db.CRMSqlQueryUnitManager;
import cloud.datatp.fforwarder.core.partner.BFSOnePartnerLogic;
import cloud.datatp.fforwarder.core.partner.SalemanPartnerObligationLogic;
import cloud.datatp.fforwarder.core.partner.entity.BFSOnePartner;
import cloud.datatp.fforwarder.core.partner.entity.BFSOnePartnerGroup;
import cloud.datatp.fforwarder.core.partner.entity.SalemanPartnerObligation;
import cloud.datatp.fforwarder.sales.booking.dto.BookingModel;
import cloud.datatp.fforwarder.sales.common.BookingShipmentInfo;
import cloud.datatp.fforwarder.sales.inquiry.entity.SpecificServiceInquiry;
import cloud.datatp.fforwarder.sales.partner.CustomerLeadsLogic;
import cloud.datatp.fforwarder.sales.partner.entity.CustomerLeads;
import cloud.datatp.fforwarder.sales.partner.entity.PartnerEventHistory;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import javax.sql.DataSource;
import lombok.extern.slf4j.Slf4j;
import net.datatp.module.account.AccountLogic;
import net.datatp.module.account.entity.Account;
import net.datatp.module.company.entity.CompanyConfig;
import net.datatp.module.data.db.ExternalDataSourceManager;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.SqlSelectView;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.hr.EmployeeReadLogic;
import net.datatp.module.hr.entity.Employee;
import net.datatp.module.service.ExecutableUnitManager;
import net.datatp.security.client.ClientContext;
import net.datatp.util.dataformat.DataSerializer;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;
import net.datatp.util.error.RuntimeError;
import net.datatp.util.text.DateUtil;
import net.datatp.util.text.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class BFSOneCRMLogic extends CRMDaoService {
  private final String accessCode = "jWQ953gZwSg6FLfnfCtJmdAxZJcYEABSQX8HOjAguSgmLHtFxThGi6ZDuBmoo1hNNI3W3r6FGPDYuGacRxYOIxvQUfoTOt4LL6rzcrIBwyTBa77Nvigxwotj8Ay97iqyiQOZ51zFhvBpWmXTG6/l/gNW+LmQ8WmX6RRzLOzbRmD5RBRdl5HwE8O5EHwjaeHMSPHG+BO+L3zHO69XWN/b3TqO+M2xdsPF32/FlwYDNKxNjDA4uJIPSySnwXw2iph0Zf7t+Ws6oT5mRYvF30r1bRYuOvKOMAQBgPiJ9PO5JkPUwsOKF2BeNBG0Y84fH6DL3fxqhcVl6VmHeVmab+wE+g==";

  @Autowired
  private EmployeeReadLogic employeeLogic;

  @Autowired
  private AccountLogic accountLogic;

  @Autowired
  private BFSOneApi bfsOneApi;

  @Autowired
  private BFSOnePartnerLogic bfsOnePartnerLogic;

  @Autowired
  private SalemanPartnerObligationLogic obligationLogic;

  @Autowired
  private CustomerLeadsLogic customerLeadsLogic;

  @Autowired
  private ExecutableUnitManager executableUnitManager;

  public BookingModel createInternalBooking(ClientContext client, ICompany company, BookingModel bookingModel) {
    Employee employee = employeeLogic.getByAccount(client, company, bookingModel.getSenderAccountId());
    Objects.assertNotNull(employee, "Employee is not found, login id = " + bookingModel.getSenderLabel());
    Objects.assertNotNull(employee.getBfsoneCode(), "Sender Contact Code is not found, name = " + bookingModel.getSenderLabel());
    Objects.assertNotNull(employee.getBfsoneUsername(), "Sender Username is not found, name = " + bookingModel.getSenderLabel());

    boolean isValidEmp = StringUtil.isNotEmpty(employee.getBfsoneCode()) && StringUtil.isNotEmpty(employee.getBfsoneUsername());
    Objects.assertTrue(isValidEmp, "Sender Contact is not valid, name = " + bookingModel.getSenderLabel());
    String bfsoneEmployeeCode = employee.getBfsoneCode();
    String bfsoneUsername = employee.getBfsoneUsername();
    String authenticate = bfsOneApi.authenticate(accessCode, bfsoneEmployeeCode, bfsoneUsername);
    log.info("------------------------TOKEN---------------------------\n");
    DataSerializer.JSON.dump(authenticate);
    log.info("--------------------------------------------------------\n");

    SpecificServiceInquiry inquiry = bookingModel.getInquiry();
    Long clientPartnerId = inquiry.getClientPartnerId();
    BFSOnePartner customerPartner = bfsOnePartnerLogic.getById(client, clientPartnerId);
    Objects.assertNotNull(customerPartner, "Customer is not found, id = " + clientPartnerId);
    MapObject bfsOneIBooking = bookingModel.toBFSOneIBooking(customerPartner);

    BFSOnePartner agent = bfsOnePartnerLogic.getById(client, inquiry.getHandlingAgentPartnerId());
    if (agent != null) bfsOneIBooking.put("AgentID", agent.getBfsonePartnerCode());

    /* --------------Saleman - Cus/ Docs------------*/
    String receiverEmpl = bookingModel.getReceiverEmployeeLabel();
    String receiverBFSOneCode = bookingModel.getReceiverBFSOneCode();
    Objects.assertNotNull(receiverBFSOneCode, "Receiver Contact Code is not found, name = " + receiverEmpl);
    bfsOneIBooking.put("ReceiveUserID", receiverBFSOneCode);
    bfsOneIBooking.put("SendUserID", bfsoneEmployeeCode);

    BookingShipmentInfo shipmentInfo = bookingModel.getShipmentInfo();
    BFSOnePartner coloader = bfsOnePartnerLogic.getById(client, shipmentInfo.getCarrierPartnerId());
    if (coloader != null) bfsOneIBooking.put("ColoaderID", coloader.getBfsonePartnerCode());
    MapObject savedIB = null;
    try {
      String bkgID = bfsOneIBooking.getString("BkgID");
      if (StringUtil.isNotBlank(bkgID)) {
        MapObject bfsOneBKExist = bfsOneApi.loadIBooking(authenticate, bkgID);
        DataSerializer.JSON.dump(bfsOneBKExist);
        String bkgIDExist = bfsOneBKExist.getString("BkgID", null);
        if (StringUtil.isEmpty(bkgIDExist)) bfsOneIBooking.put("BkgID", null);
      }
      savedIB = bfsOneApi.createIBooking(authenticate, bfsOneIBooking);
    } catch (Exception ex) {
      log.info(ex.getMessage());
      log.error("Error when create internal booking: \n");
      DataSerializer.JSON.dump(bfsOneIBooking);
      throw RuntimeError.UnknownError(ex.getMessage());
    }

    String bkgID = savedIB.getString("BkgID");
    Objects.assertNotNull(bkgID, "BFSOne Reference is not found, id = " + bookingModel.getId());
    bookingModel.setReferenceNo(bkgID);
    return bookingModel;
  }

  public Boolean deleteInternalBooking(ClientContext client, ICompany company, String ibCode) {
    Employee employee = employeeLogic.getEmployee(client, company, client.getRemoteUser());
    Objects.assertNotNull(employee, "Employee is not found, login id = " + client.getRemoteUser());
    String bfsoneEmployeeCode = employee.getBfsoneCode();
    String bfsoneUsername = employee.getBfsoneUsername();
    String authenticate = bfsOneApi.authenticate(accessCode, bfsoneEmployeeCode, bfsoneUsername);
    bfsOneApi.deleteInternalBooking(authenticate, ibCode);
    return true;
  }

  public MapObject loadInternalBooking(ClientContext client, ICompany company, String ibCode) {
    Employee employee = employeeLogic.getEmployee(client, company, client.getRemoteUser());
    Objects.assertNotNull(employee, "Employee is not found, login id = " + client.getRemoteUser());
    String bfsoneEmployeeCode = employee.getBfsoneCode();
    String bfsoneUsername = employee.getBfsoneUsername();
    String authenticate = bfsOneApi.authenticate(accessCode, bfsoneEmployeeCode, bfsoneUsername);
    return bfsOneApi.loadIBooking(authenticate, ibCode);
  }

  public BFSOnePartner createBFSPartner(ClientContext client, ICompany company, BFSOnePartner partner) {
    final boolean isNew = partner.isNew();
    if(isNew && StringUtil.isNotEmpty(partner.getBfsonePartnerCode())) {
      //TODO: fetch BFSOnePartner from BFSOne system to update permission.
      BFSOnePartner bfsOnePartner = bfsOnePartnerLogic.fetchBFSOnePartnersByCode(client, company, partner.getBfsonePartnerCode());
      Objects.assertNotNull(bfsOnePartner, "BFSOne Partner is not found, code = " + partner.getBfsonePartnerCode());
      List<Employee> employees = employeeLogic.findEmployeeByBFSOneCode(client, bfsOnePartner.getSaleOwnerContactCode());
      Objects.assertNotNull(employees, "Saleman is not found, code = " + bfsOnePartner.getSaleOwnerContactCode());
      Employee saleman = employees.get(0);
      Objects.assertNotNull(saleman, "Saleman is not found, code = " + bfsOnePartner.getSaleOwnerContactCode());
      List<BFSOnePartner> partners = bfsOnePartnerLogic.importPartners(client, saleman, Collections.singletonList(partner));
      return partners.get(0);
    } else {
      Employee saleman = employeeLogic.getEmployee(client, company, client.getRemoteUser());
      Objects.assertNotNull(saleman, "Creator Employee {} is not found", client.getRemoteUser());
      partner.withInputEmployee(saleman);
      partner.computePartnerGroup();
      partner.withBillReference();
      MapObject bfsOnePartner = createBFSOnePartner(client, company, partner);
      String partnerCode = bfsOnePartner.getString("PartnerCodeTemp", "");
      partner.setBfsonePartnerCode(partnerCode);
      partner.setPartnerCodeTemp(partnerCode);
      // key: isUnApproved (boolean)
      String unApprovedReason = bfsOnePartner.getString("ReasionUnApproved", "");
      String note = partner.getNote();
      note = note + " " + unApprovedReason;
      partner.setNote(note);
      BFSOnePartner savedPartner = bfsOnePartnerLogic.saveBFSOnePartner(client, partner);
      obligationLogic.createIfNotExists(client, new SalemanPartnerObligation(saleman, savedPartner));

      if(isNew) {
        String leadCode = savedPartner.getLeadCode();
        PartnerEventHistory createdEvent = PartnerEventHistory.partnerCreator(savedPartner, saleman);
        if(StringUtil.isNotEmpty(leadCode)) {
          CustomerLeads lead = customerLeadsLogic.getCustomerLeadByCode(client, company, leadCode);
          Objects.assertNotNull(lead, "Lead not found: leadCode = " + leadCode);
          createdEvent.setLabel("Customer Converted from lead: " + leadCode);
        }
        customerLeadsLogic.savePartnerEventHistory(client, company, createdEvent);
      }
      return savedPartner;
    }
  }

  public MapObject createBFSOnePartner(ClientContext client, ICompany company, BFSOnePartner partner) {
    MapObject bfsOnePartner = partner.toBFSOnePartner();
    Employee saleman = employeeLogic.getByAccount(client, company, client.getAccountId());
    Objects.assertNotNull(saleman, "Saleman is not found, account id = " + client.getAccountId());
    Account salemanAccount = accountLogic.getAccountById(client, client.getAccountId());
    String bfsoneEmployeeCode = saleman.getBfsoneCode();
    String bfsoneUsername = saleman.getBfsoneUsername();
    bfsOnePartner.put("RequestUser", bfsoneUsername);
    bfsOnePartner.put("SalemanID", bfsoneEmployeeCode);
    bfsOnePartner.put("Email_Request", salemanAccount.getEmail());

    log.info("------------------------Partner---------------------------\n");
    DataSerializer.JSON.dump(bfsOnePartner);
    log.info("--------------------------------------------------------\n");

    BFSOnePartnerGroup group = partner.getPartnerGroup();
    String authenticate = bfsOneApi.authenticate(accessCode, bfsoneEmployeeCode, bfsoneUsername);
    return bfsOneApi.createPartner(group, authenticate, bfsOnePartner);
  }

  public List<SqlMapRecord> findBookingLocalByHawb(ClientContext client, String hawb) {
    SqlQueryParams params = new SqlQueryParams();
    params.addParam("hawb", hawb.trim());
    String scriptDir = bfsOnePartnerLogic.getAppEnv().addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fforwarder/core/groovy/FindBookingLocalSql.groovy";
    String scriptName = "FindBookingLocalByHawb";
    CompanyConfig companyConfig = bfsOnePartnerLogic.getCompanyConfigLogic().getCompanyConfigByCompanyId(client, client.getCompanyId());
    ExternalDataSourceManager.DataSourceParams dsPrams = companyConfig.getSubConfigAs("bfsone.ds", ExternalDataSourceManager.DataSourceParams.class);
    DataSource ds = bfsOnePartnerLogic.getDataSourceManager().getDataSource(client, dsPrams);
    CRMSqlQueryUnitManager.QueryContext queryContext = bfsOnePartnerLogic.getSqlQueryUnitManager().create(scriptDir, scriptFile, scriptName);
    SqlSelectView view = queryContext.createSqlSelectView(ds, params);
    List<SqlMapRecord> records = view.getSqlMapRecords();
    log.info("Retrieved {} records", records.size());
    return records;
  }

  public MapObject saveBFSOneCustomerLead(ClientContext client, ICompany company, CustomerLeads lead) {
    Employee employee = employeeLogic.getEmployeeRepo().getByAccountId(lead.getSalemanCompanyId(), lead.getSalemanAccountId());
    Objects.assertNotNull(employee, "Employee is not found, account id = " + lead.getSalemanAccountId());
    String bfsoneEmployeeCode = employee.getBfsoneCode();
    String bfsoneUsername = employee.getBfsoneUsername();
    String authenticate = bfsOneApi.authenticate(accessCode, bfsoneEmployeeCode, bfsoneUsername);

    String leadCode = lead.getBfsoneLeadCode() != null ? lead.getBfsoneLeadCode() : lead.getCode();
    if (StringUtil.isNotEmpty(leadCode)) {
      MapObject bfsOneCustomerLeadInDb = bfsOneApi.loadLead(authenticate, leadCode);
      if (Objects.nonNull(bfsOneCustomerLeadInDb)) {
        String contactId = bfsOneCustomerLeadInDb.getString("ContactID");
        if(StringUtil.isNotEmpty(contactId)) {
          lead.setBfsoneLeadCode(contactId);
          lead.setCode(contactId);
        }
      }
    }
    MapObject bfsOneCustomerLead = lead.toBFSOneCustomerLead(bfsoneUsername);
    return bfsOneApi.saveBFSOneCustomerLead(authenticate, bfsOneCustomerLead);
  }
  
  public MapObject loadLead(ClientContext client, String leadCode, Employee employee) {
    Objects.assertNotNull(employee, "Employee is not found");
    String bfsoneEmployeeCode = employee.getBfsoneCode();
    String bfsoneUsername = employee.getBfsoneUsername();
    String authenticate = bfsOneApi.authenticate(accessCode, bfsoneEmployeeCode, bfsoneUsername);
    return bfsOneApi.loadLead(authenticate, leadCode);
  }
  
  public MapObject deleteLead(ClientContext client, CustomerLeads lead) {
    Employee employee = employeeLogic.getByAccount(client, client.getCompany(), lead.getSalemanAccountId());
    Objects.assertNotNull(employee, "Employee is not found, login id = " + client.getRemoteUser());
    String bfsoneEmployeeCode = employee.getBfsoneCode();
    String bfsoneUsername = employee.getBfsoneUsername();
    String authenticate = bfsOneApi.authenticate(accessCode, bfsoneEmployeeCode, bfsoneUsername);
    String leadCode = lead.getBfsoneLeadCode() != null ? lead.getBfsoneLeadCode() : lead.getCode();
    return bfsOneApi.deleteLead(authenticate, leadCode);
  }

  public List<SqlMapRecord> checkPartnerExistInSystem(ClientContext client, String searchPattern) {
    /* check partners, agent/ agent potential, customer lead */
    Objects.assertNotNull(StringUtil.isNotEmpty(searchPattern), "Partner Name/ Tax code must be required!!!");
    SqlQueryParams sqlParams = new SqlQueryParams();
    String normalizePattern = searchPattern.trim().toUpperCase(Locale.ROOT);
    sqlParams.addParam("searchPattern", normalizePattern);
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fforwarder/core/groovy/BFSOnePartnerSql.groovy";
    String scriptName = "CheckBFSOnePartnerByTaxCode";
    CompanyConfig companyConfig = bfsOnePartnerLogic.getCompanyConfigLogic().getCompanyConfigByCompanyId(client, client.getCompanyId());
    ExternalDataSourceManager.DataSourceParams dsPrams = companyConfig.getSubConfigAs("bfsone.ds", ExternalDataSourceManager.DataSourceParams.class);
    DataSource ds = bfsOnePartnerLogic.getDataSourceManager().getDataSource(client, dsPrams);
    CRMSqlQueryUnitManager.QueryContext queryContext = sqlQueryUnitManager.create(scriptDir, scriptFile, scriptName);
    SqlSelectView view = queryContext.createSqlSelectView(ds, sqlParams);
    List<SqlMapRecord> existingPartners = view.renameColumWithJavaConvention().getSqlMapRecords();

    if(net.datatp.util.ds.Collections.isNotEmpty(existingPartners)) {
      Map<String, SqlMapRecord> existingPartnerMap = new HashMap<>();
      for(SqlMapRecord partner: existingPartners) {
        String partnerId = partner.getString("partnerId", "");
        if(partnerId.startsWith("AG")) partner.put("partnerType", "AGENT");
        else partner.put("partnerType", "CUSTOMER");
        if(existingPartnerMap.containsKey(partnerId)) continue;
        existingPartnerMap.put(partnerId, partner);
      }
      sqlParams.addParam("partnerIds", new ArrayList<>(existingPartnerMap.keySet()));
      CRMSqlQueryUnitManager.QueryContext queryContextBFSOne = sqlQueryUnitManager.create(scriptDir, scriptFile, "FindTop10LatestTransactionByCustomer");
      DataSource bfsoneReportDataSource = bfsOnePartnerLogic.getLegacyDataSource();
      SqlSelectView viewBFSOne = queryContextBFSOne.createSqlSelectView(bfsoneReportDataSource, sqlParams);
      List<SqlMapRecord> top10Transactions = viewBFSOne.renameColumWithJavaConvention().getSqlMapRecords();
      if (net.datatp.util.ds.Collections.isNotEmpty(top10Transactions)) {
        Map<String, List<SqlMapRecord>> partnerTransactionMap = new LinkedHashMap<>();
        for (SqlMapRecord record : top10Transactions) {
          String customerCode = record.getString("customerCode");
          if(StringUtil.isEmpty(customerCode)) continue;
          partnerTransactionMap
            .computeIfAbsent(customerCode, k -> new ArrayList<>())
            .add(record);
        }

        for(String partnerCode: existingPartnerMap.keySet()) {
          if(!partnerTransactionMap.containsKey(partnerCode)) continue;
          SqlMapRecord record = existingPartnerMap.get(partnerCode);
          List<SqlMapRecord> transactions = partnerTransactionMap.get(partnerCode);
          SqlMapRecord lastedRecord = transactions.get(0);
          record.put("transactionId", lastedRecord.getString("transactionId"));
          record.put("transactionDate", lastedRecord.getString("reportDate"));
          record.put("shipmentType", lastedRecord.getString("shipmentType"));
          record.put("salemanContactId", lastedRecord.getString("salemanContactId"));
          for(SqlMapRecord rec: transactions) {
            Date reportDate = rec.getDate("reportDate", null);
            if(reportDate == null) continue;
            rec.put("reportDate", DateUtil.asCompactDate(reportDate));
          }
          record.put("transactionSummary", transactions);
        }
      }

    }

    List<CustomerLeads> customerLeads = customerLeadsLogic.findCustomerLeadByTaxCodeOrName(client, normalizePattern);
    if(net.datatp.util.ds.Collections.isNotEmpty(customerLeads)) {
      for (CustomerLeads lead : customerLeads) {
        SqlMapRecord leadRec = new SqlMapRecord();
        leadRec.put("partnerType", "LEAD");
        leadRec.put("partnerId", lead.getCode());
        leadRec.put("partnerName", lead.getName());
        leadRec.put("address", lead.getAddress());
        leadRec.put("taxCode", lead.getTaxCode());
        leadRec.put("dateCreated", DateUtil.asCompactDateTime(lead.getDate()));
        try {
          Employee salemanAccount = employeeLogic.getByAccount(client, null, lead.getSalemanAccountId());
          Objects.assertNotNull(salemanAccount, "Account is not found by id = {}", lead.getSalemanAccountId());
          leadRec.put("salemanLabel", salemanAccount.getLabel() + " (" + salemanAccount.getBfsoneUsername() + ")");
          leadRec.put("salemanUsername", salemanAccount.getBfsoneUsername());
        } catch (Exception e) {
          leadRec.put("salemanLabel", lead.getSalemanLabel());
        }
        existingPartners.add(leadRec);
      }
    }
    return existingPartners;
  }

}