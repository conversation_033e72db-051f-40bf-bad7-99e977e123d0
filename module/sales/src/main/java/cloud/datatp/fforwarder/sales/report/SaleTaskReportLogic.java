package cloud.datatp.fforwarder.sales.report;

import cloud.datatp.fforwarder.core.db.CRMDaoService;
import cloud.datatp.fforwarder.core.message.CRMMessageLogic;
import cloud.datatp.fforwarder.core.message.entity.CRMMessageSystem;
import cloud.datatp.fforwarder.core.message.entity.MessageType;
import cloud.datatp.fforwarder.core.partner.BFSOnePartnerLogic;
import cloud.datatp.fforwarder.core.partner.entity.BFSOnePartner;
import cloud.datatp.fforwarder.price.InquiryRequestLogic;
import cloud.datatp.fforwarder.price.common.ClientPartnerType;
import cloud.datatp.fforwarder.sales.common.TaskNotificationTemplate;
import cloud.datatp.fforwarder.sales.partner.CustomerLeadsLogic;
import cloud.datatp.fforwarder.sales.partner.entity.CustomerLeads;
import cloud.datatp.fforwarder.sales.partner.entity.PartnerEventHistory;
import cloud.datatp.fforwarder.sales.report.entity.SalesDailyTask;
import cloud.datatp.fforwarder.sales.report.entity.SalesDailyTask.SalesTaskStatus;
import cloud.datatp.fforwarder.sales.report.repository.SalesDailyTaskRepository;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.Getter;
import net.datatp.module.account.AccountLogic;
import net.datatp.module.account.entity.Account;
import net.datatp.module.communication.CommunicationMessageLogic;
import net.datatp.module.communication.entity.CommunicationAccount;
import net.datatp.module.company.CompanyLogic;
import net.datatp.module.core.security.SecurityLogic;
import net.datatp.module.core.security.entity.AppPermission;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.SqlQueryUnitManager;
import net.datatp.module.data.db.SqlSelectView;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.data.db.seq.SeqService;
import net.datatp.module.graphapi.GraphApiService;
import net.datatp.module.hr.EmployeeLogic;
import net.datatp.module.http.upload.UploadService;
import net.datatp.module.wfms.entity.EntityTask;
import net.datatp.module.wfms.entity.EntityTaskApprovalStatus;
import net.datatp.module.wfms.entity.EntityTaskRequest;
import net.datatp.module.wfms.entity.EntityTaskStatus;
import net.datatp.module.zalo.ZaloLogic;
import net.datatp.security.client.ClientContext;
import net.datatp.security.client.DataScope;
import net.datatp.util.ds.Collections;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;
import net.datatp.util.text.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Getter
public class SaleTaskReportLogic extends CRMDaoService {

  @Autowired
  private SalesDailyTaskRepository dailyTaskRepo;

  @Autowired
  private CommunicationMessageLogic messageLogic;

  @Autowired
  private AccountLogic accountLogic;

  @Autowired
  private GraphApiService graphApiService;

  @Autowired
  private UploadService uploadService;

  @Autowired
  private SeqService seqService;

  @Autowired
  private SecurityLogic securityLogic;

  @Autowired
  private ZaloLogic zaloLogic;

  @Autowired
  private EmployeeLogic employeeLogic;

  @Autowired
  private CompanyLogic companyLogic;

  @Autowired
  private InquiryRequestLogic inquiryReqLogic;

  @Autowired
  private CustomerLeadsLogic customerLeadsLogic;

  @Autowired
  private BFSOnePartnerLogic bfsonePartnerLogic;

  @Autowired
  private CommunicationMessageLogic communicationMessageLogic;

  @Autowired
  private CRMMessageLogic crmMessageLogic;

  @Autowired
  private SalesDailyTaskPlugin taskPlugin;


  //   ---------------------- Sales Daily Tasks ----------------------
  public SalesDailyTask getSalesDailyTaskById(ClientContext client, ICompany company, Long id) {
    return dailyTaskRepo.findById(id).get();
  }

  public List<SalesDailyTask> findYesterdayUncompletedTasks(ClientContext client, ICompany company) {
    Calendar cal = Calendar.getInstance();
    cal.add(Calendar.DATE, -1);
    cal.set(Calendar.HOUR_OF_DAY, 0);
    cal.set(Calendar.MINUTE, 0);
    cal.set(Calendar.SECOND, 0);
    cal.set(Calendar.MILLISECOND, 0);
    Date startDate = cal.getTime();
    cal.add(Calendar.DATE, 1);
    Date endDate = cal.getTime();
    return dailyTaskRepo.findYesterdayUncompletedTasks(company.getId(), startDate, endDate);
  }

  public SalesDailyTask handleSalesDailyTask(ClientContext client, ICompany company, SalesDailyTask saleTask) {
    EntityTask entityTask = new EntityTask();
    if (!saleTask.isNew()) {
      List<EntityTask> entityTasks = taskPlugin.findByEntityId(client, SalesDailyTask.TABLE_NAME, saleTask.getId());
      if (Collections.isNotEmpty(entityTasks)) entityTask = entityTasks.get(0);
    } else {
      saleTask.setEntityTaskRequest(new EntityTaskRequest());
    }

    computeEntityTaskData(entityTask, saleTask);

    taskPlugin.handle(client, company, saleTask, entityTask, EntityTaskStatus.Submitted);

    return saleTask;
  }


  private void computeEntityTaskData(EntityTask entityTask, SalesDailyTask saleTask) {
    if (StringUtil.isNotEmpty(saleTask.getLabel())) {
      entityTask.setLabel(saleTask.getLabel());
    } else {
      entityTask.setLabel("N/A");
    }

    entityTask.setStatus(EntityTaskStatus.Submitted);
    entityTask.setApprovalStatus(EntityTaskApprovalStatus.Approved);
    entityTask.setTaskType("SALES");
    entityTask.setDueDate(saleTask.getDueDate());
    entityTask.setDeadline(saleTask.getDueDate());
    entityTask.setAssigneeAccountId(saleTask.getCreatorAccountId());
    entityTask.setAssigneeFullName(saleTask.getCreatorLabel());
    entityTask.setReporterAccountId(saleTask.getSalemanAccountId());
    entityTask.setReporterFullName(saleTask.getSalemanLabel());
  }

  public SalesDailyTask saveSalesDailyTask(ClientContext client, ICompany company, SalesDailyTask task) {
    boolean isNotiEnabled = task.isSendingZalo() || task.isSendingEmail();
    boolean isNewTask = task.isNew();
    boolean hasNotificationTime = task.getNotificationTime() != null;

    if (task.isNew()) {
      if (task.getCreatedDate() == null) {
        task.setCreatedDate(new Date());
      }
      if (task.getCreatorAccountId() == null) {
        Account account = accountLogic.getActiveAccountByLoginId(client, client.getRemoteUser());
        Objects.assertNotNull(account, "Account not found!!!, login id: " + client.getRemoteUser());
        task.setCreatorAccountId(account.getId());
        task.setCreatorLabel(account.getFullName());
      }
      if (task.getDueDate() == null) task.initializeDueDate();

      if (task.getPartnerId() != null) {
        if (task.getPartnerType().equals(ClientPartnerType.CUSTOMER_LEAD)) {
          CustomerLeads customerLead = customerLeadsLogic.getCustomerLeadById(client, company, task.getPartnerId());
          PartnerEventHistory eventHistory = new PartnerEventHistory(customerLead);
          eventHistory.withSalesDailyTask(task);
          customerLeadsLogic.savePartnerEventHistory(client, company, eventHistory);
        } else if (task.getPartnerType().equals(ClientPartnerType.CUSTOMERS)) {
          BFSOnePartner customer = bfsonePartnerLogic.getById(client, task.getPartnerId());
          PartnerEventHistory eventHistory = new PartnerEventHistory(customer);
          eventHistory.withSalesDailyTask(task);
          customerLeadsLogic.savePartnerEventHistory(client, company, eventHistory);
        }
      }
    } else {
      SalesDailyTask taskInDb = getSalesDailyTaskById(client, company, task.getId());
      SalesTaskStatus status = task.getStatus();
      if (!SalesTaskStatus.isCompleted(taskInDb.getStatus()) && SalesTaskStatus.isCompleted(status)) {
        task.setDueDate(new Date());
      }

    }

    task.set(client, company);
    SalesDailyTask saved = dailyTaskRepo.save(task);

    // Handle Zalo notification
    if (isNotiEnabled && hasNotificationTime) {

      if (!isNewTask) {
        SalesDailyTask taskInDb = getSalesDailyTaskById(client, company, task.getId());
        if (!taskInDb.getNotificationTime().equals(task.getNotificationTime())) {
          if (task.isSendingZalo()) {
            CRMMessageSystem crmMessageSystem = createCRMMessageSystem(client, task, MessageType.ZALO);
            crmMessageLogic.scheduleMessage(client, company, crmMessageSystem);
          }

          if (task.isSendingEmail()) {
            CRMMessageSystem crmMessageSystem = createCRMMessageSystem(client, task, MessageType.MAIL);
            crmMessageLogic.scheduleMessage(client, company, crmMessageSystem);
          }
        }
      } else {
        if (task.isSendingZalo()) {
          CRMMessageSystem crmMessageSystem = createCRMMessageSystem(client, task, MessageType.ZALO);
          crmMessageLogic.scheduleMessage(client, company, crmMessageSystem);
        }
        if (task.isSendingEmail()) {
          CRMMessageSystem crmMessageSystem = createCRMMessageSystem(client, task, MessageType.MAIL);
          crmMessageLogic.scheduleMessage(client, company, crmMessageSystem);
        }
      }
    }

    return saved;
  }

  public List<SalesDailyTask> findTasksByCreatedTimeRange(ClientContext client, Date startTime, Date endTime) {
    return dailyTaskRepo.findTasksByCreatedTimeRange(startTime, endTime);
  }

  public CRMMessageSystem createCRMMessageSystem(ClientContext client, SalesDailyTask task, MessageType messageType) {
    CommunicationAccount communicationAccount = communicationMessageLogic.getCommunicationAccountByAccountId(client, task.getSalemanAccountId());

    CRMMessageSystem crmMessageSystem = new CRMMessageSystem();
    crmMessageSystem.setCompanyId(task.getCompanyId());
    crmMessageSystem.setScheduledAt(task.getNotificationTime());
    crmMessageSystem.setPluginName(SaleDailyTaskMessagePlugin.PLUGIN_TYPE);
    crmMessageSystem.setReferenceId(task.getId());
    crmMessageSystem.setReferenceType(SalesDailyTask.TABLE_NAME);
    crmMessageSystem.setMessageType(messageType);
    MapObject metadata = new MapObject();
    if (messageType == MessageType.ZALO) {
      crmMessageSystem.setContent(TaskNotificationTemplate.buildZaloTaskMessage(task));
      crmMessageSystem.setRecipients(Set.of(communicationAccount.getMobile()));
    } else if (messageType == MessageType.MAIL) {
      crmMessageSystem.setContent(TaskNotificationTemplate.buildMailTaskMessage(task));
      crmMessageSystem.setRecipients(Set.of(communicationAccount.getEmail()));
      metadata.put("subject", "CRM - Daily Task Notification");
      metadata.put("fromEmail", "<EMAIL>");
      List<String> ccList = new ArrayList<>();
      ccList.add("<EMAIL>");
      metadata.put("ccList", ccList);
    }
    crmMessageSystem.setMetadata(metadata);
    return crmMessageSystem;
  }

  public List<MapObject> saveSaleDailyTaskRecords(ClientContext client, ICompany company, List<MapObject> requests) {
    if (Collections.isNotEmpty(requests)) {
      for (MapObject req : requests) {
        final Long id = req.getLong("id", null);
        SalesDailyTask task = new SalesDailyTask();
        if (id != null) {
          task = getSalesDailyTaskById(client, company, id);
          Objects.assertNotNull(task, "Sales Daily Task not found: id = " + id);
        }
        task = task.computeFromMapObject(req);
        SalesDailyTask updated = handleSalesDailyTask(client, company, task);
        req.put("id", updated.getId());
      }
    }
    return requests;
  }

  public List<SqlMapRecord> searchSalesDailyTasks(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    sqlParams.addParam("companyId", company.getId());
    sqlParams.addParam("accessAccountId", client.getAccountId());
    AppPermission permission = securityLogic.getAppPermission(client, company.getId(), "logistics", "user-logistics-sales");
    if (permission == null) return java.util.Collections.emptyList();

    DataScope dataScope = permission.getDataScope();
    if (DataScope.Group == dataScope) {
      String scriptDir = appEnv.addonPath("core", "groovy");
      String scriptFile = "net/datatp/module/hr/groovy/EmployeeSql.groovy";
      List<SqlMapRecord> accountIds = searchPlatformDbRecords(client, scriptDir, scriptFile, "FindEmployeeIdsByManagerId", sqlParams);
      List<Long> participantAccountIds = accountIds.stream()
        .map(record -> record.getLong("accountId", null))
        .collect(Collectors.toList());
      sqlParams.addParam("participantAccountIds", participantAccountIds);
    }

    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fforwarder/sales/groovy/SalesDailyTaskSql.groovy";
    return searchDbRecords(client, scriptDir, scriptFile, "SearchSalesDailyTasks", sqlParams);
  }

  public List<SqlMapRecord> searchSalesDailyTaskReport(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    AppPermission permission = securityLogic.getAppPermission(client, company.getId(), "logistics", "company-logistics-sales");
    if (permission == null) return java.util.Collections.emptyList();
    sqlParams.addParam("companyId", company.getId());
    sqlParams.addParam("accessAccountId", client.getAccountId());

    DataScope dataScope = permission.getDataScope();
    if (DataScope.Group == dataScope) {
      sqlParams.addParam("managerAccountId", client.getAccountId());
    }
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fforwarder/sales/groovy/SalesDailyTaskSql.groovy";

    SqlQueryUnitManager.QueryContext queryContext = platformQueryUnitManager.create(scriptDir, scriptFile, "SearchSalemansWithDepartment");
    final SqlSelectView view = queryContext.createSqlSelectView(sqlParams);
    List<SqlMapRecord> salemans = view.renameColumWithJavaConvention().getSqlMapRecords();

    List<Long> salemanAccountIds = salemans.stream().map(s -> s.getLong("accountId")).collect(Collectors.toList());

    Map<Long, List<SqlMapRecord>> salesGroupByAccountId = salemans.stream().collect(Collectors.groupingBy(s -> s.getLong("accountId")));

    sqlParams.addParam("salemanAccountIds", salemanAccountIds);

    List<SqlMapRecord> salemanReports = searchDbRecords(client, scriptDir, scriptFile, "SearchSalesDailyTaskReport", sqlParams);

    List<SqlMapRecord> collect = salemanReports.stream().map((SqlMapRecord report) -> {
      Long accountId = report.getLong("salemanAccountId");
      List<SqlMapRecord> list = salesGroupByAccountId.get(accountId);
      if (Collections.isNotEmpty(list)) {
        SqlMapRecord saleman = list.get(0);
        report.set("departmentName", saleman.getString("departmentName"));
        report.set("departmentLabel", saleman.getString("departmentLabel"));
      }
      return report;
    }).collect(Collectors.toList());

    return collect;
  }

  public boolean deleteSalesDailyTaskByIds(ClientContext client, ICompany company, List<Long> ids) {
    dailyTaskRepo.deleteByIds(company.getId(), ids);
    return true;
  }

  public boolean changeDailyTaskStorageState(ClientContext client, ICompany company, ChangeStorageStateRequest req) {
    dailyTaskRepo.setSaleDailyTaskState(req.getNewStorageState(), req.getEntityIds());
    return true;
  }

  public List<SqlMapRecord> saleAccountReport(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fforwarder/sales/groovy/SaleReportSql.groovy";

    SqlQueryUnitManager.QueryContext queryContext = platformQueryUnitManager.create(scriptDir, scriptFile, "SaleAccountFullInfoReport");
    final SqlSelectView view = queryContext.createSqlSelectView(sqlParams);
    List<SqlMapRecord> accountInfos = view.renameColumWithJavaConvention().getSqlMapRecords();

    List<Long> accountIds = accountInfos.stream().map(r -> r.getLong("saleAccountId")).collect(Collectors.toList());
    sqlParams.addParam("accountIds", accountIds);

    List<SqlMapRecord> stats = searchDbRecords(client, scriptDir, scriptFile, "SaleAccountStatsReport", sqlParams);

    for (SqlMapRecord stat : stats) {
      Long accountId = stat.getLong("saleAccountId");
      SqlMapRecord info = accountInfos.stream().filter(r -> accountId.equals(r.getLong("saleAccountId"))).findFirst().orElse(null);
      if (info != null) {
        stat.put("companyCode", info.getString("companyCode"));
        stat.put("saleManLabel", info.getString("saleManLabel"));
        stat.put("totalSearchCount", info.getLong("totalSearchCount"));
        stat.put("searchSource", info.getString("searchSource"));
        stat.put("searchDestination", info.getString("searchDestination"));
        stat.put("topRouteSearchCount", info.getLong("topRouteSearchCount"));
      }
    }
    stats.sort((a, b) -> Long.compare(b.getLong("totalSearchCount", 0L), a.getLong("totalSearchCount", 0L)));
    return stats;
  }

  public List<SqlMapRecord> salemanSystemPerformanceReport(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    AppPermission permission = securityLogic.getAppPermission(client, company.getId(), "logistics", "company-logistics-sales");
    if (permission == null) return java.util.Collections.emptyList();
    sqlParams.addParam("companyId", company.getId());
    sqlParams.addParam("accessAccountId", client.getAccountId());
    DataScope dataScope = permission.getDataScope();
    if (DataScope.Group == dataScope) {
      sqlParams.addParam("managerAccountId", client.getAccountId());
    }
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fforwarder/sales/groovy/SaleReportSql.groovy";
    if (!sqlParams.hasParam("companyId")) sqlParams.addParam("companyId", company.getId());

    // Step 1: Get salemanAccountIds
    List<SqlMapRecord> salemanAccountRecords = searchPlatformDbRecords(client, scriptDir, scriptFile, "SalemanActivityTrackerReportAccount", sqlParams);
    if (Collections.isEmpty(salemanAccountRecords)) return java.util.Collections.emptyList();

    Map<Long, SqlMapRecord> groupedByAccountId = salemanAccountRecords.stream().collect(
      Collectors.toMap(record -> record.getLong("employeeAccountId"), record -> record, (existing, replacement) -> existing));
    sqlParams.addParam("salemanAccountIds", new ArrayList<>(groupedByAccountId.keySet()));

    // Step 2: Get stats
    List<SqlMapRecord> stats = searchDbRecords(client, scriptDir, scriptFile, "SalemanActivityTrackerReport", sqlParams);
    Map<Long, SqlMapRecord> groupedStatByAccountId = stats.stream().collect(
      Collectors.toMap(record -> record.getLong("employeeAccountId"), record -> record, (existing, replacement) -> existing));

    List<SqlMapRecord> mergedResults = new ArrayList<>();

    for (Long salemanAccountId : groupedByAccountId.keySet()) {
      SqlMapRecord accountInfo = groupedByAccountId.get(salemanAccountId);
      SqlMapRecord statInfo = groupedStatByAccountId.get(salemanAccountId);

      if (statInfo != null) {
        // Account có stats data - merge account info vào stats
        statInfo.add("employeeLabel", accountInfo.getString("employeeLabel"));
        statInfo.add("companyBranch", accountInfo.getString("companyBranch"));
        mergedResults.add(statInfo);
      } else {
        //mergedResults.add(accountInfo);

        SqlMapRecord defaultStat = new SqlMapRecord();
        defaultStat.add("employeeAccountId", salemanAccountId);
        defaultStat.add("employeeLabel", accountInfo.getString("employeeLabel"));
        defaultStat.add("companyBranch", accountInfo.getString("companyBranch"));
        // Add default values for all stat fields
        defaultStat.add("total_tasks", 0);
        defaultStat.add("in_progress_tasks", 0);
        defaultStat.add("meet_customer_tasks", 0);
        defaultStat.add("total_requests_pricing", 0);
        defaultStat.add("no_response_requests", 0);
        defaultStat.add("booking_count", 0);
        defaultStat.add("quotation_count", 0);
        defaultStat.add("inquiry_count", 0);
        defaultStat.add("new_customer_count", 0);
        defaultStat.add("new_lead_count", 0);
        defaultStat.add("overdue_request_count", 0);
        mergedResults.add(defaultStat);
      }
    }
    sortStatsByBranchAndEmployee(mergedResults);
    return mergedResults;
  }

  private void sortStatsByBranchAndEmployee(List<SqlMapRecord> stats) {
    stats.sort((recordA, recordB) -> {
      String branchA = recordA.getString("companyBranch", "");
      String branchB = recordB.getString("companyBranch", "");
      boolean isEmptyBranchA = StringUtil.isEmpty(branchA);
      boolean isEmptyBranchB = StringUtil.isEmpty(branchB);

      // Empty branches go last
      if (isEmptyBranchA && !isEmptyBranchB) return 1;
      if (!isEmptyBranchA && isEmptyBranchB) return -1;

      int branchComparison = branchA.compareToIgnoreCase(branchB);
      if (branchComparison != 0) {
        return branchComparison;
      }

      String labelA = recordA.getString("employeeLabel", "");
      String labelB = recordB.getString("employeeLabel", "");
      return labelA.compareToIgnoreCase(labelB);
    });
  }

  public List<SqlMapRecord> saleConversationRateReport(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/fforwarder/sales/groovy/SaleReportSql.groovy";
    if (!sqlParams.hasParam("companyId")) sqlParams.addParam("companyId", company.getId());
    return searchDbRecords(client, scriptDir, scriptFile, "SalemanActivityTrackerReport", sqlParams);
  }

}