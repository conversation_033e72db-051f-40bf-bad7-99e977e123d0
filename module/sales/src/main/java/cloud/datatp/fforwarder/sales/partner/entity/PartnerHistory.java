package cloud.datatp.fforwarder.sales.partner.entity;

import cloud.datatp.fforwarder.core.partner.entity.BFSOnePartner;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotNull;
import java.util.Date;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.account.entity.Account;
import net.datatp.module.company.entity.CompanyEntity;
import net.datatp.util.ds.MapObject;
import net.datatp.util.text.DateUtil;

//TODO: An - delete this table, entity.
/**
 * <AUTHOR>
 */
@Entity
@Table(
    name = PartnerHistory.TABLE_NAME,
    indexes = {
        @Index(
            name = PartnerHistory.TABLE_NAME + "_partner_id_idx",
            columnList = "partner_id"
        ),
        @Index(
            name = PartnerHistory.TABLE_NAME + "_employee_account_id_idx",
            columnList = "employee_account_id"
        ),
    }
)
@JsonInclude(Include.NON_NULL)
@NoArgsConstructor
@Getter @Setter
public class PartnerHistory extends CompanyEntity {

  final static public String TABLE_NAME = "lgc_forwarder_partner_history";

  @Column(name = "transaction_date")
  @JsonFormat(pattern = DateUtil.COMPACT_DATE_FORMAT)
  private Date transactionDate;

  @Column(name = "employee_account_id")
  private Long employeeAccountId;

  @Column(name = "employee_label")
  private String employeeLabel;

  @NotNull
  @Column(name = "partner_id")
  private Long partnerId;

  @Column(name = "type")
  private String type;

  @Column(name = "label", length = 2 * 1024)
  private String label;

  @Column(length = 9 * 1024)
  private String description;

  public PartnerHistory(BFSOnePartner partner) {
    transactionDate = new Date();
    partnerId = partner.getId();
  }

  public PartnerHistory(MapObject record) {

  }

  public PartnerHistory withEmployee(Account saleman) {
    this.employeeAccountId = saleman.getId();
    this.employeeLabel = saleman.getFullName();
    return this;
  }

}