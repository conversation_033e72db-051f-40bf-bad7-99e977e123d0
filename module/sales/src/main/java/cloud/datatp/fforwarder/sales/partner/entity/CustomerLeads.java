package cloud.datatp.fforwarder.sales.partner.entity;

import cloud.datatp.fforwarder.core.common.TextNormalizer;
import cloud.datatp.fforwarder.core.partner.entity.BFSOnePartner;
import cloud.datatp.fforwarder.core.partner.entity.Category;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;
import jakarta.validation.constraints.NotNull;
import java.lang.reflect.Field;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.StringJoiner;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import net.datatp.module.company.entity.CompanyEntity;
import net.datatp.module.hr.entity.Employee;
import net.datatp.util.ds.MapObject;
import net.datatp.util.error.ErrorType;
import net.datatp.util.error.RuntimeError;
import net.datatp.util.text.DateUtil;
import net.datatp.util.text.StringUtil;

/**
 * <AUTHOR>
 */
@Slf4j
@Entity
@Table(
  name = CustomerLeads.TABLE_NAME,
  uniqueConstraints = {
    @UniqueConstraint(
      name = CustomerLeads.TABLE_NAME + "_code",
      columnNames = {"code"}
    ),
  },
  indexes = {
    @Index(
      name = CustomerLeads.TABLE_NAME + "_name_idx",
      columnList = "name"
    ),
    @Index(
      name = CustomerLeads.TABLE_NAME + "_tax_code_idx",
      columnList = "tax_code"
    ),
  }
)
@JsonInclude(Include.NON_NULL)
@NoArgsConstructor
@Getter @Setter
public class CustomerLeads extends CompanyEntity {
  final static public String TABLE_NAME = "forwarder_customer_leads";

  public enum CustomerLeadType {
    CUSTOMER_LEAD, AGENTS_APPROACHED;

    static public CustomerLeadType parse(String token) {
      if (token == null) return CUSTOMER_LEAD;
      return valueOf(token.toUpperCase());
    }

    public static boolean isAgent(CustomerLeadType type) {
      return type == AGENTS_APPROACHED;
    }

  }

  @NotNull
  private String code;

  @Column(name = "bfsone_lead_code")
  private String bfsoneLeadCode;

  @Enumerated(EnumType.STRING)
  @Column(name = "star_rating")
  private StarRating starRating = StarRating.THREE_STARS;

  @Enumerated(EnumType.STRING)
  @Column(name = "status")
  private LeadStatus status = LeadStatus.NEW;

  @Enumerated(EnumType.STRING)
  @Column(name = "type")
  private CustomerLeadType type;

  // For backward compatibility, prevent updates by the user.
  // Sync (create) customer to the legacy system and manually set the code when a customer is created.
  // Process flow: datatp : Customer Lead -> bfsone system: Customer -> datatp: partner
  @Column(name = "bfsone_partner_code")
  private String bfsonePartnerCode;

  @Column(name = "date")
  @JsonFormat(pattern = DateUtil.COMPACT_DATE_FORMAT)
  private Date date;

  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "date_modified")
  private Date dateModified;

  @Column(name = "name", length = 1024)
  private String name;

  @Column(name = "label", length = 2 * 1024)
  private String label;

  @Column(length = 9 * 1024)
  private String address;

  @Column(name = "kcn_code")
  private String kcnCode;

  @Column(name = "kcn_label")
  private String kcnLabel;

  @Column(name = "localized_address", length = 9 * 1024)
  private String localizedAddress;

  @Column(name = "localized_label", length = 2 * 1024)
  private String localizedLabel;

  @Column(name = "personal_contact", length = 2 * 1024)
  private String personalContact;

  @Column(name = "industry_code")
  private String industryCode;

  @Column(name = "industry_label")
  private String industryLabel;

  @Column(name = "source")
  private String source;

  @Column(name = "fax")
  private String fax;

  @Column(name = "email")
  private String email;

  @Column(name = "cell")
  private String cell;

  @Column(name = "tax_code")
  private String taxCode;

  @Column(name = "country_id")
  private Long countryId;

  @Column(name = "country_label")
  private String countryLabel;

  @Column(name = "province_id")
  private Long provinceId;

  @Column(name = "province_label")
  private String provinceLabel;

  @Column(length = 1024 * 32)
  private String note;

  @Column(name = "saleman_account_id")
  private Long salemanAccountId;

  @Column(name = "saleman_label")
  private String salemanLabel;

  @Column(name = "saleman_company_id")
  private Long salemanCompanyId;

  @Column(name = "account_creator_id")
  private Long accountCreatorId;

  @Column(name = "account_creator_label")
  private String accountCreatorLabel;

  @Column(name = "routing", length = 1024)
  private String routing;

  @Column(name = "volume_note", length = 1024)
  private String volumeNote;

  @Column(name = "customer_onboarding_progress", length = 1024)
  private String customerOnboardingProgress;

  public void setName(String name) {
    if(StringUtil.isEmpty(name)) {
      throw new RuntimeError(ErrorType.IllegalArgument, "Name is empty!!!");
    }
    this.name = TextNormalizer.removeVietnameseTone(name).toUpperCase(Locale.ROOT);
  }

  public boolean isAgentApproach() {
    return CustomerLeadType.isAgent(type);
  }

  public CustomerLeads computeFromMapObject(MapObject sel) {
    if (sel.containsKey("status")) {
      String statusStr = sel.getString("status", null);
      if (!StringUtil.isEmpty(statusStr)) {
        this.status = LeadStatus.parse(statusStr);
      }
    }

    if (sel.containsKey("starRating")) {
      String starRatingStr = sel.getString("starRating", null);
      if (!StringUtil.isEmpty(starRatingStr)) {
        this.starRating = StarRating.parse(starRatingStr);
      }
    }

    if (sel.containsKey("type")) {
      String typeStr = sel.getString("type", null);
      if (!StringUtil.isEmpty(typeStr)) {
        this.type = CustomerLeadType.parse(typeStr);
      }
    }

    List<String> modifiedFields = List.of("routing", "volumeNote", "customerOnboardingProgress", "note", "email", "cell", "fax", "taxCode");
    updateFieldMapping(this, sel, modifiedFields);
    return this;
  }

  private void updateFieldMapping(Object source, MapObject sel, List<String> fields) {
    for (String fieldName : fields) {
      if (sel.containsKey(fieldName)) {
        try {
          Field field = source.getClass().getDeclaredField(fieldName);
          field.setAccessible(true);
          final Class<?> type = field.getType();
          if (type.equals(Date.class)) {
            field.set(source, sel.getDate(fieldName, null));
          } else if (type.equals(Long.class) || type.equals(long.class)) {
            field.set(source, sel.getLong(fieldName, null));
          } else if (type.equals(Boolean.class) || type.equals(boolean.class)) {
            field.set(source, sel.getBoolean(fieldName, false));
          } else if (type.equals(Double.class) || type.equals(double.class)) {
            field.set(source, sel.getDouble(fieldName, 0D));
          } else {
            field.set(source, sel.getString(fieldName, null));
          }
        } catch (Exception e) {
          log.error("Customer Lead \n: Cannot update field: {}", fieldName);
        }
      }
    }
  }

  public CustomerLeads(MapObject record) {
    this.code = record.getString("contactId");
    this.bfsoneLeadCode = record.getString("contactId");
    this.name = record.getString("name", "");
    this.label = record.getString("name", "");
    this.address = record.getString("address", "");
    this.date = record.getDate("date", null);

    StringJoiner joiner = new StringJoiner("\n");
    joiner.add(record.getString("onomatology", ""));
    joiner.add(record.getString("personalContact", ""));
    joiner.add(record.getString("phone", ""));
    joiner.add(record.getString("email", ""));
    this.personalContact = joiner.toString();

    this.note = record.getString("note");
  }

  public CustomerLeads computeFromObject(MapObject record) {
    this.name = record.getString("name", "");
    this.label = record.getString("name", "");
    this.address = record.getString("address", "");
    this.date = record.getDate("date", null);

    StringJoiner joiner = new StringJoiner("\n");
    joiner.add(record.getString("onomatology", ""));
    joiner.add(record.getString("personalContact", ""));
    joiner.add(record.getString("phone", ""));
    joiner.add(record.getString("email", ""));
    this.personalContact = joiner.toString();
    return this;
  }

  public CustomerLeads withSaleman(Employee saleman) {
    this.salemanAccountId = saleman.getAccountId();
    this.salemanLabel = saleman.getLabel();
    this.salemanCompanyId = saleman.getCompanyId();
    return this;
  }

  public BFSOnePartner convertToBFSOnePartner() {
    BFSOnePartner partner = new BFSOnePartner();
    if (this.type == CustomerLeadType.CUSTOMER_LEAD) {
      partner.setCategory(Category.CUSTOMER);
    } else if (this.type == CustomerLeadType.AGENTS_APPROACHED) {
      partner.setCategory(Category.AGENT_DOMESTIC);
    } else {
      partner.setCategory(Category.OTHER);
    }
    partner.setLeadCode(this.code);
    partner.setName(this.name);
    partner.setAddress(this.address);
    partner.setTaxCode(this.taxCode);
    partner.setCountryLabel(this.countryLabel);
    partner.setFax(this.fax);
    partner.setCell(this.cell);
    partner.setPersonalContact(this.personalContact);
    partner.setIndustryCode(this.industryCode);
    partner.setIndustryLabel(this.industryLabel);
    partner.setSource(this.source);
    partner.setBfsonePartnerCode(this.bfsonePartnerCode);
    partner.setKcnCode(this.kcnCode);
    partner.setKcnLabel(this.kcnLabel);
    partner.setLabel(this.label);
    partner.setLocalizedAddress(this.localizedAddress);
    partner.setLocalizedLabel(this.localizedLabel);
    partner.setNote(this.note);
    partner.setRouting(this.routing);
    return partner;
  }

  public MapObject toBFSOneCustomerLead(String salemanUsername) {
    MapObject customerLead = new MapObject();
    customerLead.put("ContactID", this.bfsoneLeadCode != null ? this.bfsoneLeadCode : "");
    customerLead.put("DateCreated", "");
    customerLead.put("DateModified", "");
    customerLead.put("Onomatology", this.name);
    customerLead.put("FristName", StringUtil.isNotEmpty(this.personalContact) ? this.personalContact : "N/A");
    customerLead.put("MiddleName", "");
    customerLead.put("LastName", "");
    customerLead.put("EnglishName", this.personalContact);
    customerLead.put("JobTitle", "");
    customerLead.put("CompanyName", this.label);
    customerLead.put("Street", StringUtil.isNotEmpty(this.address) ? this.address : "N/A");
    customerLead.put("TaxCode", this.taxCode);
    customerLead.put("TelNo", "");
    customerLead.put("CellPhone", this.cell);
    customerLead.put("Email",  this.email);
    customerLead.put("FieldInterested", this.routing != null ? this.routing : "");
    customerLead.put("Industry", this.industryLabel != null ? this.industryLabel : "");
    customerLead.put("City", this.provinceLabel != null ? this.provinceLabel : "");
    customerLead.put("PostalCode", "");
    customerLead.put("Country", this.countryLabel);
    customerLead.put("UserName", salemanUsername);
    customerLead.put("Notes", this.note != null ? this.note : "");
    return customerLead;
  }

}