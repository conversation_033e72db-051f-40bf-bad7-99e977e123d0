package cloud.datatp.fforwarder.sales.report;

import cloud.datatp.fforwarder.core.message.MailMessageProvider;
import cloud.datatp.fforwarder.core.message.MessageServicePlugin;
import cloud.datatp.fforwarder.core.message.entity.CRMMessageSystem;
import cloud.datatp.fforwarder.core.message.entity.MessageStatus;
import cloud.datatp.fforwarder.core.message.entity.MessageType;
import cloud.datatp.fforwarder.sales.common.TaskNotificationTemplate;
import cloud.datatp.fforwarder.sales.report.entity.SalesDailyTask;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import net.datatp.module.communication.CommunicationMessageLogic;
import net.datatp.module.communication.entity.CommunicationAccount;
import net.datatp.module.company.CompanyLogic;
import net.datatp.module.company.entity.Company;
import net.datatp.security.client.ClientContext;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class SaleDailyTaskMessagePlugin extends MessageServicePlugin {
  public static final String PLUGIN_TYPE = "sale-daily-task-message";

  @Autowired
  private MailMessageProvider mailMessageProvider;

  @Autowired
  private CompanyLogic companyLogic;

  @Autowired
  private SaleTaskReportLogic saleTaskReportLogic;

  @Autowired
  private CommunicationMessageLogic communicationMessageLogic;

  protected SaleDailyTaskMessagePlugin() {
    super(PLUGIN_TYPE);
  }

  public void onPostSend(ClientContext client, CRMMessageSystem message) {
  }

  public void onSendError(ClientContext client, CRMMessageSystem message, Exception error) throws Exception {
    if (message.getStatus() != MessageStatus.CANCELLED && message.getMessageType() == MessageType.ZALO) {
      Objects.assertNotNull(message.getReferenceType(), "Message has no reference type");
      Objects.assertNotNull(message.getReferenceId(), "Message has no reference id");
      if (!SalesDailyTask.TABLE_NAME.equals(message.getReferenceType())) {
        throw new RuntimeException("Message reference type is not SalesDailyTask: " + message.getReferenceType());
      }
      Company company = companyLogic.getCompany(client, message.getCompanyId());
      SalesDailyTask task = saleTaskReportLogic.getSalesDailyTaskById(client, company, message.getReferenceId());
      Objects.assertNotNull(task, "Sales Daily Task {} is not found", message.getReferenceId());
      Objects.assertNotNull(company, "Company {} is not found", message.getCompanyId());

      CommunicationAccount communicationAccount = communicationMessageLogic.getCommunicationAccountByAccountId(client, task.getSalemanAccountId());
      CRMMessageSystem crmMessageSystem = new CRMMessageSystem();
      crmMessageSystem.setCompanyId(task.getCompanyId());
      crmMessageSystem.setScheduledAt(task.getNotificationTime());
      crmMessageSystem.setPluginName(SaleDailyTaskMessagePlugin.PLUGIN_TYPE);
      crmMessageSystem.setReferenceId(task.getId());
      crmMessageSystem.setReferenceType(SalesDailyTask.TABLE_NAME);
      crmMessageSystem.setMessageType(MessageType.MAIL);
      MapObject metadata = new MapObject();
      crmMessageSystem.setContent(TaskNotificationTemplate.buildMailTaskMessage(task));
      crmMessageSystem.setRecipients(Set.of(communicationAccount.getEmail()));
      List<String> ccList = Arrays.asList("<EMAIL>");

      metadata.put("subject", "CRM - Daily Task Notification");
      metadata.put("fromEmail", "<EMAIL>");
      metadata.put("ccList", ccList);
      crmMessageSystem.setMetadata(metadata);

      CRMMessageSystem mailMessage = saleTaskReportLogic.createCRMMessageSystem(client, task, MessageType.MAIL);

      mailMessageProvider.send(client, mailMessage);
    }
  }
}