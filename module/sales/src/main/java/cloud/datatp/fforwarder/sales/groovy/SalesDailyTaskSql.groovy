package cloud.datatp.fforwarder.sales.groovy

import net.datatp.lib.executable.ExecutableContext
import net.datatp.lib.executable.Executor
import net.datatp.module.data.db.ExecutableSqlBuilder
import net.datatp.util.ds.MapObject
import org.springframework.context.ApplicationContext

public class SalesDailyTaskSql extends Executor {

    public class SearchSalesDailyTasks extends ExecutableSqlBuilder {
        @Override
        public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
            MapObject sqlParams = ctx.getParam("sqlParams");

            String query = """
                SELECT sat.*
                 FROM forwarder_sales_daily_task sat
                 WHERE
                    ${FILTER_BY_STORAGE_STATE("sat", sqlParams)}
                    ${AND_FILTER_BY_RANGE('sat.due_date', 'dueDate', sqlParams)}
                    ${AND_FILTER_BY_RANGE('sat.created_date', 'createdDate', sqlParams)}
                    ${AND_FILTER_BY_PARAM('sat.task_type', "taskType", sqlParams)}
                    ${AND_FILTER_BY_PARAM('sat.id', 'ids', sqlParams)}
                    ${AND_FILTER_BY_PARAM('sat.saleman_account_id', 'participantAccountIds', sqlParams)}
                    ${AND_SEARCH_BY_PARAMS(['label', 'saleman_label', 'task_type'], "search", sqlParams)}
                    AND (
                         'System' = :space OR
                         ('Company' = :space AND sat.company_id = :companyId) OR
                         ('User' = :space AND sat.company_id = :companyId AND sat.saleman_account_id = :accessAccountId)
                    )
                  ORDER BY sat.created_date DESC, sat.task_type ASC
                 ${MAX_RETURN(sqlParams)}
            """;
            return query;
        }
    }

    public class SearchSalemansWithDepartment extends ExecutableSqlBuilder {
        @Override
        public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
            MapObject sqlParams = ctx.getParam("sqlParams");

            String query = """
                SELECT DISTINCT ON (rel.employee_id)
                    child.id AS department_id,
                    child."name" AS department_name,
                    child."label" AS department_label,
                    rel.employee_id,
                    rel.role,
                    emp.account_id
                FROM company_hr_department child
                JOIN company_hr_department parent ON parent.id = child.parent_id
                JOIN company_hr_department_employee_rel rel ON rel.department_id = child.id
                JOIN company_hr_employee emp ON emp.id = rel.employee_id
                JOIN company_hr_department_employee_rel manager_rel ON manager_rel.department_id = child.id
                JOIN company_hr_employee manager_emp ON manager_emp.id = manager_rel.employee_id
                WHERE parent.company_id = :companyId
                    AND rel."role" <> 'Manager'
                    ${addAndClause(sqlParams, "managerAccountId", "manager_emp.account_id = :managerAccountId AND manager_rel.\"role\" IN ('Leader', 'Manager')")}
                    ${AND_FILTER_BY_PARAM('emp.account_id', 'participantAccountIds', sqlParams)}
              """;
            return query;
        }
    }


    public class SearchSalesDailyTaskReport extends ExecutableSqlBuilder {
        @Override
        public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
            MapObject sqlParams = ctx.getParam("sqlParams");

            String query = """
                SELECT
                    sat.*
                FROM forwarder_sales_daily_task sat
                WHERE 1 = 1
                ${AND_FILTER_BY_PARAM('sat.saleman_account_id', "salemanAccountIds", sqlParams)}
                ${AND_FILTER_BY_PARAM('sat.company_id', "companyId", sqlParams)}
                ${AND_FILTER_BY_RANGE('sat.due_date', 'dueDate', sqlParams)}
                ${AND_FILTER_BY_PARAM('sat.task_type', "taskType", sqlParams)}
                ORDER BY sat.saleman_label asc, sat.created_date desc
            """;
            return query;
        }
    }

    public SalesDailyTaskSql() {
        register(new SearchSalesDailyTasks());
        register(new SearchSalemansWithDepartment());
        register(new SearchSalesDailyTaskReport());
    }
}