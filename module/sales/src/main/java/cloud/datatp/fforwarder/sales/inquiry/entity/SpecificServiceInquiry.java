package cloud.datatp.fforwarder.sales.inquiry.entity;

import cloud.datatp.fforwarder.core.common.Purpose;
import cloud.datatp.fforwarder.core.common.TransportationMode;
import cloud.datatp.fforwarder.price.common.ClientPartnerType;
import cloud.datatp.fforwarder.price.common.ShipmentDetail;
import cloud.datatp.fforwarder.price.entity.InquiryRequest;
import cloud.datatp.fforwarder.price.entity.InquiryRequest.InquiryStatus;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.EnumType;
import jakarta.persistence.Enumerated;
import jakarta.persistence.Index;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToMany;
import jakarta.persistence.Table;
import java.io.Serial;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.company.entity.CompanyEntity;
import net.datatp.module.data.db.entity.EditMode;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.util.DeleteGraph;
import net.datatp.module.data.db.util.DeleteGraphJoinType;
import net.datatp.module.data.db.util.DeleteGraphs;
import net.datatp.security.client.ClientContext;
import net.datatp.util.dataformat.DataSerializer;
import net.datatp.util.ds.Arrays;
import net.datatp.util.ds.Objects;
import net.datatp.util.text.DateUtil;

@Entity
@Table(
  name = SpecificServiceInquiry.TABLE_NAME,
  indexes = {
    @Index(name = SpecificServiceInquiry.TABLE_NAME + "_company_id_idx", columnList = "company_id"),
    @Index(name = SpecificServiceInquiry.TABLE_NAME + "_storage_state_idx", columnList = "storage_state"),
    @Index(name = SpecificServiceInquiry.TABLE_NAME + "_client_idx", columnList = "client_partner_id, client_label"),
    @Index(name = SpecificServiceInquiry.TABLE_NAME + "_saleman_idx", columnList = "saleman_account_id, saleman_label"),
    @Index(name = SpecificServiceInquiry.TABLE_NAME + "_reference_idx", columnList = "reference_code"),
    @Index(name = SpecificServiceInquiry.TABLE_NAME + "_request_date_idx", columnList = "request_date"),
    @Index(name = SpecificServiceInquiry.TABLE_NAME + "_location_idx", columnList = "from_location_code, to_location_code"),
    @Index(name = SpecificServiceInquiry.TABLE_NAME + "_mode_idx", columnList = "mode")
  }
)
@DeleteGraphs({
  @DeleteGraph(target = Container.class, joinField = "specific_service_inquiry_id", joinType = DeleteGraphJoinType.OneToMany),
})
@JsonInclude(Include.NON_NULL)
@NoArgsConstructor
@Getter
@Setter
public class SpecificServiceInquiry extends CompanyEntity {
  public static final String TABLE_NAME = "lgc_sales_specific_service_inquiry";

  @Serial
  private static final long serialVersionUID = 1L;

  @Column(name = "reference_code")
  private String referenceCode;

  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "request_date")
  private Date requestDate;

  @Enumerated(EnumType.STRING)
  @Column(name = "edit_mode")
  protected EditMode editMode = EditMode.DRAFT; // VALIDATE  | LOCKED

  @Enumerated(EnumType.STRING)
  @Column(name = "mode")
  private TransportationMode mode;

  @Enumerated(EnumType.STRING)
  private Purpose purpose = Purpose.IMPORT;

  /* -------------- Customer/ Agent ------------------- */
  @Enumerated(EnumType.STRING)
  @Column(name = "client_partner_type")
  private ClientPartnerType clientPartnerType;

  @Column(name = "client_partner_id")
  private Long clientPartnerId;

  @Column(name = "client_label")
  private String clientLabel;

  @Column(name = "attention")
  private String attention;

  @Column(name = "handling_agent_partner_id")
  private Long handlingAgentPartnerId;

  @Column(name = "handling_agent_label")
  private String handlingAgentLabel;

  /* -------------- Saleman------------------- */
  @Column(name = "saleman_account_id")
  private Long salemanAccountId;

  @Column(name = "saleman_label")
  private String salemanLabel;

  /* -------------- Port/ Airport/ Location------------------- */
  @Column(name = "from_location_code")
  private String fromLocationCode;

  @Column(name = "from_location_label")
  private String fromLocationLabel;

  @Column(name = "to_location_code")
  private String toLocationCode;

  @Column(name = "to_location_label")
  private String toLocationLabel;

  @Column(name = "final_destination")
  private String finalDestination;

  @Column(name = "pickup_address", length = 2 * 1024)
  private String pickupAddress;

  @Column(name = "delivery_address", length = 2 * 1024)
  private String deliveryAddress;

  /* -------------- Shipment Info------------------- */
  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "estimated_time_departure")
  private Date estimatedTimeDeparture;

  @Enumerated(EnumType.STRING)
  @Column(name = "term_of_service")
  private TermOfService termOfService;

  @Column(name = "incoterms")
  private String incoterms;

  @JsonFormat(pattern = DateUtil.COMPACT_DATETIME_FORMAT)
  @Column(name = "cargo_ready_date")
  private Date cargoReadyDate;

  @Column(name = "target_rate_and_charges")
  private String targetRateAndCharges;

  @Column(name = "container_types")
  private String containerTypes;

  @Column(name = "packaging_type")
  private String packagingType = "PK";

  @Column(name = "package_quantity")
  private int packageQty;

  @Column(name = "report_volume")
  private Double reportVolume;

  @Column(name = "report_volume_unit")
  private String reportVolumeUnit;

  @Column(name = "desc_of_goods", length = 1024 * 32)
  private String descOfGoods = "";

  @Column(name = "commodity", length = 1024 * 32)
  private String commodity;

  @Column(name = "gross_weight_kg")
  private double grossWeightKg;

  @Column(name = "volume_cbm")
  private double volumeCbm;

  @Column(name = "chargeable_weight")
  private double chargeableWeight;

  @Column(name = "chargeable_volume")
  private double chargeableVolume;

  @Column(name = "terms_and_conditions", length = 1024 * 32)
  private String termsAndConditions;

  @Column(name = "feedback", length = 2 * 1024)
  private String feedback;

  @Column(length = 1024 * 32)
  private String note;

  @OneToMany(cascade = CascadeType.ALL, orphanRemoval = true)
  @JoinColumn(name = "specific_service_inquiry_id")
  private List<Container> containers = new ArrayList<>();

  public SpecificServiceInquiry withContainer(Container... container) {
    containers = Arrays.addToList(containers, container);
    return this;
  }

  public void set(ClientContext client, ICompany company) {
    super.set(client, company);
    set(client, company, containers);
  }

  @SuppressWarnings("unchecked")
  public SpecificServiceInquiry clearIds() {
    clearId(this);
    setCreatedBy(null);
    setCreatedTime(null);
    setModifiedBy(null);
    setModifiedTime(null);
    clearIds(containers);
    return this;
  }

  public ClientPartnerType getClientPartnerType() {
    if(clientPartnerType == null) clientPartnerType = ClientPartnerType.CUSTOMERS;
    return clientPartnerType;
  }

  public void setClientPartnerType(ClientPartnerType type) {
    if(type == null) type = ClientPartnerType.CUSTOMERS;
    this.clientPartnerType = type;
  }

  public SpecificServiceInquiry clone() {
    SpecificServiceInquiry clone = DataSerializer.JSON.clone(this);
    return clone;
  }

  public SpecificServiceInquiry computeFromRequest(InquiryRequest request) {
    this.referenceCode = request.getCode();
    this.mode = request.getMode();
    this.purpose = request.getPurpose();
    this.requestDate = request.getRequestDate();

    this.fromLocationCode = request.getFromLocationCode();
    this.fromLocationLabel = request.getFromLocationLabel();
    this.toLocationCode = request.getToLocationCode();
    this.toLocationLabel = request.getToLocationLabel();
    this.pickupAddress = request.getPickupAddress();
    this.deliveryAddress = request.getDeliveryAddress();

    setClientPartnerType(request.getClientPartnerType());
    if(ClientPartnerType.isAgent(this.clientPartnerType)) {
      this.handlingAgentPartnerId = request.getClientPartnerId();
      this.handlingAgentLabel = request.getClientLabel();
    } else {
      this.clientPartnerId = request.getClientPartnerId();
      this.clientLabel = request.getClientLabel();
    }

    this.salemanAccountId = request.getSalemanAccountId();
    this.salemanLabel = request.getSalemanLabel();

    this.cargoReadyDate = request.getCargoReadyDate();
    this.estimatedTimeDeparture = request.getCargoReadyDate();
    this.targetRateAndCharges = request.getTargetRate();
    this.note = request.getNote();
    this.feedback = request.getFeedback();
    this.incoterms = request.getTermOfService();
    this.requestDate = request.getRequestDate();
    this.containerTypes = request.getShipmentDetail().getVolumeInfo();
    this.grossWeightKg = request.getShipmentDetail().getGrossWeightKg();
    this.volumeCbm = request.getShipmentDetail().getVolumeCbm();
    this.reportVolume = request.getShipmentDetail().getReportVolume();
    this.reportVolumeUnit = request.getShipmentDetail().getReportVolumeUnit();
    this.descOfGoods = request.getShipmentDetail().getDescOfGoods();
    this.commodity = request.getShipmentDetail().getCommodity();
    return this;
  }

  public InquiryRequest toInquiryRequest(InquiryRequest template) {
    InquiryRequest request = Objects.ensureNotNull(template, InquiryRequest::new);
    request.setMode(this.mode);
    request.setPurpose(this.purpose);

    request.setRequestDate(this.requestDate);
    request.setStatus(InquiryStatus.NO_RESPONSE);

    request.setFromLocationCode(this.fromLocationCode);
    request.setFromLocationLabel(this.fromLocationLabel);
    request.setToLocationCode(this.toLocationCode);
    request.setToLocationLabel(this.toLocationLabel);
    request.setPickupAddress(this.pickupAddress);
    request.setDeliveryAddress(this.deliveryAddress);

    request.setClientPartnerId(this.clientPartnerId);
    request.setClientLabel(this.clientLabel);
    request.setClientPartnerType(this.clientPartnerType);

    request.setSalemanAccountId(this.salemanAccountId);
    request.setSalemanLabel(this.salemanLabel);

    request.setTermOfService(this.incoterms);
    request.setCargoReadyDate(this.cargoReadyDate);
    request.setTargetRate(this.targetRateAndCharges);
    request.setFeedback(this.feedback);
    request.setNote(this.note);

    // Set shipment details
    ShipmentDetail shipmentDetail = new ShipmentDetail();
    shipmentDetail.setPackageQty(String.valueOf(this.packageQty));
    shipmentDetail.setVolumeInfo(this.containerTypes);
    shipmentDetail.setVolumeCbm(this.volumeCbm);
    shipmentDetail.setGrossWeightKg(this.grossWeightKg);
    shipmentDetail.setReportVolume(this.reportVolume);
    shipmentDetail.setReportVolumeUnit(this.reportVolumeUnit);
    shipmentDetail.setDescOfGoods(this.descOfGoods);
    shipmentDetail.setCommodity(this.commodity);
    request.setShipmentDetail(shipmentDetail);
    return request;
  }

}