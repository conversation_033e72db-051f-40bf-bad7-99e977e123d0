package cloud.datatp.fforwarder.sales.inquiry;

import cloud.datatp.fforwarder.core.common.Purpose;
import cloud.datatp.fforwarder.core.db.CRMDaoService;
import cloud.datatp.fforwarder.price.http.Params.ComputePurpose;
import cloud.datatp.fforwarder.sales.inquiry.entity.GenericServiceInquiry;
import cloud.datatp.fforwarder.sales.inquiry.entity.SpecificServiceInquiry;
import cloud.datatp.fforwarder.sales.inquiry.repository.GenericServiceInquiryRepository;
import cloud.datatp.fforwarder.sales.inquiry.repository.SpecificServiceInquiryRepository;
import jakarta.annotation.PostConstruct;
import java.util.Date;
import java.util.List;
import net.datatp.module.account.AccountLogic;
import net.datatp.module.account.entity.Account;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.entity.EditMode;
import net.datatp.module.data.db.entity.EditState;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.seq.SeqService;
import net.datatp.module.hr.EmployeeLogic;
import net.datatp.module.hr.entity.Employee;
import net.datatp.module.partner.PartnerLogic;
import net.datatp.module.partner.entity.Partner;
import net.datatp.module.resource.location.CountryLogic;
import net.datatp.module.resource.location.LocationLogic;
import net.datatp.module.resource.location.entity.Country;
import net.datatp.module.resource.location.entity.Location;
import net.datatp.security.client.ClientContext;
import net.datatp.util.dataformat.DataSerializer;
import net.datatp.util.ds.Objects;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class InquiryLogic extends CRMDaoService {

  @Autowired
  private GenericServiceInquiryRepository genericInquiryRepo;

  @Autowired
  private SpecificServiceInquiryRepository specificInquiryRepo;

  @Autowired
  private LocationLogic locationLogic;

  @Autowired
  private SeqService seqService;

  @Autowired
  private EmployeeLogic employeeLogic;

  @Autowired
  private AccountLogic accountLogic;

  @Autowired
  private PartnerLogic partnerLogic;

  @Autowired
  private CountryLogic countryLogic;

  @PostConstruct
  private void onInit() {
    seqService.createIfNotExists(GenericServiceInquiry.SEQUENCE, 10);
  }

  public Purpose computePurposeFromRouting(ClientContext client, ICompany company, ComputePurpose computePurpose) {
    Location fromLocation = locationLogic.getLocation(client, computePurpose.getFromLocationCode());
    Objects.assertNotNull(fromLocation, "Location is not found!!!, code = " + computePurpose.getFromLocationCode());
    Location toLocation = locationLogic.getLocation(client, computePurpose.getToLocationCode());
    Objects.assertNotNull(toLocation, "Location is not found!!!, code = " + computePurpose.getToLocationCode());
    Long fromCountryId = fromLocation.getCountryId();
    Long toCountryId = toLocation.getCountryId();

    // TODO: Assert if both countryId is null
    if (java.util.Objects.equals(fromCountryId, toCountryId))
      return Purpose.DOMESTIC;
    if (fromCountryId != null) {
      Country country = countryLogic.getCountry(client, fromCountryId);
      if (country.getCode().equals("VN"))
        return Purpose.EXPORT;
    }
    if (toCountryId != null) {
      Country country = countryLogic.getCountry(client, toCountryId);
      if (country.getCode().equals("VN"))
        return Purpose.IMPORT;
    }
    return Purpose.EXPORT;
  }

  // ============================================
  // Generic
  // ============================================
  public GenericServiceInquiry newGenericInquiry(ClientContext client, ICompany company, GenericServiceInquiry template) {
    template = Objects.ensureNotNull(template, GenericServiceInquiry::new);
    GenericServiceInquiry inquiry = DataSerializer.JSON.clone(template);

    Objects.assertNotNull(inquiry.getPurpose(), "Please specify inquiry purpose (Exp/ Imp/ Domestics)");

    if (Objects.isNull(inquiry.getRequestDate()))
      inquiry.setRequestDate(new Date());
    if (Objects.isNull(inquiry.getEditMode()))
      inquiry.setEditMode(EditMode.DRAFT);

    if (Objects.isNull(inquiry.getSalemanEmployeeId())) {
      Employee employee = employeeLogic.getByAccount(client, client.getCompany(), client.getAccountId());
      if (Objects.nonNull(employee)) {
        inquiry.setSalemanEmployeeId(employee.getId());
        inquiry.setSalemanLabel(employee.getLabel());
      } else {
        Employee companyEmployee = employeeLogic.getCompanyEmployee(client, company);
        inquiry.setSalemanEmployeeId(companyEmployee.getId());
        inquiry.setSalemanLabel(companyEmployee.getLabel());
        final Partner partner = partnerLogic.getPartnerByLoginIdPattern(client, company, client.getRemoteUser());
        if (Objects.nonNull(partner)) {
          inquiry.setClientPartnerId(partner.getId());
          inquiry.setClientLabel(partner.getLabel());
        }
      }
    }
    return inquiry;
  }

  public GenericServiceInquiry saveGenericServiceInquiry(ClientContext client, ICompany company,
      GenericServiceInquiry inquiry) {
    if (inquiry.isNew()) {
      Employee employee = employeeLogic.getByAccount(client, client.getCompany(), client.getAccountId());
      inquiry.setSalemanLabel(employee.getLabel());
      inquiry.setSalemanEmployeeId(employee.getId());
    }
    if (inquiry.getEditState() != EditState.IMPORT) {
      new InquiryValidator().validate(inquiry);
    }
    inquiry.set(client, company);
    inquiry = genericInquiryRepo.save(inquiry);
    return inquiry;
  }

  public List<GenericServiceInquiry> findGenericInquiryByCompany(ClientContext client, ICompany company) {
    return genericInquiryRepo.findByCompanyId(company.getId());
  }

  public boolean changeGenericInquiryStorageState(ClientContext client, ChangeStorageStateRequest req) {
    genericInquiryRepo.setStorageState(req.getNewStorageState(), req.getEntityIds());
    return true;
  }

  public SpecificServiceInquiry saveSpecificInquiry(ClientContext client, ICompany company, SpecificServiceInquiry inquiry) {
    if (Objects.isNull(inquiry.getSalemanAccountId())) {
      Account saleman = accountLogic.getAccountById(client, client.getAccountId());
      inquiry.setSalemanAccountId(saleman.getId());
      inquiry.setSalemanLabel(saleman.getFullName());
    }
    inquiry.set(client, company);
    inquiry = specificInquiryRepo.save(inquiry);
    return inquiry;
  }

  public SpecificServiceInquiry newSpecificServiceInquiry(ClientContext client, ICompany company, SpecificServiceInquiry template) {
    template = Objects.ensureNotNull(template, SpecificServiceInquiry::new);
    SpecificServiceInquiry inquiry = DataSerializer.JSON.clone(template);
    Objects.assertNotNull(inquiry.getPurpose(), "Please specify inquiry purpose (Exp/ Imp/ Domestics)");
    Objects.assertNotNull(inquiry.getMode(), "Please specify inquiry mode (Sea FCL/ Sea LCL/ Air/ ...)");
    if (Objects.isNull(inquiry.getRequestDate())) inquiry.setRequestDate(new Date());
    if (Objects.isNull(inquiry.getSalemanAccountId())) {
      Account saleman = accountLogic.getAccountById(client, client.getAccountId());
      inquiry.setSalemanAccountId(saleman.getId());
      inquiry.setSalemanLabel(saleman.getFullName());
    }
    return inquiry;
  }

  public boolean changeSpecificInquiryStorageState(ClientContext client, ChangeStorageStateRequest req) {
    specificInquiryRepo.setStorageState(req.getNewStorageState(), req.getEntityIds());
    return true;
  }

  public List<SpecificServiceInquiry> findSpecificInquiryByCompany(ClientContext client, ICompany company) {
    return specificInquiryRepo.findByCompanyId(company.getId());
  }

}