package cloud.datatp.fforwarder.sales.quotation.repository;

import cloud.datatp.fforwarder.sales.quotation.entity.SpecificQuotation;
import java.io.Serializable;
import java.util.List;
import net.datatp.module.data.db.entity.StorageState;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

public interface SpecificQuotationRepository extends JpaRepository<SpecificQuotation, Serializable> {

  @Modifying
  @Query("UPDATE SpecificQuotation q SET q.storageState = :state WHERE q.id IN (:ids)")
  int setStorageState(@Param("state") StorageState state, @Param("ids") List<Long> ids);

  @Query("SELECT q FROM SpecificQuotation q WHERE q.companyId = :companyId")
  List<SpecificQuotation> findByCompany(@Param("companyId") Long companyId);
}