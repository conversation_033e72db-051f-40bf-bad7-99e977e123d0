package cloud.datatp.fforwarder.sales.groovy

import net.datatp.lib.executable.ExecutableContext
import net.datatp.lib.executable.Executor
import net.datatp.module.data.db.ExecutableSqlBuilder
import net.datatp.util.ds.MapObject
import org.springframework.context.ApplicationContext

public class SpecificQuotationSql extends Executor {

    public class SearchSpecificQuotation extends ExecutableSqlBuilder {
        @Override
        public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
            MapObject sqlParams = ctx.getParam("sqlParams");
            String query = """
                WITH filtered_requests AS (
                        SELECT 
                            c.*,
                            q.id AS quotation_id,
                            b.id AS booking_id
                        FROM lgc_price_inquiry_request c
                        LEFT JOIN (
                            SELECT 
                                reference_code,
                                company_id,
                                id,
                                ROW_NUMBER() OVER (PARTITION BY reference_code, company_id ORDER BY id DESC) AS rn
                            FROM lgc_sales_specific_service_inquiry
                            WHERE EXISTS (
                                SELECT 1 
                                FROM lgc_sales_specific_quotation q 
                                WHERE q.inquiry_id = lgc_sales_specific_service_inquiry.id
                            )
                        ) inquiry_quotation 
                            ON inquiry_quotation.reference_code = c.code 
                            AND inquiry_quotation.company_id = c.company_id
                            AND inquiry_quotation.rn = 1  -- Chỉ lấy inquiry mới nhất
                        LEFT JOIN lgc_sales_specific_quotation q 
                            ON q.inquiry_id = inquiry_quotation.id
                        LEFT JOIN (
                            SELECT 
                                reference_code,
                                company_id,
                                id,
                                ROW_NUMBER() OVER (PARTITION BY reference_code, company_id ORDER BY id DESC) AS rn
                            FROM lgc_sales_specific_service_inquiry
                            WHERE EXISTS (
                                SELECT 1 
                                FROM lgc_sales_booking b 
                                WHERE b.inquiry_id = lgc_sales_specific_service_inquiry.id
                            )
                        ) inquiry_booking 
                            ON inquiry_booking.reference_code = c.code 
                            AND inquiry_booking.company_id = c.company_id
                            AND inquiry_booking.rn = 1  -- Chỉ lấy inquiry mới nhất
                            AND (inquiry_booking.id != inquiry_quotation.id OR inquiry_quotation.id IS NULL)  -- Đảm bảo inquiry khác nhau
                        LEFT JOIN lgc_sales_booking b 
                            ON b.inquiry_id = inquiry_booking.id
                    WHERE ${FILTER_BY_STORAGE_STATE("c", sqlParams)}
                      ${AND_FILTER_BY_OPTION("c.status", "status", sqlParams)}
                      ${AND_FILTER_BY_RANGE("c.request_date", "requestDate", sqlParams)}
                      ${addAndClause(sqlParams, "isFavorite", "q.is_favorite = :isFavorite")}
                      AND (
                        'System' = :space 
                        OR ('Company' = :space AND c.company_id = :companyId)
                        OR ('User' = :space AND c.company_id = :companyId AND c.saleman_account_id = :accessAccountId)
                      )
                )
                SELECT r.*
                FROM filtered_requests r
                ORDER BY r.request_date DESC
            """;
            return query;
        }
    }

    public class QuotationDeleteValidate extends ExecutableSqlBuilder {
        @Override
        public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
            MapObject sqlParams = ctx.getParam("sqlParams");
            String query = """
                SELECT count(q.id) AS valid_counting
                FROM lgc_sales_specific_quotation q
                  JOIN lgc_sales_specific_service_inquiry i 
                    ON i.id = q.inquiry_id 
                WHERE 
                  ${FILTER_BY_PARAM('q.company_id', 'companyId', sqlParams)}
                  AND :writeCap IS TRUE
                  AND (
                    :dataScope IN ('Company', 'All') 
                    ${OR_FILTER_BY_PARAM("i.saleman_account_id", "participants", sqlParams)}
                  )                      
                  AND q.id IN (:ids)
            """;
            return query;
        }
    }

    public SpecificQuotationSql() {
        register(new SearchSpecificQuotation());
        register(new QuotationDeleteValidate());
    }
}