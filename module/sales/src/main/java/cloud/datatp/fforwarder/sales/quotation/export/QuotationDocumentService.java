package cloud.datatp.fforwarder.sales.quotation.export;

import java.io.InputStream;
import java.util.Date;
import java.util.List;

import net.datatp.module.account.AccountLogic;
import net.datatp.module.account.entity.Account;
import net.datatp.module.app.AppEnv;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.xlsx.XLSXSectionWriter;
import net.datatp.module.data.xlsx.XLSXSheetWriter;
import net.datatp.module.data.xlsx.XLSXWorkbookWriter;
import net.datatp.module.data.xlsx.export.DataListExportModel;
import net.datatp.module.data.xlsx.export.ExportStyleBuilder;
import net.datatp.module.data.xlsx.export.XLSXExportHelper;
import net.datatp.module.http.get.GETContent;
import net.datatp.module.http.get.GETTmpStoreHandler;
import net.datatp.module.http.get.StoreInfo;
import net.datatp.module.monitor.activity.StatisticService;
import net.datatp.module.monitor.activity.entity.StatisticKey;
import net.datatp.module.service.BaseComponent;
import net.datatp.security.client.ClientContext;
import net.datatp.util.ds.Objects;
import net.datatp.util.io.IOUtil;
import net.datatp.util.text.DateUtil;
import net.datatp.util.text.StringUtil;
import org.apache.poi.hssf.util.HSSFColor.HSSFColorPredefined;
import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.util.IOUtils;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.apache.poi.xssf.usermodel.XSSFDrawing;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("QuotationDocumentService")
public class QuotationDocumentService extends BaseComponent {
  @Autowired
  AppEnv appEnv;

  @Autowired
  private GETTmpStoreHandler tmpStoreHandler;

  @Autowired
  private StatisticService statService;

  @Autowired
  private AccountLogic accountLogic;

  public StoreInfo exportQuotationQuoteAsXlsx(ClientContext client, ICompany company, QuotationExportRequest req) throws Exception {
    Account account = accountLogic.getAccountById(client, client.getAccountId());
    Objects.assertNotNull(account, "Account {0} is not found", client.getRemoteUser());
    int maxColIdx = req.getMaxColumnIdx();
    ExportQuotationHelper quotationHelper = new ExportQuotationHelper(req);
    XLSXWorkbookWriter bookWriter = quotationHelper.getBookWriter();
    Workbook wb = bookWriter.getWorkbook();
    XLSXSheetWriter sheetWriter = quotationHelper.getSheetWriter();
    Sheet sheet = sheetWriter.getSheet();

    XLSXSectionWriter sectionWriter = quotationHelper.getSectionWriter();
    ExportStyleBuilder styleBuilder = new ExportStyleBuilder(bookWriter.getWorkbook());
    quotationHelper.buildBanner(company);
    final float DEFAULT_HEIGHT = quotationHelper.getDEFAULT_HEIGHT();
    Row currentRow = sectionWriter.nextRow();

    CellStyle DEFAULT_ST = XLSXExportHelper.getContentStyle(wb);
    Font defaultFont = defaultFont(wb);
    XLSXExportHelper.applyBorder(DEFAULT_ST, BorderStyle.NONE);
    DEFAULT_ST.setFont(defaultFont);

    final CellStyle DF_BORDER_ST = wb.createCellStyle();
    DF_BORDER_ST.cloneStyleFrom(DEFAULT_ST);
    XLSXExportHelper.applyBorder(DF_BORDER_ST, BorderStyle.THIN);

    List<DataListExportModel.XLSXRowTemplate> rows = req.getQuoteSections();
    sectionWriter.nextRow();
    DataListExportModel.XLSXBlockTemplate template = new DataListExportModel.XLSXBlockTemplate(rows);
    sectionWriter.writeBlockTemplate(styleBuilder, template);
    sectionWriter.nextRow();
    currentRow = sectionWriter.nextRow();
    currentRow.setHeightInPoints(1.5F * DEFAULT_HEIGHT);

    // ----------------------------- FOOTER ---------------------------------
    final CellStyle COMPANY_BORDER_ST = wb.createCellStyle();
    COMPANY_BORDER_ST.cloneStyleFrom(DEFAULT_ST);
    final Font FONT_ORANGE = defaultFont(wb);
    FONT_ORANGE.setBold(true);
    FONT_ORANGE.setColor(HSSFColorPredefined.ORANGE.getIndex());
    COMPANY_BORDER_ST.setFont(FONT_ORANGE);
    XLSXExportHelper.setCellText(wb, currentRow, 0, "BEE LOGISTICS CORPORATION: ").setCellStyle(COMPANY_BORDER_ST);

    String mobile = !StringUtil.isEmpty(account.getMobile()) ? account.getMobile() : "84.";
    currentRow = sectionWriter.nextRow();
    XLSXExportHelper.setCellText(wb, currentRow, 0, "Cell: " + mobile).setCellStyle(DEFAULT_ST);

    currentRow = sectionWriter.nextRow();
    String email = !StringUtil.isEmpty(account.getEmail()) ? "Email: " + account.getEmail() : "Email: @beelogistics.com";
    XLSXExportHelper.setCellText(wb, currentRow, 0, email).setCellStyle(DEFAULT_ST);

    currentRow = sectionWriter.nextRow();
    XLSXExportHelper.setCellText(wb, currentRow, 0, "Skype: " + mobile).setCellStyle(DEFAULT_ST);

    String website = "Website: www.beelogistics.com";
    final String companyCode = company.getCode();
    if (companyCode.equals("thudo")) {
      website = "Website: www.thudologistics.com";
    }
    currentRow = sectionWriter.nextRow();
    XLSXExportHelper.setCellText(wb, currentRow, 0, website).setCellStyle(DEFAULT_ST);

    sectionWriter.skipRow(2);
    currentRow = sectionWriter.nextRow();
  
    if (!companyCode.equals("thudo")) {
      InputStream footer1 = IOUtil.loadResource("classpath:data/logistics/sales/img/footer_1.jpg");
      if (footer1 != null) {
        byte[] bannerBytes = IOUtils.toByteArray(footer1);
        int imagePictureID = wb.addPicture(bannerBytes, Workbook.PICTURE_TYPE_JPEG);
        XSSFDrawing drawing = (XSSFDrawing) sheet.createDrawingPatriarch();
        XSSFClientAnchor imgAnchor = new XSSFClientAnchor();
        imgAnchor.setCol1(0); // Sets the column (0 based) of the first cell.
        imgAnchor.setCol2(6); // Sets the column (0 based) of the Second cell.
        imgAnchor.setRow1(currentRow.getRowNum()); // Sets the row (0 based) of the first cell.
        imgAnchor.setRow2(currentRow.getRowNum() + 3); // Sets the row (0 based) of the Second cell.
        drawing.createPicture(imgAnchor, imagePictureID);
      }
      sectionWriter.skipRow(3);
      currentRow = sectionWriter.nextRow();
    }
    InputStream footer2 = IOUtil.loadResource("classpath:data/logistics/sales/img/footer_2.png");
    if (footer2 != null) {
      byte[] bannerBytes = IOUtils.toByteArray(footer2);
      int imagePictureID = wb.addPicture(bannerBytes, Workbook.PICTURE_TYPE_PNG);
      XSSFDrawing drawing = (XSSFDrawing) sheet.createDrawingPatriarch();
      XSSFClientAnchor imgAnchor = new XSSFClientAnchor();
      imgAnchor.setCol1(0); // Sets the column (0 based) of the first cell.
      imgAnchor.setCol2(maxColIdx + 1); // Sets the column (0 based) of the Second cell.
      imgAnchor.setRow1(currentRow.getRowNum()); // Sets the row (0 based) of the first cell.
      imgAnchor.setRow2(currentRow.getRowNum() + 5); // Sets the row (0 based) of the Second cell.
      drawing.createPicture(imgAnchor, imagePictureID);
    }

    sheet.setColumnWidth(0, 50 * 256);
    for (int i = 1; i < 9; i++) {
      sheet.setColumnWidth(i, 20 * 256);
    }

    byte[] data = bookWriter.createByteArrayData();
    bookWriter.close();

    String fileName = "Quotation-" + DateUtil.asCompactDateId(new Date()) + ".xlsx";
    ;
    if (StringUtil.isNotEmpty(req.getQuotationNo())) {
      fileName = req.getQuotationNo() + ".xlsx";
    }
    String mode = req.getInquiry().getMode().getShortLabel().toLowerCase();
    String purpose = req.getInquiry().getPurpose().getLabel().toLowerCase();
    StatisticKey key = new StatisticKey(new Date(), client, "fforwarder:quotation:export", mode + "-" + purpose);
    statService.incr(client, key, 1);

    GETContent getContent = new GETContent(fileName, data);
    return tmpStoreHandler.store(client, getContent, true);
  }

  private static Font defaultFont(Workbook wb) {
    Font font = wb.createFont();
    font.setFontName("Times New Roman");
    font.setFontHeightInPoints((short) 12);
    return font;
  }

}