package cloud.datatp.fforwarder.sales.quotation.entity;

import cloud.datatp.fforwarder.price.entity.AirTransportCharge;
import cloud.datatp.fforwarder.price.entity.SeaFclTransportCharge;
import cloud.datatp.fforwarder.price.entity.SeaLclTransportCharge;
import cloud.datatp.fforwarder.sales.common.entity.CustomerAdditionalCharge;
import cloud.datatp.fforwarder.sales.common.entity.CustomerAirTransportAdditionalCharge;
import cloud.datatp.fforwarder.sales.common.entity.CustomerSeaTransportAdditionalCharge;
import cloud.datatp.fforwarder.sales.common.entity.QuotationCharge;
import cloud.datatp.fforwarder.sales.inquiry.entity.SpecificServiceInquiry;
import cloud.datatp.fforwarder.sales.quotation.dto.LocalCharge;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import jakarta.persistence.CascadeType;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.OneToOne;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import java.io.Serial;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import net.datatp.module.company.entity.CompanyEntity;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.util.DeleteGraph;
import net.datatp.module.data.db.util.DeleteGraphs;
import net.datatp.security.client.ClientContext;
import net.datatp.util.dataformat.DataSerializer;
import net.datatp.util.ds.Arrays;

@Entity
@Table(name = SpecificQuotation.TABLE_NAME)
@DeleteGraphs({
    @DeleteGraph(target = SpecificServiceInquiry.class, joinField = "inquiry_id"),
})
@JsonInclude(Include.NON_NULL)
@NoArgsConstructor @Setter @Getter
public class SpecificQuotation extends CompanyEntity {
  @Serial
  private static final long serialVersionUID = 1L;
  public static final String TABLE_NAME = "lgc_sales_specific_quotation";

  @OneToOne(optional = false, cascade = CascadeType.ALL, orphanRemoval = true)
  @JoinColumn(name = "inquiry_id", referencedColumnName = "id")
  private SpecificServiceInquiry inquiry;

  @Column(name = "inquiry_id", nullable = false, updatable = false, insertable = false)
  private Long inquiryId;

  @Column(name = "is_favorite")
  private boolean is_favorite;

  @Column(name = "markup_model_id")
  private Long markupModelId; // OLD: customerChargeModel

  /* ----------------- Sea LCL, Sea FCL, Air Freight, Trucking Template ---------------*/
  @Transient
  private List<QuotationCharge>  quoteList = new ArrayList<>();

  @Transient
  private List<LocalCharge>           localHandlingCharges = new ArrayList<>();

  public SpecificQuotation(SpecificServiceInquiry inquiry) {
    setInquiry(inquiry);
  }

  public SpecificQuotation withLCLQuotes(List<SeaLclTransportCharge> prices) {
    for(SeaLclTransportCharge price : prices) {
      QuotationCharge quote = new QuotationCharge(price);
      withQuote(quote);
      List<CustomerSeaTransportAdditionalCharge> collect = price.getAdditionalCharges()
        .stream()
        .map(CustomerSeaTransportAdditionalCharge::new)
        .collect(Collectors.toList());
      withAdditionalCharges(collect);
    }
    return this;
  }

  public SpecificQuotation withFCLQuotes(List<SeaFclTransportCharge> prices) {
    for(SeaFclTransportCharge price : prices) {
      QuotationCharge quote = new QuotationCharge(price);
      withQuote(quote);

      List<CustomerSeaTransportAdditionalCharge> collect = price.getAdditionalCharges()
        .stream()
        .map(CustomerSeaTransportAdditionalCharge::new)
        .collect(Collectors.toList());
      withAdditionalCharges(collect);
    }
    return this;
  }

  public SpecificQuotation withAirQuotes(List<AirTransportCharge> prices) {
    for(AirTransportCharge price : prices) {
      QuotationCharge quote = new QuotationCharge(price);
      withQuote(quote);

      List<CustomerAirTransportAdditionalCharge> collect = price.getAdditionalCharges()
        .stream()
        .map(CustomerAirTransportAdditionalCharge::new)
        .collect(Collectors.toList());
      withAdditionalCharges(collect);
    }
    return this;
  }

  public SpecificQuotation withAdditionalCharges(List<? extends CustomerAdditionalCharge> addCharges) {
    List<LocalCharge> seaLocalCharges = LocalCharge.convertToLocalCharge(addCharges);
    for(LocalCharge localCharge : seaLocalCharges) {
      withLocalHandlingCharges(localCharge);
    }
    return this;
  }

  public SpecificQuotation withLocalHandlingCharges(LocalCharge localCharge) {
    localHandlingCharges = Arrays.addToList(localHandlingCharges, localCharge);
    return this;
  }

  public SpecificQuotation withQuote(QuotationCharge quote) {
    quoteList = Arrays.addToList(quoteList, quote);
    return this;
  }

  public void setInquiry(SpecificServiceInquiry inquiry) {
    /*
    if(Collections.isNotEmpty(quoteList)) {
      QuotationCharge quotationCharge = quoteList.get(0);
      String fromLocationCode = quotationCharge.getFromLocationCode();
      if(StringUtil.isNotEmpty(fromLocationCode)) {
        inquiry.setFromLocationCode(fromLocationCode);
        inquiry.setFromLocationLabel(quotationCharge.getFromLocationLabel());
      }

      String toLocationCode = quotationCharge.getToLocationCode();
      if(StringUtil.isNotEmpty(toLocationCode)) {
        inquiry.setFromLocationCode(toLocationCode);
        inquiry.setFromLocationLabel(quotationCharge.getToLocationLabel());
      }

      String finalDestination = quotationCharge.getFinalDestination();
      if(StringUtil.isNotEmpty(finalDestination)) {
        inquiry.setFinalDestination(finalDestination);
      }
    }
     */
    this.inquiry = inquiry;
    this.inquiryId = inquiry.getId();
  }

  public void set(ClientContext client, ICompany company) {
    super.set(client, company);
    if (inquiry != null) {
      inquiry.set(client, company);
    }
    set(client, company, quoteList);
  }

  public SpecificQuotation clone() {
    return DataSerializer.JSON.clone(this);
  }

  @SuppressWarnings("unchecked")
  public SpecificQuotation clearIds() {
    clearId(this);
    setCreatedBy(null);
    setCreatedTime(null);
    setModifiedBy(null);
    setModifiedTime(null);
    inquiry.clearIds();
    return this;
  }
}