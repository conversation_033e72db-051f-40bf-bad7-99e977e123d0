package cloud.datatp.fforwarder.sales.quotation;

import cloud.datatp.fforwarder.sales.quotation.dto.ConfirmQuotationModel;
import cloud.datatp.fforwarder.sales.quotation.dto.Params;
import cloud.datatp.fforwarder.sales.quotation.entity.GenericQuotation;
import cloud.datatp.fforwarder.sales.quotation.entity.SpecificQuotation;
import java.util.List;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.service.BaseComponent;
import net.datatp.security.client.ClientContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service("QuotationService")
public class QuotationService extends BaseComponent {

  @Autowired
  private GenericQuotationLogic genericQuotationLogic;

  @Autowired
  private SpecificQuotationLogic sQuotationLogic;

  @Transactional
  public GenericQuotation saveGenericQuotation(ClientContext client, ICompany company, GenericQuotation quotation) {
    return genericQuotationLogic.saveGenericQuotation(client, company, quotation);
  }

  @Transactional
  public boolean updateStorageState(ClientContext client, ChangeStorageStateRequest req) {
    return genericQuotationLogic.updateStorageState(client, req);
  }

  @Transactional
  public GenericQuotation newCombinedQuotation(ClientContext client, ICompany company, GenericQuotation quotation) {
    return genericQuotationLogic.newCombinedQuotation(client, company, quotation);
  }

  @Transactional(readOnly = true)
  public List<SpecificQuotation> findSQuotationByCompany(ClientContext client, ICompany company) {
    return sQuotationLogic.findByCompany(client, company);
  }

  @Transactional(readOnly = true)
  public SpecificQuotation getSpecificQuotationById(ClientContext client, ICompany company, Long id) {
    return sQuotationLogic.getById(client, company, id);
  }

  @Transactional
  public SpecificQuotation saveSpecificQuotation(ClientContext client, ICompany company, SpecificQuotation quotation) {
    return sQuotationLogic.saveSpecificQuotation(client, company, quotation);
  }

  @Transactional
  public SpecificQuotation matchPriceSpecificQuotation(ClientContext client, ICompany company, Long sQuotationId, List<Long> priceReferenceIds) {
    return sQuotationLogic.matchPriceSpecificQuotation(client, company, sQuotationId, priceReferenceIds);
  }

  @Transactional
  public SpecificQuotation newSpecificQuotation(ClientContext client, ICompany company, Params.SQuotationCreation template) {
    return sQuotationLogic.newSpecificQuotation(client, company, template);
  }

  @Transactional
  public SpecificQuotation copySpecificQuotation(ClientContext client, ICompany company, Long quotationId) {
    return sQuotationLogic.copySpecificQuotation(client, company, quotationId);
  }

  @Transactional
  public ConfirmQuotationModel sendQuotation(ClientContext client, ICompany company, ConfirmQuotationModel template) {
    return sQuotationLogic.sendQuotation(client, company, template);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchSpecificQuotations(ClientContext client, ICompany company, SqlQueryParams params) {
    return sQuotationLogic.searchSpecificQuotations(client, company, params);
  }

  @Transactional
  public boolean changeSpecificQuotationStorageState(ClientContext client, ChangeStorageStateRequest req) {
    return sQuotationLogic.changeSpecificQuotationStorageState(client, req);
  }

  @Transactional
  public int deleteByIds(ClientContext client, ICompany company, List<Long> ids) {
    return sQuotationLogic.deleteByIds(client, company, ids);
  }

}