package cloud.datatp.bfsone.okr.groovy

import java.time.LocalDate
import java.time.format.DateTimeFormatter

import org.springframework.context.ApplicationContext

import net.datatp.lib.executable.ExecutableContext
import net.datatp.lib.executable.Executor
import net.datatp.module.data.db.ExecutableSqlBuilder
import net.datatp.module.data.db.query.SqlQueryParams
import net.datatp.util.ds.MapObject
import net.datatp.util.ds.Objects
import net.datatp.util.text.StringUtil

class BFSOneOKRQuery extends Executor {

  public class FetchJob extends ExecutableSqlBuilder {

    public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
      MapObject params = ctx.getParam("sqlParams")
      String resultField = params.getString("resultField", "");
      String branchOffice = params.getString("branchOffice", "All");
      String branchCondition = "";
      if (StringUtil.isNotEmpty(branchOffice) || !branchOffice.contains("All")) {
        String[] tokens = branchOffice.split(",");
        branchCondition += "AND (";
        for (int i = 0; i < tokens.length; i++) {
          branchCondition = branchCondition + "trans_id LIKE '%${tokens[i].trim()}%'";
          if (i != tokens.length - 1) branchCondition = branchCondition + " OR ";
        }
        branchCondition += ")\r\n";
      }

      Objects.assertNotNull(params.get("fromDate"), "FromDate must not be null");
      Objects.assertNotNull(params.get("toDate"), "ToDate must not be null");
      String fromDate = params.getString("fromDate");
      String toDate = params.getString("toDate");

      String finishDateCondition = ""
      String etaEtdCondition = ""
      String method = params.getString("method");
      if ("ETD_ETA".equals(method)) etaEtdCondition = "AND loading_date >= '" + fromDate + "' AND loading_date <= '" + toDate + "'\r\n";
      else finishDateCondition = "AND cp.FinishDate >= '" + fromDate + "' AND cp.FinishDate <= '" + toDate + "'\r\n";

      String sourceCondition = "";
      String source = params.getString("source");
      if (StringUtil.isNotEmpty(source)) sourceCondition = "AND shipment_type LIKE '%${source}%'"

      String serviceTypeCondition = ""
      String serviceTypes = parseListString(params.getString("serviceTypes", ""));
      if (StringUtil.isNotEmpty(serviceTypes)) {
        serviceTypeCondition = "AND type_of_service IN (" + serviceTypes + ")";
      }

      String salemanCondition = "";
      String includeSalemans = parseListString(params.getString("includeSalemans", ""));
      String excludeSalemans = parseListString(params.getString("excludeSalemans", ""));
      if (StringUtil.isNotEmpty(includeSalemans)) {
        salemanCondition = "AND saleman IN (" + includeSalemans + ")";
      } else if (StringUtil.isNotEmpty(excludeSalemans)) {
        salemanCondition = "AND saleman NOT IN (" + excludeSalemans + ")";
      }

      String creatorCondition = ""
      String includeCreators = parseListString(params.getString("includeCreators", ""));
      String excludeCreators = parseListString(params.getString("excludeCreators", ""));
      if (StringUtil.isNotEmpty(includeCreators)) {
        creatorCondition = "AND creator IN (" + includeCreators + ")";
      } else if (StringUtil.isNotEmpty(excludeCreators)) {
        creatorCondition = "AND creator NOT IN (" + excludeCreators + ")";
      }

      String polCondition = ""
      String includePolCountrys = parseListString(params.getString("includePolCountries", ""));
      String excludePolCountrys = parseListString(params.getString("excludePolCountries", ""));
      String includePols = parseListString(params.getString("includePols", ""));
      String excludePols = parseListString(params.getString("excludePols", ""));

      if (StringUtil.isNotEmpty(includePolCountrys)) {
        polCondition = "AND pol_country_code IN (" + includePolCountrys + ")";
      } else if (StringUtil.isNotEmpty(excludePolCountrys)) {
        polCondition = "AND pol_country_code NOT IN (" + excludePolCountrys + ")";
      } else if (StringUtil.isNotEmpty(includePols)) {
        polCondition = "AND pol_code IN (" + includePols + ")";
      } else if (StringUtil.isNotEmpty(excludePols)) {
        polCondition = "AND pol_code NOT IN (" + excludePols + ")";
      }

      String podCondition = ""
      String includePodCountrys = parseListString(params.getString("includePodCountries", ""));
      String excludePodCountrys = parseListString(params.getString("excludePodCountries", ""));
      String includePods = parseListString(params.getString("includePods", ""));
      String excludePods = parseListString(params.getString("excludePods", ""));
      if (StringUtil.isNotEmpty(includePodCountrys)) {
        podCondition = "AND pod_country_code IN (" + includePodCountrys + ")";
      } else if (StringUtil.isNotEmpty(excludePodCountrys)) {
        podCondition = "AND pod_country_code NOT IN (" + excludePodCountrys + ")";
      } else if (StringUtil.isNotEmpty(includePods)) {
        podCondition = "AND pod_code IN (" + includePods + ")";
      } else if (StringUtil.isNotEmpty(excludePods)) {
        podCondition = "AND pod_code NOT IN (" + excludePods + ")";
      }

      String agentCondition = ""
      String includeAgents = parseListString(params.getString("includeAgents", ""));
      String excludeAgents = parseListString(params.getString("excludeAgents", ""));
      if (StringUtil.isNotEmpty(includeAgents)) {
        agentCondition = "AND agent IN (" + includeAgents + ")";
      } else if (StringUtil.isNotEmpty(excludeAgents)) {
        agentCondition = "AND agent NOT IN (" + excludeAgents + ")";
      }

      String partnerCondition = ""
      String includePartners = parseListString(params.getString("includePartners", ""));
      String excludePartners = parseListString(params.getString("excludePartners", ""));
      if (StringUtil.isNotEmpty(includePartners)) {
        partnerCondition = "AND partner IN (" + includePartners + ")";
      } else if (StringUtil.isNotEmpty(excludePartners)) {
        partnerCondition = "AND partner NOT IN (" + excludePartners + ")";
      }

      String carrierCondition = ""
      String includeCarriers = parseListString(params.getString("includeCarriers", ""));
      String excludeCarriers = parseListString(params.getString("excludeCarriers", ""));
      if (StringUtil.isNotEmpty(includeCarriers)) {
        carrierCondition = "AND carrier IN (" + includeCarriers + ")";
      } else if (StringUtil.isNotEmpty(excludeCarriers)) {
        carrierCondition = "AND carrier NOT IN (" + excludeCarriers + ")";
      }

      if ("profit".equals(resultField) && "FINISH_DATE".equals(method)) {
        branchCondition = branchCondition.replace("trans_id", "t.TransID");
        serviceTypeCondition = serviceTypeCondition.replace("type_of_service", "t.TpyeofService");
        sourceCondition = sourceCondition.replace("shipment_type", "td.ShipmentType");
        salemanCondition = salemanCondition.replace("saleman", "saleman.Username");
        creatorCondition = creatorCondition.replace("creator", "t.WhoisMaking ");
        if (StringUtil.isNotEmpty(includePolCountrys) || StringUtil.isNotEmpty(excludePolCountrys)) {
          polCondition = polCondition.replace("pol_country_code", "pol.Country");
        } else {
          polCondition = polCondition.replace("pol_code", "pol.AirPortID");
        }
        if (StringUtil.isNotEmpty(includePodCountrys) || StringUtil.isNotEmpty(excludePodCountrys)) {
          podCondition = podCondition.replace("pod_country_code", "pod.Country");
        } else {
          podCondition = podCondition.replace("pod_code", "pod.AirPortID");
        }
        agentCondition = agentCondition.replace("agent", "t.AgentID");
        partnerCondition = partnerCondition.replace("partner", "td.ShipperID");
        carrierCondition = carrierCondition.replace("carrier", "t.ColoaderID");

        String query = """
          WITH CollectProfit AS (
            SELECT
              brwh.HAWBNO,
              brwh.FinishDate,
              SUM(brwh.Quantity * brwh.UnitPrice * brwh.ExtVND) AS BuyingRate,
              0 AS SellingRate,
              0 AS ProfitShareBuying,
              0 AS ProfitShareSelling
            FROM BuyingRateWithHBL brwh
            GROUP BY
              HAWBNO,
              brwh.IDKeyIndex,
              brwh.FinishDate
          UNION ALL
            SELECT
              sr.HAWBNO,
              sr.FinishDate,
              0 AS BuyingRate,
              SUM(sr.Quantity * sr.UnitPrice * sr.ExtVND) AS SellingRate,
              0 AS ProfitShareBuying,
              0 AS ProfitShareSelling
            FROM SellingRate sr
            GROUP BY
              HAWBNO,
              sr.FinishDate
          UNION ALL
            SELECT
              ps.HAWBNO,
              ps.FinishDate,
              0 AS BuyingRate,
              0 AS SellingRate,
              SUM(
                CASE
                  WHEN ps.Obh != 1 AND ps.SortDes LIKE 'B%' THEN ps.Quantity * ps.UnitPrice * ps.ExtVND
                  ELSE 0
                END
              ) AS ProfitShareBuying,
              SUM(
                CASE
                  WHEN ps.Obh != 1 AND ps.SortDes LIKE 'S%' then ps.Quantity * ps.UnitPrice * ps.ExtVND
                  ELSE 0
                END
              ) AS ProfitShareSelling
            FROM ProfitShares ps
            GROUP BY
              HAWBNO,
              ps.FinishDate
          )
          SELECT
            FORMAT(cp.FinishDate, 'dd/MM/yyyy') as date,
            SUM(cp.SellingRate + cp.ProfitShareSelling - cp.BuyingRate - cp.ProfitShareBuying) AS profit,
            COUNT (cp.HAWBNO) AS count_hawb
          FROM Transactions t
          INNER JOIN TransactionDetails td  ON td.TransID = t.TransID
          INNER JOIN HAWB h                 ON h.HWBNO = td.HWBNO
          INNER JOIN CollectProfit cp       ON h.HWBNO = cp.HAWBNO
          LEFT JOIN UserInfos saleman       ON saleman.UserID = td.ContactID
          LEFT JOIN Airports pol            ON pol.AirPortName = t.PortofLading
          LEFT JOIN Airports pod            ON pod.AirPortName = t.PortofUnlading
          WHERE t.TransID IS NOT NULL AND cp.HAWBNO IS NOT NULL
            ${branchCondition}
            ${serviceTypeCondition}
            ${finishDateCondition}
            ${sourceCondition}
            ${salemanCondition}
            ${creatorCondition}
            ${polCondition}
            ${podCondition}
            ${agentCondition}
            ${partnerCondition}
            ${carrierCondition}
          GROUP BY FORMAT(cp.FinishDate, 'dd/MM/yyyy')
          ORDER BY FORMAT(cp.FinishDate, 'dd/MM/yyyy')
          """;
          return query;
        }

        String buyingRateQuery =
        """
          SELECT
            brwh.HAWBNO,
            brwh.FinishDate,
            SUM(brwh.Quantity * brwh.UnitPrice * brwh.ExtVND) AS BuyingRate,
            0 AS SellingRate,
            0 AS ProfitShareBuying,
            0 AS ProfitShareSelling
          FROM BuyingRateWithHBL brwh
          GROUP BY
            HAWBNO,
            FinishDate
        """;

        String sellingRateQuery =
        """
          SELECT
            sr.HAWBNO,
            sr.FinishDate,
            0 AS BuyingRate,
            SUM(sr.Quantity * sr.UnitPrice * sr.ExtVND) AS SellingRate,
            0 AS ProfitShareBuying,
            0 AS ProfitShareSelling
          FROM SellingRate sr
          GROUP BY
            HAWBNO,
            FinishDate
        """;

        String profitSharesQuery =
        """
          SELECT
            ps.HAWBNO,
            ps.FinishDate,
            0 AS BuyingRate,
            0 AS SellingRate,
            SUM(
              CASE
                WHEN ps.Obh != 1 AND ps.SortDes LIKE 'B%' THEN ps.Quantity * ps.UnitPrice * ps.ExtVND
                ELSE 0
              END
            ) AS ProfitShareBuying,
            SUM(
              CASE
                WHEN ps.Obh != 1 AND ps.SortDes LIKE 'S%' THEN ps.Quantity * ps.UnitPrice * ps.ExtVND
                ELSE 0
              END
            ) AS ProfitShareSelling
          FROM ProfitShares ps
          GROUP BY
            HAWBNO,
            FinishDate
        """;

        String profitQuery = """
          SELECT
            t.TransID,
            cp.HAWBNO,
            SUM(cp.SellingRate + cp.ProfitShareSelling) AS Revenue,
            SUM(cp.BuyingRate + cp.ProfitShareBuying)   AS Costing
          FROM CollectProfit cp
          LEFT JOIN TransactionDetails td ON td.HWBNO = cp.HAWBNO
          INNER JOIN Transactions t ON t.TransID = td.TransID
          WHERE cp.HAWBNO IS NOT NULL ${finishDateCondition}
          GROUP BY
            t.TransID,
            cp.HAWBNO
        """;

        String profitAndVolumeQuery = """
          SELECT
            p.TransID,
            p.HAWBNO,
            p.Revenue,
            p.Costing,
            (p.Revenue - p.Costing) AS Profit,
            STRING_AGG(si.ContainerNo, '/') AS ContainerNo,
            SUM(
              CASE
                WHEN si.Container LIKE '4%' THEN si.Qty * 2
                WHEN si.Container LIKE '2%' THEN si.Qty * 1
                ELSE 0
              END
            ) as Teus
          FROM Profit p
          LEFT JOIN ShippingInstruction si ON si.TransID = p.TransID
          GROUP BY
            p.TransID,
            p.HAWBNO,
            p.Revenue,
            p.Costing
        """;

        String queryWithFinishDate = """
          WITH CollectProfit AS (
            ${buyingRateQuery}
            UNION ALL
            ${sellingRateQuery}
            UNION ALL
            ${profitSharesQuery}
          ),
          Profit AS (
            ${profitQuery}
          ),
          ProfitAndVolume AS (
            ${profitAndVolumeQuery}
          ),
          HouseBills AS (
            SELECT
              t.TransID               AS trans_id,
              t.MAWB                  AS mawb,
              t.TransDate             AS trans_date,
              t.ModifyDate            AS modify_date,
              t.TpyeofService         AS type_of_service,
              t.LoadingDate           AS loading_date,
              t.ArrivalDate           AS arrival_date,
              t.AgentID               AS agent,
              agent.PartnerName       AS agent_full_name,
              t.FlghtNo               AS flight_no,
              t.ContainerSize         AS container_size,
              CONCAT(t.EstimatedVessel, ' // ', t.ContSealNo) AS vessel_voy,
              td.HWBNO                AS hawb,
              td.ShipmentType         AS shipment_type,
              td.Quantity             AS quantity,
              td.UnitDetail           AS unit_detail,
              (CASE
                WHEN td.WeightChargeable != 0 THEN td.WeightChargeable
                ELSE t.ChargeableWeight
              END)                    AS cw,
              (CASE
                WHEN td.GrosWeight != 0 THEN td.GrosWeight
                ELSE t.GrossWeight
              END)                    AS gw,
              td.CBMSea               AS cbm,
              pol.AirPortID           AS pol_code,
              pol.AirPortName         AS pol_name,
              polct.CountryName       AS pol_country,
              polct.CountryCode       AS pol_country_code,
              pod.AirPortID           AS pod_code,
              pod.AirPortName         AS pod_name,
              podct.CountryName       AS pod_country,
              podct.CountryCode       AS pod_country_code,
              t.ColoaderID            AS carrier,
              carrier.PartnerName     AS carrier_full_name,
              td.ShipperId            AS partner,
              partner.PartnerName     AS partner_full_name,
              h.LocalVessel           AS local_vessel,
              h.ReferrenceNo          AS booking,
              pav.ContainerNo         AS container_no,
              pav.Revenue             AS revenue,
              pav.Costing             AS costing,
              pav.Profit              AS profit,
              pav.Teus                AS teus,
              cd.SoluongTK            AS cds_count,
              saleman.Username        AS saleman,
              saleman.FullName        AS saleman_full_name,
              t.WhoisMaking           AS creator,
              creator.FullName        AS creator_full_name
            FROM Transactions t
            INNER JOIN TransactionDetails td  ON td.TransID = t.TransID
            INNER JOIN HAWB h                 ON h.HWBNO = td.HWBNO
            INNER JOIN ProfitAndVolume pav    ON h.HWBNO = pav.HAWBNO
            LEFT JOIN CustomsDeclaration cd   ON cd.MasoTK = td.CustomsID
            LEFT JOIN UserInfos creator       ON creator.Username = t.WhoisMaking
            LEFT JOIN UserInfos saleman       ON saleman.UserID = td.ContactID
            LEFT JOIN Partners carrier        ON carrier.PartnerID = t.ColoaderID
            LEFT JOIN Partners agent          ON agent.PartnerID = t.AgentID
            LEFT JOIN Partners partner        ON partner.PartnerID = td.ShipperId
            LEFT JOIN Airports pol            ON pol.AirPortName = t.PortofLading
            LEFT JOIN lst_Countries polct     ON polct.CountryCode = pol.Country
            LEFT JOIN Airports pod            ON pod.AirPortName = t.PortofUnlading
            LEFT JOIN lst_Countries podct     ON podct.CountryCode = pod.Country
          )
          SELECT *
          FROM HouseBills
          WHERE trans_id IS NOT NULL
            ${branchCondition}
            ${serviceTypeCondition}
            ${sourceCondition}
            ${salemanCondition}
            ${creatorCondition}
            ${polCondition}
            ${podCondition}
            ${agentCondition}
            ${partnerCondition}
            ${carrierCondition}
        """;

        String queryWithoutFinishDate = """
          WITH CollectProfit AS (
            ${buyingRateQuery}
            UNION ALL
            ${sellingRateQuery}
            UNION ALL
            ${profitSharesQuery}
          ),
          Profit AS (
            ${profitQuery}
          ),
          ProfitAndVolume AS (
            ${profitAndVolumeQuery}
          ),
          HouseBills AS (
            SELECT
              t.TransID               AS trans_id,
              t.MAWB                  AS mawb,
              t.TransDate             AS trans_date,
              t.ModifyDate            AS modify_date,
              t.TpyeofService         AS type_of_service,
              t.LoadingDate           AS loading_date,
              t.ArrivalDate           AS arrival_date,
              t.AgentID               AS agent,
              agent.PartnerName       AS agent_full_name,
              t.FlghtNo               AS flight_no,
              t.ContainerSize         AS container_size,
              CONCAT(t.EstimatedVessel, ' // ', t.ContSealNo) AS vessel_voy,
              td.HWBNO                AS hawb,
              td.ShipmentType         AS shipment_type,
              td.Quantity             AS quantity,
              td.UnitDetail           AS unit_detail,
              (CASE
                WHEN td.WeightChargeable != 0 THEN td.WeightChargeable
                ELSE t.ChargeableWeight
              END)                    AS cw,
              (CASE
                WHEN td.GrosWeight != 0 THEN td.GrosWeight
                ELSE t.GrossWeight
              END)                    AS gw,
              td.CBMSea               AS cbm,
              pol.AirPortID           AS pol_code,
              pol.AirPortName         AS pol_name,
              polct.CountryName       AS pol_country,
              polct.CountryCode       AS pol_country_code,
              pod.AirPortID           AS pod_code,
              pod.AirPortName         AS pod_name,
              podct.CountryName       AS pod_country,
              podct.CountryCode       AS pod_country_code,
              t.ColoaderID            AS carrier,
              carrier.PartnerName     AS carrier_full_name,
              td.ShipperId            AS partner,
              partner.PartnerName     AS partner_full_name,
              h.LocalVessel           AS local_vessel,
              h.ReferrenceNo          AS booking,
              pav.ContainerNo         AS container_no,
              pav.Revenue             AS revenue,
              pav.Costing             AS costing,
              pav.Profit              AS profit,
              pav.Teus                AS teus,
              cd.SoluongTK            AS cds_count,
              saleman.Username        AS saleman,
              saleman.FullName        AS saleman_full_name,
              t.WhoisMaking           AS creator,
              creator.FullName        AS creator_full_name
            FROM Transactions t
            LEFT JOIN TransactionDetails td   ON td.TransID = t.TransID
            LEFT JOIN CustomsDeclaration cd   ON cd.MasoTK = td.CustomsID
            LEFT JOIN HAWB h                  ON h.HWBNO = td.HWBNO
            LEFT JOIN ProfitAndVolume pav     ON h.HWBNO = pav.HAWBNO
            LEFT JOIN UserInfos creator       ON creator.Username = t.WhoisMaking
            LEFT JOIN UserInfos saleman       ON saleman.UserID = td.ContactID
            LEFT JOIN Partners carrier        ON carrier.PartnerID = t.ColoaderID
            LEFT JOIN Partners agent          ON agent.PartnerID = t.AgentID
            LEFT JOIN Partners partner        ON partner.PartnerID = td.ShipperId
            LEFT JOIN Airports pol            ON pol.AirPortName = t.PortofLading
            LEFT JOIN lst_Countries polct     ON polct.CountryCode = pol.Country
            LEFT JOIN Airports pod            ON pod.AirPortName = t.PortofUnlading
            LEFT JOIN lst_Countries podct     ON podct.CountryCode = pod.Country
          )
          SELECT *
          FROM HouseBills
          WHERE trans_id IS NOT NULL
            ${branchCondition}
            ${serviceTypeCondition}
            ${etaEtdCondition}
            ${sourceCondition}
            ${salemanCondition}
            ${creatorCondition}
            ${polCondition}
            ${podCondition}
            ${agentCondition}
            ${partnerCondition}
            ${carrierCondition}
        """;

        if ("ETD_ETA".equals(method)) return queryWithoutFinishDate;
        else return queryWithFinishDate;
    }
  }

  String parseListString(String token) {
    if (StringUtil.isEmpty(token)) return "";
    String result = "";
    String[] tokens = token.split(",");
    for (int i = 0; i < tokens.length; i++) {
      result = result + "'" + tokens[i].trim() + "'";
      if (i != tokens.length - 1) result = result + ", "
    }
    return result;
  }

  public BFSOneOKRQuery() {
    register(new FetchJob());
  }
}
