package cloud.datatp.bfsone;

import groovy.lang.Binding;
import jakarta.annotation.PostConstruct;

import java.util.List;
import javax.sql.DataSource;

import lombok.extern.slf4j.Slf4j;
import net.datatp.security.client.ClientContext;
import net.datatp.module.company.CompanyConfigLogic;
import net.datatp.module.company.CompanyLogic;
import net.datatp.module.company.entity.Company;
import net.datatp.module.company.entity.CompanyConfig;
import net.datatp.module.data.db.DAOService;
import net.datatp.module.data.db.ExternalDataSourceManager;
import net.datatp.module.data.db.ExternalDataSourceManager.DataSourceParams;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.SqlQueryManager.QueryContext;
import net.datatp.module.data.db.SqlQueryUnitManager;
import net.datatp.module.data.db.SqlSelectView;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.hr.EmployeeLogic;
import net.datatp.module.partner.PartnerLogic;
import net.datatp.module.partner.PartnerMergeBFSOnePartnerPreviewModel;
import net.datatp.module.partner.entity.Partner;
import net.datatp.util.ds.Collections;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;
import net.datatp.util.text.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class BFSOneDataLogic extends DAOService {
  private String SCRIPT_DIR;

  @Autowired
  private CompanyConfigLogic companyConfigLogic;

  @Autowired
  private ExternalDataSourceManager dataSourceManager;

  @Autowired
  private PartnerLogic partnerLogic;


  @Autowired
  private CompanyLogic companyLogic;

  @Autowired
  private EmployeeLogic employeeLogic;

  @Autowired
  protected SqlQueryUnitManager sqlQueryUnitManager;

  @PostConstruct
  public void onInit() {
    SCRIPT_DIR = appEnv.addonPath("logistics", "groovy/");
  }


  public List<SqlMapRecord> findTransactionInfoByCustomNo(ClientContext client, ICompany company, String customNo) {
    CompanyConfig companyConfig = companyConfigLogic.getCompanyConfigByCompanyId(client, company.getId());
    ExternalDataSourceManager.DataSourceParams dsPrams = companyConfig.getSubConfigAs("bfsone.ds", ExternalDataSourceManager.DataSourceParams.class);
    DataSource ds = dataSourceManager.getDataSource(client, dsPrams);
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/bfsone/groovy/TransactionToolsSql.groovy";
    String scriptName = "FindTransactionInfoByCustomNo";

    SqlQueryUnitManager.QueryContext queryContext = sqlQueryUnitManager.create(scriptDir, scriptFile, scriptName);
    SqlQueryParams sqlQueryParams = new SqlQueryParams();
    sqlQueryParams.addParam("customDocNo", customNo);
    SqlSelectView view = queryContext.createSqlSelectView(ds, sqlQueryParams);
    List<SqlMapRecord> records = view.renameColumWithJavaConvention().getSqlMapRecords();
    log.info("Retrieved {} records", records.size());
    return records;
  }

  public List<SqlMapRecord> searchBFSOneTransactionsData(ClientContext client, ICompany company, SqlQueryParams params) {
    CompanyConfig companyConfig = companyConfigLogic.getCompanyConfigByCompanyId(client, company.getId());
    DataSourceParams dsPrams = companyConfig.getSubConfigAs("bfsone.ds", DataSourceParams.class);
    MapObject config = companyConfig.getSubConfigAs("bfsone.config", MapObject.class);
    DataSource ds = dataSourceManager.getDataSource(client, dsPrams);
    QueryContext queryContext = sqlQueryManager.create(SCRIPT_DIR, "lgc/bfsone/BFSOneTransactionsQuery.groovy");

    Binding binding = new Binding();
    binding.setVariable("company", company);
    binding.setVariable("sqlparams", params);
    binding.setVariable("companyConfig", config);

    SqlSelectView view = queryContext.createSqlSelectView(ds, binding);
    return view.renameColumWithJavaConvention().getSqlMapRecords();
  }

  public List<SqlMapRecord> fetchDataForOkr(ClientContext client, ICompany company, SqlQueryParams params) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/bfsone/okr/groovy/BFSOneOKRQuery.groovy";

    CompanyConfig companyConfig = companyConfigLogic.getCompanyConfigByCompanyId(client, company.getId());
    DataSourceParams dsPrams = companyConfig.getSubConfigAs("bfsone.ds", DataSourceParams.class);
    DataSource ds = dataSourceManager.getDataSource(client, dsPrams);
    String scriptName = "FetchJob";
    SqlQueryUnitManager.QueryContext queryContext = sqlQueryUnitManager.create(scriptDir, scriptFile, scriptName);
    SqlSelectView view = queryContext.createSqlSelectView(ds, params);
    return view.renameColumWithJavaConvention().getSqlMapRecords();
  }

  public List<SqlMapRecord> fetchDataWithJobNoAndHawbNo(ClientContext client, ICompany company, MapObject params) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/bfsone/groovy/BFSOneJobQueryWithJobNoAndHawbNo.groovy";

    CompanyConfig companyConfig = companyConfigLogic.getCompanyConfigByCompanyId(client, company.getId());
    DataSourceParams dsPrams = companyConfig.getSubConfigAs("bfsone.ds", DataSourceParams.class);
    DataSource ds = dataSourceManager.getDataSource(client, dsPrams);

    SqlQueryParams sqlParams = new SqlQueryParams();
    sqlParams.setParams(params);
    String scriptName = "FetchJobByJobNoAndHawbNo";
    SqlQueryUnitManager.QueryContext queryContext = sqlQueryUnitManager.create(scriptDir, scriptFile, scriptName);
    SqlSelectView view = queryContext.createSqlSelectView(ds, sqlParams);
    return view.renameColumWithJavaConvention().getSqlMapRecords();
  }

  public List<SqlMapRecord> findUserInfoData(ClientContext client, ICompany company, MapObject params) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/bfsone/groovy/BFSOneSql.groovy";

    CompanyConfig companyConfig = companyConfigLogic.getCompanyConfigByCompanyId(client, company.getId());
    DataSourceParams dsPrams = companyConfig.getSubConfigAs("bfsone.ds", DataSourceParams.class);
    DataSource ds = dataSourceManager.getDataSource(client, dsPrams);

    Binding binding = new Binding();
    binding.setVariable("params", params);
    String scriptName = "BFSOneUserInfoSql";
    SqlQueryUnitManager.QueryContext queryContext = sqlQueryUnitManager.create(scriptDir, scriptFile, scriptName);
    SqlSelectView view = queryContext.createSqlSelectView(ds, binding);
    return view.renameColumWithJavaConvention().getSqlMapRecords();
  }

  public List<SqlMapRecord> checkBFSOneUsername(ClientContext client, ICompany company, String loginId) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/bfsone/groovy/BFSOneSql.groovy";

    CompanyConfig companyConfig = companyConfigLogic.getCompanyConfigByCompanyId(client, company.getId());
    DataSourceParams dsPrams = companyConfig.getSubConfigAs("bfsone.ds", DataSourceParams.class);
    DataSource ds = dataSourceManager.getDataSource(client, dsPrams);
    SqlQueryParams sqlParams = new SqlQueryParams();
    sqlParams.addParam("loginId", loginId);

    String scriptName = "BFSOneUserInfoByUsernameSql";
    SqlQueryUnitManager.QueryContext queryContext = sqlQueryUnitManager.create(scriptDir, scriptFile, scriptName);
    SqlSelectView view = queryContext.createSqlSelectView(ds, sqlParams);
    return view.renameColumWithJavaConvention().getSqlMapRecords();
  }

  public List<SqlMapRecord> findPartnersData(ClientContext client, ICompany company, SqlQueryParams sqlParams) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/bfsone/groovy/BFSOneSql.groovy";

    CompanyConfig companyConfig = companyConfigLogic.getCompanyConfigByCompanyId(client, company.getId());
    DataSourceParams dsPrams = companyConfig.getSubConfigAs("bfsone.ds", DataSourceParams.class);
    DataSource ds = dataSourceManager.getDataSource(client, dsPrams);

    String scriptName = "BFSOnePartnerSql";
    SqlQueryUnitManager.QueryContext queryContext = sqlQueryUnitManager.create(scriptDir, scriptFile, scriptName);
    SqlSelectView view = queryContext.createSqlSelectView(ds, sqlParams);
    return view.renameColumWithJavaConvention().getSqlMapRecords();
  }

  public List<SqlMapRecord> findBFSOnePartnerById(ClientContext client, ICompany company, String bfsOnePartnerId) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/bfsone/groovy/FindBFSOnePartnerWithPartnerIdSql.groovy";

    CompanyConfig companyConfig = companyConfigLogic.getCompanyConfigByCompanyId(client, company.getId());
    DataSourceParams dsPrams = companyConfig.getSubConfigAs("bfsone.ds", DataSourceParams.class);
    DataSource ds = dataSourceManager.getDataSource(client, dsPrams);

    SqlQueryParams params = new SqlQueryParams();
    params.addParam("bfsOnePartnerId", bfsOnePartnerId);
    SqlQueryUnitManager.QueryContext queryContext = sqlQueryUnitManager.create(scriptDir, scriptFile, "FindBFSOnePartnerWithPartnerId");
    SqlSelectView view = queryContext.createSqlSelectView(ds, params);
    return view.renameColumWithJavaConvention().getSqlMapRecords();
  }

  public List<SqlMapRecord> findBFSOnePartner(ClientContext client, ICompany company, String bfsOnePartnerId, String taxCode) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/bfsone/groovy/FindBFSOnePartnerWithPartnerIdSql.groovy";

    CompanyConfig companyConfig = companyConfigLogic.getCompanyConfigByCompanyId(client, company.getId());
    DataSourceParams dsPrams = companyConfig.getSubConfigAs("bfsone.ds", DataSourceParams.class);
    DataSource ds = dataSourceManager.getDataSource(client, dsPrams);

    SqlQueryParams params = new SqlQueryParams();
    params.addParam("bfsOnePartnerId", bfsOnePartnerId);
    params.addParam("taxCode", taxCode);
    SqlQueryUnitManager.QueryContext queryContext = sqlQueryUnitManager.create(scriptDir, scriptFile, "FindBFSOnePartnerWithPartnerId");
    SqlSelectView view = queryContext.createSqlSelectView(ds, params);
    return view.renameColumWithJavaConvention().getSqlMapRecords();
  }

  public MapObject previewBFSOnePartnerMergeToPartner(ClientContext client, ICompany company, Partner partner) {
    List<SqlMapRecord> bfsOnePartners = findBFSOnePartnerById(client, company, partner.getCompanyLegacyLoginId());
    if (Collections.isEmpty(bfsOnePartners)) return null;
    SqlMapRecord bfsOnePartner = bfsOnePartners.get(0);
    PartnerMergeBFSOnePartnerPreviewModel previewMergeModel = partnerLogic.previewMergePartnerWithBFSOnePartnerData(client, company, partner, bfsOnePartner);
    return new MapObject("bfsOnePartner", bfsOnePartner, "previewPartnerMergeModel", previewMergeModel);
  }

  public List<SqlMapRecord> findLocationsData(ClientContext client, ICompany company) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/bfsone/groovy/BFSOneSql.groovy";

    CompanyConfig companyConfig = companyConfigLogic.getCompanyConfigByCompanyId(client, company.getId());
    DataSourceParams dsPrams = companyConfig.getSubConfigAs("bfsone.ds", DataSourceParams.class);
    DataSource ds = dataSourceManager.getDataSource(client, dsPrams);

    String scriptName = "BFSOneLocationSql";
    SqlQueryUnitManager.QueryContext queryContext = sqlQueryUnitManager.create(scriptDir, scriptFile, scriptName);
    SqlSelectView view = queryContext.createSqlSelectView(ds, new Binding());
    return view.renameColumWithJavaConvention().getSqlMapRecords();
  }

  public List<SqlMapRecord> findCountriesData(ClientContext client, ICompany company) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/bfsone/groovy/BFSOneSql.groovy";

    CompanyConfig companyConfig = companyConfigLogic.getCompanyConfigByCompanyId(client, company.getId());
    DataSourceParams dsPrams = companyConfig.getSubConfigAs("bfsone.ds", DataSourceParams.class);
    DataSource ds = dataSourceManager.getDataSource(client, dsPrams);

    String scriptName = "BFSOneCountrySql";
    SqlQueryUnitManager.QueryContext queryContext = sqlQueryUnitManager.create(scriptDir, scriptFile, scriptName);
    SqlSelectView view = queryContext.createSqlSelectView(ds, new Binding());
    return view.renameColumWithJavaConvention().getSqlMapRecords();
  }

  public List<SqlMapRecord> searchBFSOneUnit(ClientContext clientCtx, ICompany company, SqlQueryParams params) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/bfsone/groovy/BFSOneSql.groovy";
    SqlQueryUnitManager.QueryContext queryContext = sqlQueryUnitManager.create(scriptDir, scriptFile, "SearchBFSOneUnit");
    SqlSelectView view = queryContext.createSqlSelectView(bfsoneReportDataSource, params);
    return view.renameColumWithJavaConvention().getSqlMapRecords();
  }
  
  public List<SqlMapRecord> searchBFSOneHouseBill(ClientContext clientCtx, ICompany company, SqlQueryParams params) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/bfsone/groovy/BFSOneSql.groovy";
//    SqlQueryUnitManager.QueryContext queryContext = sqlQueryUnitManager.create(scriptDir, scriptFile, "FindHousebills");
//    SqlSelectView view = queryContext.createSqlSelectView(bfsoneReportDataSource, params);
    
    CompanyConfig companyConfig = companyConfigLogic.getCompanyConfigByCompanyId(clientCtx, company.getId());
    DataSourceParams dsPrams = companyConfig.getSubConfigAs("bfsone.ds", DataSourceParams.class);
    DataSource ds = dataSourceManager.getDataSource(clientCtx, dsPrams);
    SqlQueryUnitManager.QueryContext queryContext = sqlQueryUnitManager.create(scriptDir, scriptFile, "FindHousebills");
    SqlSelectView view = queryContext.createSqlSelectView(ds, params);
    return view.renameColumWithJavaConvention().getSqlMapRecords();
  }
}