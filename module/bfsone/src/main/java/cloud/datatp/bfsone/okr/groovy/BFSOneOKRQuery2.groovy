package cloud.datatp.bfsone.okr.groovy

import java.time.LocalDate
import java.time.format.DateTimeFormatter

import org.springframework.context.ApplicationContext

import net.datatp.lib.executable.ExecutableContext
import net.datatp.lib.executable.Executor
import net.datatp.module.data.db.ExecutableSqlBuilder
import net.datatp.module.data.db.query.SqlQueryParams
import net.datatp.util.ds.MapObject
import net.datatp.util.ds.Objects
import net.datatp.util.text.StringUtil

class BFSOneOKRQuery2 extends Executor {
  
  public class FetchJob extends ExecutableSqlBuilder {
    
    @Override
    public Object execute(ApplicationContext appCtx, ExecutableContext ctx) {
      MapObject sqlParams = ctx.getParam("sqlParams");
//      ${branchCondition}
//      ${serviceTypeCondition}
//      ${finishDateCondition}
//      ${sourceCondition}
//      ${salemanCondition}
//      ${creatorCondition}
//      ${polCondition}
//      ${podCondition}
//      ${agentCondition}
//      ${partnerCondition}
//      ${carrierCondition}
      
//      transaction_id
//      transaction_date
//      etd
//      eta
//      type_of_service
//      hawb_no
//      container_size
//      shipment_type
//      customs_no_summary
//      customer_code
//      customer_name
//      agent_code
//      agent_name
//      saleman_contact_id
//      hawb_gw
//      hawb_cbm
//      container_trip_count
//      trucking_trip_count
//      customs_number_count
//      total_selling_vnd
//      total_buying_vnd
//      total_other_debit_vnd
//      total_other_credit_vnd
//      exchange_rate_usd
//      created_at
//      updated_at
//      subtotal_selling_vnd
//      subtotal_buying_vnd
//      subtotal_other_debit_vnd
//      subtotal_other_credit_vnd
//      report_date
      

      String query = """ 
          SELECT * 
          FROM integrated_housebill h 
      """;
      return query;
    }
  }

  String parseListString(String token) {
    if (StringUtil.isEmpty(token)) return "";
    String result = "";
    String[] tokens = token.split(",");
    for (int i = 0; i < tokens.length; i++) {
      result = result + "'" + tokens[i].trim() + "'";
      if (i != tokens.length - 1) result = result + ", "
    }
    return result;
  }
  
  public BFSOneOKRQuery() {
    register(new FetchJob());
  }
}
