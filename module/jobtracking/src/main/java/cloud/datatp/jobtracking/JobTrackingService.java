package cloud.datatp.jobtracking;

import java.util.Collection;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import cloud.datatp.jobtracking.entity.JobTracking;
import cloud.datatp.jobtracking.entity.JobTrackingCell;
import cloud.datatp.jobtracking.entity.JobTrackingCellIssue;
import cloud.datatp.jobtracking.entity.JobTrackingClaim;
import cloud.datatp.jobtracking.entity.JobTrackingClaim.ClaimStatus;
import cloud.datatp.jobtracking.entity.JobTrackingClaimComment;
import cloud.datatp.jobtracking.entity.JobTrackingColumn;
import cloud.datatp.jobtracking.entity.JobTrackingPlugin;
import cloud.datatp.jobtracking.entity.JobTrackingProject;
import cloud.datatp.jobtracking.entity.JobTrackingProjectPlugin;
import cloud.datatp.jobtracking.model.JobTrackingModel;
import cloud.datatp.jobtracking.model.JobTrackingSearchParams;
import cloud.datatp.jobtracking.plugin.JobTrackingProjectPluginLogic;
import cloud.datatp.jobtracking.plugin.ProjectPluginExecuteModel;
import cloud.datatp.jobtracking.plugin.ProjectPluginOutput;
import net.datatp.security.client.ClientContext;
import net.datatp.module.company.entity.Company;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.util.ds.MapObject;

@Service("JobTrackingService")
public class JobTrackingService {
  @Autowired
  private JobTrackingProjectLogic projectLogic;
  
  @Autowired
  private JobTrackingLogic jobTrackingLogic;

  @Autowired
  private JobTrackingProjectPluginLogic projectPluginLogic;
  
  @Autowired
  private JobTrackingClaimLogic claimLogic;
  
//  PROJECT
  @Transactional(readOnly = true)
  public JobTrackingProject getJobTrackingProject(ClientContext client, ICompany company, Long id) {
    return projectLogic.getJobTrackingProject(client, company, id);
  }

  @Transactional(readOnly = true)
  public JobTrackingProject getJobTrackingProjectByCode(ClientContext client, ICompany company, String code) {
    return projectLogic.getJobTrackingProject(client, company, code);
  }
  
  @Transactional(readOnly = true)
  public JobTrackingProject getJobTrackingProjectByCompanyConfigCode(ClientContext client, ICompany company, String configCode) {
    return projectLogic.getJobTrackingProjectByCompanyConfigCode(client, company, configCode);
  }
  
  @Transactional(readOnly = true)
  public SqlMapRecord getJobTrackingMapRecordById(ClientContext client, ICompany company, Long trackingId) {
    return jobTrackingLogic.getJobTrackingMapRecordById(client, company, trackingId);
  }
  
  @Transactional
  public MapObject createJobTracking(ClientContext client, ICompany company, String jobTrackingProjectCode, MapObject data) {
    return jobTrackingLogic.createJobTracking(client, company, jobTrackingProjectCode, data);
  }

  @Transactional
  public JobTrackingProject saveJobTrackingProject(ClientContext client, ICompany company, JobTrackingProject project) {
    return projectLogic.saveJobTrackingProject(client, company, project);
  }
  
  @Transactional
  public boolean deleteJobTrackingProject(ClientContext client, ICompany company, List<Long> ids) {
    return projectLogic.deleteJobTrackingProjects(client, company, ids);
  }

  @Transactional
  public boolean updateJobTrackingProjectStorageState(ClientContext clientCtx, ICompany company, ChangeStorageStateRequest req) {
    return projectLogic.updateJobTrackingProjectStorageState(clientCtx, company, req);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchJobTrackingProjects(ClientContext client, ICompany company, SqlQueryParams params) {
    return projectLogic.searchJobTrackingProjects(client, company, params);
  }

// PROJECT PLUGIN
  @Transactional(readOnly = true)
  public List<JobTrackingProjectPlugin> findAvailablePlugins(ClientContext client, ICompany company) {
    return projectPluginLogic.findAvailablePlugins(client, company);
  }
  
  @Transactional
  public ProjectPluginOutput pluginExecute(ClientContext client, ICompany company, ProjectPluginExecuteModel pluginExecuteModel) {
    return projectPluginLogic.execute(client, company, pluginExecuteModel);
  }

//  JOB TRACKING
  @Transactional(readOnly = true)
  public JobTracking getJobTracking(ClientContext client, ICompany company, Long id) {
    return jobTrackingLogic.getJobTracking(client, company, id);
  }

  @Transactional
  public JobTracking saveJobTracking(ClientContext client, ICompany company, JobTracking jobTracking) {
    return jobTrackingLogic.saveJobTracking(client, company, jobTracking);
  }

  @Transactional
  public boolean updateJobTrackingStorageState(ClientContext clientCtx, ICompany company, ChangeStorageStateRequest req) {
    return jobTrackingLogic.updateJobTrackingStorageState(clientCtx, company, req);
  }

  @Transactional(readOnly = true)
  public List<JobTrackingModel> findJobTrackingModelsByProject(ClientContext client, ICompany company, JobTrackingSearchParams param) {
    return jobTrackingLogic.findJobTrackingModelsByProject(client, company, param);
  }

  @Transactional
  public List<JobTrackingModel> processModels(ClientContext client, ICompany company, List<JobTrackingModel> models) {
    return jobTrackingLogic.processModels(client, company, models);
  }

  @Transactional
  public JobTrackingModel newJobTrackingModel(ClientContext client, ICompany company, JobTrackingModel model) {
    return jobTrackingLogic.newJobTrackingModel(client, company, model);
  }

  // JOB TRACKING CELL
  @Transactional(readOnly = true)
  public JobTrackingCell getJobTrackingCell(ClientContext client, ICompany company, Long id) {
    return jobTrackingLogic.getJobTrackingCell(client, company, id);
  }
  
  @Transactional(readOnly = true)
  public Collection<SqlMapRecord> searchJobTrackings(ClientContext client, ICompany company, SqlQueryParams params) {
    return jobTrackingLogic.searchJobTrackings(client, company, params);
  }
  
  public SqlMapRecord createOrGetJobTrackingMapRecordById(ClientContext client, ICompany company, Long jobProjectId, Long jobTrackigId) {
    return jobTrackingLogic.createOrGetJobTrackingMapRecordById(client, company, jobProjectId, jobTrackigId);
  }

  @Transactional
  public JobTrackingCell saveJobTrackingCell(ClientContext client, ICompany company, JobTrackingCell jobTrackingStep) {
    return jobTrackingLogic.saveJobTrackingCell(client, company, jobTrackingStep);
  }

  @Transactional(readOnly = true)
  public List<JobTrackingCell> findJobTrackingCellsByJobTracking(ClientContext client, ICompany company, Long jobTrackingId) {
    return jobTrackingLogic.findJobTrackingCellsByJobTracking(client, company, jobTrackingId);
  }

  // JOB TRACKING CELL ISSUE
  @Transactional(readOnly = true)
  public JobTrackingCellIssue getJobTrackingCellIssue(ClientContext client, ICompany company, Long id) {
    return jobTrackingLogic.getJobTrackingCellIssue(client, company, id);
  }

  @Transactional
  public JobTrackingCellIssue saveJobTrackingCellIssue(ClientContext client, ICompany company, JobTrackingCellIssue jobTrackingStepIssue) {
    return jobTrackingLogic.saveJobTrackingCellIssue(client, company, jobTrackingStepIssue);
  }

  @Transactional(readOnly = true)
  public List<JobTrackingCellIssue> findJobTrackingCellIssuesByJobTracking(ClientContext client, ICompany company, Long jobTrackingStepId) {
    return jobTrackingLogic.findJobTrackingCellIssuesByJobTracking(client, company, jobTrackingStepId);
  }
  
  @Transactional
  public boolean deleteJobTrackingCellIssues(ClientContext client, ICompany company, List<Long> ids) {
    return jobTrackingLogic.deleteJobTrackingCellIssues(client, company, ids);
  }
  
  // CLAIM
  @Transactional(readOnly = true)
  public JobTrackingClaim getJobTrackingClaim(ClientContext client, ICompany company, Long id) {
    return claimLogic.getJobTrackingClaim(client, company, id);
  }
  
  @Transactional
  public JobTrackingClaim newJobTrackingClaim(ClientContext client, ICompany company, JobTrackingClaim claim) {
    return claimLogic.newJobTrackingClaim(client, company, claim);
  }

  @Transactional
  public JobTrackingClaim saveJobTrackingClaim(ClientContext client, ICompany company, JobTrackingClaim claim) {
    return claimLogic.saveJobTrackingClaim(client, company, claim);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> searchJobTrackingClaims(ClientContext client, ICompany company, SqlQueryParams params) {
    return claimLogic.searchJobTrackingClaims(client, company, params);
  }
  
  @Transactional
  public boolean deleteJobTrackingClaims(ClientContext client, ICompany company, List<Long> ids) {
    return claimLogic.deleteJobTrackingClaims(client, company, ids);
  }

  @Transactional
  public JobTrackingClaimComment saveClaimComment(ClientContext client, ICompany company, JobTrackingClaimComment comment) {
    return claimLogic.saveClaimComment(client, company, comment);
  }

  @Transactional
  public boolean deleteClaimComment(ClientContext client, ICompany company, Long id) {
    return claimLogic.deleteClaimComment(client, company, id);
  }

  @Transactional(readOnly = true)
  public List<JobTrackingClaimComment> findClaimComments(ClientContext client, ICompany company, Long claimId) {
    return claimLogic.findClaimComments(client, company, claimId);
  }
  
  @Transactional
  public boolean changeClaimStatus(ClientContext client, ICompany company, Long claimId, ClaimStatus newStatus) {
    return claimLogic.changeClaimStatus(client, company, claimId, newStatus);
  }
  
  //Plugin
  @Transactional(readOnly = true)
  public List<JobTrackingPlugin> findJobTrackingPlugins(ClientContext client, ICompany company, Long projectId) {
    return projectLogic.findJobTrackingPlugins(client, company, projectId);
  }
  
  //Column
  @Transactional(readOnly = true)
  public List<JobTrackingColumn> findJobTrackingColumnsByPluginId(ClientContext client, ICompany company, Long pluginId) {
    return projectLogic.findJobTrackingColumnsByPluginId(client, company, pluginId);
  }

  @Transactional(readOnly = true)
  public List<SqlMapRecord> jobTrackingReport(ClientContext client, SqlQueryParams params) {
    return jobTrackingLogic.jobTrackingReport(client, params);
  }
  
  @Transactional(readOnly = true)
  public List<SqlMapRecord> jobTrackingFCLImportReport(ClientContext client, SqlQueryParams params) {
    return jobTrackingLogic.jobTrackingFCLImportReport(client, params);
  }
}