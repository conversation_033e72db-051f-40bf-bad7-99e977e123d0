package cloud.datatp.jobtracking;

import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import org.springframework.context.ApplicationContext;

import cloud.datatp.jobtracking.entity.JobTrackingProject;
import cloud.datatp.jobtracking.model.JobTrackingPermissionHelper;
import groovy.lang.Binding;
import net.datatp.lib.executable.ExecutableContext;
import net.datatp.lib.executable.ExecutableUnit;
import net.datatp.lib.executable.Executor;
import net.datatp.module.account.AccountLogic;
import net.datatp.module.account.entity.Account;
import net.datatp.module.app.AppEnv;
import net.datatp.module.company.entity.Company;
import net.datatp.security.client.DataScope;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.query.SearchFilter;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.security.client.ClientContext;
import net.datatp.util.text.StringUtil;

public class JobTrackingLogicExecutor extends Executor {

  static public class SearchJobTracking extends ExecutableUnit {
    public Collection<SqlMapRecord> execute(ApplicationContext appCtx, ExecutableContext ctx) {
      ClientContext client = ctx.getParam(ClientContext.class);
      ICompany company = ctx.getParam(ICompany.class);

      JobTrackingProjectLogic projectLogic = ctx.getParam(JobTrackingProjectLogic.class);
      JobTrackingLogic jobTrackingLogic = ctx.getParam(JobTrackingLogic.class);
      AccountLogic accountLogic = ctx.getParam("accountLogic");

      SqlQueryParams params = ctx.getParam(SqlQueryParams.class);
      AppEnv appEnv = ctx.getParam(AppEnv.class);
      String scriptDir = appEnv.addonPath("logistics", "groovy");
      String scriptFile = "cloud/datatp/jobtracking/groovy/JobTrackingSql.groovy";

      Long projectId = params.getParams().getLong("jobTrackingProjectId", null);
      JobTrackingProject project = projectLogic.getJobTrackingProject(client, company, projectId);

      Account account = accountLogic.getEditable(client, client.getRemoteUser());
      JobTrackingPermissionHelper permissionHelper = new JobTrackingPermissionHelper(account.getId(), project);
      if (!permissionHelper.hasAllRowPermission() && params.hasParam("withPermission")) {
        params.getParams().add("ownerAccountId", account.getId());
      }
      params.getParams().add("accessColumns", new ArrayList<String>(permissionHelper.getAllowedAccessColumnMap().keySet()));

      //searchJobTracking
      params.addParam("companyId", company.getId());
      List<SqlMapRecord> jobTrackings = jobTrackingLogic.searchDbRecords(client, scriptDir, scriptFile,
        "SearchJobTracking", params);
      if (jobTrackings.size() == 0) return jobTrackings;
      Map<Long, SqlMapRecord> jobTrackingMaps = new LinkedHashMap<>();
      for (SqlMapRecord jobTracking : jobTrackings) {
        Long jobTrackingId = jobTracking.getLong("jobTrackingId");
        jobTrackingMaps.put(jobTrackingId, jobTracking);
      }

      //mergeTrackingCells
      List<Long> jobTrackingIds = new ArrayList<>(jobTrackingMaps.keySet());
      params.addParam("jobTrackingIds", jobTrackingIds);

      JobTrackingSqlResultSetExtractor extractor = new JobTrackingSqlResultSetExtractor(jobTrackingMaps).withRenameColum();
      jobTrackingMaps = jobTrackingLogic.searchDbRecords(client, scriptDir, scriptFile, "SearchJobTrackingCells", params, extractor);
      return jobTrackingMaps.values();
    }
  }

  static public class SearchJobTrackingIssue extends ExecutableUnit {
    public List<SqlMapRecord> execute(ApplicationContext appCtx, ExecutableContext ctx) {
      JobTrackingIssueLogic   logic   = ctx.getParam(JobTrackingIssueLogic.class);
      ClientContext              client  = ctx.getParam(ClientContext.class);
      ICompany                company = ctx.getParam(ICompany.class);
      SqlQueryParams          params  = ctx.getParam(SqlQueryParams.class);

      String scriptDir = logic.appEnv.addonPath("logistics", "groovy");
      String scriptFile = "cloud/datatp/jobtracking/groovy/JobTrackingSql.groovy";

      Account account  = logic.accountLogic.getReadOnly(client, client.getRemoteUser());
      String dataScope = params.getString("dataScope");
      if(DataScope.Owner.toString().equals(dataScope)) {
        params.addParam("accountId", account.getId());
      }
      params.addParam("companyId", company.getId());
      List<SqlMapRecord> recordList = logic.searchDbRecords(client, scriptDir, scriptFile, "SearchJobTrackingIssue", params);
      return recordList;
    }
  }


  //  static public class FindTMSBillIssueGroupByBill extends ExecutableUnit {
  //    public List<SqlMapRecord> execute(ExecutableContext ctx) {
  //      TMSBillTrackingLogic logic = ctx.getParam(TMSBillTrackingLogic.class);
  //      ClientContext          client = ctx.getParam(ClientContext.class);
  //      Company            company = ctx.getParam(Company.class);
  //      SqlQueryParams      params = ctx.getParam(SqlQueryParams.class);
  //
  //      Account user = logic.accountLogic.getReadOnly(client, client.getRemoteUser());
  //      params.addParam("handlerAccountId", user.getId());
  //      params.addParam("companyId", company.getId());
  //      String scriptDir = logic.appEnv.addonPath("logistics", "groovy");
  //      String scriptFile = "cloud/datatp/tms/groovy/TMSSql.groovy";
  //      List<SqlMapRecord> issues = logic.searchDbRecords(client, scriptDir, scriptFile, "FindTMSBillTrackingIssue", params);
  //
  //      RecordGroupByMap<Long, SqlMapRecord> commentGroupByMap = new RecordGroupByMap<>(issues, issue -> issue.getLong("tmsBillId"));
  //      List<SqlMapRecord> results = new ArrayList<>();
  //      for(Map.Entry<Long, List<SqlMapRecord>> entry : commentGroupByMap.getAll().entrySet()) {
  //        List<SqlMapRecord> groupIssues    =  entry.getValue();
  //        List<SqlMapRecord> groupIssueNews =
  //            groupIssues
  //            .stream()
  //            .filter(sel -> TMSBillTrackingIssueStatus.NEW.toString().equals(sel.getString("status")))
  //            .toList();
  //
  //        SqlMapRecord result   = groupIssues.get(0);
  //        result.add("new", groupIssueNews.size());
  //        result.add("totalIssue", groupIssues.size());
  //        results.add(result);
  //      }
  //      return results;
  //    }
  //  }

  public JobTrackingLogicExecutor() {
    register(new SearchJobTracking());
    register(new SearchJobTrackingIssue());
  }
}