package cloud.datatp.jobtracking;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import cloud.datatp.jobtracking.entity.JobTracking;
import cloud.datatp.jobtracking.entity.JobTrackingCell;
import cloud.datatp.jobtracking.entity.JobTrackingCellIssue;
import cloud.datatp.jobtracking.entity.JobTrackingCellIssue.IssueType;
import cloud.datatp.jobtracking.entity.JobTrackingColumn;
import cloud.datatp.jobtracking.entity.JobTrackingColumn.CellDataType;
import cloud.datatp.jobtracking.entity.JobTrackingColumn.StepStatus;
import cloud.datatp.jobtracking.entity.JobTrackingPlugin;
import cloud.datatp.jobtracking.entity.JobTrackingProject;
import cloud.datatp.jobtracking.model.JobTrackingModel;
import cloud.datatp.jobtracking.model.JobTrackingPermissionHelper;
import cloud.datatp.jobtracking.model.JobTrackingSearchParams;
import cloud.datatp.jobtracking.repository.JobTrackingCellIssueRepository;
import cloud.datatp.jobtracking.repository.JobTrackingCellRepository;
import cloud.datatp.jobtracking.repository.JobTrackingColumnRepository;
import cloud.datatp.jobtracking.repository.JobTrackingRepository;
import lombok.Getter;
import net.datatp.lib.executable.ExecutableContext;
import net.datatp.module.account.AccountLogic;
import net.datatp.module.account.entity.Account;
import net.datatp.module.app.AppEnv;
import net.datatp.security.client.ClientContext;
import net.datatp.module.company.entity.Company;
import net.datatp.module.data.db.DAOService;
import net.datatp.module.data.db.SqlMapRecord;
import net.datatp.module.data.db.entity.ChangeStorageStateRequest;
import net.datatp.module.data.db.entity.EditState;
import net.datatp.module.data.db.entity.ICompany;
import net.datatp.module.data.db.entity.StorageState;
import net.datatp.module.data.db.query.SqlQueryParams;
import net.datatp.module.service.ExecutableUnitManager;
import net.datatp.util.ds.Collections;
import net.datatp.util.ds.MapObject;
import net.datatp.util.ds.Objects;
import net.datatp.util.error.ErrorType;
import net.datatp.util.error.RuntimeError;

@Component @Getter
public class JobTrackingLogic extends DAOService {
  @Autowired
  private JobTrackingRepository repo;

  @Autowired
  private JobTrackingCellRepository cellRepo;

  @Autowired
  private JobTrackingColumnRepository columnRepo;

  @Autowired
  private JobTrackingCellIssueRepository cellIssueRepo;

  @Autowired
  private JobTrackingProjectLogic projectLogic;

  @Autowired
  private AccountLogic accountLogic;

  @Autowired
  ExecutableUnitManager executableUnitManager;

  @Autowired
  private AppEnv appEnv;

  @Value("${app.env:#{null}}")
  private String env;

  public JobTracking getJobTracking(ClientContext client, ICompany company, Long id) {
    return repo.getById(company.getId(), id);
  }

  public JobTracking saveJobTracking(ClientContext client, ICompany company, JobTracking jobTracking) {
    jobTracking.set(client, company);
    return repo.save(jobTracking);
  }

  public boolean updateJobTrackingStorageState(ClientContext clientCtx, ICompany company, ChangeStorageStateRequest req) {
    repo.updateStorageState(company.getId(), req.getNewStorageState(), req.getEntityIds());
    return true;
  }

  public List<JobTracking> findJobTrackingByIds(ClientContext client, ICompany company, List<Long> ids) {
    return repo.findByIds(ids);
  }

  public List<JobTracking> findByProject(ClientContext client, ICompany company, Long projectId) {
    return repo.findByProjectId(company.getId(), projectId);
  }

  public List<JobTracking> findByPermission(ClientContext client, ICompany company, Long projectId, boolean withPermission) {
    if(projectId == null) return new ArrayList<>();
    List<JobTracking> jobTrackings;
    JobTrackingProject project = projectLogic.getJobTrackingProject(client, company, projectId);
    Account account =accountLogic.getEditable(client, client.getRemoteUser());
    JobTrackingPermissionHelper permissionHelper = new JobTrackingPermissionHelper(account.getId(), project);
    if (!withPermission || permissionHelper.hasAllRowPermission()) {
      jobTrackings = repo.findByProjectId(company.getId(), projectId);
    } else {
      jobTrackings = repo.findMine(company.getId(), projectId, account.getId());
    }
    return jobTrackings;
  }

  public Collection<SqlMapRecord> searchJobTrackings(ClientContext client, ICompany company, SqlQueryParams params) {
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    ExecutableContext ctx =
      new ExecutableContext()
        .withScriptEnv(scriptDir, JobTrackingLogicExecutor.class, JobTrackingLogicExecutor.SearchJobTracking.class)
        .withParam(this).withParam(client).withParam(company).withParam(params)
        .withParam(appEnv).withParam(projectLogic).withParam("accountLogic", accountLogic);
    return (Collection<SqlMapRecord>) executableUnitManager.execute(ctx);
  }

  public SqlMapRecord createOrGetJobTrackingMapRecordById(ClientContext client, ICompany company, Long jobProjectId, Long jobTrackingId) {
    JobTrackingProject project = projectLogic.getJobTrackingProject(client, company, jobProjectId);
    Account account =accountLogic.getEditable(client, client.getRemoteUser());
    SqlMapRecord jobTrackingMapRecord = new SqlMapRecord();
    JobTracking jobTracking = getJobTracking(client, company, jobTrackingId);
    
    if(Objects.isNull(jobTracking)) {
      jobTracking = new JobTracking();
      jobTracking.setJobTrackingProjectId(jobProjectId);
      jobTracking.setOwnerAccountId(account.getId());
      jobTracking.setOwnerAccountFullName(account.getFullName());
      jobTracking = saveJobTracking(client, company, jobTracking);

      jobTrackingMapRecord.add("jobTrackingProjectId", jobTracking.getJobTrackingProjectId());
      jobTrackingMapRecord.add("customPrintDocumentDate", jobTracking.getCustomPrintDocumentDate());
      jobTrackingMapRecord.add("ownerAccountId", jobTracking.getOwnerAccountId());
      jobTrackingMapRecord.add("ownerAccountFullName", jobTracking.getOwnerAccountFullName());
      jobTrackingMapRecord.add("rowHeight", jobTracking.getRowHeight());
      jobTrackingMapRecord.add("stepDoneCount", jobTracking.getStepDoneCount());
      jobTrackingMapRecord.add("lastStepName", jobTracking.getLastStepName());
      jobTrackingMapRecord.add("jobTrackingId", jobTracking.getId());
    } else {
      SqlQueryParams params = new SqlQueryParams();
      params.addParam("jobTrackingProjectId", jobTracking.getJobTrackingProjectId());
      params.addParam("jobTrackingId", jobTrackingId);
      Collection<SqlMapRecord> sqlRecords = searchJobTrackings(client, company, params);
      List<SqlMapRecord> sqlMapRecordLists = new ArrayList<>(sqlRecords);
      if(sqlMapRecordLists.size() == 0 || sqlMapRecordLists.size() > 1) throw RuntimeError.UnknownError("Must have only 1 JobTracking with JobTrackingID = {0}", jobTrackingId);
      jobTrackingMapRecord = sqlMapRecordLists.get(0);
    }

    JobTrackingPermissionHelper permissionHelper = new JobTrackingPermissionHelper(account.getId(), project);
    List<JobTrackingPlugin> plugins = project.getPlugins();
    if (Collections.isEmpty(plugins)) return jobTrackingMapRecord;
    for (JobTrackingPlugin plugin : plugins) {
      List<JobTrackingColumn> columns = plugin.getColumns();
      if (Collections.isEmpty(columns)) continue;
      for (JobTrackingColumn column : columns) {
        if(permissionHelper.canAccessColumn(column.getName()) && !jobTrackingMapRecord.containsKey(column.getName())) {
          JobTrackingCell cell = new JobTrackingCell().withJobTracking(jobTracking).withJobColumn(column);
          jobTrackingMapRecord.put(column.getName(), cell.getValue(column.getDataType() ,column.getDataInputType()));
        }

      }
    }
    return jobTrackingMapRecord;
  }

  public JobTracking createJobTracking(ClientContext client, ICompany company, Long jobTrackingProjectId) {
    Account account = accountLogic.getEditable(client, client.getRemoteUser());
    JobTracking  jobTracking          = new JobTracking();
    jobTracking.setJobTrackingProjectId(jobTrackingProjectId);
    jobTracking.setOwnerAccountId(account.getId());
    jobTracking.setOwnerAccountFullName(account.getFullName());
    return saveJobTracking(client, company, jobTracking);
  }

  public SqlMapRecord createOrUpdateJobTracking(ClientContext client, ICompany company, Long jobTrackingProjectId, MapObject jobTrackingData) {
    JobTrackingProject project = projectLogic.getJobTrackingProject(client, company, jobTrackingProjectId);
    return createOrUpdateJobTracking(client, company, project, jobTrackingData);
  }

  public MapObject createJobTracking(ClientContext client, ICompany company, String jobTrackingProjectCode, MapObject jobTrackingData) {
    JobTrackingProject project = projectLogic.getJobTrackingProject(client, company, jobTrackingProjectCode);
    return createOrUpdateJobTracking(client, company, project, jobTrackingData);
  }

  public SqlMapRecord createOrUpdateJobTracking(ClientContext client, ICompany company, JobTrackingProject project, MapObject jobTrackingData) {
    if(project == null) throw RuntimeError.UnknownError("Project not null");
    Account account =accountLogic.getEditable(client, client.getRemoteUser());
    SqlMapRecord jobTrackingMapRecord = new SqlMapRecord();
    Long jobTrackingId = jobTrackingData.getLong("jobTrackingId", null);
    JobTracking  jobTracking = getJobTracking(client, company, jobTrackingId);
    if (Objects.isNull(jobTracking)) jobTracking = createJobTracking(client, company, project.getId());
    
    jobTrackingMapRecord.add("jobTrackingProjectId"   , jobTracking.getJobTrackingProjectId());
    jobTrackingMapRecord.add("customPrintDocumentDate", jobTracking.getCustomPrintDocumentDate());
    jobTrackingMapRecord.add("ownerAccountId"         , jobTracking.getOwnerAccountId());
    jobTrackingMapRecord.add("ownerAccountFullName"   , jobTracking.getOwnerAccountFullName());
    jobTrackingMapRecord.add("rowHeight"              , jobTracking.getRowHeight());
    jobTrackingMapRecord.add("stepDoneCount"          , jobTracking.getStepDoneCount());
    jobTrackingMapRecord.add("lastStepName"           , jobTracking.getLastStepName());
    jobTrackingMapRecord.add("jobTrackingId"          , jobTracking.getId());

    Long jobTrackingProjectId = project.getId();
    JobTrackingPermissionHelper permissionHelper = new JobTrackingPermissionHelper(account.getId(), project);
    List<JobTrackingPlugin> plugins = project.getPlugins();
    if (Collections.isEmpty(plugins)) return jobTrackingMapRecord;
    for (JobTrackingPlugin plugin : plugins) {
      List<JobTrackingColumn> columns = plugin.getColumns();
      if (Collections.isEmpty(columns)) continue;
      for (JobTrackingColumn column : columns) {
        if(permissionHelper.canAccessColumn(column.getName()) && !jobTrackingMapRecord.containsKey(column.getName())) {
          JobTrackingCell cell = getJobTrackingCellByJobTrackingIdAndColumnId(client, company, jobTrackingId, column.getId());
          if (Objects.isNull(cell)) cell = new JobTrackingCell().withJobTracking(jobTracking).withJobColumn(column);
          if(column.isStatus()) {
            jobTrackingMapRecord.put(column.getName(), cell.getValue(column.getDataType() ,column.getDataInputType()));
          } else if(jobTrackingData != null && jobTrackingData.has(column.getName())) {
            cell.setJobTrackingProjectId(jobTrackingProjectId);
            cell.setJobTrackingId(jobTracking.getId());
            cell.setJobTrackingColumnId(column.getId());
            cell.updateValue(column.getDataInputType(), jobTrackingData, column.getName());
            cell = saveJobTrackingCell(client, company, cell);
          }
        }

      }
    }

    jobTrackingMapRecord.add("jobTrackingProject", project);
    return jobTrackingMapRecord;
  }

  public SqlMapRecord getJobTrackingMapRecordById(ClientContext client, ICompany company, Long jobTrackingId) {
    JobTracking        jobTracking          = getJobTracking(client, company, jobTrackingId);
    if(jobTracking == null) return null;
    Account            account              = accountLogic.getEditable(client, client.getRemoteUser());
    JobTrackingProject project              = projectLogic.getJobTrackingProject(client, company, jobTracking.getJobTrackingProjectId());
    SqlQueryParams     params               = new SqlQueryParams();
    params.addParam("jobTrackingProjectId", jobTracking.getJobTrackingProjectId());
    params.addParam("jobTrackingId", jobTrackingId);

    Collection<SqlMapRecord> sqlRecords  = searchJobTrackings(client, company, params);
    List<SqlMapRecord> sqlMapRecordLists = new ArrayList<>(sqlRecords);
    if(sqlMapRecordLists.size() > 1) throw RuntimeError.UnknownError("Must have only 1 JobTracking with JobTrackingID = {0}", jobTrackingId);
    if(sqlMapRecordLists.isEmpty()) return null;
    SqlMapRecord       jobTrackingMapRecord = sqlMapRecordLists.get(0);

    JobTrackingPermissionHelper permissionHelper = new JobTrackingPermissionHelper(account.getId(), project);
    List<JobTrackingPlugin> plugins = project.getPlugins();
    if (Collections.isEmpty(plugins)) return jobTrackingMapRecord;
    for (JobTrackingPlugin plugin : plugins) {
      List<JobTrackingColumn> columns = plugin.getColumns();
      if (Collections.isEmpty(columns)) continue;
      for (JobTrackingColumn column : columns) {
        if(permissionHelper.canAccessColumn(column.getName()) && !jobTrackingMapRecord.containsKey(column.getName())) {
          JobTrackingCell cell = new JobTrackingCell().withJobTracking(jobTracking).withJobColumn(column);
          jobTrackingMapRecord.put(column.getName(), cell.getValue(column.getDataType() ,column.getDataInputType()));
        }

      }
    }
    jobTrackingMapRecord.add("jobTrackingProject", project);
    return jobTrackingMapRecord;
  }

  public List<JobTrackingModel> findJobTrackingModelsByProject(ClientContext client, ICompany company, JobTrackingSearchParams param) {
    Long projectId = param.getProjectId();
    StorageState storageState = param.getStorageState();
    List<JobTrackingModel> models = new ArrayList<>();
    JobTrackingProject project = projectLogic.getJobTrackingProject(client, company, projectId);
    Objects.assertNotNull(project, "JobTrackingProject is not found by id = {}", projectId);

    Account account;
    if (Objects.nonNull(param.getPermissionAccountId())) account = accountLogic.getAccountById(client, param.getPermissionAccountId());
    else account = accountLogic.getEditable(client, client.getRemoteUser());

    JobTrackingPermissionHelper permissionHelper = new JobTrackingPermissionHelper(account.getId(), project);


    List<JobTrackingPlugin> plugins = project.getPlugins();

    List<JobTracking> jobTrackings;
    if (!param.isWithPermission() || permissionHelper.hasAllRowPermission()) {
      jobTrackings = repo.findByProjectId(company.getId(), projectId);
    } else {
      jobTrackings = repo.findMine(company.getId(), projectId, account.getId());
    }
    Set<Long> ids = new HashSet<>();
    for(JobTracking sel : jobTrackings) {
      ids.add(sel.getId());
    }

    List<JobTrackingCell> jobTrackingCells = cellRepo.findByJobTrackingIds(company.getId(), new ArrayList<>(ids));
    if (Collections.isEmpty(jobTrackingCells)) return models;
    final Map<Long, List<JobTrackingCell>> mapCellByJobTracking = jobTrackingCells.stream()
        .collect(Collectors.groupingBy(JobTrackingCell::getJobTrackingId));

    for (JobTracking jobTracking : jobTrackings) {
      if (!jobTracking.getStorageState().equals(storageState)) continue;
      JobTrackingModel model = new JobTrackingModel().withJobTracking(jobTracking);
      if(!project.isEditable() && !jobTracking.getOwnerAccountId().equals(account.getId())) model.add("readOnly", true);

      List<JobTrackingCell> cells = mapCellByJobTracking.get(jobTracking.getId());
      if (Collections.isEmpty(cells)) continue;
      final Map<Long, List<JobTrackingCell>> mapCellByColumn = cells.stream()
          .collect(Collectors.groupingBy(JobTrackingCell::getJobTrackingColumnId));

      if (Collections.isNotEmpty(plugins)) {
        for (JobTrackingPlugin plugin : plugins) {
          List<JobTrackingColumn> columns = plugin.getColumns();
          if (Collections.isNotEmpty(columns)) {
            for (JobTrackingColumn column : columns) {
              List<JobTrackingCell> cellList = mapCellByColumn.get(column.getId());
              if (Collections.isNotEmpty(cellList)) {
                if (cellList.size() > 1) {
                  throw new RuntimeError(ErrorType.IllegalState,
                      "Must have only 1 JobTrackingCell with JobTrackingID = {0}, JobTrackingColumn = {1}",
                      jobTracking.getId(), column.getId());
                }
                JobTrackingCell cell = cellList.get(0);
                model.put(column.getName(), cell.getValue(column.getDataType() ,column.getDataInputType()));
              } else if(column.isStatus()) {
                JobTrackingCell cell = new JobTrackingCell().withJobTracking(jobTracking).withJobColumn(column);
                model.put(column.getName(), cell.getValue(column.getDataType() ,column.getDataInputType()));
              }
            }
          }
        }
      }

      models.add(model);
    }
    if(Objects.nonNull(param.getMaxReturn())) {
      models = models.stream().limit(param.getMaxReturn()).collect(Collectors.toList());
    }
    return models;
  }

  public JobTrackingModel newJobTrackingModel(ClientContext client, ICompany company, JobTrackingModel model) {
    if (Objects.isNull(model.getJobTrackingProjectId()))
      throw new RuntimeError(ErrorType.IllegalArgument, "JobTrackingProject must not be null!");
    JobTrackingProject project = projectLogic.getJobTrackingProject(client, company, model.getJobTrackingProjectId());
    Objects.assertNotNull(project, "JobTrackingProject is not found by id = {}", model.getJobTrackingProjectId());

    Account owner =accountLogic.getEditable(client, client.getRemoteUser());
    model.withOwnerAccountId(owner.getId());
    model.withOwnerAccountFullName(owner.getFullName());
    model.withEditState(EditState.NEW);

    List<JobTrackingPlugin> plugins = project.getPlugins();
    if (Collections.isNotEmpty(plugins)) {
      for (JobTrackingPlugin plugin : plugins) {
        List<JobTrackingColumn> columns = plugin.getColumns();
        if (Collections.isNotEmpty(columns)) {
          for (JobTrackingColumn column : columns) {
            JobTrackingCell cell = new JobTrackingCell();
            if(CellDataType.Status.equals(column.getDataType())) cell.setStatus(StepStatus.NA);
            model.put(column.getName(), cell.getValue(column.getDataType(), column.getDataInputType()));
          }
        }
      }
    }

    return model;
  }

  public List<JobTrackingModel> processModels(ClientContext client, ICompany company, List<JobTrackingModel> models) {
    List<JobTrackingModel> modifiedRecords = models.stream().filter(sel -> EditState.MODIFIED.equals(sel.getEditState())).collect(Collectors.toList());
    List<JobTrackingModel> deleteRecords = models.stream().filter(sel -> EditState.DELETED.equals(sel.getEditState())).collect(Collectors.toList());
    processDeleteModels(client, company, deleteRecords);
    return processSaveModels(client, company, modifiedRecords);
  }

  private void processDeleteModels(ClientContext client, ICompany company, List<JobTrackingModel> models) {
    if (Collections.isEmpty(models)) return;
    for (JobTrackingModel model : models) {
      deleteJobTracking(client, company, model.getJobTrackingId());
    }
  }

  public JobTrackingCell getJobTrackingCellByJobTrackingIdAndColumnId(ClientContext client, ICompany company, Long jobTrackingId, Long colId ) {
    return cellRepo.getByJobTrackingIdAndColumnId(company.getId(), jobTrackingId, colId);
  }

  private List<JobTrackingModel> processSaveModels(ClientContext client, ICompany company, List<JobTrackingModel> models) {
    if (Collections.isEmpty(models)) return models;
    boolean allMatch = models.stream().allMatch(sel -> sel.getEditState().equals(EditState.MODIFIED));
    Objects.assertTrue(allMatch, "All record must be have state = MODIFIED");
    for (JobTrackingModel model : models) {
      processUpdateModel(client, company, model);
    }
    return models;
  }

  public JobTrackingModel processUpdateModel(ClientContext client, ICompany company, JobTrackingModel model) {
    Long projectId = model.getJobTrackingProjectId();
    List<JobTrackingColumn> allColumns = columnRepo.findByJobTrackingProjectId(company.getId(), model.getJobTrackingProjectId());
    JobTracking jobTracking = new JobTracking();
    if (Objects.nonNull(model.getJobTrackingId())) {
      jobTracking = getJobTracking(client, company, model.getJobTrackingId());
      Objects.assertNotNull(jobTracking, "JobTracking is not found by id = {}", model.getJobTrackingId());
    } else if (Objects.isNull(model.getOwnerAccountId())) {
      Account owner = accountLogic.getAccountById(client, client.getAccountId());
      model.withOwnerAccountId(owner.getId());
      model.withOwnerAccountFullName(owner.getFullName());
    }
    jobTracking = model.mapJobTracking(jobTracking);
    jobTracking = saveJobTracking(client, company, jobTracking);
    model.withJobTracking(jobTracking);

    for(JobTrackingColumn col : allColumns) {
      String name = col.getName();
      if(col.isStatus()) {
        JobTrackingCell cell = Objects.ensureNotNull(model.getJobTrackingCell(name), JobTrackingCell::new);
        if(cell.isNew()) {
          cell.setJobTrackingProjectId(projectId);
          cell.setJobTrackingId(jobTracking.getId());
          cell.setJobTrackingColumnId(col.getId());
        } else {
          JobTrackingCell cellDb = getJobTrackingCell(client, company, cell.getId());
          cellDb.setInternalIssueCount(cell.getInternalIssueCount());
          cellDb.setExternalIssueCount(cell.getExternalIssueCount());
          cellDb.setEndTime(cell.getEndTime());
          cellDb.setStartTime(cell.getStartTime());
          cellDb.setStatus(cell.getStatus());
          cell = cellDb;
        }
        if(EditState.MODIFIED.equals(cell.getEditState()) || EditState.NEW.equals(cell.getEditState())) {
          cell = saveJobTrackingCell(client, company, cell);
        }
      } else {
        JobTrackingCell cell = cellRepo.getByJobTrackingIdAndColumnId(company.getId(), model.getJobTrackingId(), col.getId());
        if(cell == null) {
          cell = new JobTrackingCell();
          cell.setJobTrackingProjectId(projectId);
          cell.setJobTrackingId(jobTracking.getId());
          cell.setJobTrackingColumnId(col.getId());
        }
        cell.updateValue(col.getDataInputType(), model, name);
        cell = saveJobTrackingCell(client, company, cell);
      }
    }
    return model;
  }

  public void deleteJobTracking(ClientContext client, ICompany company, Long id) {
    JobTracking jobTracking = getJobTracking(client, company, id);
    if (Objects.isNull(jobTracking)) return;

    List<JobTrackingCell> cells = cellRepo.findByJobTracking(company.getId(), jobTracking.getId());
    if (Collections.isNotEmpty(cells)) {
      for (JobTrackingCell cell : cells) {
        List<JobTrackingCellIssue> cellIssues = cellIssueRepo.findByJobTrackingCell(company.getId(), cell.getId());
        if (Collections.isNotEmpty(cellIssues)) {
          cellIssueRepo.deleteAll(cellIssues);
        }
      }
      cellRepo.deleteAll(cells);
    }

    repo.delete(jobTracking);
  }

//  CELL

  public JobTrackingCell getJobTrackingCell(ClientContext client, ICompany company, Long id) {
    return cellRepo.getById(company.getId(), id);
  }

  public JobTrackingCell saveJobTrackingCell(ClientContext client, ICompany company, JobTrackingCell jobTrackingCell) {
    jobTrackingCell.set(client, company);
    return cellRepo.save(jobTrackingCell);
  }

  public List<JobTrackingCell> saveJobTrackingCells(ClientContext client, ICompany company, List<JobTrackingCell> jobTrackingCells) {
    return cellRepo.saveAll(jobTrackingCells);
  }

  public List<JobTrackingCell> findJobTrackingCellsByJobTracking(ClientContext client, ICompany company, Long jobTrackingId) {
    return cellRepo.findByJobTracking(company.getId(), jobTrackingId);
  }

  // CELL ISSUE

  public JobTrackingCellIssue getJobTrackingCellIssue(ClientContext client, ICompany company, Long id) {
    return cellIssueRepo.getById(company.getId(), id);
  }

  public JobTrackingCellIssue saveJobTrackingCellIssue(ClientContext client, ICompany company, JobTrackingCellIssue jobTrackingCellIssue) {
    jobTrackingCellIssue.set(client, company);
    jobTrackingCellIssue = cellIssueRepo.save(jobTrackingCellIssue);

    JobTrackingCell jobTrackingCell = getJobTrackingCell(client, company, jobTrackingCellIssue.getJobTrackingCellId());
    Objects.assertNotNull(jobTrackingCell, "JobTrackingCell is not found by id = {}", jobTrackingCellIssue.getJobTrackingCellId());

    List<JobTrackingCellIssue> cellIssues = findJobTrackingCellIssuesByJobTracking(client, company, jobTrackingCell.getId());
    if (Collections.isNotEmpty(cellIssues)) {
      int internalIssueCount = 0;
      int externalIssueCount = 0;
      for (JobTrackingCellIssue issue : cellIssues) {
        if (IssueType.Internal.equals(issue.getIssueType())) internalIssueCount += 1;
        else externalIssueCount += 1;
      }
      jobTrackingCell.setInternalIssueCount(internalIssueCount);
      jobTrackingCell.setExternalIssueCount(externalIssueCount);
      saveJobTrackingCell(client, company, jobTrackingCell);
    }
    return jobTrackingCellIssue;
  }

  public List<JobTrackingCellIssue> findJobTrackingCellIssuesByJobTracking(ClientContext client, ICompany company, Long jobTrackingCellId) {
    return cellIssueRepo.findByJobTrackingCell(company.getId(), jobTrackingCellId);
  }

  public boolean deleteJobTrackingCellIssues(ClientContext client, ICompany company, List<Long> ids) {
    if (Collections.isEmpty(ids)) return false;
    JobTrackingCellIssue cellIssue = cellIssueRepo.getById(company.getId(), ids.get(0));
    Objects.assertNotNull(cellIssue, "JobTrackingCellIssue is not found by id = {}", ids.get(0));
    JobTrackingCell jobTrackingCell = getJobTrackingCell(client, company, cellIssue.getJobTrackingCellId());
    Objects.assertNotNull(jobTrackingCell, "JobTrackingCell is not found by id = {}", cellIssue.getJobTrackingCellId());

    for (Long id : ids) {
      JobTrackingCellIssue issue = cellIssueRepo.getById(company.getId(), id);
      Objects.assertNotNull(issue, "JobTrackingCellIssue is not found by id = {}", id);
      cellIssueRepo.delete(issue);
    }

    List<JobTrackingCellIssue> cellIssues = findJobTrackingCellIssuesByJobTracking(client, company, jobTrackingCell.getId());
    if (Collections.isNotEmpty(cellIssues)) {
      int internalIssueCount = 0;
      int externalIssueCount = 0;
      for (JobTrackingCellIssue issue : cellIssues) {
        if (IssueType.Internal.equals(issue.getIssueType())) internalIssueCount += 1;
        else externalIssueCount += 1;
      }
      jobTrackingCell.setInternalIssueCount(internalIssueCount);
      jobTrackingCell.setExternalIssueCount(externalIssueCount);
      saveJobTrackingCell(client, company, jobTrackingCell);
    }
    return true;
  }
  
  public List<SqlMapRecord> jobTrackingReport(ClientContext client, SqlQueryParams params) {
    Long projectId = params.getParams().getLong("jobTrackingProjectId", null);
    JobTrackingProject project = projectLogic.getJobTrackingProject(client, client.getCompany(), projectId);

    JobTrackingPermissionHelper permissionHelper = new JobTrackingPermissionHelper(client.getAccountId(), project);
    if (!permissionHelper.hasAllRowPermission() && params.hasParam("withPermission")) {
      params.getParams().add("ownerAccountId", client.getAccountId());
    }
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/jobtracking/groovy/JobTrackingSql.groovy";
    return searchDbRecords(client, scriptDir, scriptFile, "JobTrackingReport", params);
  }
  
  public List<SqlMapRecord> jobTrackingFCLImportReport(ClientContext client, SqlQueryParams params) {
    Long projectId = params.getParams().getLong("jobTrackingProjectId", null);
    JobTrackingProject project = projectLogic.getJobTrackingProject(client, client.getCompany(), projectId);
    
    JobTrackingPermissionHelper permissionHelper = new JobTrackingPermissionHelper(client.getAccountId(), project);
    if (!permissionHelper.hasAllRowPermission() && params.hasParam("withPermission")) {
      params.getParams().add("ownerAccountId", client.getAccountId());
    }
    String scriptDir = appEnv.addonPath("logistics", "groovy");
    String scriptFile = "cloud/datatp/jobtracking/groovy/JobTrackingSql.groovy";
    return searchDbRecords(client, scriptDir, scriptFile, "JobTrackingFCLImportReport", params);
  }
}