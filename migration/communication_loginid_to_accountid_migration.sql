-- Migration script: <PERSON><PERSON><PERSON><PERSON> đổi từ loginId sang accountId trong communication module
-- Thực hiện: 2025-08-05
-- Mô tả: Thay thế các trường loginId (String) bằng accountId (Long) trong các bảng communication

-- =====================================================
-- BƯỚC 1: Thêm các cột mới với accountId
-- =====================================================

-- Thêm cột sender_account_id vào bảng message_message
ALTER TABLE message_message 
ADD COLUMN sender_account_id BIGINT;

-- Thêm cột sender_account_id và recipient_account_id vào bảng message_recipient_message  
ALTER TABLE message_recipient_message 
ADD COLUMN sender_account_id BIGINT,
ADD COLUMN recipient_account_id BIGINT;

-- Thêm cột recipient_account_id vào bảng message_target_recipient
ALTER TABLE message_target_recipient 
ADD COLUMN recipient_account_id BIGINT;

-- =====================================================
-- BƯỚC 2: Migrate dữ liệu từ loginId sang accountId
-- =====================================================

-- Cập nhật sender_account_id trong message_message
UPDATE message_message mm
SET sender_account_id = (
    SELECT a.id 
    FROM account_account a 
    WHERE LOWER(a.login_id) = LOWER(mm.sender_login_id)
    AND a.storage_state = 'ACTIVE'
)
WHERE mm.sender_login_id IS NOT NULL;

-- Cập nhật sender_account_id và recipient_account_id trong message_recipient_message
UPDATE message_recipient_message mrm
SET sender_account_id = (
    SELECT a.id 
    FROM account_account a 
    WHERE LOWER(a.login_id) = LOWER(mrm.sender_id)
    AND a.storage_state = 'ACTIVE'
),
recipient_account_id = (
    SELECT a.id 
    FROM account_account a 
    WHERE LOWER(a.login_id) = LOWER(mrm.recipient_id)
    AND a.storage_state = 'ACTIVE'
)
WHERE mrm.sender_id IS NOT NULL AND mrm.recipient_id IS NOT NULL;

-- Cập nhật recipient_account_id trong message_target_recipient
UPDATE message_target_recipient mtr
SET recipient_account_id = (
    SELECT a.id 
    FROM account_account a 
    WHERE LOWER(a.login_id) = LOWER(mtr.recipient_id)
    AND a.storage_state = 'ACTIVE'
)
WHERE mtr.recipient_id IS NOT NULL 
AND mtr.deliver_type = 'Private';

-- =====================================================
-- BƯỚC 3: Kiểm tra dữ liệu sau migration
-- =====================================================

-- Kiểm tra số lượng records không thể migrate (không tìm thấy accountId)
SELECT 'message_message' as table_name, COUNT(*) as unmigrated_count
FROM message_message 
WHERE sender_login_id IS NOT NULL AND sender_account_id IS NULL

UNION ALL

SELECT 'message_recipient_message' as table_name, COUNT(*) as unmigrated_count
FROM message_recipient_message 
WHERE (sender_id IS NOT NULL AND sender_account_id IS NULL) 
   OR (recipient_id IS NOT NULL AND recipient_account_id IS NULL)

UNION ALL

SELECT 'message_target_recipient' as table_name, COUNT(*) as unmigrated_count
FROM message_target_recipient 
WHERE recipient_id IS NOT NULL AND recipient_account_id IS NULL AND deliver_type = 'Private';

-- =====================================================
-- BƯỚC 4: Thêm constraints và indexes cho các cột mới
-- =====================================================

-- Thêm NOT NULL constraint cho sender_account_id trong message_message
ALTER TABLE message_message 
ALTER COLUMN sender_account_id SET NOT NULL;

-- Thêm NOT NULL constraint cho message_recipient_message
ALTER TABLE message_recipient_message 
ALTER COLUMN sender_account_id SET NOT NULL,
ALTER COLUMN recipient_account_id SET NOT NULL;

-- Thêm NOT NULL constraint cho message_target_recipient (chỉ với Private type)
-- Note: Không thể thêm NOT NULL vì có các deliver_type khác như Channel, Email

-- Thêm indexes cho performance
CREATE INDEX idx_message_message_sender_account_id ON message_message(sender_account_id);
CREATE INDEX idx_message_recipient_message_sender_account_id ON message_recipient_message(sender_account_id);
CREATE INDEX idx_message_recipient_message_recipient_account_id ON message_recipient_message(recipient_account_id);
CREATE INDEX idx_message_target_recipient_recipient_account_id ON message_target_recipient(recipient_account_id);

-- Thêm foreign key constraints
ALTER TABLE message_message 
ADD CONSTRAINT fk_message_message_sender_account 
FOREIGN KEY (sender_account_id) REFERENCES account_account(id);

ALTER TABLE message_recipient_message 
ADD CONSTRAINT fk_message_recipient_message_sender_account 
FOREIGN KEY (sender_account_id) REFERENCES account_account(id),
ADD CONSTRAINT fk_message_recipient_message_recipient_account 
FOREIGN KEY (recipient_account_id) REFERENCES account_account(id);

ALTER TABLE message_target_recipient 
ADD CONSTRAINT fk_message_target_recipient_recipient_account 
FOREIGN KEY (recipient_account_id) REFERENCES account_account(id);

-- =====================================================
-- BƯỚC 5: Cập nhật communication_account table
-- =====================================================

-- communication_account đã có sẵn account_id, chỉ cần đảm bảo dữ liệu đồng bộ
UPDATE communication_account ca
SET account_id = (
    SELECT a.id 
    FROM account_account a 
    WHERE LOWER(a.login_id) = LOWER(ca.login_id)
    AND a.storage_state = 'ACTIVE'
)
WHERE ca.login_id IS NOT NULL AND ca.account_id IS NULL;

-- Thêm NOT NULL constraint cho account_id
ALTER TABLE communication_account 
ALTER COLUMN account_id SET NOT NULL;

-- Thêm unique constraint cho account_id
ALTER TABLE communication_account 
ADD CONSTRAINT communication_account_account_id_unique UNIQUE (account_id);

-- =====================================================
-- BƯỚC 6: Drop các cột cũ (thực hiện sau khi test)
-- =====================================================

-- CẢNH BÁO: Chỉ thực hiện sau khi đã test kỹ và backup dữ liệu
-- ALTER TABLE message_message DROP COLUMN sender_login_id;
-- ALTER TABLE message_recipient_message DROP COLUMN sender_id, DROP COLUMN recipient_id;
-- ALTER TABLE message_target_recipient DROP COLUMN recipient_id;
-- ALTER TABLE communication_account DROP COLUMN login_id;

-- Drop các constraint và index cũ
-- ALTER TABLE communication_account DROP CONSTRAINT communication_account_login_id_unique;
-- DROP INDEX IF EXISTS idx_communication_account_login_id;

-- =====================================================
-- BƯỚC 7: Verification queries
-- =====================================================

-- Kiểm tra tổng số records sau migration
SELECT 
    'message_message' as table_name,
    COUNT(*) as total_records,
    COUNT(sender_account_id) as migrated_records,
    COUNT(*) - COUNT(sender_account_id) as failed_records
FROM message_message

UNION ALL

SELECT 
    'message_recipient_message' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN sender_account_id IS NOT NULL AND recipient_account_id IS NOT NULL THEN 1 END) as migrated_records,
    COUNT(CASE WHEN sender_account_id IS NULL OR recipient_account_id IS NULL THEN 1 END) as failed_records
FROM message_recipient_message

UNION ALL

SELECT 
    'message_target_recipient' as table_name,
    COUNT(*) as total_records,
    COUNT(recipient_account_id) as migrated_records,
    COUNT(*) - COUNT(recipient_account_id) as failed_records
FROM message_target_recipient
WHERE deliver_type = 'Private'

UNION ALL

SELECT 
    'communication_account' as table_name,
    COUNT(*) as total_records,
    COUNT(account_id) as migrated_records,
    COUNT(*) - COUNT(account_id) as failed_records
FROM communication_account;
